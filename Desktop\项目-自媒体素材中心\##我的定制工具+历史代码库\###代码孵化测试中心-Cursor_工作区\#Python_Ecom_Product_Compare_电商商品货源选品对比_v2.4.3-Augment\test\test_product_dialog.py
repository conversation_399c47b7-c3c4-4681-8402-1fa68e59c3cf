#!/usr/bin/env python3
"""
专门测试ProductDialog的简单程序
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("=" * 50)
print("ProductDialog 测试程序")
print("=" * 50)

try:
    print("1. 导入PyQt6...")
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from PyQt6.QtCore import Qt

    print("   PyQt6导入成功")

    print("2. 导入数据库服务...")
    from models.database import ProductService, TagService

    print("   数据库服务导入成功")

    print("3. 导入ProductDialog...")
    from views.dialogs.product_dialog import ProductDialog

    print("   ProductDialog导入成功")

    print("4. 创建QApplication...")
    app = QApplication(sys.argv)
    app.setApplicationName("ProductDialog测试")
    print("   QApplication创建成功")

    print("5. 创建数据库服务...")
    try:
        product_service = ProductService()
        print("   ProductService创建成功")

        tag_service = TagService()
        print("   TagService创建成功")
    except Exception as e:
        print(f"   数据库服务创建失败: {e}")
        product_service = None
        tag_service = None

    print("6. 创建ProductDialog...")
    try:
        dialog = ProductDialog(
            product_service=product_service, tag_service=tag_service, parent=None
        )
        print("   ProductDialog创建成功")

        print("7. 显示对话框...")
        result = dialog.exec()
        print(f"   对话框关闭，返回值: {result}")

    except Exception as e:
        print(f"   ProductDialog创建或显示失败: {e}")
        import traceback

        traceback.print_exc()

        # 显示错误消息
        QMessageBox.critical(None, "错误", f"ProductDialog测试失败: {str(e)}")

    print("=" * 50)
    print("测试完成")
    print("=" * 50)

except Exception as e:
    print(f"程序启动失败: {str(e)}")
    import traceback

    traceback.print_exc()
    input("按Enter键退出...")
