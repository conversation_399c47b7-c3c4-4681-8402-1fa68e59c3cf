"""
产品详情视图
显示产品的详细信息、货源列表、自定义字段等
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QMessageBox,
    QScrollArea,
    QFrame,
    QDialog,
    QListWidget,
    QListWidgetItem,
    QCheckBox,
    QGridLayout,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
import sys
import os
from pathlib import Path
import webbrowser
import logging

from models.database import ProductService
from models.product import Product
from utils import ImageDuplicateChecker, DuplicateResultDialog
from utils.image_utils import load_image_safely


class ProductDetailWidget(QWidget):
    """产品详情视图"""

    # 信号定义
    product_updated = pyqtSignal()  # 产品更新
    product_deleted = pyqtSignal()  # 产品删除

    def __init__(self, product_service: ProductService):
        super().__init__()
        self.product_service = product_service
        self.current_product = None

        # UI状态管理 - 防止重复创建UI
        self._ui_rebuilding = False
        self._ui_components = {}  # 存储UI组件引用
        self._pending_refresh = False  # 防止连续刷新

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.content_layout = QVBoxLayout(scroll_widget)

        # 默认显示空状态
        self.show_empty_state()

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

    def show_empty_state(self):
        """显示空状态"""
        # 清空布局
        self.clear_layout()

        # 空状态标签
        empty_label = QLabel("请从左侧列表选择一个产品")
        empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        empty_label.setStyleSheet("font-size: 16px; padding: 50px; opacity: 0.7;")

        self.content_layout.addWidget(empty_label)

    def load_product(self, product_id: int):
        """加载产品详情"""
        try:
            product = self.product_service.get_product(product_id)
            if not product:
                QMessageBox.warning(self, "警告", "产品不存在")
                self.show_empty_state()
                return

            self.current_product = product
            self.display_product()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载产品详情失败: {str(e)}")
            self.show_empty_state()

    def display_product(self):
        """显示产品信息 - 同步重建，防止重复创建UI"""
        # 防重入检查
        if self._ui_rebuilding:
            self._pending_refresh = True
            return

        if not self.current_product:
            self.show_empty_state()
            return

        try:
            self._ui_rebuilding = True
            self._pending_refresh = False

            # 同步清理和重建UI
            self._synchronous_rebuild_ui()

        finally:
            self._ui_rebuilding = False

            # 如果在重建过程中有新的刷新请求，处理它
            if self._pending_refresh:
                self._pending_refresh = False
                # 延迟执行，避免无限递归
                from PyQt6.QtCore import QTimer

                QTimer.singleShot(50, self.display_product)

    def _synchronous_rebuild_ui(self):
        """同步重建UI - 核心实现"""
        # 1. 彻底清理旧UI
        self._thorough_cleanup()

        # 2. 立即重建新UI
        self._display_product_content()

        # 3. 强制处理所有待处理事件
        from PyQt6.QtCore import QCoreApplication

        QCoreApplication.processEvents()

    def _display_product_content(self):
        """显示产品内容的实际实现"""
        if not self.current_product:
            return

        product = self.current_product
        # 确保product不为None
        if not product:
            return

        # 标题区域
        title_layout = QHBoxLayout()

        # 产品名称
        title_label = QLabel(product.name)
        title_label.setObjectName("productTitle")
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 编辑按钮
        edit_button = QPushButton("编辑")
        edit_button.clicked.connect(self.edit_product)
        title_layout.addWidget(edit_button)

        # 删除按钮
        delete_button = QPushButton("删除")
        delete_button.setStyleSheet("background-color: #f44336;")
        delete_button.clicked.connect(self.delete_product)
        title_layout.addWidget(delete_button)

        self.content_layout.addLayout(title_layout)

        # 图片区域
        images_widget = self.create_images_section()
        self.content_layout.addWidget(images_widget)

        # 基本信息
        info_widget = self.create_info_section()
        self.content_layout.addWidget(info_widget)

        # 货源信息
        sources_widget = self.create_sources_section()
        self.content_layout.addWidget(sources_widget)

        self.content_layout.addStretch()

    def create_info_section(self):
        """创建基本信息区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标题
        title_label = QLabel("基本信息")
        title_label.setObjectName("sectionTitle")
        layout.addWidget(title_label)

        product = self.current_product

        # 信息列表
        info_layout = QVBoxLayout()

        # SKU
        if product.sku:
            sku_label = QLabel(f"产品编码: {product.sku}")
            sku_label.setObjectName("infoLabel")
            info_layout.addWidget(sku_label)

        # 售价信息
        price_label = QLabel(f"基础售价: ¥{product.selling_price:.2f}")
        price_label.setObjectName("priceLabel")
        info_layout.addWidget(price_label)

        # 运费信息
        shipping_fee = getattr(product, "shipping_fee", 0.0)
        free_shipping = getattr(product, "free_shipping", True)

        if free_shipping:
            shipping_label = QLabel("运费: 包邮")
            shipping_label.setStyleSheet("color: #4caf50; font-weight: bold;")
        else:
            shipping_label = QLabel(f"运费: ¥{shipping_fee:.2f}")
            shipping_label.setStyleSheet("color: #ff9800; font-weight: bold;")

        shipping_label.setObjectName("infoLabel")
        info_layout.addWidget(shipping_label)

        # 实际收款价格
        actual_price = getattr(product, "actual_selling_price", product.selling_price)
        if abs(actual_price - product.selling_price) > 0.01:  # 有差异时显示
            actual_price_label = QLabel(f"实际收款: ¥{actual_price:.2f}")
            actual_price_label.setStyleSheet(
                "color: #2196f3; font-weight: bold; font-size: 14px;"
            )
            actual_price_label.setObjectName("priceLabel")
            info_layout.addWidget(actual_price_label)

        # 商品描述
        if product.description:
            desc_label = QLabel("商品描述:")
            desc_label.setObjectName("infoLabel")
            info_layout.addWidget(desc_label)

            desc_content = QLabel(product.description)
            desc_content.setObjectName("infoLabel")
            desc_content.setWordWrap(True)
            info_layout.addWidget(desc_content)

        # 销量参考
        if product.sales_reference > 0:
            sales_label = QLabel(f"销量参考: {product.sales_reference:,} 件")
            sales_label.setObjectName("salesLabel")
            info_layout.addWidget(sales_label)

        # 参考链接
        if (
            hasattr(product, "reference_url")
            and product.reference_url
            and product.reference_url.strip()
        ):
            reference_layout = QHBoxLayout()

            reference_label = QLabel("参考链接:")
            reference_label.setObjectName("infoLabel")
            reference_layout.addWidget(reference_label)

            # 打开链接按钮
            open_link_button = QPushButton("打开链接")
            open_link_button.setStyleSheet(
                "QPushButton { background-color: #4CAF50; color: white; padding: 6px 12px; font-size: 11px; border-radius: 4px; }"
            )
            open_link_button.clicked.connect(
                lambda: self.open_website(product.reference_url)
            )
            reference_layout.addWidget(open_link_button)

            reference_layout.addStretch()
            info_layout.addLayout(reference_layout)

        # 创建时间
        created_label = QLabel(
            f"创建时间: {product.created_at.strftime('%Y-%m-%d %H:%M:%S')}"
        )
        created_label.setObjectName("timeLabel")
        info_layout.addWidget(created_label)

        # 更新时间
        updated_label = QLabel(
            f"更新时间: {product.updated_at.strftime('%Y-%m-%d %H:%M:%S')}"
        )
        updated_label.setObjectName("timeLabel")
        info_layout.addWidget(updated_label)

        layout.addLayout(info_layout)

        return widget

    def create_sources_section(self):
        """创建货源信息区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标题行
        title_layout = QHBoxLayout()
        title_label = QLabel("货源信息")
        title_label.setObjectName("sectionTitle")
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 添加货源按钮
        add_source_button = QPushButton("添加货源")
        add_source_button.clicked.connect(self.add_source)
        title_layout.addWidget(add_source_button)

        layout.addLayout(title_layout)

        # 货源列表
        product = self.current_product
        if product.sources:
            for source in product.sources:
                source_widget = self.create_source_item(source)
                layout.addWidget(source_widget)
        else:
            no_sources_label = QLabel("暂无货源信息")
            no_sources_label.setStyleSheet(
                "font-style: italic; padding: 20px; opacity: 0.7;"
            )
            no_sources_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_sources_label)

        return widget

    def create_source_item(self, source):
        """创建货源项"""
        widget = QWidget()
        # 使用更轻的边框样式，让图片显示更自然
        widget.setStyleSheet(
            """
            QWidget {
                border: none;
                background-color: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
            }
        """
        )
        layout = QVBoxLayout(widget)

        # 标题行
        title_layout = QHBoxLayout()

        name_label = QLabel(source.name)
        name_label.setStyleSheet("font-weight: bold;")
        title_layout.addWidget(name_label)

        title_layout.addStretch()

        price_label = QLabel(f"¥{source.price:.2f}")
        price_label.setStyleSheet("font-weight: bold; color: #1976d2;")
        title_layout.addWidget(price_label)

        # 图片管理快捷按钮
        image_btn = QPushButton("管理图片")
        image_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; }")
        image_btn.clicked.connect(lambda: self.manage_source_images(source))
        title_layout.addWidget(image_btn)

        # 打开网站按钮
        if source.url and source.url.strip():
            open_website_button = QPushButton("打开网站")
            open_website_button.setStyleSheet(
                "QPushButton { background-color: #4CAF50; color: white; padding: 8px 16px; font-size: 12px; }"
            )
            open_website_button.clicked.connect(lambda: self.open_website(source.url))
            title_layout.addWidget(open_website_button)

        edit_button = QPushButton("编辑")
        edit_button.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; }")
        edit_button.clicked.connect(lambda: self.edit_source(source.id))
        title_layout.addWidget(edit_button)

        delete_button = QPushButton("删除")
        delete_button.setStyleSheet(
            "QPushButton { background-color: #f44336; padding: 8px 16px; font-size: 12px; }"
        )
        delete_button.clicked.connect(lambda: self.delete_source(source.id))
        title_layout.addWidget(delete_button)

        layout.addLayout(title_layout)

        # 基本信息行
        details_layout = QHBoxLayout()

        if source.shop_info:
            shop_label = QLabel(f"店铺: {source.shop_info}")
            shop_label.setStyleSheet("font-size: 12px; opacity: 0.7;")
            details_layout.addWidget(shop_label)

        # 状态标识
        if not source.is_active:
            status_label = QLabel("已停用")
            status_label.setStyleSheet(
                "color: #f44336; font-size: 12px; font-weight: bold;"
            )
            details_layout.addWidget(status_label)

        details_layout.addStretch()

        if details_layout.count() > 1:  # 只有在有内容时才添加
            layout.addLayout(details_layout)

        # 计算结果显示区域 - 为每种模式创建单独的行
        if self.current_product:
            selling_price = self.current_product.selling_price

            # 获取货源支持的模式
            supported_modes = getattr(source, "modes", ["wholesale"])
            if isinstance(supported_modes, str):
                supported_modes = [supported_modes]

            supports_wholesale = "wholesale" in supported_modes
            supports_dropship = "dropship" in supported_modes

            # 代发模式计算
            if supports_dropship:
                dropship_layout = QHBoxLayout()

                # 获取代发模式的独立参数
                dropship_price = getattr(source, "dropship_price", source.price)
                dropship_min_qty = getattr(
                    source,
                    "dropship_min_quantity",
                    getattr(source, "min_order_quantity", 1),
                )
                dropship_shipping = getattr(
                    source,
                    "dropship_shipping_cost",
                    getattr(source, "shipping_cost", 0),
                )
                dropship_quantity = getattr(
                    source, "dropship_quantity", getattr(source, "quantity", 1)
                )

                # 代发模式：成本为0，利润 = 售价 - (单价 + 运费) × 起批数量
                unit_cost_with_shipping = dropship_price + dropship_shipping
                dropship_profit = selling_price - unit_cost_with_shipping
                dropship_total_profit = dropship_profit * dropship_min_qty

                # 利润信息
                profit_text = f"利润: ¥{dropship_total_profit:.2f}"
                if dropship_min_qty > 1:
                    profit_text += f"(¥{dropship_profit:.2f}×{dropship_min_qty})"

                profit_label = QLabel(profit_text)
                profit_label.setStyleSheet(
                    "font-size: 12px; color: #FF69B4; font-weight: bold; min-width: 120px;"
                )
                if dropship_profit <= 0:
                    profit_label.setStyleSheet(
                        "font-size: 12px; color: #f44336; font-weight: bold; min-width: 120px;"
                    )
                dropship_layout.addWidget(profit_label)

                # 运费信息
                shipping_text = f"运费: ¥{dropship_shipping:.2f}"
                shipping_label = QLabel(shipping_text)
                shipping_label.setStyleSheet(
                    "font-size: 12px; opacity: 0.7; color: #4caf50; min-width: 80px;"
                )
                dropship_layout.addWidget(shipping_label)

                # 起批信息
                min_order_text = f"起批: {dropship_min_qty}"
                min_order_label = QLabel(min_order_text)
                min_order_label.setStyleSheet(
                    "font-size: 12px; opacity: 0.7; color: #9c27b0; min-width: 70px;"
                )
                dropship_layout.addWidget(min_order_label)

                # 库存信息
                if dropship_quantity > 0:
                    quantity_text = f"库存: {dropship_quantity}"
                    quantity_label = QLabel(quantity_text)
                    quantity_label.setStyleSheet(
                        "font-size: 12px; opacity: 0.7; min-width: 80px;"
                    )
                    dropship_layout.addWidget(quantity_label)

                # 成本信息（放在最后，避免影响前面信息的对齐）
                cost_label = QLabel("代发成本: ¥0.00(无囤货)")
                cost_label.setStyleSheet(
                    "font-size: 12px; opacity: 0.7; min-width: 140px;"
                )
                dropship_layout.addWidget(cost_label)

                dropship_layout.addStretch()

                # 模式标签
                mode_label = QLabel("代发模式")
                mode_label.setStyleSheet(
                    "font-size: 12px; color: #FF6B35; font-weight: bold; background-color: rgba(255, 107, 53, 0.1); padding: 2px 6px; border-radius: 3px;"
                )
                dropship_layout.addWidget(mode_label)

                layout.addLayout(dropship_layout)

            # 批发模式计算
            if supports_wholesale:
                wholesale_layout = QHBoxLayout()

                # 获取批发模式的独立参数
                wholesale_price = getattr(source, "wholesale_price", source.price)
                wholesale_min_qty = getattr(
                    source,
                    "wholesale_min_order_quantity",
                    getattr(source, "min_order_quantity", 1),
                )
                wholesale_shipping = getattr(
                    source,
                    "wholesale_shipping_cost",
                    getattr(source, "shipping_cost", 0),
                )
                wholesale_quantity = getattr(
                    source, "wholesale_quantity", getattr(source, "quantity", 1)
                )

                # 批发模式：成本 = (单价 × 起批数量) + 运费，利润 = 售价 - 成本
                wholesale_cost = (
                    wholesale_price * wholesale_min_qty
                ) + wholesale_shipping
                wholesale_profit = selling_price - wholesale_cost

                # 利润信息
                profit_label = QLabel(f"利润: ¥{wholesale_profit:.2f}")
                profit_label.setStyleSheet(
                    "font-size: 12px; color: #FF69B4; font-weight: bold; min-width: 120px;"
                )
                if wholesale_profit <= 0:
                    profit_label.setStyleSheet(
                        "font-size: 12px; color: #f44336; font-weight: bold; min-width: 120px;"
                    )
                wholesale_layout.addWidget(profit_label)

                # 运费信息
                shipping_text = f"运费: ¥{wholesale_shipping:.2f}"
                shipping_label = QLabel(shipping_text)
                shipping_label.setStyleSheet(
                    "font-size: 12px; opacity: 0.7; color: #4caf50; min-width: 80px;"
                )
                wholesale_layout.addWidget(shipping_label)

                # 起批信息
                min_order_text = f"起批: {wholesale_min_qty}"
                min_order_label = QLabel(min_order_text)
                min_order_label.setStyleSheet(
                    "font-size: 12px; opacity: 0.7; color: #9c27b0; min-width: 70px;"
                )
                wholesale_layout.addWidget(min_order_label)

                # 库存信息
                if wholesale_quantity > 0:
                    quantity_text = f"库存: {wholesale_quantity}"
                    quantity_label = QLabel(quantity_text)
                    quantity_label.setStyleSheet(
                        "font-size: 12px; opacity: 0.7; min-width: 80px;"
                    )
                    wholesale_layout.addWidget(quantity_label)

                # 成本信息（放在最后，避免影响前面信息的对齐）
                cost_text = f"批发成本: ¥{wholesale_cost:.2f}"
                if wholesale_min_qty > 1:
                    cost_text += f"(¥{wholesale_price:.2f}×{wholesale_min_qty}+¥{wholesale_shipping:.2f})"
                elif wholesale_shipping > 0:
                    cost_text += f"(¥{wholesale_price:.2f}+¥{wholesale_shipping:.2f})"

                cost_label = QLabel(cost_text)
                cost_label.setStyleSheet(
                    "font-size: 12px; opacity: 0.7; min-width: 140px;"
                )
                wholesale_layout.addWidget(cost_label)

                wholesale_layout.addStretch()

                # 模式标签
                mode_label = QLabel("批发模式")
                mode_label.setStyleSheet(
                    "font-size: 12px; color: #2196f3; font-weight: bold; background-color: rgba(33, 150, 243, 0.1); padding: 2px 6px; border-radius: 3px;"
                )
                wholesale_layout.addWidget(mode_label)

                layout.addLayout(wholesale_layout)

        # 货源图片显示
        if source.image_urls:
            # 使用图片查看器组件，优先显示主要图片
            from views.image_viewer import ImageViewer

            # 获取用于显示的图片（优先显示主要图片）
            display_images = source.get_display_images()

            image_viewer = ImageViewer(display_images, editable=False)
            image_viewer.setMaximumHeight(500)  # 与产品图片显示保持一致的高度

            self.apply_clean_image_viewer_style(image_viewer)

            layout.addWidget(image_viewer)
        else:
            no_images_label = QLabel("暂无货源图片")
            no_images_label.setStyleSheet(
                "font-style: italic; padding: 10px; opacity: 0.7;"
            )
            no_images_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_images_label)

        # 联系方式和备注
        if source.contact_info:
            contact_label = QLabel(f"联系方式: {source.contact_info}")
            contact_label.setStyleSheet("font-size: 11px; opacity: 0.7;")
            layout.addWidget(contact_label)

        if source.notes:
            notes_label = QLabel(f"备注: {source.notes}")
            notes_label.setStyleSheet("font-size: 11px; opacity: 0.7;")
            notes_label.setWordWrap(True)
            layout.addWidget(notes_label)

        return widget

    def apply_clean_image_viewer_style(self, image_viewer):
        """应用简洁的图片查看器样式，去除多余边框"""
        image_viewer.setStyleSheet(
            """
            /* 主要容器去除边框 */
            QWidget#imageViewer {
                border: none;
                background-color: transparent;
            }
            
            /* 分割器去除边框 */
            QSplitter#imageViewerSplitter {
                border: none;
                background-color: transparent;
            }
            
            /* 图片显示容器去除边框 */
            QWidget#imageDisplayContainer {
                border: none;
                background-color: transparent;
            }
            
            QFrame#imageDisplayContainer {
                border: none;
                background-color: transparent;
            }
            
            /* 图片框架去除边框 */
            QFrame[objectName^="imageFrame"] {
                border: none;
                background-color: transparent;
            }
            
            /* 图片标签去除边框 */
            QLabel[objectName^="imageLabel"] {
                border: none;
                background-color: transparent;
            }
            
            /* 控制按钮区域去除边框 */
            QWidget#imageControlsWidget {
                border: none;
                background-color: transparent;
            }
            
            /* 图片列表区域去除边框 */
            QWidget#imageListContainer {
                border: none;
                background-color: transparent;
            }
            
            QListWidget#imageList {
                border: none;
                background-color: transparent;
            }
            
            /* 所有内部widget去除边框 */
            QWidget {
                border: none;
            }
            
            /* 所有内部frame去除边框 */
            QFrame {
                border: none;
                background-color: transparent;
            }
        """
        )

    def create_source_images_section(self, source):
        """创建货源图片区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标题行
        title_layout = QHBoxLayout()
        title_label = QLabel("货源图片")
        title_label.setStyleSheet(
            "font-size: 14px; font-weight: bold; margin-bottom: 5px; color: #4CAF50;"
        )
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 管理图片按钮
        manage_images_button = QPushButton("管理图片")
        manage_images_button.clicked.connect(lambda: self.manage_source_images(source))
        title_layout.addWidget(manage_images_button)

        layout.addLayout(title_layout)

        # 图片显示
        if source.image_urls:
            # 使用图片查看器组件，优先显示主要图片
            from views.image_viewer import ImageViewer

            # 获取用于显示的图片（优先显示主要图片）
            display_images = source.get_display_images()

            image_viewer = ImageViewer(display_images, editable=False)
            image_viewer.setMaximumHeight(500)  # 与产品图片显示保持一致的高度

            self.apply_clean_image_viewer_style(image_viewer)

            # 追踪货源图片查看器组件
            component_key = f"source_{source.id}_image_viewer"
            self._ui_components[component_key] = image_viewer

            layout.addWidget(image_viewer)
        else:
            no_images_label = QLabel("暂无货源图片")
            no_images_label.setStyleSheet(
                "font-style: italic; padding: 10px; opacity: 0.7;"
            )
            no_images_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_images_label)

        return widget

    def manage_source_images(self, source):
        """管理货源图片"""
        from views.dialogs.source_dialog import SourceImageManagementDialog

        dialog = SourceImageManagementDialog(source, self.product_service, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 刷新显示
            self.refresh()

    def create_images_section(self):
        """创建图片区域 - 带组件追踪"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标题行
        title_layout = QHBoxLayout()
        title_label = QLabel("产品图片")
        title_label.setStyleSheet(
            "font-size: 16px; font-weight: bold; margin-bottom: 10px;"
        )
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 管理图片按钮
        manage_images_button = QPushButton("管理图片")
        manage_images_button.clicked.connect(self.manage_images)
        title_layout.addWidget(manage_images_button)

        layout.addLayout(title_layout)

        # 图片显示
        product = self.current_product
        if product and product.images:
            # 使用图片查看器组件，优先显示主要图片
            from views.image_viewer import ImageViewer

            # 获取用于显示的图片（优先显示主要图片）
            display_images = product.get_display_images()

            # 创建并追踪ImageViewer组件
            image_viewer = ImageViewer(display_images, editable=False)
            image_viewer.setMaximumHeight(500)  # 增加最大高度以显示完整内容

            self.apply_clean_image_viewer_style(image_viewer)

            # 追踪组件以便后续清理
            self._ui_components["product_image_viewer"] = image_viewer

            layout.addWidget(image_viewer)
        else:
            no_images_label = QLabel("暂无产品图片")
            no_images_label.setStyleSheet(
                "font-style: italic; padding: 20px; opacity: 0.7;"
            )
            no_images_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_images_label)

        return widget

    def manage_images(self):
        """管理图片"""
        if not self.current_product:
            return

        # 重新从数据库加载最新的产品数据，确保数据同步
        fresh_product = self.product_service.get_product(self.current_product.id)
        if not fresh_product:
            QMessageBox.warning(self, "错误", "无法加载产品数据")
            return

        # 创建图片管理对话框
        dialog = ImageManagementDialog(fresh_product, self.product_service, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 刷新显示
            self.refresh()

    def _thorough_cleanup(self):
        """彻底清理UI组件 - 同步版本"""
        # 1. 清理已知的UI组件引用
        for component_name, component in self._ui_components.items():
            try:
                if component and hasattr(component, "setParent"):
                    component.setParent(None)
                    component.hide()
                    if hasattr(component, "deleteLater"):
                        component.deleteLater()
            except:
                pass
        self._ui_components.clear()

        # 2. 清理布局中的所有控件
        self.clear_layout()

        # 3. 强制处理删除事件
        from PyQt6.QtCore import QCoreApplication

        for _ in range(5):  # 增加处理次数确保彻底清理
            QCoreApplication.processEvents()

    def clear_layout(self):
        """清空布局"""
        # 收集所有要删除的控件
        widgets_to_delete = []

        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                widgets_to_delete.append(child.widget())
            elif child.layout():
                # 清理嵌套布局
                self.clear_nested_layout(child.layout())

        # 立即删除所有控件
        for widget in widgets_to_delete:
            try:
                widget.setParent(None)
                widget.hide()  # 立即隐藏
                widget.deleteLater()
            except:
                pass

        # 强制刷新布局
        self.content_layout.update()

        # 多次处理事件，确保完全清理
        from PyQt6.QtCore import QCoreApplication

        for _ in range(3):  # 多次处理事件
            QCoreApplication.processEvents()

        # 再次确保布局为空
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)
                child.widget().hide()
                child.widget().deleteLater()
            elif child.layout():
                self.clear_nested_layout(child.layout())

    def clear_nested_layout(self, layout):
        """清理嵌套布局"""
        widgets_to_delete = []

        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                widgets_to_delete.append(child.widget())
            elif child.layout():
                self.clear_nested_layout(child.layout())

        # 立即删除所有控件
        for widget in widgets_to_delete:
            try:
                widget.setParent(None)
                widget.deleteLater()
            except:
                pass

    def edit_product(self):
        """编辑产品"""
        if not self.current_product:
            return

        from views.dialogs import ProductDialog

        dialog = ProductDialog(
            product=self.current_product,
            product_service=self.product_service,
            parent=self,
        )
        dialog.product_saved.connect(self.on_product_saved)
        dialog.exec()

    def on_product_saved(self, product):
        """产品保存后的回调"""
        self.refresh()
        self.product_updated.emit()

    def delete_product(self):
        """删除产品"""
        if not self.current_product:
            return

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除产品 '{self.current_product.name}' 吗？\n\n此操作将同时删除相关的货源和自定义字段数据。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                success = self.product_service.delete_product(self.current_product.id)
                if success:
                    # QMessageBox.information(self, "成功", "产品删除成功")  # 移除弹窗
                    self.show_empty_state()
                    self.product_deleted.emit()
                else:
                    QMessageBox.warning(self, "警告", "产品删除失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除产品失败: {str(e)}")

    def add_source(self):
        """添加货源"""
        if not self.current_product:
            return

        from views.dialogs import SourceDialog

        dialog = SourceDialog(
            product_id=self.current_product.id,
            product_service=self.product_service,
            parent=self,
        )
        dialog.source_saved.connect(self.on_source_saved)
        dialog.exec()

    def edit_source(self, source_id: int):
        """编辑货源"""
        if not self.current_product:
            return

        # 找到对应的货源
        source = None
        for s in self.current_product.sources:
            if s.id == source_id:
                source = s
                break

        if not source:
            QMessageBox.warning(self, "警告", "货源不存在")
            return

        from views.dialogs import SourceDialog

        dialog = SourceDialog(
            source=source, product_service=self.product_service, parent=self
        )
        dialog.source_saved.connect(self.on_source_saved)
        dialog.exec()

    def delete_source(self, source_id: int):
        """删除货源"""
        if not self.current_product:
            return

        # 找到对应的货源
        source = None
        for s in self.current_product.sources:
            if s.id == source_id:
                source = s
                break

        if not source:
            QMessageBox.warning(self, "警告", "货源不存在")
            return

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除货源 '{source.name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                success = self.product_service.delete_source(source_id)
                if success:
                    # QMessageBox.information(self, "成功", "货源删除成功")  # 移除弹窗
                    self.refresh()
                else:
                    QMessageBox.warning(self, "警告", "货源删除失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除货源失败: {str(e)}")

    def on_source_saved(self, source):
        """货源保存后的回调"""
        self.refresh()

    def refresh(self):
        """刷新当前产品 - 使用优化的重建机制"""
        if self.current_product:
            self.load_product(self.current_product.id)

    def force_refresh(self):
        """强制刷新当前产品 - 用于解决UI重复显示问题"""
        print("=== 开始强制刷新ProductDetailWidget ===")

        # 强制清理所有UI元素
        self.clear_layout()

        # 额外延迟确保完全清理
        from PyQt6.QtCore import QTimer, QCoreApplication

        # 多次处理事件
        for _ in range(5):
            QCoreApplication.processEvents()

        # 使用更长的延迟重新加载
        if self.current_product:
            print(f"将重新加载产品: {self.current_product.id}")
            QTimer.singleShot(200, lambda: self.load_product(self.current_product.id))
        else:
            print("没有当前产品，显示空状态")
            QTimer.singleShot(200, self.show_empty_state)

        print("=== 强制刷新ProductDetailWidget完成 ===")

    def open_website(self, url):
        """打开网站"""
        try:
            if not url or not url.strip():
                QMessageBox.warning(self, "警告", "网址为空或无效")
                return

            # 确保URL包含协议
            if not url.startswith(("http://", "https://")):
                url = "https://" + url

            # 使用系统默认浏览器打开网站
            webbrowser.open(url)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开网站: {str(e)}")


class ImageManagementDialog(QDialog):
    """图片管理对话框"""

    def __init__(self, product: Product, product_service: ProductService, parent=None):
        super().__init__(parent)
        self.product = product
        self.product_service = product_service

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle(f"管理图片 - {self.product.name}")
        self.setMinimumSize(1400, 900)  # 调整窗口尺寸以更好利用空间
        self.resize(1400, 900)  # 设置初始大小

        # 初始化图片列表属性
        self.updated_images = self.product.images.copy() if self.product.images else []
        # 安全地获取主要图片列表
        if hasattr(self.product, "featured_images") and self.product.featured_images:
            self.updated_featured_images = self.product.featured_images.copy()
        else:
            self.updated_featured_images = []

        # 初始化UI组件列表（确保在任何情况下都有这些属性）
        self.image_frames = []
        self.image_labels = []
        self.featured_checkboxes = []
        self.delete_checkboxes = []
        self.selected_for_deletion = set()
        self.current_group_index = 0

        # 验证并清理图片列表
        self.validate_and_clean_images()

        layout = QVBoxLayout(self)

        # 说明文字
        info_label = QLabel(
            "您可以添加、删除或重新排序产品图片。勾选图片复选框可设置为主要图片（最多3个），主要图片将在产品详情页面优先显示。"
        )
        info_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 图片统计信息
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet(
            "color: #1976d2; font-weight: bold; margin-bottom: 10px;"
        )
        self.update_stats_display()
        layout.addWidget(self.stats_label)

        # 创建自定义的图片查看器
        self.create_custom_image_viewer(layout)

        # 按钮
        from PyQt6.QtWidgets import QDialogButtonBox

        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 添加一个立即保存按钮
        save_now_button = QPushButton("💾 立即保存")
        # 不设置工具提示，避免空白提示
        save_now_button.clicked.connect(self.save_now)
        save_now_button.setStyleSheet(
            """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        )
        button_box.addButton(save_now_button, QDialogButtonBox.ButtonRole.ActionRole)

    def create_custom_image_viewer(self, layout):
        """创建自定义的图片查看器，在图片预览区域集成复选框"""
        from PyQt6.QtWidgets import QSplitter, QListWidget, QListWidgetItem
        from PyQt6.QtCore import Qt

        # 创建分割窗口
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：图片列表（用于选择和管理）
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # 列表标题
        list_title = QLabel("图片列表")
        list_title.setStyleSheet(
            "font-weight: bold; margin-bottom: 5px; color: #1976d2;"
        )
        left_layout.addWidget(list_title)

        # 图片列表（支持多选）
        self.simple_image_list = QListWidget()
        self.simple_image_list.setMinimumWidth(250)  # 减少左侧列表宽度
        self.simple_image_list.setMaximumWidth(300)  # 设置最大宽度
        self.simple_image_list.setSelectionMode(
            QListWidget.SelectionMode.ExtendedSelection
        )
        self.simple_image_list.currentRowChanged.connect(self.on_simple_image_selected)
        left_layout.addWidget(self.simple_image_list)

        # 初始化选中状态追踪
        self.selected_for_deletion = set()

        # 操作按钮 - 2行3列布局
        buttons_widget = QWidget()
        buttons_layout = QGridLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 10)
        buttons_layout.setSpacing(8)

        # 第一行按钮
        add_button = QPushButton("添加图片")
        add_button.clicked.connect(self.add_images)
        buttons_layout.addWidget(add_button, 0, 0)

        remove_button = QPushButton("删除选中")
        remove_button.clicked.connect(self.remove_selected_images)
        remove_button.setStyleSheet(
            """
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """
        )
        buttons_layout.addWidget(remove_button, 0, 1)

        # 打开目录按钮
        open_folder_button = QPushButton("📁 打开目录")
        open_folder_button.setToolTip("打开产品图片目录")
        open_folder_button.clicked.connect(self.open_image_folder)
        open_folder_button.setStyleSheet(
            """
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """
        )
        buttons_layout.addWidget(open_folder_button, 0, 2)

        # 第二行按钮
        select_all_button = QPushButton("全选")
        select_all_button.clicked.connect(self.select_all_images)
        buttons_layout.addWidget(select_all_button, 1, 0)

        select_none_button = QPushButton("取消选择")
        select_none_button.clicked.connect(self.select_none_images)
        buttons_layout.addWidget(select_none_button, 1, 1)

        # 查重按钮移到第二行
        duplicate_button = QPushButton("🔍 查重")
        duplicate_button.setToolTip("查找重复或相似的图片")
        duplicate_button.clicked.connect(self.check_duplicates)
        duplicate_button.setStyleSheet(
            """
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """
        )
        buttons_layout.addWidget(duplicate_button, 1, 2)

        left_layout.addWidget(buttons_widget)

        splitter.addWidget(left_widget)

        # 右侧：带复选框的图片预览区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # 标题
        preview_title = QLabel(
            "图片预览（勾选删除❌或设为主要图片⭐，最多3个主要图片）🖱️滚轮滑动浏览"
        )
        preview_title.setStyleSheet(
            "font-weight: bold; margin-bottom: 5px; color: #1976d2;"
        )
        right_layout.addWidget(preview_title)

        # 创建自定义的图片显示区域
        self.create_custom_image_display(right_layout)

        # 控制按钮
        self.create_image_controls(right_layout)

        splitter.addWidget(right_widget)

        # 设置分割比例 - 让右侧图片预览区域占更多空间
        splitter.setSizes([280, 1120])

        layout.addWidget(splitter)

        # 初始化显示
        self.current_group_index = 0

        # 使用QTimer延迟初始化显示，确保UI完全构建完成
        from PyQt6.QtCore import QTimer

        def delayed_init():
            if self._is_ui_initialized():
                self.update_simple_image_list()
                self.update_image_display()
                if hasattr(self, "update_stats_display"):
                    self.update_stats_display()
                print("UI组件初始化完成，显示已更新")
            else:
                print("UI组件仍未完全初始化")

        QTimer.singleShot(100, delayed_init)

    def create_custom_image_display(self, layout):
        """创建带复选框的图片显示区域"""
        from PyQt6.QtWidgets import QFrame, QCheckBox, QGridLayout
        from PyQt6.QtCore import Qt

        # 清空之前的列表（不重新初始化，因为在setup_ui中已经初始化了）
        self.image_frames.clear()
        self.image_labels.clear()
        self.featured_checkboxes.clear()
        self.delete_checkboxes.clear()

        # 主图片显示容器
        self.display_container = QWidget()
        self.display_container.setObjectName("displayContainer")
        self.display_container.setMinimumHeight(700)  # 增加容器高度
        self.display_container.setMaximumHeight(750)  # 设置最大高度

        display_layout = QGridLayout(self.display_container)
        display_layout.setContentsMargins(10, 10, 10, 10)  # 适当的边距
        display_layout.setSpacing(15)  # 适当的间距

        # 显示当前组的图片 (最多6张，2行3列)
        for i in range(6):
            row = i // 3  # 行号 (0或1)
            col = i % 3  # 列号 (0, 1, 2)

            # 图片框架 - 大幅增大尺寸以更好利用空间
            frame = QFrame()
            frame.setObjectName(f"imageFrame{i}")
            frame.setMinimumSize(300, 350)  # 大幅增大框架尺寸
            frame.setMaximumSize(350, 400)  # 大幅增大最大尺寸

            frame_layout = QVBoxLayout(frame)
            frame_layout.setContentsMargins(8, 8, 8, 25)  # 适当的边距给复选框空间
            frame_layout.setSpacing(5)

            # 图片标签 - 大幅增大图片显示区域高度
            image_label = QLabel()
            image_label.setObjectName(f"imageLabel{i}")
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label.setMinimumHeight(280)  # 大幅增大图片区域高度
            image_label.setMaximumHeight(310)
            image_label.setStyleSheet(
                """
                QLabel {
                    border: 1px solid #444;
                    border-radius: 4px;
                    background-color: #2a2a2a;
                }
            """
            )

            frame_layout.addWidget(image_label)

            # 图片信息标签
            info_label = QLabel()
            info_label.setObjectName(f"infoLabel{i}")
            info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            info_label.setStyleSheet(
                "font-size: 10px; color: #888; padding: 2px; max-height: 15px;"
            )
            frame_layout.addWidget(info_label)

            # 复选框区域 - 确保可见且不重叠
            checkbox_container = QWidget()
            checkbox_container.setFixedHeight(20)  # 固定高度确保可见
            checkbox_layout = QHBoxLayout(checkbox_container)
            checkbox_layout.setContentsMargins(2, 0, 2, 0)
            checkbox_layout.setSpacing(8)

            # 主要图片复选框（左侧）
            featured_checkbox = QCheckBox("⭐")
            featured_checkbox.setObjectName(f"featuredCheckbox{i}")
            featured_checkbox.setToolTip("设为主要图片")
            featured_checkbox.setStyleSheet(
                """
                QCheckBox {
                    font-size: 12px;
                    font-weight: bold;
                    color: #4CAF50;
                    padding: 1px;
                }
                QCheckBox::indicator {
                    width: 14px;
                    height: 14px;
                    border: 2px solid #4CAF50;
                    border-radius: 3px;
                    background-color: #2a2a2a;
                }
                QCheckBox::indicator:checked {
                    background-color: #4CAF50;
                    border: 2px solid #4CAF50;
                }
                QCheckBox::indicator:hover {
                    border: 2px solid #66BB6A;
                    background-color: #2e2e2e;
                }
                QCheckBox::indicator:checked:hover {
                    background-color: #66BB6A;
                    border: 2px solid #66BB6A;
                }
            """
            )
            checkbox_layout.addWidget(featured_checkbox)

            checkbox_layout.addStretch()

            # 删除复选框（右侧）
            delete_checkbox = QCheckBox("🗑️")
            delete_checkbox.setObjectName(f"deleteCheckbox{i}")
            delete_checkbox.setToolTip("标记删除")
            delete_checkbox.setStyleSheet(
                """
                QCheckBox {
                    font-size: 12px;
                    font-weight: bold;
                    color: #f44336;
                    padding: 1px;
                }
                QCheckBox::indicator {
                    width: 14px;
                    height: 14px;
                    border: 2px solid #f44336;
                    border-radius: 3px;
                    background-color: #2a2a2a;
                }
                QCheckBox::indicator:checked {
                    background-color: #f44336;
                    border: 2px solid #f44336;
                }
                QCheckBox::indicator:hover {
                    border: 2px solid #ef5350;
                    background-color: #2e2e2e;
                }
                QCheckBox::indicator:checked:hover {
                    background-color: #ef5350;
                    border: 2px solid #ef5350;
                }
            """
            )
            checkbox_layout.addWidget(delete_checkbox)

            frame_layout.addWidget(checkbox_container)

            # 应用样式到框架
            frame.setStyleSheet(
                """
                QFrame {
                    border: 1px solid #555;
                    border-radius: 6px;
                    background-color: #333;
                    margin: 2px;
                }
                QFrame:hover {
                    border: 1px solid #777;
                    background-color: #383838;
                }
            """
            )

            # 添加到网格布局，确保不重叠
            display_layout.addWidget(frame, row, col)

            # 存储到列表中供后续使用
            self.image_frames.append(frame)
            self.image_labels.append(image_label)
            self.featured_checkboxes.append(featured_checkbox)
            self.delete_checkboxes.append(delete_checkbox)

        # 滚轮事件处理
        def wheelEvent(event):
            # 处理滚轮事件进行图片组翻页
            if event.angleDelta().y() > 0:
                self.previous_group()
            else:
                self.next_group()

        self.display_container.wheelEvent = wheelEvent

        layout.addWidget(self.display_container)

    def create_image_controls(self, layout):
        """创建图片控制按钮区域"""
        widget = QWidget()
        widget.setObjectName("imageControlsWidget")
        controls_layout = QHBoxLayout(widget)
        controls_layout.setContentsMargins(0, 10, 0, 0)

        self.prev_button = QPushButton("◀ 上一组")
        self.prev_button.setObjectName("prevButton")
        self.prev_button.clicked.connect(self.previous_group)
        controls_layout.addWidget(self.prev_button)

        self.info_label = QLabel("暂无图片")
        self.info_label.setObjectName("infoLabel")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        controls_layout.addWidget(self.info_label)

        self.next_button = QPushButton("下一组 ▶")
        self.next_button.setObjectName("nextButton")
        self.next_button.clicked.connect(self.next_group)
        controls_layout.addWidget(self.next_button)

        layout.addWidget(widget)

    def select_all_images(self):
        """全选所有图片"""
        for i in range(len(self.updated_images)):
            if i < self.simple_image_list.count():
                item = self.simple_image_list.item(i)
                if item:
                    item.setSelected(True)

        # 同时选中右侧所有显示的删除复选框
        current_start = self.current_group_index * 6
        for i in range(6):
            image_index = current_start + i
            if image_index < len(self.updated_images):
                checkbox = self.display_container.findChild(
                    QCheckBox, f"deleteCheckbox{i}"
                )
                if checkbox:
                    checkbox.setChecked(True)
                    image_path = self.updated_images[image_index]
                    self.selected_for_deletion.add(image_path)

    def select_none_images(self):
        """取消选择所有图片"""
        # 取消左侧列表选择
        self.simple_image_list.clearSelection()

        # 取消右侧删除复选框选择
        for i in range(6):
            checkbox = self.display_container.findChild(QCheckBox, f"deleteCheckbox{i}")
            if checkbox:
                checkbox.setChecked(False)

        # 清空删除选择集合
        self.selected_for_deletion.clear()

        # 更新状态显示
        self.update_simple_image_list()

    def on_delete_checkbox_changed(self, state, image_index):
        """删除复选框状态改变处理 - 修复索引计算错误"""
        try:
            # 直接使用传入的image_index，不再重复计算
            if image_index >= len(self.updated_images):
                print(
                    f"警告：图片索引超出范围 {image_index} >= {len(self.updated_images)}"
                )
                return

            image_path = self.updated_images[image_index]

            if state == Qt.CheckState.Checked.value:
                self.selected_for_deletion.add(image_path)
                print(f"🗑️ 标记删除图片 #{image_index + 1}")
            else:
                self.selected_for_deletion.discard(image_path)
                print(f"↩️ 取消删除图片 #{image_index + 1}")

            # 同步左侧列表选择状态
            if image_index < self.simple_image_list.count():
                item = self.simple_image_list.item(image_index)
                if item:
                    item.setSelected(state == Qt.CheckState.Checked.value)

            # 更新状态显示
            self.update_simple_image_list()
            self.update_image_display()
            self.update_stats_display()
        except Exception as e:
            print(f"删除复选框状态改变异常: {e}")
            import traceback

            traceback.print_exc()

    def on_featured_checkbox_changed(self, state, image_index):
        """主要图片复选框状态改变处理 - 修复索引计算错误"""
        try:
            from pathlib import Path

            # 直接使用传入的image_index，不再重复计算
            if image_index >= len(self.updated_images):
                print(
                    f"警告：图片索引超出范围 {image_index} >= {len(self.updated_images)}"
                )
                return

            image_path = self.updated_images[image_index]

            if state == Qt.CheckState.Checked.value:
                # 添加到主要图片列表（最多3个）
                if len(self.updated_featured_images) < 3:
                    if image_path not in self.updated_featured_images:
                        self.updated_featured_images.append(image_path)
                        print(
                            f"✅ 添加主要图片 #{image_index + 1}: {Path(image_path).name}"
                        )
                else:
                    # 如果已达到上限，提示用户并取消选择
                    from PyQt6.QtWidgets import QMessageBox

                    QMessageBox.warning(self, "警告", "最多只能选择3张主要图片！")

                    # 计算当前图片在显示框架中的位置
                    position_in_frame = image_index - self.current_group_index * 6
                    if 0 <= position_in_frame < len(self.featured_checkboxes):
                        checkbox = self.featured_checkboxes[position_in_frame]
                        if checkbox:
                            checkbox.setChecked(False)
                    return
            else:
                # 从主要图片列表中移除
                if image_path in self.updated_featured_images:
                    self.updated_featured_images.remove(image_path)
                    print(
                        f"➖ 移除主要图片 #{image_index + 1}: {Path(image_path).name}"
                    )

            # 立即保存到数据库以确保数据一致性
            self.save_changes_to_database()

            # 更新状态显示
            self.update_simple_image_list()
            self.update_image_display()  # 同步更新右侧复选框状态
            self.update_stats_display()

            print(f"📊 当前主要图片数量: {len(self.updated_featured_images)}")

        except Exception as e:
            print(f"主要图片复选框状态改变异常: {e}")
            import traceback

            traceback.print_exc()

    def remove_selected_images(self):
        """删除选中的图片（支持多选）"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            # 获取选中的图片（从列表选择和删除复选框）
            selected_indices = []
            selected_items = self.simple_image_list.selectedItems()

            # 从列表选择获取索引
            for item in selected_items:
                for i in range(self.simple_image_list.count()):
                    if self.simple_image_list.item(i) == item:
                        selected_indices.append(i)
                        break

            # 合并从删除复选框选择的图片
            selected_paths = set()
            for idx in selected_indices:
                if 0 <= idx < len(self.updated_images):
                    selected_paths.add(self.updated_images[idx])

            # 添加通过删除复选框选择的图片
            selected_paths.update(self.selected_for_deletion)

            if not selected_paths:
                QMessageBox.information(self, "提示", "请先选择要删除的图片")
                return

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {len(selected_paths)} 张图片吗？\n\n注意：这将永久删除图片文件！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 记录删除前的数量
                before_count = len(self.updated_images)

                # 删除选中的图片
                removed_images = []
                removed_count = 0
                for image_path in selected_paths:
                    if image_path in self.updated_images:
                        self.updated_images.remove(image_path)
                        removed_images.append(image_path)  # 记录被删除的图片路径
                        removed_count += 1

                    # 如果被删除的图片是主要图片，也要从主要图片列表中移除
                    if image_path in self.updated_featured_images:
                        self.updated_featured_images.remove(image_path)

                # 删除物理文件
                if removed_images:
                    self.cleanup_removed_images(removed_images)
                    print(f"已删除 {len(removed_images)} 个物理图片文件")

                # 清空选择状态
                self.selected_for_deletion.clear()

                # 调整当前显示的组索引
                if self.updated_images:
                    total_groups = max(1, (len(self.updated_images) + 5) // 6)
                    if self.current_group_index >= total_groups:
                        self.current_group_index = max(0, total_groups - 1)
                else:
                    self.current_group_index = 0

                # 立即保存到数据库
                self.save_changes_to_database()

                # 更新显示
                self.update_simple_image_list()
                self.update_image_display()
                self.update_stats_display()

                # 发出图片变化信号
                self.on_images_changed(self.updated_images)

                # 显示删除结果
                after_count = len(self.updated_images)
                QMessageBox.information(
                    self,
                    "删除成功",
                    f"已删除 {removed_count} 张图片\n"
                    f"删除前：{before_count} 张\n"
                    f"删除后：{after_count} 张\n"
                    f"✓ 已同步到数据库\n"
                    f"✓ 已删除物理文件",
                )

        except Exception as e:
            print(f"删除图片失败: {e}")
            import traceback

            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"删除图片失败: {str(e)}")

    def remove_selected_image(self):
        """删除选中的图片（保持向后兼容）"""
        try:
            current_row = self.simple_image_list.currentRow()
            if 0 <= current_row < len(self.updated_images):
                # 使用新的多选删除功能
                self.simple_image_list.setCurrentRow(current_row)
                self.remove_selected_images()
        except Exception as e:
            print(f"删除图片失败: {e}")
            import traceback

            traceback.print_exc()

    def update_simple_image_list(self):
        """更新简洁版图片列表，显示名称和状态"""
        try:
            from pathlib import Path

            self.simple_image_list.clear()

            for i, image_path in enumerate(self.updated_images):
                # 创建列表项文本
                item_text = f"{i+1}. {Path(image_path).name}"

                # 添加状态标识
                status_icons = []
                if image_path in self.updated_featured_images:
                    status_icons.append("⭐")
                if image_path in self.selected_for_deletion:
                    status_icons.append("🗑️")

                if status_icons:
                    item_text += f" {''.join(status_icons)}"

                item = QListWidgetItem(item_text)

                # 设置工具提示显示完整路径
                item.setToolTip(f"路径: {image_path}")

                self.simple_image_list.addItem(item)

        except Exception as e:
            print(f"更新图片列表失败: {e}")

    def _is_ui_initialized(self):
        """检查UI组件是否已完全初始化"""
        required_attrs = [
            "image_frames",
            "image_labels",
            "featured_checkboxes",
            "delete_checkboxes",
            "info_label",
            "prev_button",
            "next_button",
        ]

        for attr in required_attrs:
            if not hasattr(self, attr):
                return False

        # 检查列表是否为空
        list_attrs = [
            "image_frames",
            "image_labels",
            "featured_checkboxes",
            "delete_checkboxes",
        ]
        for attr in list_attrs:
            if not getattr(self, attr):
                return False

        return True

    def update_image_display(self):
        """更新图片显示"""
        try:
            # 全面的安全检查：确保必要的UI组件已初始化
            if not self._is_ui_initialized():
                print("图片查看器UI组件未完全初始化，跳过显示更新")
                return

            if not hasattr(self, "updated_images") or not self.updated_images:
                # 显示中间的框架，显示"暂无图片"
                for i, frame in enumerate(self.image_frames):
                    if frame is not None:
                        if i == 1:  # 中间框架
                            frame.show()
                            if (
                                i < len(self.image_labels)
                                and self.image_labels[i] is not None
                            ):
                                self.image_labels[i].clear()
                                self.image_labels[i].setText("暂无图片")
                            if (
                                i < len(self.featured_checkboxes)
                                and self.featured_checkboxes[i] is not None
                            ):
                                self.featured_checkboxes[i].hide()
                            if (
                                i < len(self.delete_checkboxes)
                                and self.delete_checkboxes[i] is not None
                            ):
                                self.delete_checkboxes[i].hide()
                        else:
                            frame.hide()
                            if (
                                i < len(self.image_labels)
                                and self.image_labels[i] is not None
                            ):
                                self.image_labels[i].clear()
                            if (
                                i < len(self.featured_checkboxes)
                                and self.featured_checkboxes[i] is not None
                            ):
                                self.featured_checkboxes[i].hide()
                            if (
                                i < len(self.delete_checkboxes)
                                and self.delete_checkboxes[i] is not None
                            ):
                                self.delete_checkboxes[i].hide()

                if hasattr(self, "info_label") and self.info_label is not None:
                    self.info_label.setText("0 / 0")
                if hasattr(self, "prev_button") and self.prev_button is not None:
                    self.prev_button.setEnabled(False)
                if hasattr(self, "next_button") and self.next_button is not None:
                    self.next_button.setEnabled(False)
                return

            # 计算显示的图片索引
            start_index = self.current_group_index * 6
            end_index = min(start_index + 6, len(self.updated_images))

            # 更新每个图片位置
            for i in range(6):
                img_idx = start_index + i

                # 安全地获取UI组件
                label = self.image_labels[i] if i < len(self.image_labels) else None
                featured_checkbox = (
                    self.featured_checkboxes[i]
                    if i < len(self.featured_checkboxes)
                    else None
                )
                delete_checkbox = (
                    self.delete_checkboxes[i]
                    if i < len(self.delete_checkboxes)
                    else None
                )
                frame = self.image_frames[i] if i < len(self.image_frames) else None

                if (
                    img_idx < len(self.updated_images)
                    and label is not None
                    and frame is not None
                ):
                    image_path = self.updated_images[img_idx]

                    # 加载图片
                    self.load_image_async(label, img_idx)

                    # 设置主要图片复选框
                    if featured_checkbox is not None:
                        featured_checkbox.setChecked(
                            image_path in self.updated_featured_images
                        )
                        # 断开之前的连接，避免重复连接
                        try:
                            featured_checkbox.stateChanged.disconnect()
                        except:
                            pass
                        featured_checkbox.stateChanged.connect(
                            lambda state, idx=img_idx: self.on_featured_checkbox_changed(
                                state, idx
                            )
                        )

                    # 设置删除复选框
                    if delete_checkbox is not None:
                        delete_checkbox.setChecked(
                            image_path in self.selected_for_deletion
                        )
                        # 断开之前的连接，避免重复连接
                        try:
                            delete_checkbox.stateChanged.disconnect()
                        except:
                            pass
                        delete_checkbox.stateChanged.connect(
                            lambda state, idx=img_idx: self.on_delete_checkbox_changed(
                                state, idx
                            )
                        )

                    # 显示组件
                    frame.show()
                    if featured_checkbox is not None:
                        featured_checkbox.show()
                    if delete_checkbox is not None:
                        delete_checkbox.show()

                else:
                    # 隐藏多余的框架
                    if frame is not None:
                        frame.hide()
                    if featured_checkbox is not None:
                        featured_checkbox.hide()
                    if delete_checkbox is not None:
                        delete_checkbox.hide()
                    if label is not None:
                        label.clear()

            # 更新页码信息
            if hasattr(self, "info_label") and self.info_label is not None:
                total_pages = max(1, (len(self.updated_images) + 5) // 6)
                current_page = self.current_group_index + 1
                self.info_label.setText(f"{current_page} / {total_pages}")

            # 更新按钮状态
            if hasattr(self, "prev_button") and self.prev_button is not None:
                self.prev_button.setEnabled(self.current_group_index > 0)
            if hasattr(self, "next_button") and self.next_button is not None:
                total_pages = max(1, (len(self.updated_images) + 5) // 6)
                self.next_button.setEnabled(self.current_group_index < total_pages - 1)

        except Exception as e:
            print(f"更新图片显示失败: {e}")
            import traceback

            traceback.print_exc()

    def load_image_async(self, label, image_index):
        """异步加载图片"""
        if image_index >= len(self.updated_images):
            return

        image_path = self.updated_images[image_index]

        # 使用QTimer异步加载图片，避免界面冻结
        from PyQt6.QtCore import QTimer
        from PyQt6.QtGui import QPixmap
        from PyQt6.QtCore import Qt
        from pathlib import Path

        def load_image():
            try:
                # 检查标签是否仍然有效
                if not label or not hasattr(label, "setPixmap"):
                    return

                # 检查图片索引是否仍然有效
                if image_index >= len(self.updated_images):
                    return

                # 使用统一的图片加载函数
                pixmap = load_image_safely(image_path)

                # 检查文件是否存在
                if not Path(image_path).exists():
                    label.setText(f"图片不存在\n{Path(image_path).name}")
                    return

                if pixmap.isNull():
                    label.setText(f"图片加载失败\n{Path(image_path).name}")
                    # 只在DEBUG级别记录错误，避免控制台输出过多信息
                    logging.debug(f"图片加载失败: {image_path}")
                    return

                # 缩放图片以适应标签
                target_size = label.size()
                if target_size.width() > 50 and target_size.height() > 50:
                    scaled_pixmap = pixmap.scaled(
                        target_size.width() - 10,
                        target_size.height() - 10,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation,
                    )
                    label.setPixmap(scaled_pixmap)
                else:
                    # 使用默认大小 - 大幅增大默认尺寸以匹配新框架
                    scaled_pixmap = pixmap.scaled(
                        280,
                        220,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation,
                    )
                    label.setPixmap(scaled_pixmap)

            except Exception as e:
                if hasattr(label, "setText"):
                    label.setText(f"加载错误\n{str(e)}")
                # 只在DEBUG级别记录错误，避免控制台输出过多信息
                logging.debug(f"加载图片失败: {e}")

        # 延迟加载，避免界面冻结
        QTimer.singleShot(50, load_image)

    def on_simple_image_selected(self, row):
        """选中图片时同步预览"""
        if 0 <= row < len(self.updated_images) and self._is_ui_initialized():
            # 计算要显示的图片组
            group_index = row // 6
            self.current_group_index = group_index
            self.update_image_display()

    def previous_group(self):
        """上一组图片"""
        if self.current_group_index > 0 and self._is_ui_initialized():
            self.current_group_index -= 1
            self.update_image_display()

    def next_group(self):
        """下一组图片"""
        if self._is_ui_initialized():
            total_groups = (len(self.updated_images) + 5) // 6
            if self.current_group_index < total_groups - 1:
                self.current_group_index += 1
                self.update_image_display()

    def add_images(self):
        """添加图片"""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox
        import os

        # 检查产品是否已保存
        if not self.product or not self.product.id:
            QMessageBox.warning(self, "提示", "请先保存产品后再添加图片")
            return

        # 设置默认路径为用户下载目录
        default_path = os.path.expanduser("~/Downloads")
        if not os.path.exists(default_path):
            default_path = ""

        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择图片文件",
            default_path,  # 使用下载目录作为默认路径
            "图片文件 (*.jpg *.jpeg *.png *.bmp *.gif *.webp);;所有文件 (*.*)",
        )

        if file_paths:
            # 过滤掉已经存在的图片
            new_files = []
            duplicate_files = []
            for file_path in file_paths:
                if file_path not in self.updated_images:
                    new_files.append(file_path)
                else:
                    duplicate_files.append(file_path)

            if new_files:
                try:
                    # 立即复制图片到项目目录
                    print(f"准备复制 {len(new_files)} 张新图片到项目目录...")
                    copied_images = self.product_service._copy_images_to_product_dir(
                        self.product.id, self.product.name, new_files
                    )

                    print(f"成功复制 {len(copied_images)} 张图片")

                    # 将复制后的相对路径添加到图片列表
                    self.updated_images.extend(copied_images)

                    # 更新显示
                    if self._is_ui_initialized():
                        self.update_simple_image_list()
                        self.update_image_display()
                        if hasattr(self, "update_stats_display"):
                            self.update_stats_display()

                    # 发出图片变化信号
                    self.on_images_changed(self.updated_images)

                    # 显示成功消息
                    QMessageBox.information(
                        self,
                        "添加成功",
                        f"成功添加 {len(copied_images)} 张图片\n"
                        f"图片已复制到项目目录\n"
                        f"当前总图片数量: {len(self.updated_images)}",
                    )

                except Exception as e:
                    print(f"复制图片失败: {e}")
                    import traceback

                    traceback.print_exc()
                    QMessageBox.critical(self, "错误", f"复制图片失败: {str(e)}")

            # 提示重复文件
            if duplicate_files:
                QMessageBox.information(
                    self, "提示", f"已跳过 {len(duplicate_files)} 个重复的图片文件"
                )
        else:
            if not self.updated_images:
                print("没有新图片需要添加")

    def on_images_changed(self, image_paths):
        """图片列表改变时的处理"""
        try:
            # 去重处理，防止重复添加
            if image_paths:
                unique_images = []
                seen = set()
                for img in image_paths:
                    if img not in seen:
                        unique_images.append(img)
                        seen.add(img)

                # 如果有重复，显示提示
                if len(unique_images) != len(image_paths):
                    removed_duplicates = len(image_paths) - len(unique_images)
                    print(f"检测到 {removed_duplicates} 个重复图片路径，已自动去重")

                self.updated_images = unique_images
            else:
                self.updated_images = []

            print(f"图片列表更新: {len(self.updated_images)} 张图片")

            # 清理主要图片列表，移除不存在的图片
            if hasattr(self, "updated_featured_images"):
                self.updated_featured_images = [
                    img
                    for img in self.updated_featured_images
                    if img in self.updated_images
                ]
            else:
                self.updated_featured_images = []

            # 更新显示 - 只有在UI已初始化时才更新
            if self._is_ui_initialized():
                self.update_simple_image_list()
                self.update_image_display()
                if hasattr(self, "update_stats_display"):
                    self.update_stats_display()
            else:
                print("UI未初始化，跳过图片变化后的显示更新")

        except Exception as e:
            print(f"处理图片变化失败: {e}")
            import traceback

            traceback.print_exc()

    def check_duplicates(self):
        """检查图片重复"""
        if not self.updated_images:
            QMessageBox.information(self, "提示", "没有图片可以进行查重")
            return

        if len(self.updated_images) < 2:
            QMessageBox.information(self, "提示", "至少需要2张图片才能进行查重")
            return

        try:
            # 创建查重器
            checker = ImageDuplicateChecker()

            # 查找重复图片
            duplicate_groups = checker.find_duplicates(self.updated_images, self)

            # 显示结果对话框
            result_dialog = DuplicateResultDialog(duplicate_groups, self)

            # 连接删除信号
            result_dialog.images_to_delete.connect(self.on_delete_duplicate_images)

            result_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"图片查重失败: {str(e)}")
            print(f"图片查重失败: {e}")
            import traceback

            traceback.print_exc()

    def on_delete_duplicate_images(self, images_to_delete):
        """删除重复图片"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            # 记录删除前的数量
            before_count = len(self.updated_images)
            removed_count = 0

            # 从图片列表中移除
            removed_images = []
            for image_path in images_to_delete:
                if image_path in self.updated_images:
                    self.updated_images.remove(image_path)
                    removed_images.append(image_path)  # 记录被删除的图片路径
                    removed_count += 1

                # 如果是主要图片，也要移除
                if image_path in self.updated_featured_images:
                    self.updated_featured_images.remove(image_path)

            # 删除物理文件
            if removed_images:
                self.cleanup_removed_images(removed_images)
                print(f"查重删除：已删除 {len(removed_images)} 个物理图片文件")

            # 调整当前显示的组索引
            if self.updated_images:
                total_groups = max(1, (len(self.updated_images) + 5) // 6)
                if self.current_group_index >= total_groups:
                    self.current_group_index = max(0, total_groups - 1)
            else:
                self.current_group_index = 0

            # 立即保存到数据库
            self.save_changes_to_database()

            # 更新显示
            if self._is_ui_initialized():
                self.update_simple_image_list()
                self.update_image_display()
                self.update_stats_display()

            # 发出图片变化信号
            self.on_images_changed(self.updated_images)

            # 显示删除结果
            after_count = len(self.updated_images)
            QMessageBox.information(
                self,
                "查重删除成功",
                f"已删除 {removed_count} 张重复图片\n"
                f"删除前：{before_count} 张\n"
                f"删除后：{after_count} 张\n"
                f"✓ 已同步到数据库\n"
                f"✓ 已删除物理文件",
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除图片失败: {str(e)}")
            print(f"删除图片失败: {e}")
            import traceback

            traceback.print_exc()

    def accept(self):
        """确认对话框"""
        try:
            # 确保属性存在
            if not hasattr(self, "updated_featured_images"):
                self.updated_featured_images = []

            print(f"准备保存 {len(self.updated_images)} 张图片")
            print(f"准备保存 {len(self.updated_featured_images)} 张主要图片")

            # 获取原始图片列表
            original_images = set(self.product.images) if self.product.images else set()
            current_images = set(self.updated_images)

            # 分析图片变化
            new_images = [
                img for img in self.updated_images if img not in original_images
            ]
            removed_images = [
                img for img in original_images if img not in current_images
            ]
            kept_images = [img for img in self.updated_images if img in original_images]

            print(f"保留的图片: {len(kept_images)} 张")
            print(f"新增的图片: {len(new_images)} 张")
            print(f"删除的图片: {len(removed_images)} 张")

            # 处理图片更新
            final_images = []

            if new_images:
                # 使用累积模式添加新图片
                try:
                    copied_new_images = self.product_service.update_product_images(
                        self.product.id, new_images, replace_all=False
                    )
                    print(f"成功复制 {len(copied_new_images)} 张新图片")
                    # 合并保留的图片和新复制的图片
                    final_images = kept_images + copied_new_images
                except Exception as e:
                    print(f"复制新图片失败: {e}")
                    # 如果复制失败，只保留原有图片
                    final_images = kept_images
            else:
                # 没有新图片，只保留现有图片
                final_images = kept_images

            # 如果有图片被删除，需要从文件系统中清理
            if removed_images:
                self.cleanup_removed_images(removed_images)

            # 更新产品的图片信息
            self.product.images = final_images

            # 清理主要图片列表，确保只包含存在的图片
            valid_featured_images = [
                img for img in self.updated_featured_images if img in final_images
            ]
            self.product.featured_images = valid_featured_images

            # 直接更新数据库，避免update_product方法中的重复图片处理
            import json

            cursor = self.product_service.db.connection.cursor()
            cursor.execute(
                "UPDATE products SET images = ?, featured_images = ? WHERE id = ?",
                (
                    json.dumps(final_images),
                    json.dumps(valid_featured_images),
                    self.product.id,
                ),
            )
            self.product_service.db.connection.commit()
            success = cursor.rowcount > 0

            if success:
                print(f"图片更新成功，最终图片数量: {len(final_images)}")
                print(f"主要图片数量: {len(valid_featured_images)}")
                # 移除弹窗，用户不想要
                # QMessageBox.information(self, "成功", "图片更新成功！")

                # 立即通知父窗口刷新显示
                if self.parent() and hasattr(self.parent(), "refresh"):
                    self.parent().refresh()

                super().accept()
            else:
                QMessageBox.warning(self, "错误", "图片更新失败")

        except Exception as e:
            print(f"保存图片失败: {e}")
            import traceback

            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"保存图片失败: {str(e)}")

    def save_now(self):
        """立即保存当前更改到数据库，不关闭对话框"""
        try:
            self.save_changes_to_database()

            # 立即通知父窗口刷新显示
            if self.parent() and hasattr(self.parent(), "refresh"):
                self.parent().refresh()

            # 成功时不弹窗，静默保存
            print(
                f"立即保存成功: 总图片数 {len(self.updated_images)} 张，主要图片数 {len(self.updated_featured_images)} 张"
            )
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存到数据库失败: {str(e)}")

    def reject(self):
        """重写reject方法，提供保存选项"""
        # 检查是否有未保存的更改
        original_images = set(self.product.images) if self.product.images else set()
        current_images = set(self.updated_images)

        original_featured = (
            set(self.product.featured_images) if self.product.featured_images else set()
        )
        current_featured = set(self.updated_featured_images)

        has_changes = (original_images != current_images) or (
            original_featured != current_featured
        )

        if has_changes:
            reply = QMessageBox.question(
                self,
                "保存更改",
                "您有未保存的更改，是否要保存？\n\n"
                "是：保存并关闭\n"
                "否：不保存直接关闭\n"
                "取消：返回继续编辑",
                QMessageBox.StandardButton.Yes
                | QMessageBox.StandardButton.No
                | QMessageBox.StandardButton.Cancel,
                QMessageBox.StandardButton.Yes,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 保存更改
                self.save_changes_to_database()

                # 立即通知父窗口刷新显示
                if self.parent() and hasattr(self.parent(), "refresh"):
                    self.parent().refresh()

                # 成功时不弹窗，静默保存
                super().reject()
            elif reply == QMessageBox.StandardButton.No:
                # 不保存直接关闭
                super().reject()
            # 取消：不关闭对话框
        else:
            # 没有更改，直接关闭
            super().reject()

    def validate_and_clean_images(self):
        """验证并清理图片列表，移除不存在的图片，并立即更新数据库"""
        try:
            from pathlib import Path
            from PyQt6.QtWidgets import QMessageBox

            # 第一步：去重处理
            original_count = len(self.updated_images)
            unique_images = []
            seen = set()
            for img in self.updated_images:
                if img not in seen:
                    unique_images.append(img)
                    seen.add(img)

            duplicate_count = original_count - len(unique_images)
            if duplicate_count > 0:
                print(f"清理了 {duplicate_count} 个重复的图片路径")
                self.updated_images = unique_images

            # 第二步：验证图片文件是否存在
            valid_images = []
            invalid_images = []

            for image_path in self.updated_images:
                if Path(image_path).is_absolute():
                    file_path = Path(image_path)
                else:
                    file_path = Path.cwd() / image_path

                if file_path.exists():
                    valid_images.append(image_path)
                else:
                    invalid_images.append(image_path)
                    print(f"发现无效图片路径: {image_path}")

            # 更新图片列表
            # 更新图片列表
            total_cleaned = duplicate_count + len(invalid_images)
            if invalid_images or duplicate_count > 0:
                self.updated_images = valid_images
                print(f"已清理 {len(invalid_images)} 个无效图片路径")

                # 同时清理主要图片列表
                self.updated_featured_images = [
                    img for img in self.updated_featured_images if img in valid_images
                ]
                print(f"更新后的图片数量: {len(self.updated_images)}")

                # 立即更新数据库，确保数据同步
                self.save_changes_to_database()

                # 提示用户
                if total_cleaned > 0:
                    message_parts = []
                    if duplicate_count > 0:
                        message_parts.append(f"{duplicate_count} 个重复路径")
                    if len(invalid_images) > 0:
                        message_parts.append(f"{len(invalid_images)} 个无效路径")

                    QMessageBox.information(
                        self,
                        "数据清理",
                        f"已自动清理 {' 和 '.join(message_parts)}，并同步到数据库\n"
                        f"清理前：{original_count} 张图片\n"
                        f"清理后：{len(self.updated_images)} 张图片",
                    )

        except Exception as e:
            print(f"验证图片列表失败: {e}")

    def save_changes_to_database(self):
        """立即保存当前更改到数据库"""
        try:
            import json

            # 更新产品对象
            self.product.images = self.updated_images.copy()
            self.product.featured_images = self.updated_featured_images.copy()

            # 直接更新数据库
            cursor = self.product_service.db.connection.cursor()
            cursor.execute(
                "UPDATE products SET images = ?, featured_images = ? WHERE id = ?",
                (
                    json.dumps(self.updated_images),
                    json.dumps(self.updated_featured_images),
                    self.product.id,
                ),
            )
            self.product_service.db.connection.commit()
            print(
                f"数据库已更新: {len(self.updated_images)} 张图片, {len(self.updated_featured_images)} 张主要图片"
            )

        except Exception as e:
            print(f"保存到数据库失败: {e}")
            import traceback

            traceback.print_exc()

    def update_stats_display(self):
        """更新图片统计信息显示"""
        try:
            total_images = len(self.updated_images)
            featured_images = len(self.updated_featured_images)
            selected_for_deletion = len(self.selected_for_deletion)

            stats_text = f"📊 图片统计: 总共 {total_images} 张 | 主要图片 {featured_images} 张 | 待删除 {selected_for_deletion} 张"

            if hasattr(self, "stats_label"):
                self.stats_label.setText(stats_text)

        except Exception as e:
            print(f"更新统计信息失败: {e}")

    def cleanup_removed_images(self, removed_images):
        """清理被删除的图片文件"""
        try:
            from pathlib import Path

            deleted_count = 0
            for image_path in removed_images:
                try:
                    if Path(image_path).is_absolute():
                        file_path = Path(image_path)
                    else:
                        file_path = Path.cwd() / image_path

                    if file_path.exists() and file_path.is_file():
                        file_path.unlink()
                        print(f"✓ 删除图片文件: {file_path}")
                        deleted_count += 1
                    else:
                        print(f"⚠ 图片文件不存在或不是文件: {file_path}")

                except Exception as file_error:
                    print(f"删除单个文件失败 {image_path}: {file_error}")

            print(
                f"清理完成：成功删除 {deleted_count}/{len(removed_images)} 个图片文件"
            )

        except Exception as e:
            print(f"清理图片文件失败: {e}")
            import traceback

            traceback.print_exc()

    def open_image_folder(self):
        """打开产品图片目录"""
        try:
            import os
            from pathlib import Path
            from PyQt6.QtWidgets import QMessageBox

            if not self.updated_images:
                QMessageBox.information(self, "提示", "没有图片可以打开目录")
                return

            # 获取第一个图片作为示例
            first_image_path = self.updated_images[0]

            # 确保路径是绝对路径
            if not Path(first_image_path).is_absolute():
                abs_path = Path.cwd() / first_image_path
            else:
                abs_path = Path(first_image_path)

            # 使用系统默认文件管理器打开目录
            if abs_path.parent.exists():
                os.startfile(abs_path.parent)
            else:
                QMessageBox.warning(self, "警告", "图片目录不存在")
        except Exception as e:
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"打开目录失败: {str(e)}")
