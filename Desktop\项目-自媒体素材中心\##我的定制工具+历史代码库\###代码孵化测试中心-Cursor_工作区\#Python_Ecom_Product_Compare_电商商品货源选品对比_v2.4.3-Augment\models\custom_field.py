"""
自定义字段模型
管理产品的自定义属性
"""

from datetime import datetime
from typing import Optional, Any
from dataclasses import dataclass, field
from enum import Enum


class FieldType(Enum):
    """字段类型枚举"""

    TEXT = "text"
    NUMBER = "number"
    URL = "url"
    DATE = "date"
    BOOLEAN = "boolean"
    MULTILINE = "multiline"
    EMAIL = "email"
    PHONE = "phone"


@dataclass
class CustomField:
    """自定义字段模型"""

    # 基本属性
    id: int = 0
    product_id: int = 0
    name: str = ""  # 字段名称
    value: str = ""  # 字段值
    field_type: str = FieldType.TEXT.value  # 字段类型
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    # 可选属性
    display_name: str = ""  # 显示名称
    description: str = ""  # 描述
    is_required: bool = False  # 是否必填
    is_visible: bool = True  # 是否可见
    order_index: int = 0  # 排序索引

    def __post_init__(self):
        """初始化后处理"""
        if not self.display_name:
            self.display_name = self.name

    @property
    def field_type_enum(self) -> FieldType:
        """字段类型枚举"""
        try:
            return FieldType(self.field_type)
        except ValueError:
            return FieldType.TEXT

    @property
    def typed_value(self) -> Any:
        """类型化的值"""
        if not self.value:
            return None

        field_type = self.field_type_enum

        try:
            if field_type == FieldType.NUMBER:
                return float(self.value)
            elif field_type == FieldType.BOOLEAN:
                return self.value.lower() in ("true", "1", "yes", "on")
            elif field_type == FieldType.DATE:
                return datetime.fromisoformat(self.value)
            else:
                return self.value
        except (ValueError, TypeError):
            return self.value

    @property
    def is_valid_value(self) -> bool:
        """值有效性检查"""
        if self.is_required and not self.value:
            return False

        if not self.value:
            return True

        field_type = self.field_type_enum

        try:
            if field_type == FieldType.NUMBER:
                float(self.value)
            elif field_type == FieldType.URL:
                return self.value.startswith(("http://", "https://"))
            elif field_type == FieldType.EMAIL:
                return "@" in self.value and "." in self.value
            elif field_type == FieldType.DATE:
                datetime.fromisoformat(self.value)

            return True
        except (ValueError, TypeError):
            return False

    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()

    def set_value(self, value: Any):
        """设置值"""
        if value is None:
            self.value = ""
        elif isinstance(value, bool):
            self.value = str(value).lower()
        elif isinstance(value, (int, float)):
            self.value = str(value)
        elif isinstance(value, datetime):
            self.value = value.isoformat()
        else:
            self.value = str(value)

        self.update_timestamp()

    def set_type(self, field_type: FieldType):
        """设置字段类型"""
        self.field_type = field_type.value
        self.update_timestamp()

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "product_id": self.product_id,
            "name": self.name,
            "value": self.value,
            "field_type": self.field_type,
            "display_name": self.display_name,
            "description": self.description,
            "is_required": self.is_required,
            "is_visible": self.is_visible,
            "order_index": self.order_index,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "typed_value": self.typed_value,
            "is_valid_value": self.is_valid_value,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "CustomField":
        """从字典创建自定义字段"""
        return cls(
            id=data.get("id", 0),
            product_id=data.get("product_id", 0),
            name=data.get("name", ""),
            value=data.get("value", ""),
            field_type=data.get("field_type", FieldType.TEXT.value),
            display_name=data.get("display_name", ""),
            description=data.get("description", ""),
            is_required=data.get("is_required", False),
            is_visible=data.get("is_visible", True),
            order_index=data.get("order_index", 0),
            created_at=datetime.fromisoformat(
                data.get("created_at", datetime.now().isoformat())
            ),
            updated_at=datetime.fromisoformat(
                data.get("updated_at", datetime.now().isoformat())
            ),
        )

    def __str__(self) -> str:
        return f"CustomField(id={self.id}, name='{self.name}', value='{self.value}', type='{self.field_type}')"

    def __repr__(self) -> str:
        return self.__str__()


# 常用自定义字段模板
FIELD_TEMPLATES = [
    {
        "name": "brand",
        "display_name": "品牌",
        "field_type": FieldType.TEXT.value,
        "is_required": True,
        "order_index": 1,
    },
    {
        "name": "color",
        "display_name": "颜色",
        "field_type": FieldType.TEXT.value,
        "order_index": 2,
    },
    {
        "name": "size",
        "display_name": "尺寸",
        "field_type": FieldType.TEXT.value,
        "order_index": 3,
    },
    {
        "name": "weight",
        "display_name": "重量",
        "field_type": FieldType.NUMBER.value,
        "order_index": 4,
    },
    {
        "name": "material",
        "display_name": "材质",
        "field_type": FieldType.TEXT.value,
        "order_index": 5,
    },
    {
        "name": "warranty",
        "display_name": "保修期",
        "field_type": FieldType.TEXT.value,
        "order_index": 6,
    },
    {
        "name": "origin",
        "display_name": "产地",
        "field_type": FieldType.TEXT.value,
        "order_index": 7,
    },
    {
        "name": "specification",
        "display_name": "规格说明",
        "field_type": FieldType.MULTILINE.value,
        "order_index": 8,
    },
    {
        "name": "official_website",
        "display_name": "官方网站",
        "field_type": FieldType.URL.value,
        "order_index": 9,
    },
    {
        "name": "is_recommended",
        "display_name": "是否推荐",
        "field_type": FieldType.BOOLEAN.value,
        "order_index": 10,
    },
]
