# 电商产品对比选品工具 - 使用指南 v2.2.2

## 项目简介

这是一个基于 PyQt6 和 SQLite 的电商产品对比选品工具，旨在帮助电商从业者高效地管理产品信息、比较不同货源的成本和利润，做出更明智的选品决策。

### 主要特点

- 🚀 **简洁高效**: 参考 Inventory_Management 项目的简洁设计理念
- 📊 **智能对比**: 多维度产品对比分析，自动计算利润和利润率
- 🔄 **实时更新**: 支持实时数据更新和同步
- 📸 **图片管理**: 完整的产品图片管理和轮播展示
- 💾 **数据导出**: 支持对比结果导出为 CSV 格式
- 🎨 **现代界面**: Material Design 风格的用户界面
- 🌐 **快速访问**: 直接打开货源网站链接
- 📝 **详细信息**: 支持商品描述和销量参考

## 功能特性

### 📦 产品管理
- **添加产品**: 支持产品名称、编码、售价等基本信息
- **商品描述**: 新增商品详细描述字段，支持多行文本
- **销量参考**: 添加销量参考数据，便于选品决策
- **编辑产品**: 完整的产品信息编辑功能
- **删除产品**: 安全的产品删除，包含确认提示
- **搜索排序**: 8种排序方式，支持实时搜索过滤
- **自定义字段**: 支持添加自定义字段扩展产品信息

### 🏪 货源管理
- **货源信息**: 供应商名称、单价、库存数量、店铺信息
- **网站链接**: 货源网址链接，支持一键打开浏览器访问
- **运费显示**: 含运费信息以橘红色高亮显示，便于识别
- **联系方式**: 完整的供应商联系信息管理
- **智能成本计算**: 根据货源模式（批发/代发）智能计算成本和利润
- **状态管理**: 货源活跃状态管理
- **批量操作**: 支持批量编辑和删除货源

### 📊 对比分析
- **多产品对比**: 支持同时对比多个产品
- **智能分析**: 自动计算最低成本、平均成本、利润率等
- **可视化展示**: 彩色表格展示，直观显示盈亏状况
- **洞察分析**: 自动生成对比洞察和建议
- **结果导出**: 支持将对比结果导出为 CSV 文件

### 🖼️ 图片管理
- **图片上传**: 支持多种图片格式（JPG、PNG、BMP、GIF、WebP）
- **图片轮播**: 美观的图片轮播展示组件
- **图片编辑**: 添加、删除、重新排序产品图片
- **自动播放**: 支持图片自动轮播功能
- **智能编号补位**：新建产品图片时，系统会自动寻找最小可用的编号进行补位，有效利用编号空缺。

### 🏷️ 标签系统（v2.1.6新增）
- **标签管理**: 创建、编辑、删除产品标签，支持自定义颜色
- **快捷筛选**: 标签快捷区域，点击标签即可筛选对应产品
- **产品标签**: 产品卡片直接显示标签，便于快速识别和分类
- **标签统计**: 显示标签使用次数和关联产品数量
- **右键添加**: 产品列表右键菜单支持快速添加标签

### 📁 数据导出导入（v2.1.6新增）
- **完整导出**: 导出包含产品、货源、图片、标签等所有数据
- **智能结构**: 数据和图片分离，便于管理和传输
- **跨设备迁移**: 支持完整的数据迁移和备份恢复
- **进度显示**: 导入导出过程实时进度显示
- **说明文档**: 自动生成操作说明文件

## 安装和运行

### 1. 环境准备

#### 🔧 系统与环境优化

##### 1. 硬件配置推荐
- **内存**: 8GB以上（AI模型需要较多内存）
- **存储**: SSD硬盘（提升抓取和分析速度）
- **网络**: 稳定的宽带连接（建议50Mbps以上）
- **CPU**: 4核心以上（支持并发处理）

##### 2. Ollama优化配置
```bash
# 设置Ollama环境变量（可选）
export OLLAMA_NUM_PARALLEL=2       # 并行模型数量
export OLLAMA_MAX_LOADED_MODELS=4  # 最大加载模型数
export OLLAMA_HOST=0.0.0.0:11434   # 绑定地址
```

### 🤖 AI功能环境配置

##### 1. 安装依赖

运行自动化设置脚本：

```bash
python setup_scraper.py
```

或手动安装：

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
python -m playwright install chromium
```

##### 2. AI模型配置

###### 安装Ollama

1. 访问 [Ollama官网](https://ollama.ai/) 下载安装
2. 启动Ollama服务
3. 安装所需模型：

```bash
# 文本分析模型
ollama pull qwen2.5:14b

# 视觉分析模型  
ollama pull qwen2.5vl:latest
```

###### 验证模型

```bash
# 检查已安装的模型
ollama list

# 测试模型
ollama run qwen2.5:14b
```

##### 3. 服务验证

确保Ollama服务运行在 `http://localhost:11434`

### 🎯 高效抓取策略

#### 1. URL选择原则
- ✅ **优先选择**: 商品详情页面（包含完整信息）
- ✅ **推荐URL格式**:
  ```
  # 1688商品页
  https://detail.1688.com/offer/123456789.html
  
  # 淘宝商品页  
  https://item.taobao.com/item.htm?id=123456789
  
  # 天猫商品页
  https://detail.tmall.com/item.htm?id=123456789
  ```
- ❌ **避免使用**: 搜索结果页、分类页、店铺首页

#### 2. 分批处理策略
```python
# 推荐做法：分批处理多个商品
urls = [
    "https://detail.1688.com/offer/111.html",
    "https://detail.1688.com/offer/222.html",
    "https://detail.1688.com/offer/333.html"
]

# 逐个处理，避免并发过多
for url in urls:
    # 处理单个URL
    time.sleep(2)  # 间隔2秒
```

## 使用教程

### 1. 添加产品

1. 点击左侧产品列表的"添加产品"按钮
2. 在弹出的对话框中填写产品信息：
   - **产品名称**: 必填，产品的显示名称
   - **产品编码**: 可选，用于内部管理的编码
   - **售价**: 必填，产品的销售价格
   - **商品描述**: 可选，产品的详细描述信息
   - **销量参考**: 可选，产品的销量参考数据（件）
   - **参考链接**: 可选，产品的参考网址，如竞品链接、市场调研网址等
3. 可以在"产品图片"选项卡中上传产品图片
4. 在"货源管理"选项卡中预先添加货源信息
5. 点击"确定"保存产品

### 2. 管理货源

1. 在产品列表中双击产品或选中后查看详情
2. 在产品详情页面点击"添加货源"按钮
3. 在基本信息标签页中填写以下信息：
   - **供应商名称**（必填）：输入供应商的名称
   - **单价**（必填）：输入商品单价，必须大于0
   - **库存数量**：输入当前库存数量
   - **最小订量**：输入最小订购数量
   - **运费**：输入运费金额
   - **网址链接**：货源网站链接
   - **店铺信息**：如金牌卖家、官方旗舰店等
   - **联系方式**：供应商联系方式
   - **备注信息**：其他需要记录的信息

4. 选择货源模式（至少选择一种）：
   - **代发模式**（默认）：适用于无需囤货的代发业务
   - **批发模式**：适用于需要囤货的批发业务
   - **可同时选择两种模式**：支持同时开展批发和代发业务

5. 根据选择的模式，填写相应的信息：
   - **代发模式信息**：
     - 单位成本
     - 运费
     - 预计利润
   - **批发模式信息**：
     - 单位成本
     - 运费
     - 最小订量
     - 预计利润

6. 系统会根据选择的模式动态显示相关字段
7. 所有必填字段填写完成后，点击"确定"保存货源

### 错误处理说明

在使用货源编辑功能时，如果遇到以下情况，系统会给出相应提示：

1. **必填字段验证**：
   - 供应商名称为空
   - 单价小于或等于0
   - 未选择任何货源模式

2. **数值范围验证**：
   - 单价超出范围（0-999999.99）
   - 库存数量超出范围（0-999999）
   - 最小订量超出范围（1-999999）
   - 运费超出范围（0-999999.99）

3. **模式选择验证**：
   - 必须至少选择一种货源模式（批发或代发）
   - 选择批发模式时必须填写最小订量

4. **异常情况处理**：
   - 如果出现程序错误，会显示友好的错误提示
   - 错误信息会记录到日志文件中
   - 即使部分功能失败，程序仍会保持运行

### 3. 货源操作

在产品详情页面的货源列表中，每个货源项根据其模式显示不同信息：

- **代发模式显示**：
  - 单位成本
  - 含运费成本
  - 预计利润
- **批发模式显示**：
  - 单位成本
  - 含运费成本
  - 最小订量
  - 预计利润
- **基本信息显示**：
  - 产品价格
  - 运费
  - 库存

操作按钮：
- **打开网站**：绿色按钮，点击用默认浏览器打开货源网站
- **打开链接**：绿色按钮，点击用默认浏览器打开产品参考链接（仅在填写了参考链接时显示）
- **编辑**：蓝色按钮，编辑货源信息
- **删除**：红色按钮，删除货源

显示说明：
- **含运费**：以橘红色高亮显示
- **利润**：绿色表示盈利，红色表示亏损
- **模式标识**：清晰显示货源支持的模式

### 货源模式说明
- **批发模式**: 显示总成本和含运费总成本，适用于需要囤货的批发业务
- **代发模式**: 不显示总成本，只计算单个利润，适用于无库存的代发业务
- **成本计算**: 代发模式理论成本为0，除非售价低于成本+运费时显示亏损警示

### 4. 产品对比分析

1. 在产品列表中选择多个产品（按住 Ctrl 键多选）
2. 点击"对比分析"按钮
3. 查看对比结果表格，包含：
   - 基本信息（名称、编码、售价）
   - 成本分析（最低、最高、平均成本）
   - 利润分析（利润、利润率）
   - 货源信息（数量、最优货源）
4. 查看底部的洞察分析，了解产品优劣势
5. 可点击"导出结果"将对比数据保存为 CSV 文件

### 5. 图片管理

1. 在产品详情页面点击"管理图片"按钮
2. 在图片管理对话框中：
   - 点击"添加"按钮选择图片文件
   - 选择图片后点击"删除"按钮移除图片
   - 图片会自动显示在右侧预览区域
3. 支持的图片格式：JPG、PNG、BMP、GIF、WebP
4. 可以通过拖拽调整图片顺序

### 6. 标签管理（v2.1.6新增）

#### 6.1 创建和管理标签
1. 在左侧标签区域点击"管理"按钮
2. 在标签管理对话框中：
   - 点击"添加标签"创建新标签
   - 输入标签名称和描述
   - 选择标签颜色（支持自定义颜色）
   - 查看标签使用统计信息
3. 编辑标签：双击标签或点击编辑按钮
4. 删除标签：选择标签后点击删除按钮

#### 6.2 使用标签筛选产品
1. 在左侧标签快捷区域，点击标签按钮筛选产品
2. 点击的标签会高亮显示，产品列表只显示包含该标签的产品
3. 点击"清除"按钮取消所有标签筛选
4. 支持多标签组合筛选

#### 6.3 为产品添加标签
有三种方式为产品添加标签：

**方式一：编辑产品时添加**
1. 双击产品或点击编辑按钮
2. 在"标签管理"选项卡中选择标签
3. 勾选需要的标签，或点击"新建标签"创建新标签

**方式二：右键菜单快速添加**
1. 在产品列表中右键点击产品
2. 选择"快速添加标签"
3. 在弹出的标签列表中选择要添加的标签

**方式三：标签管理界面关联**
1. 在标签管理界面创建标签
2. 后续通过编辑产品方式关联标签

### 7. 数据导出导入（v2.1.6新增）

#### 7.1 导出数据
1. 在菜单栏选择"文件 → 导出数据"
2. 选择要保存导出文件的文件夹
3. 等待导出完成，系统会显示进度。
4. 导出完成后会生成包含以下内容的文件夹：
   - `data.json`: 包含所有产品、货源、标签等数据
   - `images/`: 包含所有产品图片文件（现在已确保正确复制）
   - `README.txt`: 导出说明文件

#### 7.2 智能导入数据（v2.1.7增强）
1. 在菜单栏选择"文件 → 导入数据"
2. 在新的导入选择界面中：
   - **自动扫描**：系统自动扫描并显示所有可用的导出版本
   - **版本信息**：查看每个版本的导出时间、产品数量、标签数量
   - **时间排序**：导出版本按时间排序，最新版本显示在前
   - **删除版本**：可以直接删除不需要的导出版本
   - **手动浏览**：支持选择其他位置的导出文件夹
3. 选择要导入的版本，点击"导入"按钮
4. 系统会验证文件夹结构和数据文件
5. 等待导入完成，系统会显示详细进度和日志信息
6. 导入成功后所有数据和图片都会恢复（现在已确保图片正确显示）

#### 7.3 数据迁移建议
- **备份数据**: 定期导出数据作为备份
- **跨设备同步**: 通过导出导入在不同设备间同步数据
- **版本管理**: 保留不同时间点的导出文件
- **安全传输**: 导出文件夹可以压缩后安全传输

### 8. 标签删除功能（v2.1.7新增）

#### 8.1 右键删除标签
1. 在产品列表中右键点击产品
2. 选择"删除标签"子菜单
3. 选择要移除的标签
4. 确认删除，标签将从产品中移除

#### 8.2 标签操作优化
- **智能显示**：删除菜单只显示当前产品已关联的标签
- **友好提示**：删除操作显示标签名称而非ID
- **实时更新**：删除后产品列表和标签区域自动刷新

### 9. 日志系统（v2.1.7新增）

#### 9.1 日志功能
- **文件记录**：所有操作日志自动保存到 `data/logs/app.log`
- **控制台输出**：日志同时显示在控制台，便于实时查看
- **详细记录**：导入导出过程的详细操作信息
- **错误追踪**：完整的错误堆栈信息，便于问题诊断

#### 9.2 日志管理
- **自动轮转**：日志文件大小超过10MB时自动创建新文件
- **历史保留**：保留最近5个日志文件
- **编码支持**：支持UTF-8编码，正确显示中文内容

#### 9.3 日志查看
- 日志文件位置：`项目目录/data/logs/app.log`
- 可以用任何文本编辑器打开查看
- 记录格式：时间 - 模块 - 级别 - 消息内容

## 数据结构

### 产品数据表 (products)
- `id`: 产品ID（主键）
- `name`: 产品名称
- `sku`: 产品编码
- `selling_price`: 售价
- `description`: 商品描述（新增）
- `sales_reference`: 销量参考（新增）
- `images`: 产品图片路径（JSON格式）
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 货源数据表 (sources)
- `id`: 货源ID（主键）
- `product_id`: 关联产品ID
- `name`: 供应商名称
- `price`: 采购价格
- `url`: 网址链接
- `shop_info`: 店铺信息
- `quantity`: 库存数量
- `shipping_cost`: 运费（新增）
- `source_mode`: 货源模式（新增）
- `contact_info`: 联系方式
- `notes`: 备注信息
- `is_active`: 是否活跃
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 自定义字段表 (custom_fields)
- `id`: 字段ID（主键）
- `product_id`: 关联产品ID
- `name`: 字段名称
- `value`: 字段值
- `field_type`: 字段类型
- `display_name`: 显示名称
- `created_at`: 创建时间

## 版本更新说明

### v2.1.7 新功能

1. **修复关键问题**
   - 修复导入功能中`update_product_images`方法缺失的错误
   - 解决导入时标签唯一性约束冲突问题
   - 改进标签导入逻辑，避免重复创建同名标签

2. **标签管理增强**
   - 右键菜单新增"删除标签"功能，支持快速移除产品标签
   - 智能标签显示，只显示当前产品已关联的标签
   - 标签删除操作显示标签名称而非ID，用户体验更友好

3. **智能导入导出系统**
   - 全新的导入选择界面，自动扫描并显示所有可用的导出版本
   - 显示详细的版本信息：导出时间、产品数量、标签数量
   - 导出版本按时间排序，最新版本显示在前
   - 支持在导入界面直接删除不需要的导出版本
   - 支持手动浏览其他位置的导出文件夹

4. **完整日志系统**
   - 程序运行日志自动保存到`data/logs/app.log`文件
   - 日志同时输出到控制台和文件，方便调试和问题诊断
   - 导入过程详细日志记录，便于追踪操作状态
   - 完整的错误堆栈信息记录，提升问题解决效率
   - 自动日志轮转管理，避免日志文件过大

5. **用户体验优化**
   - 导入过程显示详细的进度和状态信息
   - 更友好的错误提示和确认机制
   - 删除导出版本需要用户确认，避免误操作
   - 导入导出界面响应更流畅，操作更直观

### v2.1.6 新功能

1. **标签系统全面升级**
   - 完整的产品标签系统，支持标签创建、编辑、删除
   - 标签快捷筛选功能，点击标签即可筛选对应产品
   - 产品卡片直接显示标签，便于快速识别分类
   - 支持标签颜色自定义和使用统计
   - 右键菜单快速添加标签功能

2. **完整导出导入功能**
   - 数据完整导出，包含产品、货源、图片、标签等所有数据
   - 智能文件夹结构，数据和图片分离便于管理
   - 完整数据导入，支持跨设备数据迁移
   - 导入导出进度可视化显示
   - 自动生成操作说明文件

3. **界面美化和优化**
   - 标签样式全面升级，现代化渐变背景效果
   - 标签位置优化，与货源信息对齐显示
   - 修复暗黑模式下按钮文字不可见问题
   - 状态栏图标化显示，提升用户体验
   - 产品列表布局优化，信息显示更清晰

4. **数据处理和性能优化**
   - 修复货源数量统计显示为0的问题
   - 优化产品列表，正确加载货源和标签关联数据
   - 支持多标签组合筛选功能
   - 完善数据结构，支持完整的标签关联关系

### v2.1.1 新功能

1. **成本计算优化**
   - 根据货源模式智能调整成本计算和显示逻辑
   - 批发模式：正常显示总成本和含运费总成本
   - 代发模式：不显示总成本，只计算单个利润

2. **界面改进**
   - 代发模式标签使用橘色高亮显示，便于区分
   - 利润显示根据模式调整（"代发利润" vs "利润"）
   - 当售价低于成本+运费时，明确显示"亏损"金额

3. **业务逻辑优化**
   - 代发模式理论成本为0，符合代发业务特点
   - 根据不同货源模式采用不同的利润计算方法
   - 代发模式下当售价过低时给出明确的亏损警示

### v2.1.0 新功能

1. **增强产品信息**
   - 新增商品描述字段，支持详细的产品描述
   - 新增销量参考字段，便于选品决策

2. **改进货源管理**
   - 新增"打开网站"按钮，一键访问货源链接
   - 含运费信息以橘红色高亮显示

3. **数据库升级**
   - 自动检测并添加新字段
   - 保持向后兼容性

4. **界面优化**
   - 改进产品详情页面布局
   - 优化货源信息显示效果

## 技术架构

### 技术栈
- **GUI框架**: PyQt6
- **数据库**: SQLite
- **架构模式**: 分层架构 (Models/Views/Services)
- **编程语言**: Python 3.8+

### 项目结构
```
New/
├── main.py                 # 应用入口
├── config.py              # 配置管理
├── requirements.txt       # 依赖包列表
├── start.bat             # Windows启动脚本
├── models/               # 数据模型
│   ├── __init__.py
│   ├── product.py        # 产品模型
│   ├── source.py         # 货源模型
│   ├── custom_field.py   # 自定义字段模型
│   └── database.py       # 数据库管理
├── views/                # 视图组件
│   ├── __init__.py
│   ├── main_window.py    # 主窗口
│   ├── product_list.py   # 产品列表
│   ├── product_detail.py # 产品详情
│   ├── comparison_view.py # 对比视图
│   ├── image_viewer.py   # 图片查看器
│   ├── image_carousel.py # 图片轮播
│   └── dialogs/          # 对话框
│       ├── __init__.py
│       ├── product_dialog.py
│       ├── source_dialog.py
│       └── custom_field_dialog.py
└── README.md             # 项目说明
```

## 性能优化

### 数据加载优化
- **延迟加载**: 产品列表不预加载关联数据
- **按需查询**: 只在需要时加载详细信息
- **搜索优化**: 500ms延迟搜索，减少频繁查询

### 界面响应优化
- **异步操作**: 数据库操作不阻塞界面
- **内存管理**: 及时释放不需要的资源
- **图片缓存**: 图片加载和缩放优化

## 常见问题

### Q: 如何快速访问货源网站？
A: 在产品详情页面的货源列表中，点击绿色的"打开网站"按钮即可用默认浏览器打开货源链接。

### Q: 含运费显示为什么是橘红色？
A: 为了便于快速识别包含运费的总成本，系统将含运费信息以橘红色高亮显示。

### Q: 如何添加商品描述？
A: 在添加或编辑产品时，在"商品描述"字段中输入详细的产品描述信息。

### Q: 销量参考有什么用？
A: 销量参考可以帮助您了解产品的市场表现，作为选品决策的参考依据。

## 6. 🤖 AI智能抓取使用 (v2.3.6新增)
- **准备工作**：首次使用需运行`python setup_scraper.py`配置AI环境
- **开始抓取**：点击"添加货源"，切换到"🤖 智能抓取"选项卡
- **输入网址**：复制1688、淘宝、天猫等商品详情页网址到输入框
- **启动抓取**：点击"开始抓取"按钮，系统自动执行以下步骤：
  1. 网页内容抓取（标题、价格、图片、描述等）
  2. AI智能分析（商品特性、规格参数提取）
  3. 数据智能映射（转换为货源表单格式）
- **预览结果**：在"抓取结果预览"区域查看AI分析的结果
- **一键应用**：确认无误后点击"应用到表单"，自动填充所有货源字段
- **支持平台**：1688、淘宝、天猫、阿里巴巴等主流电商平台

#### 🎯 更详细的使用方法

##### 1. 通过智能抓取界面
1. 打开货源对话框
2. 切换到"智能抓取"选项卡
3. 输入1688商品URL
4. 点击"开始抓取"
5. 等待AI分析完成
6. 查看提取的详细信息
7. 点击"应用到货源"

##### 2. 通过代码调用
```python
from scrapers import WebScraper, AIAnalyzer, SourceDataMapper

async def extract_1688_info(url):
    # 抓取网页内容
    async with WebScraper() as scraper:
        scraped_content = await scraper.scrape_url(url)
    
    # AI分析
    analyzer = AIAnalyzer()
    analysis_result = await analyzer.analyze_scraped_content(scraped_content)
    
    # 数据映射
    mapper = SourceDataMapper()
    mapped_data = mapper.map_to_source_data(scraped_content, analysis_result)
    
    return mapped_data
```

##### 3. 测试功能
运行测试脚本验证功能：
```bash
python test_enhanced_extraction.py
```

#### 📋 信息提取示例

##### 输入: 1688商品页面
URL: `https://detail.1688.com/offer/781751893588981663.html`

##### 输出: 结构化商品信息
```json
{
  "基本信息": {
    "name": "跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳",
    "price": 2.80,
    "supplier_name": "温州辛宠宠物用品有限公司"
  },
  "代发服务": {
    "pickup_rate_24h": 83.0,
    "pickup_rate_48h": 98.0,
    "distributor_count": 400,
    "monthly_dropship_orders": 100
  },
  "自定义字段": [
    {"name": "material", "value": "PC+硅胶", "display_name": "材质"},
    {"name": "net_weight", "value": "57", "display_name": "净重(克)"},
    {"name": "annual_sales", "value": "100000", "display_name": "年销量"},
    {"name": "review_tags", "value": "[\"价格很便宜(6)\", \"客服很热情(5)\"]"}
  ]
}
```

#### 🚀 更详细的使用步骤

##### 步骤1: 启动程序

```bash
python main.py
```

##### 步骤2: 打开货源对话框

- 点击主界面的 "添加货源" 按钮
- 或编辑现有货源

##### 步骤3: 切换到智能抓取选项卡

在货源对话框中，切换到 **"🤖 智能抓取"** 选项卡

##### 步骤4: 配置抓取参数

1. **输入URL**: 在URL输入框中粘贴货源网页链接
2. **选择模式**: 
   - 标准模式：平衡速度和质量
   - 深度模式：更详细的内容抓取 
   - 快速模式：仅抓取基本信息
3. **AI分析**: 确保勾选"启用AI智能分析"

##### 步骤5: 开始抓取

点击 **"🚀 开始抓取和分析"** 按钮

##### 步骤6: 等待处理

系统将依次执行：
1. 🔗 测试AI模型连接
2. 🚀 抓取网页内容  
3. 🤖 AI智能分析
4. ✅ 完成处理

##### 步骤7: 预览结果

在结果预览区域查看：
- 产品名称和供应商信息
- 价格信息（批发价、代发价）
- 产品描述和规格
- 产品图片缩略图
- AI分析统计信息

##### 步骤8: 应用数据

点击 **"✅ 应用到货源"** 将抓取的数据自动填充到货源表单中

#### 🎯 支持的网站

##### 完全支持
- **1688.com** - 阿里巴巴批发网
- **taobao.com** - 淘宝网  
- **tmall.com** - 天猫
- **alibaba.com** - 阿里巴巴国际站

##### 基本支持
- 其他电商平台（使用通用抓取规则）

#### 🛡️ 验证绕过功能使用方法

#### 1. 运行测试
```bash
python test_auth_bypass.py
```

#### 2. 在代码中使用
```python
from scrapers import WebScraper

async def test_auto_bypass():
    async with WebScraper(headless=False) as scraper:
        # 自动绕过验证码！
        result = await scraper.scrape_url("https://detail.1688.com/offer/xxx.html")
        
        if result.success:
            print("✅ 自动绕过成功！")
            print(f"标题: {result.title}")
            print(f"价格: {result.price}")
        else:
            print(f"❌ 失败: {result.error_message}")

# 运行
import asyncio
asyncio.run(test_auto_bypass())
```

### 📊 验证绕过工作流程

```mermaid
graph TD
    A[访问1688页面] --> B{检测验证页面?}
    B -->|是| C[步骤1: 按回车键]
    C --> D[步骤2: 自动刷新5次]
    D --> E{绕过成功?}
    E -->|是| F[✅ 自动绕过成功]
    E -->|否| G[步骤3: 重新访问]
    G --> H{绕过成功?}
    H -->|是| F
    H -->|否| I[等待手动验证]
    B -->|否| F
    I --> J{手动完成?}
    J -->|是| F
    J -->|否| K[❌ 验证失败]
```

### ⚡ 效果预期

#### 成功场景
```
🚀 开始自动绕过验证流程...
📖 第一次访问页面...
🔍 检测到验证页面，开始自动绕过...
⌨️  模拟按回车键触发页面刷新...
🔄 执行第1次自动刷新，绕过验证页面...
✅ 自动绕过验证成功！
```

#### 备用方案
如果自动绕过失败，系统会：
1. 显示浏览器窗口
2. 等待用户手动完成验证
3. 自动检测验证完成
4. 继续抓取流程

### 🚨 重要说明

1. **显示浏览器** - 测试时建议设置 `headless=False` 观察过程
2. **网络稳定** - 确保网络连接稳定，避免刷新中断
3. **频率控制** - 避免短时间内大量访问同一网站
4. **备用方案** - 如果自动绕过失败，请准备手动验证

## 🚧 注意事项

### 1. AI模型要求
- 需要安装Ollama服务
- 推荐使用qwen2.5:14b模型
- 确保足够的系统内存和计算资源

### 2. 网络要求
- 需要稳定的网络连接访问1688网站
- 验证绕过功能需要实际页面加载

### 3. 数据质量
- AI分析结果可能因页面结构变化而有所差异
- 建议对重要数据进行人工验证
- 系统会自动处理常见的数据格式变化

##### ⚠️ 验证绕过重要说明

###### 验证码处理
- **显示浏览器**: 验证时必须设置 `headless=False`
- **手动验证**: 遇到验证码时需要用户手动完成
- **超时设置**: 默认等待5分钟，可自定义
- **自动检测**: 验证完成后自动继续

###### 使用建议
1. **首次使用**: 建议先用测试脚本验证功能
2. **验证码处理**: 保持浏览器可见，及时处理验证码
3. **网络环境**: 确保网络稳定，避免访问中断
4. **频率控制**: 避免频繁访问同一网站

## 📞 技术支持

如有问题或需要进一步功能定制，请：
1. 查看测试脚本的运行结果
2. 检查日志文件中的详细错误信息
3. 确认AI模型和网络连接状态

---

*最后更新时间: 2025年1月*

### 界面主题

#### 暗黑主题
本版本优化了暗黑主题的视觉体验：

1. 标题显示
- 移除了标题边框，使界面更加简洁
- 文字颜色采用白色，提高可读性
- 保持了信息的层次感

2. 颜色方案
- 基本文字：白色
- 价格标签：蓝色
- 销量标签：橙色
- 时间信息：灰色

3. 切换主题
- 在设置中可以随时切换明暗主题
- 主题设置会被保存，下次启动时自动应用 