"""
主窗口 - 采用左右分栏布局
左侧：产品列表区域
右侧：详情/对比区域
"""

from PyQt6.QtWidgets import (
    QMainWindow,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QSplitter,
    QStackedWidget,
    QMenuBar,
    QMenu,
    QToolBar,
    QStatusBar,
    QMessageBox,
    QApplication,
    QLabel,
    QPushButton,
    QDialog,
    QListWidget,
    QListWidgetItem,
    QProgressDialog,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QKeySequence

from models.database import DatabaseManager, ProductService, TagService
from .product_list import ProductListWidget
from .product_detail import ProductDetailWidget
from .comparison_view import ComparisonWidget
from .tag_widget import TagWidget
from config import UI_CONFIG, APP_NAME, APP_VERSION, get_current_theme, set_theme
import os
import logging
import json
import shutil


class MainWindow(QMainWindow):
    """主窗口 - 采用左右分栏布局"""

    # 信号定义
    product_selected = pyqtSignal(int)  # 产品被选中
    comparison_requested = pyqtSignal(list)  # 请求对比

    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.product_service = ProductService(db_manager)
        self.tag_service = TagService(db_manager)
        self.current_product_id = None
        self.comparison_mode = False

        self.setup_ui()
        self.setup_menus()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        self.load_initial_data()

        # 应用初始主题
        self.apply_theme()

        # 设置定时器用于自动刷新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # 每5秒更新一次

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle(f"{APP_NAME} v{APP_VERSION}")
        self.setMinimumSize(UI_CONFIG["min_width"], UI_CONFIG["min_height"])
        self.resize(UI_CONFIG["default_width"], UI_CONFIG["default_height"])

        # 创建中央部件
        central_widget = QWidget()
        central_widget.setObjectName("centralWidget")
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建分割器
        self.splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：产品列表区域
        left_widget = QWidget()
        left_widget.setObjectName("leftPanel")
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(5)

        # 标签组件
        self.tag_widget = TagWidget(self.tag_service)
        self.tag_widget.setObjectName("tagWidget")
        self.tag_widget.setMinimumHeight(160)
        self.tag_widget.setMaximumHeight(220)
        left_layout.addWidget(self.tag_widget)

        # 产品列表
        self.product_list_widget = ProductListWidget(
            self.product_service, self.tag_service
        )
        self.product_list_widget.setObjectName("productList")
        left_layout.addWidget(self.product_list_widget)

        left_widget.setMinimumWidth(280)
        left_widget.setMaximumWidth(400)

        # 右侧：详情/对比区域
        self.detail_stack = QStackedWidget()
        self.detail_stack.setObjectName("detailStack")

        # 创建详情视图
        self.product_detail_widget = ProductDetailWidget(self.product_service)
        self.product_detail_widget.setObjectName("productDetail")
        self.detail_stack.addWidget(self.product_detail_widget)

        # 创建对比视图
        self.comparison_widget = ComparisonWidget(self.product_service)
        self.comparison_widget.setObjectName("comparisonView")
        self.detail_stack.addWidget(self.comparison_widget)

        # 创建欢迎页面
        self.welcome_widget = self.create_welcome_widget()
        self.welcome_widget.setObjectName("welcomeWidget")
        self.detail_stack.addWidget(self.welcome_widget)

        # 默认显示欢迎页面
        self.detail_stack.setCurrentWidget(self.welcome_widget)

        # 添加到分割器
        self.splitter.addWidget(left_widget)
        self.splitter.addWidget(self.detail_stack)

        # 设置分割器比例
        self.splitter.setSizes(UI_CONFIG["splitter_ratio"])

        main_layout.addWidget(self.splitter)

    def create_welcome_widget(self) -> QWidget:
        """创建欢迎页面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 欢迎标题
        title_label = QLabel(f"欢迎使用 {APP_NAME}")
        title_label.setStyleSheet(
            "font-size: 24px; font-weight: bold; color: #1976d2; margin-bottom: 20px;"
        )
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 版本信息
        version_label = QLabel(f"版本 {APP_VERSION}")
        version_label.setStyleSheet(
            "font-size: 14px; margin-bottom: 30px; opacity: 0.7;"
        )
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 说明文字
        description_label = QLabel(
            "这是一个简洁高效的电商产品对比选品工具\n\n"
            "主要功能：\n"
            "• 产品管理（增删改查）\n"
            "• 多货源对比分析\n"
            "• 图片轮播查看\n"
            "• 数据导入导出\n\n"
            '开始使用：点击左侧"添加产品"按钮创建第一个产品'
        )
        description_label.setStyleSheet("font-size: 12px; line-height: 1.5;")
        description_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 快速开始按钮
        quick_start_btn = QPushButton("快速开始")
        quick_start_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #1976d2;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
        """
        )
        quick_start_btn.clicked.connect(self.show_quick_start_dialog)

        layout.addWidget(title_label)
        layout.addWidget(version_label)
        layout.addWidget(description_label)
        layout.addWidget(quick_start_btn)

        return widget

    def setup_menus(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 新建产品
        new_action = QAction("新建产品(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.product_list_widget.add_product)
        file_menu.addAction(new_action)

        file_menu.addSeparator()

        # 导入/导出
        import_action = QAction("导入数据(&I)", self)
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)

        export_action = QAction("导出数据(&E)", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 标签管理
        tag_action = QAction("标签管理(&T)", self)
        tag_action.triggered.connect(self.open_tag_manager)
        file_menu.addAction(tag_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&Q)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        # 查找
        find_action = QAction("查找产品(&F)", self)
        find_action.setShortcut(QKeySequence.StandardKey.Find)
        find_action.triggered.connect(self.product_list_widget.focus_search)
        edit_menu.addAction(find_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")

        # 切换视图
        detail_action = QAction("详情视图(&D)", self)
        detail_action.triggered.connect(self.switch_to_detail_view)
        view_menu.addAction(detail_action)

        comparison_action = QAction("对比视图(&C)", self)
        comparison_action.triggered.connect(self.switch_to_comparison_view)
        view_menu.addAction(comparison_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 统计信息
        stats_action = QAction("统计信息(&S)", self)
        stats_action.triggered.connect(self.show_statistics)
        tools_menu.addAction(stats_action)

        tools_menu.addSeparator()

        # 清空商品
        clear_action = QAction("清空所有商品(&C)", self)
        clear_action.triggered.connect(self.clear_all_products)
        tools_menu.addAction(clear_action)

        # 强制清理数据库
        force_clean_action = QAction("强制清理数据库(&F)", self)
        force_clean_action.triggered.connect(self.force_clean_database)
        tools_menu.addAction(force_clean_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = QToolBar("主工具栏")
        self.addToolBar(toolbar)

        # 添加产品
        add_action = QAction("添加产品", self)
        add_action.triggered.connect(self.product_list_widget.add_product)
        toolbar.addAction(add_action)

        toolbar.addSeparator()

        # 详细视图 (替换原来的刷新按钮)
        detail_action = QAction("详细视图", self)
        detail_action.triggered.connect(self.switch_to_detail_view)
        toolbar.addAction(detail_action)

        # 对比视图
        comparison_action = QAction("对比视图", self)
        comparison_action.triggered.connect(self.switch_to_comparison_view)
        toolbar.addAction(comparison_action)

        toolbar.addSeparator()

        # 暗黑模式切换
        self.theme_action = QAction("暗黑模式", self)
        self.theme_action.triggered.connect(self.toggle_dark_mode)
        toolbar.addAction(self.theme_action)

        # 初始化主题状态
        self.update_theme_button_text()

    def setup_statusbar(self):
        """设置状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.statusbar.addWidget(self.status_label)

        # 统计信息标签
        self.stats_label = QLabel("")
        self.statusbar.addPermanentWidget(self.stats_label)

        # 更新状态
        self.update_status()

    def setup_connections(self):
        """设置信号连接"""
        # 产品列表选择信号
        self.product_list_widget.product_selected.connect(self.on_product_selected)

        # 对比请求信号
        self.product_list_widget.comparison_requested.connect(
            self.on_comparison_requested
        )

        # 产品更新信号
        self.product_detail_widget.product_updated.connect(
            self.product_list_widget.refresh
        )
        self.product_detail_widget.product_deleted.connect(
            self.product_list_widget.refresh
        )

        # 对比结果信号
        self.comparison_widget.comparison_completed.connect(
            self.on_comparison_completed
        )

        # 标签组件信号
        self.tag_widget.tag_filter_changed.connect(self.on_tag_filter_changed)

    def load_initial_data(self):
        """加载初始数据"""
        self.product_list_widget.refresh()
        self.update_status()

    def on_product_selected(self, product_id: int):
        """处理产品选中事件"""
        self.current_product_id = product_id

        # 无论是否在对比模式，都切换到详情视图并退出对比模式
        self.product_detail_widget.load_product(product_id)
        self.detail_stack.setCurrentWidget(self.product_detail_widget)

        # 如果之前在对比模式，现在退出对比模式
        if self.comparison_mode:
            self.comparison_mode = False
            self.status_label.setText("已退出对比模式，显示单独产品详情")
        else:
            self.status_label.setText("显示产品详情")

        self.product_selected.emit(product_id)

    def on_comparison_requested(self, product_ids: list):
        """处理对比请求"""
        if len(product_ids) < 2:
            QMessageBox.warning(self, "警告", "请至少选择两个产品进行对比")
            return

        self.comparison_widget.load_comparison(product_ids)
        self.detail_stack.setCurrentWidget(self.comparison_widget)
        self.comparison_mode = True

        self.comparison_requested.emit(product_ids)

    def on_comparison_completed(self, results: list):
        """处理对比完成事件"""
        self.status_label.setText(f"对比完成，共对比 {len(results)} 个产品")

    def on_tag_filter_changed(self, tag_ids: list):
        """处理标签筛选变化事件"""
        if tag_ids:
            # 根据标签筛选产品
            filtered_products = self.product_service.search_products_by_tags(tag_ids)
            self.product_list_widget.set_filtered_products(filtered_products)

            # 更新状态
            tag_names = []
            for tag_id in tag_ids:
                tag = self.tag_service.get_tag(tag_id)
                if tag:
                    tag_names.append(tag.name)
            self.status_label.setText(
                f"标签筛选: {', '.join(tag_names)} ({len(filtered_products)} 个产品)"
            )
        else:
            # 清除筛选，显示所有产品
            self.product_list_widget.clear_filter()
            self.status_label.setText("已清除标签筛选")

    def switch_to_detail_view(self):
        """切换到详情视图"""
        if self.current_product_id:
            self.product_detail_widget.load_product(self.current_product_id)
            self.detail_stack.setCurrentWidget(self.product_detail_widget)
        else:
            self.detail_stack.setCurrentWidget(self.welcome_widget)

        self.comparison_mode = False
        self.status_label.setText("切换到详情视图")

    def switch_to_comparison_view(self):
        """切换到对比视图"""
        selected_products = self.product_list_widget.get_selected_products()
        if len(selected_products) < 2:
            QMessageBox.information(
                self, "提示", "请在产品列表中选择至少两个产品进行对比"
            )
            return

        self.on_comparison_requested(selected_products)
        self.status_label.setText("切换到对比视图")

    def update_status(self):
        """更新状态信息"""
        try:
            stats = self.product_service.get_statistics()
            self.stats_label.setText(
                f"产品: {stats['total_products']} | "
                f"货源: {stats['total_sources']} | "
                f"盈利产品: {stats['profitable_products']}"
            )
        except Exception as e:
            self.stats_label.setText("状态更新失败")

    def show_quick_start_dialog(self):
        """显示快速开始对话框"""
        self.product_list_widget.add_product()

    def open_tag_manager(self):
        """打开标签管理器"""
        from views.dialogs.tag_manager_dialog import TagManagerDialog

        dialog = TagManagerDialog(self.tag_service, self)
        dialog.tag_updated.connect(self.tag_widget.refresh)
        dialog.tag_updated.connect(
            self.product_list_widget.refresh
        )  # 也刷新产品列表，更新标签信息
        dialog.exec()

    def show_statistics(self):
        """显示统计信息"""
        try:
            stats = self.product_service.get_statistics()

            # 创建自定义对话框以控制字体
            dialog = QDialog(self)
            dialog.setWindowTitle("统计信息")
            dialog.setMinimumSize(400, 300)

            # 设置字体
            from PyQt6.QtGui import QFont

            font = QFont("Microsoft YaHei UI", 12)
            dialog.setFont(font)

            layout = QVBoxLayout(dialog)

            # 添加统计信息标签
            stats_label = QLabel(
                f"""产品统计信息：

总产品数量: {stats['total_products']}
总货源数量: {stats['total_sources']}
平均售价: ¥{stats['avg_selling_price']:.2f}
最低售价: ¥{stats['min_selling_price']:.2f}
最高售价: ¥{stats['max_selling_price']:.2f}
平均利润: ¥{stats['avg_profit']:.2f}
盈利产品数量: {stats['profitable_products']}"""
            )

            stats_label.setStyleSheet(
                "font-size: 12px; padding: 20px; line-height: 1.5;"
            )
            stats_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
            layout.addWidget(stats_label)

            # 添加确定按钮
            from PyQt6.QtWidgets import QDialogButtonBox

            button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
            button_box.setStyleSheet(
                "QPushButton { font-size: 12px; padding: 8px 16px; }"
            )
            button_box.accepted.connect(dialog.accept)
            layout.addWidget(button_box)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取统计信息失败: {str(e)}")

    def show_about(self):
        """显示关于对话框"""
        # 创建自定义对话框以控制字体
        dialog = QDialog(self)
        dialog.setWindowTitle("关于")
        dialog.setMinimumSize(400, 350)

        # 设置字体
        from PyQt6.QtGui import QFont

        font = QFont("Microsoft YaHei UI", 12)
        dialog.setFont(font)

        layout = QVBoxLayout(dialog)

        # 创建内容标签
        about_text = f"""
        <h3 style="color: #1976d2; margin-bottom: 10px;">{APP_NAME}</h3>
        <p style="font-size: 12px; margin-bottom: 5px;"><b>版本:</b> {APP_VERSION}</p>
        <p style="font-size: 12px; margin-bottom: 15px;">一个简洁高效的电商产品对比选品工具</p>
        
        <p style="font-size: 12px; font-weight: bold; margin-bottom: 5px;">主要功能：</p>
        <ul style="font-size: 12px; margin-left: 20px; margin-bottom: 15px;">
        <li>产品管理（增删改查）</li>
        <li>多货源对比分析</li>
        <li>图片轮播查看</li>
        <li>数据导入导出</li>
        </ul>
        
        <p style="font-size: 12px; font-weight: bold; margin-bottom: 5px;">技术特点：</p>
        <ul style="font-size: 12px; margin-left: 20px;">
        <li>基于PyQt6 + SQLite</li>
        <li>轻量级架构，高性能</li>
        <li>简洁的用户界面</li>
        <li>完整的功能实现</li>
        </ul>
        """

        about_label = QLabel(about_text)
        about_label.setStyleSheet("padding: 20px; line-height: 1.4;")
        about_label.setWordWrap(True)
        layout.addWidget(about_label)

        # 添加确定按钮
        from PyQt6.QtWidgets import QDialogButtonBox

        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        button_box.setStyleSheet("QPushButton { font-size: 12px; padding: 8px 16px; }")
        button_box.accepted.connect(dialog.accept)
        layout.addWidget(button_box)

        dialog.exec()

    def import_data(self):
        """导入数据"""
        from PyQt6.QtWidgets import (
            QDialog,
            QVBoxLayout,
            QHBoxLayout,
            QListWidget,
            QListWidgetItem,
            QPushButton,
            QLabel,
            QProgressDialog,
            QMessageBox,
        )
        from PyQt6.QtCore import Qt
        import json
        import os
        import shutil
        from pathlib import Path
        from config import IMAGES_DIR
        from datetime import datetime

        # 创建导入选择对话框
        class ImportSelectionDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.selected_folder = None
                self.setup_ui()
                self.load_export_folders()

            def setup_ui(self):
                self.setWindowTitle("选择要导入的数据")
                self.setMinimumSize(600, 400)
                self.resize(600, 400)

                layout = QVBoxLayout(self)

                # 说明文字
                info_label = QLabel("请选择要导入的数据版本：")
                info_label.setStyleSheet(
                    "font-size: 14px; font-weight: bold; margin-bottom: 10px;"
                )
                layout.addWidget(info_label)

                # 导出版本列表
                self.export_list = QListWidget()
                self.export_list.setStyleSheet("font-size: 12px;")
                layout.addWidget(self.export_list)

                # 按钮
                button_layout = QHBoxLayout()

                # 刷新按钮
                refresh_btn = QPushButton("刷新列表")
                refresh_btn.clicked.connect(self.load_export_folders)
                button_layout.addWidget(refresh_btn)

                # 浏览文件夹按钮
                browse_btn = QPushButton("浏览其他文件夹...")
                browse_btn.clicked.connect(self.browse_folder)
                button_layout.addWidget(browse_btn)

                # 删除导出版本按钮
                delete_btn = QPushButton("删除选中版本")
                delete_btn.setStyleSheet("background-color: #f44336; color: white;")
                delete_btn.clicked.connect(self.delete_selected_export)
                button_layout.addWidget(delete_btn)

                button_layout.addStretch()

                # 确定和取消按钮
                ok_btn = QPushButton("导入")
                ok_btn.clicked.connect(self.accept)
                button_layout.addWidget(ok_btn)

                cancel_btn = QPushButton("取消")
                cancel_btn.clicked.connect(self.reject)
                button_layout.addWidget(cancel_btn)

                layout.addLayout(button_layout)

            def load_export_folders(self):
                """加载导出文件夹列表"""
                self.export_list.clear()

                # 扫描export文件夹
                export_dir = Path("export")
                if not export_dir.exists():
                    item = QListWidgetItem("未找到导出文件夹")
                    item.setFlags(Qt.ItemFlag.NoItemFlags)  # 不可选择
                    self.export_list.addItem(item)
                    return

                export_folders = []
                for folder in export_dir.iterdir():
                    if folder.is_dir() and folder.name.startswith("产品数据导出_"):
                        data_file = folder / "data.json"
                        if data_file.exists():
                            # 读取导出信息
                            try:
                                with open(data_file, "r", encoding="utf-8") as f:
                                    data = json.load(f)

                                export_info = data.get("export_info", {})
                                timestamp = export_info.get("timestamp", "")
                                product_count = len(data.get("products", []))
                                tag_count = len(data.get("tags", []))

                                # 解析时间戳
                                if timestamp:
                                    try:
                                        dt = datetime.fromisoformat(
                                            timestamp.replace("Z", "+00:00")
                                        )
                                        time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                                    except:
                                        time_str = timestamp
                                else:
                                    time_str = "未知时间"

                                export_folders.append(
                                    {
                                        "folder": folder,
                                        "name": folder.name,
                                        "time": time_str,
                                        "products": product_count,
                                        "tags": tag_count,
                                        "timestamp": timestamp,
                                    }
                                )
                            except Exception as e:
                                print(f"读取导出信息失败: {e}")

                # 按时间排序（最新的在前）
                export_folders.sort(key=lambda x: x["timestamp"], reverse=True)

                if not export_folders:
                    item = QListWidgetItem("未找到有效的导出数据")
                    item.setFlags(Qt.ItemFlag.NoItemFlags)  # 不可选择
                    self.export_list.addItem(item)
                else:
                    for export_info in export_folders:
                        display_text = (
                            f"{export_info['name']}\n"
                            f"导出时间: {export_info['time']}\n"
                            f"产品数量: {export_info['products']} | 标签数量: {export_info['tags']}"
                        )

                        item = QListWidgetItem(display_text)
                        item.setData(
                            Qt.ItemDataRole.UserRole, str(export_info["folder"])
                        )
                        item.setSizeHint(item.sizeHint())  # 调整行高
                        self.export_list.addItem(item)

            def browse_folder(self):
                """浏览其他文件夹"""
                from PyQt6.QtWidgets import QFileDialog

                folder_path = QFileDialog.getExistingDirectory(
                    self,
                    "选择导入数据的文件夹",
                    "export",
                    QFileDialog.Option.ShowDirsOnly,
                )

                if folder_path:
                    # 验证文件夹
                    data_file = Path(folder_path) / "data.json"
                    if data_file.exists():
                        self.selected_folder = folder_path
                        super().accept()
                    else:
                        QMessageBox.warning(
                            self, "错误", "所选文件夹中未找到 data.json 文件"
                        )

            def delete_selected_export(self):
                """删除选中的导出版本"""
                current_item = self.export_list.currentItem()
                if not current_item or not current_item.data(Qt.ItemDataRole.UserRole):
                    QMessageBox.warning(self, "提示", "请选择要删除的导出版本")
                    return

                folder_path = current_item.data(Qt.ItemDataRole.UserRole)
                folder_name = Path(folder_path).name

                # 确认删除
                reply = QMessageBox.question(
                    self,
                    "确认删除",
                    f"确定要删除导出版本 '{folder_name}' 吗？\n\n此操作无法撤销！",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No,
                )

                if reply == QMessageBox.StandardButton.Yes:
                    try:
                        # 删除文件夹
                        import shutil

                        shutil.rmtree(folder_path)

                        # 刷新列表
                        self.load_export_folders()

                        QMessageBox.information(
                            self, "成功", f"已删除导出版本: {folder_name}"
                        )

                        # 记录日志
                        import logging

                        logger = logging.getLogger(__name__)
                        logger.info(f"删除导出版本: {folder_path}")

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"删除失败: {str(e)}")
                        # 记录错误日志
                        import logging

                        logger = logging.getLogger(__name__)
                        logger.error(f"删除导出版本失败: {folder_path}, 错误: {str(e)}")

            def accept(self):
                """确认选择"""
                current_item = self.export_list.currentItem()
                if current_item and current_item.data(Qt.ItemDataRole.UserRole):
                    self.selected_folder = current_item.data(Qt.ItemDataRole.UserRole)
                    super().accept()
                else:
                    QMessageBox.warning(self, "提示", "请选择要导入的数据版本")

        # 显示导入选择对话框
        dialog = ImportSelectionDialog(self)
        if dialog.exec() != QDialog.DialogCode.Accepted or not dialog.selected_folder:
            return

        folder_path = dialog.selected_folder

        # 验证文件夹结构
        data_file = os.path.join(folder_path, "data.json")
        images_dir = os.path.join(folder_path, "images")

        if not os.path.exists(data_file):
            QMessageBox.warning(self, "错误", "未找到数据文件 data.json")
            return

        try:
            # 显示进度对话框
            progress = QProgressDialog("正在导入数据...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            # 添加日志记录
            logger = logging.getLogger(__name__)
            logger.info(f"开始导入数据 - 文件夹: {folder_path}")

            # 读取数据文件
            with open(data_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            logger.info(
                f"数据文件读取成功 - 产品数量: {len(data.get('products', []))}, 标签数量: {len(data.get('tags', []))}"
            )

            progress.setValue(20)

            # 导入标签
            if "tags" in data:
                logger.info("开始导入标签...")
                for tag_data in data["tags"]:
                    # 检查标签是否已存在
                    existing_tag = self.tag_service.get_tag_by_name(tag_data["name"])
                    if not existing_tag:
                        # 只有当标签不存在时才创建
                        self.tag_service.create_tag(
                            tag_data["name"],
                            tag_data["color"],
                            tag_data.get("description", ""),
                        )
                        logger.info(f"创建标签: {tag_data['name']}")
                    else:
                        logger.info(f"标签已存在，跳过创建: {tag_data['name']}")

            progress.setValue(40)

            # 导入产品
            if "products" in data:
                logger.info("开始导入产品...")
                for product_data in data["products"]:
                    logger.info(f"导入产品: {product_data['name']}")
                    # 创建产品
                    product = self.product_service.create_product(
                        name=product_data["name"],
                        sku=product_data.get("sku", ""),
                        selling_price=product_data.get("selling_price", 0.0),
                        description=product_data.get("description", ""),
                        sales_reference=product_data.get("sales_reference", 0),
                        reference_url=product_data.get("reference_url", ""),
                    )

                    # 导入图片
                    if "images" in product_data and os.path.exists(images_dir):
                        logger.info(f"处理产品图片: {product_data['name']}")
                        product_images_dir = os.path.join(
                            images_dir, f"product_{product_data['id']}"
                        )
                        if os.path.exists(product_images_dir):
                            # 创建产品图片目录
                            target_dir = (
                                Path(IMAGES_DIR) / "products" / f"product_{product.id}"
                            )
                            target_dir.mkdir(parents=True, exist_ok=True)

                            # 复制图片文件
                            copied_images = []
                            for image_name in product_data["images"]:
                                src_path = os.path.join(product_images_dir, image_name)
                                dst_path = target_dir / image_name
                                if os.path.exists(src_path):
                                    shutil.copy2(src_path, dst_path)
                                    # 存储完整的相对路径，而不只是文件名
                                    relative_path = str(
                                        dst_path.relative_to(Path.cwd())
                                    ).replace("\\", "/")
                                    copied_images.append(relative_path)
                                    logger.info(
                                        f"复制图片: {image_name} -> {relative_path}"
                                    )

                            # 直接更新数据库中的图片信息
                            if copied_images:
                                cursor = self.db_manager.connection.cursor()
                                cursor.execute(
                                    "UPDATE products SET images = ? WHERE id = ?",
                                    (json.dumps(copied_images), product.id),
                                )
                                self.db_manager.connection.commit()
                                logger.info(
                                    f"更新产品图片信息: {len(copied_images)}张图片"
                                )

                            # 导入主要图片列表
                            if "featured_images" in product_data:
                                featured_images = []
                                for image_filename in product_data["featured_images"]:
                                    # 构造完整路径
                                    relative_path = str(
                                        Path(IMAGES_DIR)
                                        / "products"
                                        / f"product_{product.id}"
                                        / image_filename
                                    ).replace("\\", "/")

                                    # 只有在已复制的图片列表中才添加到主要图片列表
                                    if relative_path in copied_images:
                                        featured_images.append(relative_path)
                                        logger.info(f"添加主要图片: {image_filename}")

                                # 更新数据库中的主要图片信息
                                if featured_images:
                                    cursor = self.db_manager.connection.cursor()
                                    cursor.execute(
                                        "UPDATE products SET featured_images = ? WHERE id = ?",
                                        (json.dumps(featured_images), product.id),
                                    )
                                    self.db_manager.connection.commit()
                                    logger.info(
                                        f"更新产品主要图片信息: {len(featured_images)}张主要图片"
                                    )

                    # 导入货源
                    if "sources" in product_data:
                        logger.info(f"导入货源数量: {len(product_data['sources'])}")
                        for source_data in product_data["sources"]:
                            # 构建货源参数，支持所有新字段
                            source_kwargs = {
                                "url": source_data.get("url", ""),
                                "shop_info": source_data.get("shop_info", ""),
                                "quantity": source_data.get("quantity", 1),
                                "min_order_quantity": source_data.get(
                                    "min_order_quantity", 1
                                ),
                                "shipping_cost": source_data.get("shipping_cost", 0.0),
                                "shipping_cost_suffix": source_data.get(
                                    "shipping_cost_suffix", ""
                                ),
                                "shipping_location": source_data.get(
                                    "shipping_location", ""
                                ),
                                "product_name": source_data.get("product_name", ""),
                                "sales_count": source_data.get("sales_count", 0),
                                "status": source_data.get("status", "active"),
                                "return_policy": source_data.get("return_policy", ""),
                                "pickup_rate_24h": source_data.get(
                                    "pickup_rate_24h", 0.0
                                ),
                                "pickup_rate_48h": source_data.get(
                                    "pickup_rate_48h", 0.0
                                ),
                                "monthly_dropship_orders": source_data.get(
                                    "monthly_dropship_orders", 0
                                ),
                                "downstream_stores": source_data.get(
                                    "downstream_stores", 0
                                ),
                                "distributor_count": source_data.get(
                                    "distributor_count", 0
                                ),
                                "available_products": source_data.get(
                                    "available_products", 0
                                ),
                                "monthly_new_products": source_data.get(
                                    "monthly_new_products", 0
                                ),
                                "modes": source_data.get("modes", ["wholesale"]),
                                "image_urls": source_data.get("image_urls", []),
                                # 批发模式专属字段
                                "wholesale_min_order_quantity": source_data.get(
                                    "wholesale_min_order_quantity", 1
                                ),
                                "wholesale_payment_terms": source_data.get(
                                    "wholesale_payment_terms", 0
                                ),
                                "wholesale_rebate_rate": source_data.get(
                                    "wholesale_rebate_rate", 0.0
                                ),
                                "wholesale_price_tiers": source_data.get(
                                    "wholesale_price_tiers", ""
                                ),
                                # 代发模式专属字段
                                "dropship_service_fee": source_data.get(
                                    "dropship_service_fee", 0.0
                                ),
                                "dropship_processing_time": source_data.get(
                                    "dropship_processing_time", 0
                                ),
                                "dropship_packaging": source_data.get(
                                    "dropship_packaging", ""
                                ),
                                "dropship_inventory_sync": source_data.get(
                                    "dropship_inventory_sync", "实时同步"
                                ),
                                "dropship_min_quantity": source_data.get(
                                    "dropship_min_quantity", 1
                                ),
                                "dropship_shipping_location": source_data.get(
                                    "dropship_shipping_location", ""
                                ),
                                "dropship_support_regions": source_data.get(
                                    "dropship_support_regions", ""
                                ),
                                "contact_info": source_data.get("contact_info", ""),
                                "notes": source_data.get("notes", ""),
                                "is_active": source_data.get("is_active", True),
                                # 向后兼容：如果存在旧的source_mode但没有modes，则使用source_mode
                                "source_mode": source_data.get(
                                    "source_mode", "wholesale"
                                ),
                            }

                            # 处理product_publish_time
                            if source_data.get("product_publish_time"):
                                try:
                                    from datetime import datetime

                                    source_kwargs["product_publish_time"] = (
                                        datetime.fromisoformat(
                                            source_data["product_publish_time"]
                                        )
                                    )
                                except (ValueError, TypeError):
                                    source_kwargs["product_publish_time"] = None

                            # 向后兼容：如果没有modes字段但有source_mode，则使用source_mode
                            if not source_data.get("modes") and source_data.get(
                                "source_mode"
                            ):
                                source_kwargs["modes"] = [source_data["source_mode"]]

                            # 处理货源图片和主要图片
                            if (
                                "image_urls" in source_data
                                and source_data["image_urls"]
                            ):
                                # 处理货源图片文件导入
                                source_images_dir = os.path.join(
                                    images_dir, f"source_{source_data['id']}"
                                )
                                if os.path.exists(source_images_dir):
                                    # 创建货源图片目录
                                    target_dir = (
                                        Path(IMAGES_DIR)
                                        / "sources"
                                        / f"source_{source_data['id']}"
                                    )
                                    target_dir.mkdir(parents=True, exist_ok=True)

                                    # 复制图片文件
                                    copied_source_images = []
                                    for image_name in source_data["image_urls"]:
                                        src_path = os.path.join(
                                            source_images_dir, image_name
                                        )
                                        dst_path = target_dir / image_name
                                        if os.path.exists(src_path):
                                            shutil.copy2(src_path, dst_path)
                                            # 存储完整的相对路径
                                            relative_path = str(
                                                dst_path.relative_to(Path.cwd())
                                            ).replace("\\", "/")
                                            copied_source_images.append(relative_path)
                                            logger.info(
                                                f"复制货源图片: {image_name} -> {relative_path}"
                                            )

                                    # 更新source_kwargs中的图片路径
                                    source_kwargs["image_urls"] = copied_source_images

                                    # 处理主要图片列表
                                    if (
                                        "featured_images" in source_data
                                        and source_data["featured_images"]
                                    ):
                                        copied_featured_images = []
                                        for image_name in source_data[
                                            "featured_images"
                                        ]:
                                            # 构造完整路径
                                            relative_path = str(
                                                Path(IMAGES_DIR)
                                                / "sources"
                                                / f"source_{source_data['id']}"
                                                / image_name
                                            ).replace("\\", "/")

                                            # 只有在已复制的图片列表中才添加到主要图片列表
                                            if relative_path in copied_source_images:
                                                copied_featured_images.append(
                                                    relative_path
                                                )
                                                logger.info(
                                                    f"添加货源主要图片: {image_name}"
                                                )

                                        source_kwargs["featured_images"] = (
                                            copied_featured_images
                                        )
                                    else:
                                        source_kwargs["featured_images"] = []
                                else:
                                    logger.warning(
                                        f"货源图片目录不存在: {source_images_dir}"
                                    )
                            else:
                                # 确保featured_images字段存在
                                source_kwargs["featured_images"] = []

                            self.product_service.add_source(
                                product.id,
                                source_data["name"],
                                source_data["price"],
                                **source_kwargs,
                            )

                    # 导入自定义字段
                    if "custom_fields" in product_data:
                        logger.info(
                            f"导入自定义字段数量: {len(product_data['custom_fields'])}"
                        )
                        for field_data in product_data["custom_fields"]:
                            self.product_service.add_custom_field(
                                product.id, field_data["name"], field_data["value"]
                            )

                    # 关联标签
                    if "tags" in product_data:
                        logger.info(f"关联标签数量: {len(product_data['tags'])}")
                        for tag_name in product_data["tags"]:
                            tag = self.tag_service.get_tag_by_name(tag_name)
                            if tag:
                                self.product_service.add_tag_to_product(
                                    product.id, tag.id
                                )

            progress.setValue(100)
            progress.close()

            # 刷新界面
            self.product_list_widget.refresh()
            self.tag_widget.refresh()

            logger.info("数据导入完成")
            # QMessageBox.information(self, "成功", "数据导入完成！")  # 移除弹窗
            self.status_label.setText("数据导入完成")

        except Exception as e:
            logger.error(f"导入数据失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导入数据失败：{str(e)}")

    def export_data(self):
        """导出数据"""
        from PyQt6.QtWidgets import QFileDialog, QProgressDialog, QMessageBox
        import json
        import os
        import shutil
        from pathlib import Path
        from config import IMAGES_DIR
        from datetime import datetime

        # 创建默认导出文件夹
        default_export_dir = Path("export")
        default_export_dir.mkdir(exist_ok=True)

        # 默认导出路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_export_path = default_export_dir / f"产品数据导出_{timestamp}"

        # 询问用户是否使用默认路径
        reply = QMessageBox.question(
            self,
            "选择导出位置",
            f"是否使用默认导出路径？\n\n默认路径: {default_export_path.absolute()}\n\n"
            "点击 '是' 使用默认路径\n"
            "点击 '否' 自定义选择路径",
            QMessageBox.StandardButton.Yes
            | QMessageBox.StandardButton.No
            | QMessageBox.StandardButton.Cancel,
            QMessageBox.StandardButton.Yes,
        )

        if reply == QMessageBox.StandardButton.Cancel:
            return

        if reply == QMessageBox.StandardButton.Yes:
            # 使用默认路径
            export_folder = str(default_export_path)
        else:
            # 用户自定义选择路径
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择导出数据的保存位置",
                str(default_export_dir),
                QFileDialog.Option.ShowDirsOnly,
            )

            if not folder_path:
                return

            # 创建导出文件夹
            export_folder = os.path.join(folder_path, f"产品数据导出_{timestamp}")

        # 创建导出文件夹
        os.makedirs(export_folder, exist_ok=True)

        try:
            # 显示进度对话框
            progress = QProgressDialog("正在导出数据...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            # 准备导出数据
            export_data = {
                "export_info": {
                    "version": "1.0",
                    "timestamp": datetime.now().isoformat(),
                    "app_name": "Python_Ecom_Product_Source_Compare",
                    "app_version": APP_VERSION,
                },
                "tags": [],
                "products": [],
            }

            # 导出标签
            tags = self.tag_service.get_all_tags()
            for tag in tags:
                export_data["tags"].append(
                    {
                        "id": tag.id,
                        "name": tag.name,
                        "color": tag.color,
                        "description": tag.description,
                    }
                )

            progress.setValue(20)

            # 导出产品
            products = self.product_service.get_all_products()
            images_export_dir = os.path.join(export_folder, "images")
            os.makedirs(images_export_dir, exist_ok=True)

            for i, product in enumerate(products):
                product_data = {
                    "id": product.id,
                    "name": product.name,
                    "sku": product.sku,
                    "selling_price": product.selling_price,
                    "description": product.description,
                    "sales_reference": getattr(product, "sales_reference", 0),
                    "reference_url": getattr(product, "reference_url", ""),
                    "created_at": (
                        product.created_at.isoformat() if product.created_at else None
                    ),
                    "updated_at": (
                        product.updated_at.isoformat() if product.updated_at else None
                    ),
                    "images": [],
                    "featured_images": [],
                    "sources": [],
                    "custom_fields": [],
                    "tags": [],
                }

                # 导出图片
                if product.images:
                    product_images_dir = os.path.join(
                        images_export_dir, f"product_{product.id}"
                    )
                    os.makedirs(product_images_dir, exist_ok=True)

                    for image_path in product.images:
                        # 处理图片路径：可能是完整路径或文件名
                        if os.path.isabs(image_path) or image_path.startswith("data/"):
                            # 完整路径，直接使用
                            src_path = Path(image_path)
                        else:
                            # 文件名，构造完整路径
                            src_path = (
                                Path(IMAGES_DIR)
                                / "products"
                                / f"product_{product.id}"
                                / image_path
                            )

                        # 获取文件名
                        image_filename = os.path.basename(str(src_path))
                        dst_path = os.path.join(product_images_dir, image_filename)

                        if src_path.exists():
                            shutil.copy2(src_path, dst_path)
                            product_data["images"].append(image_filename)
                            print(f"导出图片: {src_path} -> {dst_path}")
                        else:
                            print(f"警告：图片文件不存在: {src_path}")

                # 导出主要图片列表
                if hasattr(product, "featured_images") and product.featured_images:
                    for image_path in product.featured_images:
                        # 处理图片路径：可能是完整路径或文件名
                        if os.path.isabs(image_path) or image_path.startswith("data/"):
                            # 完整路径，获取文件名
                            image_filename = os.path.basename(image_path)
                        else:
                            # 构造完整路径
                            src_path = (
                                Path(IMAGES_DIR)
                                / "products"
                                / f"product_{product.id}"
                                / image_path
                            )
                            image_filename = os.path.basename(str(src_path))

                        # 只有在已导出的图片列表中才添加到主要图片列表
                        if image_filename in product_data["images"]:
                            product_data["featured_images"].append(image_filename)
                            print(f"添加主要图片: {image_filename}")

                # 导出货源
                for source in product.sources:
                    # 构建货源数据，包含所有新字段
                    source_data = {
                        "id": source.id,
                        "name": source.name,
                        "price": source.price,
                        "url": source.url,
                        "shop_info": source.shop_info,
                        "quantity": source.quantity,
                        "min_order_quantity": source.min_order_quantity,
                        "shipping_cost": source.shipping_cost,
                        "shipping_cost_suffix": source.shipping_cost_suffix,
                        "shipping_location": source.shipping_location,
                        "product_name": source.product_name,
                        "sales_count": source.sales_count,
                        "status": source.status,
                        "return_policy": source.return_policy,
                        "pickup_rate_24h": source.pickup_rate_24h,
                        "pickup_rate_48h": source.pickup_rate_48h,
                        "monthly_dropship_orders": source.monthly_dropship_orders,
                        "downstream_stores": source.downstream_stores,
                        "distributor_count": source.distributor_count,
                        "product_publish_time": (
                            source.product_publish_time.isoformat()
                            if source.product_publish_time
                            else None
                        ),
                        "available_products": source.available_products,
                        "monthly_new_products": source.monthly_new_products,
                        "modes": source.modes,
                        "image_urls": source.image_urls,
                        "featured_images": (
                            source.featured_images
                            if hasattr(source, "featured_images")
                            else []
                        ),
                        # 批发模式专属字段
                        "wholesale_min_order_quantity": source.wholesale_min_order_quantity,
                        "wholesale_payment_terms": source.wholesale_payment_terms,
                        "wholesale_rebate_rate": source.wholesale_rebate_rate,
                        "wholesale_price_tiers": source.wholesale_price_tiers,
                        # 代发模式专属字段
                        "dropship_service_fee": source.dropship_service_fee,
                        "dropship_processing_time": source.dropship_processing_time,
                        "dropship_packaging": source.dropship_packaging,
                        "dropship_inventory_sync": source.dropship_inventory_sync,
                        "dropship_min_quantity": source.dropship_min_quantity,
                        "dropship_shipping_location": source.dropship_shipping_location,
                        "dropship_support_regions": source.dropship_support_regions,
                        "contact_info": source.contact_info,
                        "notes": source.notes,
                        "is_active": source.is_active,
                        # 保持向后兼容
                        "source_mode": source.modes[0] if source.modes else "wholesale",
                    }

                    # 导出货源图片文件
                    if source.image_urls:
                        source_images_dir = os.path.join(
                            images_export_dir, f"source_{source.id}"
                        )
                        os.makedirs(source_images_dir, exist_ok=True)

                        exported_images = []
                        for image_path in source.image_urls:
                            # 处理图片路径：可能是完整路径或文件名
                            if os.path.isabs(image_path) or image_path.startswith(
                                "data/"
                            ):
                                # 完整路径，直接使用
                                src_path = Path(image_path)
                            else:
                                # 文件名，构造完整路径
                                src_path = (
                                    Path(IMAGES_DIR)
                                    / "sources"
                                    / f"source_{source.id}"
                                    / image_path
                                )

                            # 获取文件名
                            image_filename = os.path.basename(str(src_path))
                            dst_path = os.path.join(source_images_dir, image_filename)

                            if src_path.exists():
                                shutil.copy2(src_path, dst_path)
                                exported_images.append(image_filename)
                                print(f"导出货源图片: {src_path} -> {dst_path}")
                            else:
                                print(f"警告：货源图片文件不存在: {src_path}")

                        # 更新导出数据中的图片路径
                        source_data["image_urls"] = exported_images

                        # 处理主要图片列表
                        if (
                            hasattr(source, "featured_images")
                            and source.featured_images
                        ):
                            exported_featured_images = []
                            for image_path in source.featured_images:
                                # 获取文件名
                                if os.path.isabs(image_path) or image_path.startswith(
                                    "data/"
                                ):
                                    image_filename = os.path.basename(image_path)
                                else:
                                    src_path = (
                                        Path(IMAGES_DIR)
                                        / "sources"
                                        / f"source_{source.id}"
                                        / image_path
                                    )
                                    image_filename = os.path.basename(str(src_path))

                                # 只有在已导出的图片列表中才添加到主要图片列表
                                if image_filename in exported_images:
                                    exported_featured_images.append(image_filename)
                                    print(f"添加货源主要图片: {image_filename}")

                            source_data["featured_images"] = exported_featured_images

                    product_data["sources"].append(source_data)

                # 导出自定义字段
                for field in product.custom_fields:
                    product_data["custom_fields"].append(
                        {"id": field.id, "name": field.name, "value": field.value}
                    )

                # 导出标签
                for tag in product.tags:
                    product_data["tags"].append(tag.name)

                export_data["products"].append(product_data)

                # 更新进度
                progress.setValue(20 + int(70 * (i + 1) / len(products)))

            # 保存数据文件
            data_file = os.path.join(export_folder, "data.json")
            with open(data_file, "w", encoding="utf-8") as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            # 创建说明文件
            readme_file = os.path.join(export_folder, "README.txt")
            with open(readme_file, "w", encoding="utf-8") as f:
                f.write("产品数据导出包\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"产品数量: {len(products)}\n")
                f.write(f"标签数量: {len(tags)}\n\n")
                f.write("文件说明:\n")
                f.write("- data.json: 包含所有产品、货源、标签等数据\n")
                f.write("- images/: 包含所有产品图片\n")
                f.write("- README.txt: 此说明文件\n\n")
                f.write("导入方法:\n")
                f.write("1. 打开应用程序\n")
                f.write("2. 选择 文件 -> 导入数据\n")
                f.write("3. 选择此文件夹进行导入\n")

            progress.setValue(100)
            progress.close()

            QMessageBox.information(
                self,
                "导出完成",
                f"数据已成功导出到:\n{export_folder}\n\n"
                f"导出内容:\n"
                f"- {len(products)} 个产品\n"
                f"- {len(tags)} 个标签\n"
                f"- 相关图片文件",
            )

            self.status_label.setText("数据导出完成")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出数据失败：{str(e)}")

    def clear_all_products(self):
        """清空所有商品"""
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空所有商品吗？\n\n此操作将删除所有商品、货源和相关数据，且无法恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 获取所有商品
                products = self.product_service.get_all_products()

                # 删除每个商品（这会自动删除相关的货源和自定义字段）
                for product in products:
                    self.product_service.delete_product(product.id)

                # 刷新界面
                self.product_list_widget.refresh()
                self.detail_stack.setCurrentWidget(self.welcome_widget)
                self.current_product_id = None

                QMessageBox.information(
                    self, "成功", f"已成功清空 {len(products)} 个商品"
                )
                self.status_label.setText("已清空所有商品")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"清空商品失败：{str(e)}")

    def force_clean_database(self):
        """强制清理数据库"""
        reply = QMessageBox.question(
            self,
            "确认强制清理",
            "确定要强制清理数据库吗？\n\n此操作将：\n"
            "1. 删除所有数据表\n"
            "2. 重新创建数据库结构\n"
            "3. 清理所有产品图片文件\n\n"
            "此操作无法恢复，请谨慎操作！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 关闭当前数据库连接
                self.db_manager.close()

                # 删除数据库文件
                import os

                if os.path.exists(self.db_manager.db_path):
                    os.remove(self.db_manager.db_path)

                # 清理图片目录
                import shutil
                from pathlib import Path
                from config import IMAGES_DIR

                images_dir = Path(IMAGES_DIR) / "products"
                if images_dir.exists():
                    shutil.rmtree(images_dir)

                # 重新初始化数据库
                self.db_manager.init_database()

                # 刷新界面
                self.product_list_widget.refresh()
                self.detail_stack.setCurrentWidget(self.welcome_widget)
                self.current_product_id = None

                QMessageBox.information(self, "成功", "数据库已强制清理完成")
                self.status_label.setText("数据库已强制清理")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"强制清理失败：{str(e)}")

    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出程序吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 清理资源
            self.status_timer.stop()
            self.db_manager.close()
            event.accept()
        else:
            event.ignore()

    def toggle_dark_mode(self):
        """切换暗黑模式"""
        current_theme = get_current_theme()
        new_theme = "dark" if current_theme == "light" else "light"

        # 更新配置
        set_theme(new_theme)

        # 应用新主题
        self.apply_theme()

        # 更新按钮文本
        self.update_theme_button_text()

        # 显示提示
        theme_name = "暗黑模式" if new_theme == "dark" else "明亮模式"
        self.status_label.setText(f"已切换到{theme_name}")

    def apply_theme(self):
        """应用主题"""
        current_theme = get_current_theme()

        if current_theme == "dark":
            # 加载暗黑模式样式
            style_path = os.path.join(
                os.path.dirname(__file__), "..", "resources", "styles", "dark_theme.qss"
            )
            if os.path.exists(style_path):
                with open(style_path, "r", encoding="utf-8") as f:
                    style = f.read()
                    # 应用样式到应用程序实例
                    QApplication.instance().setStyleSheet(style)
            else:
                # 如果文件不存在，使用内置的暗黑样式
                QApplication.instance().setStyleSheet(self.get_builtin_dark_style())
        else:
            # 加载明亮模式样式
            style_path = os.path.join(
                os.path.dirname(__file__),
                "..",
                "resources",
                "styles",
                "light_theme.qss",
            )
            if os.path.exists(style_path):
                with open(style_path, "r", encoding="utf-8") as f:
                    style = f.read()
                    # 应用样式到应用程序实例
                    QApplication.instance().setStyleSheet(style)
            else:
                # 如果文件不存在，清空样式
                QApplication.instance().setStyleSheet("")

        # 更新欢迎页面样式
        self.update_welcome_widget_style()

        # 强制更新所有子组件
        for widget in self.findChildren(QWidget):
            widget.style().unpolish(widget)
            widget.style().polish(widget)
            widget.update()

    def get_builtin_dark_style(self):
        """获取内置的暗黑样式"""
        return """
        QMainWindow {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        QWidget {
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: "Microsoft YaHei UI";
        }
        QPushButton {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            min-width: 80px;
        }
        QPushButton:hover {
            background-color: #404040;
            border-color: #2196f3;
        }
        QPushButton:pressed {
            background-color: #505050;
        }
        QToolBar {
            background-color: #2d2d2d;
            border: 1px solid #404040;
            padding: 4px;
            spacing: 4px;
        }
        QToolBar QToolButton {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            min-width: 80px;
        }
        QToolBar QToolButton:hover {
            background-color: #404040;
            border-color: #2196f3;
        }
        QMenuBar {
            background-color: #2d2d2d;
            color: #ffffff;
            border-bottom: 1px solid #404040;
        }
        QMenuBar::item {
            background-color: transparent;
            color: #ffffff;
            padding: 6px 12px;
        }
        QMenuBar::item:selected {
            background-color: #404040;
            color: #ffffff;
        }
        QMenu {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
        }
        QMenu::item {
            background-color: transparent;
            color: #ffffff;
            padding: 6px 24px;
        }
        QMenu::item:selected {
            background-color: #404040;
            color: #ffffff;
        }
        QStatusBar {
            background-color: #2d2d2d;
            color: #ffffff;
            border-top: 1px solid #404040;
        }
        QStatusBar QLabel {
            color: #ffffff;
            background-color: transparent;
        }
        QListWidget {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
        }
        QListWidget::item {
            color: #ffffff;
            background-color: transparent;
        }
        QListWidget::item:selected {
            background-color: #2196f3;
            color: #ffffff;
        }
        QListWidget::item:hover {
            background-color: #404040;
            color: #ffffff;
        }
        QLineEdit {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
            padding: 6px;
            border-radius: 4px;
        }
        QLineEdit:focus {
            border-color: #2196f3;
        }
        QTextEdit {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
            padding: 6px;
            border-radius: 4px;
        }
        QLabel {
            color: #ffffff;
            background-color: transparent;
        }
        QTableWidget {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
        }
        QTableWidget::item {
            color: #ffffff;
            background-color: transparent;
        }
        QTableWidget::item:selected {
            background-color: #2196f3;
            color: #ffffff;
        }
        QHeaderView::section {
            background-color: #404040;
            color: #ffffff;
            padding: 6px;
            border: 1px solid #505050;
        }
        QDialog {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        QMessageBox {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        QMessageBox QLabel {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        QMessageBox QPushButton {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
            padding: 6px 12px;
            border-radius: 4px;
        }
        QMessageBox QPushButton:hover {
            background-color: #404040;
            border-color: #2196f3;
        }
        QDialogButtonBox QPushButton {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
            padding: 6px 12px;
            border-radius: 4px;
        }
        QDialogButtonBox QPushButton:hover {
            background-color: #404040;
            border-color: #2196f3;
        }
        """

    def update_theme_button_text(self):
        """更新主题按钮文本"""
        current_theme = get_current_theme()
        if current_theme == "dark":
            self.theme_action.setText("明亮模式")
        else:
            self.theme_action.setText("暗黑模式")

    def update_welcome_widget_style(self):
        """更新欢迎页面样式"""
        current_theme = get_current_theme()

        # 获取所有QLabel控件
        labels = self.welcome_widget.findChildren(QLabel)

        if current_theme == "dark":
            # 更新欢迎页面的样式
            if len(labels) >= 1:  # 标题标签
                labels[0].setStyleSheet(
                    "font-size: 24px; font-weight: bold; color: #2196f3; margin-bottom: 20px;"
                )
            if len(labels) >= 2:  # 版本标签
                labels[1].setStyleSheet(
                    "font-size: 14px; color: #b0b0b0; margin-bottom: 30px;"
                )
            if len(labels) >= 3:  # 描述标签
                labels[2].setStyleSheet(
                    "font-size: 12px; color: #ffffff; line-height: 1.5;"
                )
        else:
            # 恢复默认样式
            if len(labels) >= 1:  # 标题标签
                labels[0].setStyleSheet(
                    "font-size: 24px; font-weight: bold; color: #1976d2; margin-bottom: 20px;"
                )
            if len(labels) >= 2:  # 版本标签
                labels[1].setStyleSheet(
                    "font-size: 14px; color: #666; margin-bottom: 30px;"
                )
            if len(labels) >= 3:  # 描述标签
                labels[2].setStyleSheet(
                    "font-size: 12px; color: #333; line-height: 1.5;"
                )

    def get_theme_colors(self):
        """获取当前主题的颜色配置"""
        current_theme = get_current_theme()

        if current_theme == "dark":
            return {
                "primary": "#2196f3",
                "text": "#ffffff",
                "secondary_text": "#b0b0b0",
                "success": "#4caf50",
                "error": "#f44336",
                "warning": "#ff9800",
                "background": "#1e1e1e",
                "surface": "#2d2d2d",
                "border": "#404040",
            }
        else:
            return {
                "primary": "#1976d2",
                "text": "#333333",
                "secondary_text": "#666666",
                "success": "#4caf50",
                "error": "#f44336",
                "warning": "#ff9800",
                "background": "#ffffff",
                "surface": "#f5f5f5",
                "border": "#dddddd",
            }
