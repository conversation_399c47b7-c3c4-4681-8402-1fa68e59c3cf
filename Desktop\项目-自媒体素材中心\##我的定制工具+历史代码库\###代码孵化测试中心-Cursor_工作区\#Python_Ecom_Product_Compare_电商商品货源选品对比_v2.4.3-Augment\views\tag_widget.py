"""
标签显示组件 - 用于显示快捷标签和支持点击筛选
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QScrollArea,
    QFrame,
    QMenu,
    QMessageBox,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QCursor, QPixmap, QPainter, QColor

from models.database import TagService
from models.tag import Tag
from views.dialogs.tag_manager_dialog import TagManagerDialog


class TagButton(QPushButton):
    """标签按钮"""

    tag_clicked = pyqtSignal(int)  # 标签被点击信号
    tag_removed = pyqtSignal(int)  # 标签被移除信号

    def __init__(self, tag: Tag, parent=None):
        super().__init__(parent)
        self.tag = tag
        self.is_active = False
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        self.setText(f"🏷️ {self.tag.name}")
        self.setFixedHeight(32)
        self.setCheckable(True)
        self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.setObjectName("tagButton")
        self.setProperty("tagColor", self.tag.color)
        self.setProperty("isActive", self.is_active)

        # 连接点击信号
        self.clicked.connect(self.on_clicked)

        # 设置右键菜单
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

    def update_style(self):
        """更新按钮样式"""
        self.setProperty("isActive", self.is_active)
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()

    def on_clicked(self):
        """点击事件"""
        self.is_active = not self.is_active
        self.update_style()
        self.tag_clicked.emit(self.tag.id)

    def set_active(self, active: bool):
        """设置激活状态"""
        self.is_active = active
        self.setChecked(active)
        self.update_style()

    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)

        # 查看产品
        view_action = menu.addAction("查看关联产品")
        view_action.triggered.connect(lambda: self.tag_clicked.emit(self.tag.id))

        # 分隔线
        menu.addSeparator()

        # 编辑标签
        edit_action = menu.addAction("编辑标签")
        edit_action.triggered.connect(self.edit_tag)

        # 删除标签
        delete_action = menu.addAction("删除标签")
        delete_action.triggered.connect(self.delete_tag)

        # 显示菜单
        menu.exec(self.mapToGlobal(position))

    def edit_tag(self):
        """编辑标签"""
        # 这里需要发送信号到父组件
        parent_widget = self.parent()
        while parent_widget and not isinstance(parent_widget, TagWidget):
            parent_widget = parent_widget.parent()

        if parent_widget:
            parent_widget.edit_tag(self.tag.id)

    def delete_tag(self):
        """删除标签"""
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除标签 '{self.tag.name}' 吗？\n\n删除后将从所有关联的产品中移除此标签。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.tag_removed.emit(self.tag.id)


class TagWidget(QWidget):
    """标签组件"""

    tag_selected = pyqtSignal(list)  # 标签被选中信号，传递选中的标签ID列表
    tag_filter_changed = pyqtSignal(list)  # 标签筛选改变信号

    def __init__(self, tag_service: TagService, parent=None):
        super().__init__(parent)
        self.tag_service = tag_service
        self.tag_buttons = {}  # 标签ID -> TagButton 映射
        self.selected_tags = set()  # 选中的标签ID集合
        self.setup_ui()
        self.load_tags()

    def setup_ui(self):
        """设置界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 标题栏
        header_layout = QHBoxLayout()
        header_widget = QWidget()
        header_widget.setObjectName("tagHeaderWidget")
        header_widget.setLayout(header_layout)

        title_label = QLabel("标签筛选")
        title_label.setObjectName("tagTitleLabel")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 管理按钮
        self.manage_btn = QPushButton("管理")
        self.manage_btn.setObjectName("tagManageButton")
        self.manage_btn.setFixedSize(50, 24)
        self.manage_btn.clicked.connect(self.open_tag_manager)
        header_layout.addWidget(self.manage_btn)

        # 清除按钮
        self.clear_btn = QPushButton("清除")
        self.clear_btn.setObjectName("tagClearButton")
        self.clear_btn.setFixedSize(50, 24)
        self.clear_btn.clicked.connect(self.clear_selection)
        header_layout.addWidget(self.clear_btn)

        main_layout.addWidget(header_widget)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setObjectName("tagScrollArea")
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(120)
        scroll_area.setMaximumHeight(180)
        self.scroll_area = scroll_area

        # 标签容器
        self.tag_container = QWidget()
        self.tag_container.setObjectName("tagContainer")
        self.tag_layout = QVBoxLayout(self.tag_container)
        self.tag_layout.setContentsMargins(12, 12, 12, 12)
        self.tag_layout.setSpacing(8)

        # 标签流式布局容器
        self.tag_flow_widget = QWidget()
        self.tag_flow_widget.setObjectName("tagFlowWidget")
        self.tag_flow_layout = FlowLayout(self.tag_flow_widget)

        self.tag_layout.addWidget(self.tag_flow_widget)
        self.tag_layout.addStretch()

        scroll_area.setWidget(self.tag_container)
        main_layout.addWidget(scroll_area)

        # 状态标签
        self.status_label = QLabel("加载中...")
        self.status_label.setStyleSheet(
            "font-size: 11px; color: #666; margin-top: 5px;"
        )
        main_layout.addWidget(self.status_label)

    def load_tags(self):
        """加载标签"""
        try:
            # 更新样式（支持主题切换）
            self.update_all_styles()

            tags = self.tag_service.get_all_tags()  # 获取所有标签，按使用次数排序显示

            # 清除现有标签
            self.clear_tags()

            if not tags:
                self.status_label.setText("暂无标签")
                return

                # 添加标签按钮
            for tag in tags:
                # 显示所有标签，不管是否有使用
                tag_button = TagButton(tag, self)
                tag_button.tag_clicked.connect(self.on_tag_clicked)
                tag_button.tag_removed.connect(self.on_tag_removed)

                self.tag_buttons[tag.id] = tag_button
                self.tag_flow_layout.addWidget(tag_button)

            # 更新状态
            if self.tag_buttons:
                used_tags = sum(1 for tag in tags if tag.usage_count > 0)
                self.status_label.setText(
                    f"共 {len(self.tag_buttons)} 个标签 (已使用: {used_tags})"
                )
            else:
                self.status_label.setText("暂无标签")

        except Exception as e:
            self.status_label.setText(f"加载失败: {str(e)}")

    def clear_tags(self):
        """清除所有标签"""
        for tag_button in self.tag_buttons.values():
            tag_button.deleteLater()
        self.tag_buttons.clear()

    def on_tag_clicked(self, tag_id: int):
        """标签被点击"""
        if tag_id in self.selected_tags:
            self.selected_tags.remove(tag_id)
        else:
            self.selected_tags.add(tag_id)

        # 更新按钮状态
        for tid, button in self.tag_buttons.items():
            button.set_active(tid in self.selected_tags)

        # 发送筛选信号
        self.tag_filter_changed.emit(list(self.selected_tags))

    def on_tag_removed(self, tag_id: int):
        """标签被删除"""
        if self.tag_service.delete_tag(tag_id):
            self.load_tags()  # 重新加载标签
            # 如果删除的标签在选中列表中，需要更新筛选
            if tag_id in self.selected_tags:
                self.selected_tags.remove(tag_id)
                self.tag_filter_changed.emit(list(self.selected_tags))
            # QMessageBox.information(self, "成功", "标签删除成功！")  # 移除弹窗
        else:
            QMessageBox.warning(self, "错误", "删除标签失败！")

    def clear_selection(self):
        """清除选择"""
        self.selected_tags.clear()
        for button in self.tag_buttons.values():
            button.set_active(False)
        self.tag_filter_changed.emit([])

    def open_tag_manager(self):
        """打开标签管理器"""
        dialog = TagManagerDialog(self.tag_service, self)
        dialog.tag_updated.connect(self.load_tags)
        dialog.exec()

    def edit_tag(self, tag_id: int):
        """编辑标签"""
        dialog = TagManagerDialog(self.tag_service, self)
        dialog.tag_updated.connect(self.load_tags)
        dialog.exec()

    def refresh(self):
        """刷新标签"""
        self.load_tags()

    def set_selected_tags(self, tag_ids: list):
        """设置选中的标签"""
        self.selected_tags = set(tag_ids)
        for tid, button in self.tag_buttons.items():
            button.set_active(tid in self.selected_tags)

    def update_button_styles(self):
        """更新按钮样式，支持暗黑模式"""
        from config import get_current_theme

        current_theme = get_current_theme()

        if current_theme == "dark":
            # 暗黑模式样式
            button_style = """
                QPushButton {
                    background-color: #3d3d3d;
                    border: 1px solid #555;
                    border-radius: 4px;
                    font-size: 11px;
                    color: #ffffff;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #4d4d4d;
                    border-color: #666;
                }
                QPushButton:pressed {
                    background-color: #2d2d2d;
                }
            """
        else:
            # 明亮模式样式
            button_style = """
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    font-size: 11px;
                    color: #333333;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                    border-color: #bbb;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
            """

        self.manage_btn.setStyleSheet(button_style)
        self.clear_btn.setStyleSheet(button_style)

    def update_scroll_area_style(self):
        """更新滚动区域样式，支持暗黑模式"""
        from config import get_current_theme

        current_theme = get_current_theme()

        if current_theme == "dark":
            # 暗黑模式样式
            scroll_style = """
                QScrollArea {
                    border: 1px solid #404040;
                    border-radius: 8px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #2d2d2d,
                        stop: 1 #1e1e1e);
                }
                QScrollBar:vertical {
                    border: none;
                    background: #3d3d3d;
                    width: 8px;
                    border-radius: 4px;
                }
                QScrollBar::handle:vertical {
                    background: #666666;
                    border-radius: 4px;
                    min-height: 20px;
                }
                QScrollBar::handle:vertical:hover {
                    background: #777777;
                }
            """
        else:
            # 明亮模式样式
            scroll_style = """
                QScrollArea {
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #ffffff,
                        stop: 1 #f8f9fa);
                }
                QScrollBar:vertical {
                    border: none;
                    background: #f0f0f0;
                    width: 8px;
                    border-radius: 4px;
                }
                QScrollBar::handle:vertical {
                    background: #c0c0c0;
                    border-radius: 4px;
                    min-height: 20px;
                }
                QScrollBar::handle:vertical:hover {
                    background: #a0a0a0;
                }
            """

        self.scroll_area.setStyleSheet(scroll_style)

    def update_all_styles(self):
        """更新所有样式组件"""
        self.update_button_styles()
        self.update_scroll_area_style()


class FlowLayout(QHBoxLayout):
    """流式布局 - 简化版"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setContentsMargins(0, 0, 0, 0)
        self.setSpacing(3)

    def addWidget(self, widget):
        """添加组件"""
        super().addWidget(widget)

        # 简单的换行逻辑：如果当前行过长，不进行特殊处理
        # Qt6的QHBoxLayout会自动处理组件排列
        pass
