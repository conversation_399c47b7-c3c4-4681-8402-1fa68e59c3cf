"""
产品对比视图
显示多个产品的对比分析结果
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QMessageBox,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from models.database import ProductService


class ComparisonWidget(QWidget):
    """产品对比视图"""

    # 信号定义
    comparison_completed = pyqtSignal(list)  # 对比完成

    def __init__(self, product_service: ProductService):
        super().__init__()
        self.product_service = product_service
        self.comparison_data = []

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 标题行
        title_layout = QHBoxLayout()

        title_label = QLabel("产品对比分析")
        title_label.setStyleSheet("font-size: 20px; font-weight: bold;")
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 导出按钮
        from PyQt6.QtWidgets import QPushButton

        self.export_button = QPushButton("导出结果")
        self.export_button.setStyleSheet("font-size: 12px; padding: 8px 16px;")
        self.export_button.clicked.connect(self.export_comparison)
        self.export_button.setEnabled(False)
        title_layout.addWidget(self.export_button)

        layout.addLayout(title_layout)

        # 对比表格
        self.comparison_table = QTableWidget()
        self.comparison_table.setStyleSheet("font-size: 12px;")
        self.setup_table()
        layout.addWidget(self.comparison_table)

        # 洞察分析区域
        self.insights_label = QLabel()
        self.insights_label.setStyleSheet(
            "background: #f0f0f0; padding: 10px; border-radius: 4px; margin-top: 10px; font-size: 12px;"
        )
        self.insights_label.setWordWrap(True)
        self.insights_label.hide()
        layout.addWidget(self.insights_label)

        # 默认显示空状态
        self.show_empty_state()

    def setup_table(self):
        """设置表格"""
        # 设置表格列
        headers = [
            "产品名称",
            "产品编码",
            "售价",
            "最低成本",
            "最高成本",
            "平均成本",
            "利润",
            "利润率",
            "货源数量",
            "最优货源",
        ]

        self.comparison_table.setColumnCount(len(headers))
        self.comparison_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.comparison_table.setAlternatingRowColors(True)
        self.comparison_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.comparison_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # 自动调整列宽
        header = self.comparison_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setStretchLastSection(True)

    def show_empty_state(self):
        """显示空状态"""
        self.comparison_table.setRowCount(1)
        self.comparison_table.setColumnCount(1)

        empty_item = QTableWidgetItem("请从左侧列表选择至少两个产品进行对比")
        empty_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        empty_item.setFlags(Qt.ItemFlag.NoItemFlags)

        self.comparison_table.setItem(0, 0, empty_item)
        self.comparison_table.setHorizontalHeaderLabels([""])
        self.comparison_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )

    def load_comparison(self, product_ids: list):
        """加载产品对比"""
        try:
            if len(product_ids) < 2:
                QMessageBox.warning(self, "警告", "请至少选择两个产品进行对比")
                return

            # 获取对比数据
            self.comparison_data = self.product_service.compare_products(product_ids)

            # 显示对比结果
            self.display_comparison()

            # 发出完成信号
            self.comparison_completed.emit(self.comparison_data)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载产品对比失败: {str(e)}")
            self.show_empty_state()

    def display_comparison(self):
        """显示对比结果"""
        if not self.comparison_data:
            self.show_empty_state()
            return

        # 重新设置表格
        self.setup_table()

        # 设置行数
        self.comparison_table.setRowCount(len(self.comparison_data))

        # 填充数据
        for row, product_data in enumerate(self.comparison_data):
            self.set_table_item(row, 0, product_data["name"])
            self.set_table_item(row, 1, product_data["sku"])
            self.set_table_item(row, 2, f"¥{product_data['selling_price']:.2f}")
            self.set_table_item(row, 3, f"¥{product_data['lowest_cost']:.2f}")
            self.set_table_item(row, 4, f"¥{product_data['highest_cost']:.2f}")
            self.set_table_item(row, 5, f"¥{product_data['average_cost']:.2f}")

            # 利润显示（带颜色）
            profit = product_data["profit"]
            profit_item = QTableWidgetItem(f"¥{profit:.2f}")
            if profit > 0:
                profit_item.setBackground(QColor(144, 238, 144))  # lightgreen
            elif profit < 0:
                profit_item.setBackground(QColor(255, 182, 193))  # lightcoral
            self.comparison_table.setItem(row, 6, profit_item)

            # 利润率显示（带颜色）
            profit_margin = product_data["profit_margin"]
            margin_item = QTableWidgetItem(f"{profit_margin:.2f}%")
            if profit_margin > 20:
                margin_item.setBackground(QColor(144, 238, 144))  # lightgreen
            elif profit_margin < 10:
                margin_item.setBackground(QColor(255, 182, 193))  # lightcoral
            else:
                margin_item.setBackground(QColor(255, 255, 0))  # yellow
            self.comparison_table.setItem(row, 7, margin_item)

            self.set_table_item(row, 8, str(product_data["source_count"]))
            self.set_table_item(row, 9, product_data["best_source"])

        # 添加分析结果
        self.add_analysis_summary()

        # 显示洞察分析
        self.display_insights()

        # 启用导出按钮
        self.export_button.setEnabled(True)

    def set_table_item(self, row: int, col: int, text: str):
        """设置表格项"""
        item = QTableWidgetItem(str(text))
        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.comparison_table.setItem(row, col, item)

    def add_analysis_summary(self):
        """添加分析总结"""
        if not self.comparison_data:
            return

        # 找出最优产品
        best_profit_product = max(self.comparison_data, key=lambda p: p["profit"])
        best_margin_product = max(
            self.comparison_data, key=lambda p: p["profit_margin"]
        )
        lowest_cost_product = min(self.comparison_data, key=lambda p: p["lowest_cost"])

        # 在表格下方添加总结信息
        # 由于当前是简化版本，这里只在状态栏显示
        summary = (
            f"对比完成 | 最高利润: {best_profit_product['name']} (¥{best_profit_product['profit']:.2f}) | "
            f"最高利润率: {best_margin_product['name']} ({best_margin_product['profit_margin']:.2f}%) | "
            f"最低成本: {lowest_cost_product['name']} (¥{lowest_cost_product['lowest_cost']:.2f})"
        )

        # 这里可以添加一个标签来显示总结
        # 暂时通过窗口标题显示
        if hasattr(self.parent(), "setWindowTitle"):
            self.parent().setWindowTitle(f"产品对比 - {summary}")

    def clear_comparison(self):
        """清空对比数据"""
        self.comparison_data = []
        self.show_empty_state()
        self.insights_label.hide()
        self.export_button.setEnabled(False)

    def display_insights(self):
        """显示洞察分析"""
        insights = self.get_comparison_insights()
        if insights:
            insights_text = "📊 对比洞察分析:\n" + "\n".join(
                [f"• {insight}" for insight in insights]
            )
            self.insights_label.setText(insights_text)
            self.insights_label.show()
        else:
            self.insights_label.hide()

    def export_comparison(self):
        """导出对比结果"""
        if not self.comparison_data:
            QMessageBox.information(self, "提示", "暂无对比数据可导出")
            return

        try:
            from PyQt6.QtWidgets import QFileDialog
            import csv
            import os

            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出对比结果", "", "CSV文件 (*.csv);;所有文件 (*.*)"
            )

            if file_path:
                # 导出到CSV文件
                with open(file_path, "w", newline="", encoding="utf-8") as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入标题行
                    headers = [
                        "产品名称",
                        "产品编码",
                        "售价",
                        "最低成本",
                        "最高成本",
                        "平均成本",
                        "利润",
                        "利润率",
                        "货源数量",
                        "最优货源",
                    ]
                    writer.writerow(headers)

                    # 写入数据行
                    for product_data in self.comparison_data:
                        row = [
                            product_data["name"],
                            product_data["sku"],
                            f"{product_data['selling_price']:.2f}",
                            f"{product_data['lowest_cost']:.2f}",
                            f"{product_data['highest_cost']:.2f}",
                            f"{product_data['average_cost']:.2f}",
                            f"{product_data['profit']:.2f}",
                            f"{product_data['profit_margin']:.2f}%",
                            str(product_data["source_count"]),
                            product_data["best_source"],
                        ]
                        writer.writerow(row)

                # QMessageBox.information(self, "成功", f"对比结果已导出到:\n{file_path}")  # 移除弹窗

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def get_comparison_insights(self):
        """获取对比洞察"""
        if not self.comparison_data:
            return []

        insights = []

        # 分析利润分布
        profits = [p["profit"] for p in self.comparison_data]
        avg_profit = sum(profits) / len(profits)

        high_profit_products = [
            p for p in self.comparison_data if p["profit"] > avg_profit
        ]
        low_profit_products = [
            p for p in self.comparison_data if p["profit"] < avg_profit
        ]

        if high_profit_products:
            insights.append(
                f"高利润产品({len(high_profit_products)}个): {', '.join([p['name'] for p in high_profit_products])}"
            )

        if low_profit_products:
            insights.append(
                f"低利润产品({len(low_profit_products)}个): {', '.join([p['name'] for p in low_profit_products])}"
            )

        # 分析利润率分布
        margins = [p["profit_margin"] for p in self.comparison_data]
        avg_margin = sum(margins) / len(margins)

        insights.append(f"平均利润率: {avg_margin:.2f}%")

        # 分析货源数量
        source_counts = [p["source_count"] for p in self.comparison_data]
        avg_sources = sum(source_counts) / len(source_counts)

        insights.append(f"平均货源数量: {avg_sources:.1f}")

        # 风险分析
        single_source_products = [
            p for p in self.comparison_data if p["source_count"] == 1
        ]
        if single_source_products:
            insights.append(
                f"单一货源风险产品({len(single_source_products)}个): {', '.join([p['name'] for p in single_source_products])}"
            )

        return insights
