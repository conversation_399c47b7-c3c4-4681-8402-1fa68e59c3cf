/*
 * 浅色模式样式
 */

/* 全局样式重置 */
* {
    color: #000000;
}

/* 主窗口 */
QMainWindow {
    background-color: #f0f0f0;
    color: #000000;
}

/* 通用控件 */
QWidget {
    background-color: #f0f0f0;
    color: #000000;
    font-family: "Microsoft YaHei UI";
}

/* 按钮样式 */
QPushButton {
    background-color: #e0e0e0;
    color: #000000;
    border: 1px solid #cccccc;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #d0d0d0;
    border-color: #2196f3;
}

QPushButton:pressed {
    background-color: #c0c0c0;
}

QPushButton:disabled {
    background-color: #e5e5e5;
    color: #999999;
    border-color: #dddddd;
}

/* 工具栏 */
QToolBar {
    background-color: #e9e9e9;
    border: 1px solid #cccccc;
    padding: 4px;
    spacing: 4px;
}

QToolBar::separator {
    background-color: #cccccc;
    width: 1px;
    height: 16px;
    margin: 0 4px;
}

QToolBar QToolButton {
    background-color: #e9e9e9;
    color: #000000;
    border: 1px solid #cccccc;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    min-width: 80px;
}

QToolBar QToolButton:hover {
    background-color: #d9d9d9;
    border-color: #2196f3;
}

QToolBar QToolButton:pressed {
    background-color: #c9c9c9;
}

/* 菜单栏 */
QMenuBar {
    background-color: #e9e9e9;
    color: #000000;
    border-bottom: 1px solid #cccccc;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
}

QMenuBar::item:selected {
    background-color: #d9d9d9;
    border-radius: 4px;
}

QMenuBar::item:pressed {
    background-color: #c9c9c9;
}

/* 菜单 */
QMenu {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
    padding: 2px;
}

QMenu::item {
    background-color: transparent;
    padding: 6px 24px;
    border-radius: 4px;
    margin: 1px;
}

QMenu::item:selected {
    background-color: #e0e0e0;
}

QMenu::separator {
    height: 1px;
    background-color: #cccccc;
    margin: 4px 0;
}

/* 状态栏 */
QStatusBar {
    background-color: #e9e9e9;
    color: #000000;
    border-top: 1px solid #cccccc;
}

QStatusBar::item {
    border: none;
}

QStatusBar QLabel {
    color: #000000;
    padding: 2px;
}

/* 分割器 */
QSplitter {
    background-color: #f0f0f0;
}

QSplitter::handle {
    background-color: #cccccc;
    border: 1px solid #cccccc;
    width: 3px;
    height: 3px;
    border-radius: 1px;
}

QSplitter::handle:hover {
    background-color: #2196f3;
}

/* 标签页 */
QTabWidget::pane {
    background-color: #ffffff;
    border: 1px solid #cccccc;
}

QTabBar::tab {
    background-color: #e9e9e9;
    color: #000000;
    padding: 8px 16px;
    border: 1px solid #cccccc;
    border-bottom: none;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #ffffff;
}

QTabBar::tab:hover {
    background-color: #f0f0f0;
}

/* 列表控件 */
QListWidget {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
    alternate-background-color: #f7f7f7;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #eeeeee;
    color: #000000;
    background-color: transparent;
}

QListWidget::item:selected {
    background-color: #2196f3;
    color: #ffffff;
}

QListWidget::item:hover {
    background-color: #e0e0e0;
    color: #000000;
}

/* 表格控件 */
QTableWidget {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
    gridline-color: #e0e0e0;
    alternate-background-color: #f7f7f7;
}

QTableWidget::item {
    padding: 4px;
    border-bottom: 1px solid #eeeeee;
}

QTableWidget::item:selected {
    background-color: #2196f3;
    color: #ffffff;
}

QHeaderView::section {
    background-color: #e9e9e9;
    color: #000000;
    padding: 6px;
    border: 1px solid #dddddd;
    font-weight: bold;
}

/* 输入框 */
QLineEdit {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
    padding: 6px;
    border-radius: 4px;
    font-size: 12px;
}

QLineEdit:focus {
    border-color: #2196f3;
}

QLineEdit:disabled {
    background-color: #f5f5f5;
    color: #999999;
    border-color: #dddddd;
}

/* 文本框 */
QTextEdit {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
    padding: 6px;
    border-radius: 4px;
    font-size: 12px;
}

QTextEdit:focus {
    border-color: #2196f3;
}

/* SpinBox样式 */
QSpinBox, QDoubleSpinBox {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
}

QSpinBox:hover, QDoubleSpinBox:hover {
    background-color: #f8f9fa;
    border: 1px solid #a0a0a0;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    background-color: #f8f9fa;
    border: 1px solid #2196f3;
    outline: none;
}

QSpinBox:disabled, QDoubleSpinBox:disabled {
    background-color: #f5f5f5;
    color: #999999;
    border: 1px solid #dddddd;
}

/* SpinBox按钮样式 */
QSpinBox::up-button, QDoubleSpinBox::up-button,
QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #e0e0e0;
    border: none;
    border-radius: 2px;
    margin: 1px;
    width: 16px;
    height: 16px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #d0d0d0;
}

QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed,
QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
    background-color: #2196f3;
}

/* SpinBox箭头图标 */
QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
    width: 0;
    height: 0;
    border: 4px solid transparent;
    border-bottom: 6px solid #333333;
    margin: 0 2px;
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    width: 0;
    height: 0;
    border: 4px solid transparent;
    border-top: 6px solid #333333;
    margin: 0 2px;
}

QSpinBox::up-arrow:hover, QDoubleSpinBox::up-arrow:hover {
    border-bottom-color: #2196f3;
}

QSpinBox::down-arrow:hover, QDoubleSpinBox::down-arrow:hover {
    border-top-color: #2196f3;
}

/* 标签 */
QLabel {
    color: #000000;
    background-color: transparent;
}

/* 复选框 */
QCheckBox {
    color: #000000;
    background-color: transparent;
}

QCheckBox::indicator {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    width: 16px;
    height: 16px;
    border-radius: 2px;
}

QCheckBox::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
}

QCheckBox::indicator:checked:hover {
    background-color: #1976d2;
}

/* 单选按钮 */
QRadioButton {
    color: #000000;
    background-color: transparent;
}

QRadioButton::indicator {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    width: 16px;
    height: 16px;
    border-radius: 8px;
}

QRadioButton::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
}

/* 下拉框 */
QComboBox {
    background-color: #e0e0e0;
    color: #000000;
    border: 1px solid #cccccc;
    padding: 6px;
    border-radius: 4px;
    font-size: 12px;
}

QComboBox:hover {
    border-color: #2196f3;
}

QComboBox::drop-down {
    border: none;
    background-color: transparent;
}

QComboBox::down-arrow {
    image: url(:/icons/arrow_down_dark.png); /* 假设有暗色箭头图标 */
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
    selection-background-color: #2196f3;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #f0f0f0;
    width: 12px;
    border: none;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #f0f0f0;
    height: 12px;
    border: none;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #c0c0c0;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}

/* 进度条 */
QProgressBar {
    background-color: #e0e0e0;
    border: 1px solid #cccccc;
    border-radius: 4px;
    text-align: center;
    color: #000000;
}

QProgressBar::chunk {
    background-color: #2196f3;
    border-radius: 3px;
}

/* 对话框 */
QDialog {
    background-color: #f0f0f0;
    color: #000000;
}

QMessageBox {
    background-color: #f0f0f0;
    color: #000000;
}

QMessageBox QLabel {
    background-color: #f0f0f0;
    color: #000000;
}

QMessageBox QPushButton {
    background-color: #e0e0e0;
    color: #000000;
    border: 1px solid #cccccc;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    min-width: 80px;
}

QMessageBox QPushButton:hover {
    background-color: #d0d0d0;
    border-color: #2196f3;
}

QDialogButtonBox QPushButton {
    background-color: #e0e0e0;
    color: #000000;
    border: 1px solid #cccccc;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
} 