"""
图片查看器组件
提供图片轮播、预览功能
"""

import logging
from functools import wraps
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QScrollArea,
    QFrame,
    QListWidget,
    QListWidgetItem,
    QSplitter,
    QFileDialog,
    QMessageBox,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QFont, QIcon
import os
from pathlib import Path
from utils.image_utils import load_image_safely

# 配置日志记录器
logger = logging.getLogger(__name__)


def safe_ui_operation(func):
    """UI操作安全装饰器"""

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        try:
            # 检查基本UI组件
            if not hasattr(self, "image_frames") or not self.image_frames:
                logger.debug(f"{func.__name__}: UI组件未初始化")
                return None

            return func(self, *args, **kwargs)
        except Exception as e:
            logger.error(f"{func.__name__} 执行失败: {e}")
            return None

    return wrapper


class ImageViewer(QWidget):
    """图片查看器"""

    # 信号定义
    images_changed = pyqtSignal(list)  # 图片列表变化

    def __init__(self, image_paths: list = None, editable: bool = True):
        super().__init__()
        self.image_paths = image_paths or []
        self.current_index = 0
        self.editable = editable

        self.setup_ui()
        self.update_display()
        self.update_image_list()  # 确保图片列表正确显示

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        self.setObjectName("imageViewer")

        # 创建分割窗口
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setObjectName("imageViewerSplitter")

        # 左侧：图片列表
        if self.editable:
            list_widget = self.create_image_list()
            list_widget.setObjectName("imageListWidget")
            splitter.addWidget(list_widget)

        # 右侧：图片显示区域
        display_container = QWidget()
        display_container.setObjectName("imageDisplayContainer")
        display_layout = QVBoxLayout(display_container)
        display_layout.setContentsMargins(0, 0, 0, 0)

        # 图片显示区域
        display_widget = self.create_image_display()
        display_widget.setObjectName("imageDisplay")
        display_layout.addWidget(display_widget)

        # 控制按钮区域
        controls_widget = self.create_image_controls()
        controls_widget.setObjectName("imageControls")
        display_layout.addWidget(controls_widget)

        splitter.addWidget(display_container)

        # 设置分割比例
        if self.editable:
            splitter.setSizes([200, 500])

        layout.addWidget(splitter)

    def create_image_list(self):
        """创建图片列表"""
        widget = QWidget()
        widget.setObjectName("imageListContainer")
        layout = QVBoxLayout(widget)

        # 标题
        title_label = QLabel("图片列表")
        title_label.setObjectName("imageListTitle")
        title_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        layout.addWidget(title_label)

        # 图片列表
        self.image_list = QListWidget()
        self.image_list.setObjectName("imageList")
        self.image_list.currentRowChanged.connect(self.on_image_selected)
        layout.addWidget(self.image_list)

        # 操作按钮
        buttons_layout = QHBoxLayout()

        add_button = QPushButton("添加")
        add_button.setObjectName("addImageButton")
        add_button.clicked.connect(self.add_images)
        buttons_layout.addWidget(add_button)

        remove_button = QPushButton("删除")
        remove_button.setObjectName("removeImageButton")
        remove_button.clicked.connect(self.remove_image)
        buttons_layout.addWidget(remove_button)

        # 打开文件夹按钮
        open_folder_button = QPushButton("打开文件夹")
        open_folder_button.setObjectName("openFolderButton")
        open_folder_button.clicked.connect(self.open_image_folder)
        buttons_layout.addWidget(open_folder_button)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        return widget

    def create_image_display(self):
        """创建图片显示区域 - 支持同时显示三张图片"""
        widget = QWidget()
        widget.setObjectName("imageDisplayWidget")
        layout = QVBoxLayout(widget)

        # 主图片显示区域 - 水平布局支持三张图片
        self.display_container = QFrame()
        self.display_container.setObjectName("imageDisplayContainer")
        self.display_container.setMinimumHeight(380)  # 调整容器高度，为控制按钮留出空间

        # 三张图片的水平布局
        self.images_layout = QHBoxLayout(self.display_container)
        self.images_layout.setContentsMargins(10, 10, 10, 10)
        self.images_layout.setSpacing(10)

        # 创建三个图片位置
        self.image_labels = []
        self.image_frames = []

        for i in range(3):
            # 图片框架
            frame = QFrame()
            frame.setObjectName(f"imageFrame{i}")
            frame.setMinimumSize(220, 300)  # 增加图片框架尺寸

            frame_layout = QVBoxLayout(frame)
            frame_layout.setContentsMargins(5, 5, 5, 5)

            # 图片标签
            image_label = QLabel()
            image_label.setObjectName(f"imageLabel{i}")
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            frame_layout.addWidget(image_label)

            self.image_frames.append(frame)
            self.image_labels.append(image_label)
            self.images_layout.addWidget(frame)

        layout.addWidget(self.display_container)

        return widget

    def create_image_controls(self):
        """创建图片控制按钮区域"""
        widget = QWidget()
        widget.setObjectName("imageControlsWidget")
        controls_layout = QHBoxLayout(widget)
        controls_layout.setContentsMargins(0, 10, 0, 0)

        self.prev_button = QPushButton("◀ 上一组")
        self.prev_button.setObjectName("prevButton")
        self.prev_button.clicked.connect(self.previous_group)
        controls_layout.addWidget(self.prev_button)

        self.info_label = QLabel("暂无图片")
        self.info_label.setObjectName("infoLabel")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        controls_layout.addWidget(self.info_label)

        self.next_button = QPushButton("下一组 ▶")
        self.next_button.setObjectName("nextButton")
        self.next_button.clicked.connect(self.next_group)
        controls_layout.addWidget(self.next_button)

        return widget

    def set_images(self, image_paths: list):
        """设置图片列表"""
        self.image_paths = image_paths or []
        self.current_index = 0
        self.update_display()
        self.update_image_list()
        # 延迟更新显示，确保布局已经完成
        QTimer.singleShot(50, self.update_display)

    def get_display_layout(self):
        """根据图片数量获取显示布局"""
        image_count = len(self.image_paths)

        if image_count == 0:
            return []
        elif image_count == 1:
            # 1张图片：显示在中间位置
            return [None, 0, None]
        elif image_count == 2:
            # 2张图片：显示在位置1、2
            return [0, 1, None]
        else:
            # 3张及以上：显示连续的3张
            start_idx = self.current_index
            indices = []
            for i in range(3):
                idx = (start_idx + i) % image_count
                indices.append(idx)
            return indices

    def update_display(self):
        """更新显示"""
        # 安全性检查：确保UI组件已正确初始化
        if not hasattr(self, "image_frames") or not self.image_frames:
            print("图片查看器UI组件未初始化，跳过显示更新")
            return

        if not hasattr(self, "image_labels") or not self.image_labels:
            print("图片标签组件未初始化，跳过显示更新")
            return

        # 检查所有frame是否为None
        for i, frame in enumerate(self.image_frames):
            if frame is None:
                print(f"图片框架{i}为None，跳过显示更新")
                return

        # 检查所有label是否为None
        for i, label in enumerate(self.image_labels):
            if label is None:
                print(f"图片标签{i}为None，跳过显示更新")
                return

        # 先隐藏所有图片框架
        for frame in self.image_frames:
            if frame is not None:
                frame.hide()

        # 清空所有图片标签
        for label in self.image_labels:
            if label is not None:
                label.clear()
                label.setText("")

        if not self.image_paths:
            # 显示中间的框架，显示"暂无图片"
            if len(self.image_frames) > 1 and self.image_frames[1] is not None:
                self.image_frames[1].show()
                if len(self.image_labels) > 1 and self.image_labels[1] is not None:
                    self.image_labels[1].setText("暂无图片")

            # 安全地更新控件状态
            if hasattr(self, "info_label") and self.info_label is not None:
                self.info_label.setText("0 / 0")
            if hasattr(self, "prev_button") and self.prev_button is not None:
                self.prev_button.setEnabled(False)
            if hasattr(self, "next_button") and self.next_button is not None:
                self.next_button.setEnabled(False)
            return

        # 获取显示布局
        layout = self.get_display_layout()

        # 根据布局显示图片
        for pos, img_idx in enumerate(layout):
            if pos < len(self.image_frames) and self.image_frames[pos] is not None:
                if img_idx is not None:
                    self.image_frames[pos].show()
                    if (
                        pos < len(self.image_labels)
                        and self.image_labels[pos] is not None
                    ):
                        self.load_image_to_label(self.image_labels[pos], img_idx)
                else:
                    self.image_frames[pos].hide()

        # 更新信息
        if hasattr(self, "info_label") and self.info_label is not None:
            if len(self.image_paths) <= 3:
                self.info_label.setText(f"{len(self.image_paths)} 张图片")
            else:
                group_start = self.current_index + 1
                group_end = min(self.current_index + 3, len(self.image_paths))
                self.info_label.setText(
                    f"{group_start}-{group_end} / {len(self.image_paths)}"
                )

        # 更新按钮状态
        image_count = len(self.image_paths)
        if hasattr(self, "prev_button") and self.prev_button is not None:
            if image_count <= 3:
                # 图片少于等于3张，不需要翻页
                self.prev_button.setEnabled(False)
            else:
                # 图片超过3张，支持翻页
                self.prev_button.setEnabled(True)

        if hasattr(self, "next_button") and self.next_button is not None:
            if image_count <= 3:
                # 图片少于等于3张，不需要翻页
                self.next_button.setEnabled(False)
            else:
                # 图片超过3张，支持翻页
                self.next_button.setEnabled(True)

    def load_image_to_label(self, label, img_idx):
        """加载图片到标签 - 优化加载过程避免变形"""
        if img_idx >= len(self.image_paths):
            label.setText("图片不存在")
            return

        image_path = self.image_paths[img_idx]

        # 预先清空标签，避免显示之前的图片
        label.clear()

        # 显示加载提示
        label.setText("加载中...")

        # 使用定时器延迟加载图片，避免阻塞界面
        def load_image():
            # 检查标签是否仍然有效
            try:
                if not label or not hasattr(label, "size"):
                    return

                # 使用统一的图片加载函数
                pixmap = load_image_safely(image_path)

                if pixmap.isNull():
                    if hasattr(label, "setText"):  # 再次检查标签是否有效
                        label.setText(f"图片加载失败")
                    # 只在DEBUG级别记录错误，避免控制台输出过多信息
                    import logging

                    logging.debug(f"图片加载失败: {image_path}")
                else:
                    # 获取目标尺寸 - 更大的尺寸以适应新的框架
                    target_width = 200
                    target_height = 280

                    # 获取标签实际尺寸
                    try:
                        label_size = label.size()
                        if label_size.width() > 50 and label_size.height() > 50:
                            # 计算合适的尺寸，保持比例，尽可能填充可用空间
                            available_width = label_size.width() - 10
                            available_height = label_size.height() - 10

                            # 使用可用空间的更大值作为目标
                            target_width = max(available_width, target_width)
                            target_height = max(available_height, target_height)
                    except RuntimeError:
                        # 如果标签已被删除，直接返回
                        return

                    # 缩放图片，尽可能填充空间
                    scaled_pixmap = pixmap.scaled(
                        target_width,
                        target_height,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation,
                    )

                    # 设置图片
                    try:
                        if hasattr(label, "setPixmap"):  # 确保标签仍然有效
                            label.setPixmap(scaled_pixmap)
                    except RuntimeError:
                        # 如果标签已被删除，忽略错误
                        pass

            except RuntimeError:
                # 如果标签已被删除，忽略错误
                pass

        # 延迟50毫秒加载，避免界面冻结
        QTimer.singleShot(50, load_image)

    def update_image_list(self):
        """更新图片列表"""
        if not self.editable:
            print("图片查看器不可编辑，跳过列表更新")
            return

        print(f"更新图片列表，共有 {len(self.image_paths)} 张图片")

        self.image_list.clear()
        for i, path in enumerate(self.image_paths):
            filename = os.path.basename(path)
            print(f"添加图片到列表: {i+1}. {filename}")
            item = QListWidgetItem(filename)
            item.setData(Qt.ItemDataRole.UserRole, path)
            self.image_list.addItem(item)

        # 选中当前图片
        if self.image_paths and self.current_index < len(self.image_paths):
            self.image_list.setCurrentRow(self.current_index)
            print(f"选中图片: {self.current_index}")

        print(f"图片列表更新完成，列表项数量: {self.image_list.count()}")

    def on_image_selected(self, row):
        """图片选择改变"""
        if 0 <= row < len(self.image_paths):
            # 如果超过3张图片，调整到包含所选图片的组
            if len(self.image_paths) > 3:
                # 计算应该显示的组起始位置
                self.current_index = (row // 3) * 3
            else:
                self.current_index = row
            self.update_display()

    def previous_group(self):
        """上一组图片"""
        if len(self.image_paths) > 3:
            self.current_index = max(0, self.current_index - 3)
            self.update_display()
            if self.editable:
                self.image_list.setCurrentRow(self.current_index)

    def next_group(self):
        """下一组图片"""
        if len(self.image_paths) > 3:
            self.current_index = min(len(self.image_paths) - 3, self.current_index + 3)
            self.update_display()
            if self.editable:
                self.image_list.setCurrentRow(self.current_index)

    def add_images(self):
        """添加图片"""
        if not self.editable:
            return

        from config import IMAGE_CONFIG

        # 构建文件过滤器
        filters = []
        for fmt in IMAGE_CONFIG["supported_formats"]:
            filters.append(f"*{fmt}")
        filter_string = f"图片文件 ({' '.join(filters)});;所有文件 (*.*)"

        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择图片文件", "", filter_string
        )

        if file_paths:
            # 添加到列表中，避免重复
            for file_path in file_paths:
                if file_path not in self.image_paths:
                    self.image_paths.append(file_path)

            self.update_display()
            self.update_image_list()
            self.images_changed.emit(self.image_paths)

    def remove_image(self):
        """删除图片"""
        if not self.editable or not self.image_paths:
            return

        current_row = self.image_list.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self,
                "确认删除",
                "确定要从列表中删除这张图片吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 删除图片
                del self.image_paths[current_row]

                # 调整当前索引
                if self.current_index >= len(self.image_paths):
                    self.current_index = len(self.image_paths) - 1
                if self.current_index < 0:
                    self.current_index = 0

                self.update_display()
                self.update_image_list()
                self.images_changed.emit(self.image_paths)

    def get_images(self):
        """获取图片路径列表"""
        return self.image_paths.copy()

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 延迟重新缩放图片，确保布局已经完成
        QTimer.singleShot(100, self.update_display)

    def open_image_folder(self):
        """打开图片所在的文件夹"""
        if not self.image_paths:
            QMessageBox.information(self, "提示", "当前没有图片文件")
            return

        # 获取当前选中的图片路径
        current_image_path = None

        # 如果有选中的图片，使用选中的图片
        if self.editable and hasattr(self, "image_list"):
            current_row = self.image_list.currentRow()
            if 0 <= current_row < len(self.image_paths):
                current_image_path = self.image_paths[current_row]

        # 如果没有选中图片，使用当前显示的图片
        if not current_image_path:
            current_image_path = self.image_paths[self.current_index]

        # 确保路径是绝对路径
        if not Path(current_image_path).is_absolute():
            current_image_path = str(Path.cwd() / current_image_path)

        # 检查文件是否存在
        if not os.path.exists(current_image_path):
            QMessageBox.warning(self, "错误", f"图片文件不存在：\n{current_image_path}")
            return

        # 获取文件夹路径
        folder_path = os.path.dirname(current_image_path)

        try:
            # 跨平台打开文件夹
            import platform
            import subprocess

            system = platform.system()
            if system == "Windows":
                # Windows系统使用explorer并选中文件
                subprocess.run(["explorer", "/select,", current_image_path])
            elif system == "Darwin":  # macOS
                # macOS系统使用Finder
                subprocess.run(["open", "-R", current_image_path])
            elif system == "Linux":
                # Linux系统使用文件管理器
                subprocess.run(["xdg-open", folder_path])
            else:
                # 其他系统，尝试使用默认方法
                import webbrowser

                webbrowser.open(folder_path)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开文件夹：\n{str(e)}")
            print(f"打开文件夹失败: {e}")

            # 备用方案：仅打开文件夹
            try:
                import webbrowser

                webbrowser.open(folder_path)
            except Exception as e2:
                QMessageBox.critical(self, "错误", f"无法打开文件夹：\n{str(e2)}")
                print(f"备用方案也失败: {e2}")
