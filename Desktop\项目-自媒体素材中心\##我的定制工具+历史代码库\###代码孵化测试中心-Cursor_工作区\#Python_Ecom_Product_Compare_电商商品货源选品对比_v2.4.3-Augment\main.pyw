#!/usr/bin/env python3
"""
电商产品对比选品工具 - 应用程序入口
基于PyQt6 + SQLite的简洁架构设计
"""
import sys
import os
import logging
import logging.handlers
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt, QCoreApplication
from PyQt6.QtGui import QFont, QIcon, QPixmap

# 抑制PNG颜色配置文件警告
os.environ["PYTHONWARNINGS"] = "ignore"
# 抑制libpng的iCCP警告
os.environ["LIBPNG_DISABLE_ICC_WARNINGS"] = "1"

# 设置PyQt6的图片加载配置
os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.xcb=false;qt.qpa.xcb.xkb=false"

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from models.database import DatabaseManager
from views.main_window import MainWindow
from config import (
    APP_NAME,
    APP_VERSION,
    STYLE_CONFIG,
    LOG_CONFIG,
    ensure_directories,
    get_current_theme,
    UI_CONFIG,
)


def setup_logging():
    """设置日志系统"""
    # 确保日志目录存在
    log_dir = LOG_CONFIG["file_path"].parent
    log_dir.mkdir(parents=True, exist_ok=True)

    # 创建根日志记录器
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, LOG_CONFIG["level"]))

    # 清除现有的处理器
    logger.handlers.clear()

    # 创建格式化器
    formatter = logging.Formatter(
        LOG_CONFIG["format"], datefmt=LOG_CONFIG["date_format"]
    )

    # 控制台输出
    if LOG_CONFIG["console_output"]:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, LOG_CONFIG["level"]))
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    # 文件输出
    if LOG_CONFIG["file_output"]:
        file_handler = logging.handlers.RotatingFileHandler(
            LOG_CONFIG["file_path"],
            maxBytes=LOG_CONFIG["max_file_size"],
            backupCount=LOG_CONFIG["backup_count"],
            encoding="utf-8",
        )
        file_handler.setLevel(getattr(logging, LOG_CONFIG["level"]))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    # 记录日志系统启动信息
    logger.info(f"日志系统已启动 - 级别: {LOG_CONFIG['level']}")
    logger.info(f"日志文件路径: {LOG_CONFIG['file_path']}")

    return logger


def setup_application():
    """设置应用程序"""
    # 创建应用实例（PyQt6中高DPI支持默认启用）
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    app.setOrganizationName("开发者")
    app.setOrganizationDomain("localhost")

    # 设置字体
    font = QFont(STYLE_CONFIG["font_family"], STYLE_CONFIG["font_size"])
    app.setFont(font)

    # 设置应用样式
    setup_application_style(app)

    return app


def setup_application_style(app):
    """设置应用程序样式"""
    # 根据当前主题加载样式
    current_theme = get_current_theme()
    if current_theme == "dark":
        style_path = os.path.join(
            os.path.dirname(__file__), "resources", "styles", "dark_theme.qss"
        )
    else:
        style_path = os.path.join(
            os.path.dirname(__file__), "resources", "styles", "light_theme.qss"
        )

    # 加载样式文件
    if os.path.exists(style_path):
        with open(style_path, "r", encoding="utf-8") as f:
            app.setStyleSheet(f.read())
    else:
        # 如果样式文件不存在，使用默认样式
        app.setStyleSheet("")


def initialize_database():
    """初始化数据库"""
    try:
        # 确保目录存在
        ensure_directories()

        # 创建数据库管理器
        db_manager = DatabaseManager()

        return db_manager
    except Exception as e:
        QMessageBox.critical(
            None, "数据库错误", f"数据库初始化失败：\n{str(e)}\n\n请检查数据目录权限。"
        )
        return None


def load_image_safely(
    image_path: str, default_text: str = "无法加载图片", max_retries: int = 1
) -> QPixmap:
    """
    安全地加载图片，避免重复的错误输出

    Args:
        image_path: 图片路径
        default_text: 默认显示文本
        max_retries: 最大重试次数

    Returns:
        QPixmap对象，如果加载失败返回空的QPixmap
    """
    try:
        # 检查文件是否存在
        if not Path(image_path).exists():
            return QPixmap()

        # 尝试加载图片
        for attempt in range(max_retries + 1):
            try:
                pixmap = QPixmap(str(image_path))
                if not pixmap.isNull():
                    return pixmap

                # 如果加载失败且是第一次尝试，等待一小段时间再试
                if attempt < max_retries:
                    QCoreApplication.processEvents()
                    continue

            except Exception as e:
                if attempt == max_retries:
                    # 只在最后一次尝试失败时记录错误
                    logging.debug(
                        f"图片加载失败 ({attempt + 1}/{max_retries + 1}): {image_path} - {str(e)}"
                    )
                continue

        return QPixmap()

    except Exception as e:
        logging.debug(f"图片加载异常: {image_path} - {str(e)}")
        return QPixmap()


def main():
    """主函数"""
    try:
        # 初始化日志系统
        logger = setup_logging()
        logger.info(f"程序启动 - {APP_NAME} v{APP_VERSION}")

        # 设置应用程序
        app = setup_application()
        logger.info("应用程序初始化完成")

        # 初始化数据库
        db_manager = initialize_database()
        if not db_manager:
            logger.error("数据库初始化失败")
            sys.exit(1)
        logger.info(f"数据库初始化成功 - 路径: {db_manager.db_path}")

        # 创建主窗口
        main_window = MainWindow(db_manager)
        logger.info("主窗口创建完成")

        # 显示主窗口
        main_window.show()

        # 居中显示
        screen_geometry = app.primaryScreen().geometry()
        window_geometry = main_window.geometry()
        x = (screen_geometry.width() - window_geometry.width()) // 2
        y = (screen_geometry.height() - window_geometry.height()) // 2
        main_window.move(x, y)

        logger.info(f"=== {APP_NAME} v{APP_VERSION} 启动成功 ===")
        logger.info(f"数据库路径: {db_manager.db_path}")
        logger.info("应用程序正在运行...")

        # 运行应用程序
        exit_code = app.exec()

        logger.info(f"应用程序退出，返回值: {exit_code}")
        return exit_code

    except Exception as e:
        error_msg = f"应用程序启动失败: {str(e)}"
        print(error_msg)

        # 记录错误到日志
        if "logger" in locals():
            logger.error(error_msg)
            logger.error("详细错误信息:", exc_info=True)

        import traceback

        traceback.print_exc()

        # 显示错误对话框
        if "app" in locals():
            QMessageBox.critical(
                None,
                "启动错误",
                f"应用程序启动失败：\n{str(e)}\n\n请查看控制台输出获取详细信息。",
            )

        return 1


if __name__ == "__main__":
    sys.exit(main())
