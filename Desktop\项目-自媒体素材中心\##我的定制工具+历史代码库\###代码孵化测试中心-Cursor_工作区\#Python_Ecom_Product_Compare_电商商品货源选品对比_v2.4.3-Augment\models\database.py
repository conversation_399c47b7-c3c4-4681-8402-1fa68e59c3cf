"""
数据库管理器 - 简化版
使用SQLite进行数据持久化，避免ORM复杂性
"""

import sqlite3
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path
import shutil
import os
import re

from .product import Product
from .source import Source
from .custom_field import CustomField
from .tag import Tag, ProductTag


class DatabaseManager:
    """数据库管理器 - 简化版"""

    def __init__(self, db_path: str = "data/database.db"):
        self.db_path = db_path
        self.connection = None
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        # 确保数据目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)

        self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
        self.connection.row_factory = sqlite3.Row  # 支持字典式访问
        self.create_tables()

    def create_tables(self):
        """创建数据表"""
        cursor = self.connection.cursor()

        # 产品表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                sku TEXT,
                selling_price REAL NOT NULL,
                shipping_fee REAL DEFAULT 0.0,  -- 运费
                free_shipping BOOLEAN DEFAULT 1,  -- 包邮选项，1=包邮，0=不包邮
                description TEXT DEFAULT '',  -- 商品描述
                sales_reference INTEGER DEFAULT 0,  -- 销量参考
                images TEXT,  -- JSON格式存储图片路径列表
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # 货源表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS sources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                url TEXT,
                shop_info TEXT,
                quantity INTEGER DEFAULT 1,
                shipping_cost REAL DEFAULT 0.0,
                source_mode TEXT DEFAULT 'wholesale',
                contact_info TEXT,
                notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
            )
        """
        )

        # 检查并添加新字段（用于数据库升级）
        self._add_missing_columns(cursor)

        # 自定义字段表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS custom_fields (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                value TEXT,
                field_type TEXT DEFAULT 'text',
                display_name TEXT,
                description TEXT,
                is_required BOOLEAN DEFAULT 0,
                is_visible BOOLEAN DEFAULT 1,
                order_index INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
            )
        """
        )

        # 标签表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS tags (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                color TEXT DEFAULT '#1976d2',
                description TEXT DEFAULT '',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        # 产品标签关联表
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS product_tags (
                product_id INTEGER NOT NULL,
                tag_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (product_id, tag_id),
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
                FOREIGN KEY (tag_id) REFERENCES tags (id) ON DELETE CASCADE
            )
        """
        )

        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)")
        cursor.execute(
            "CREATE INDEX IF NOT EXISTS idx_sources_product_id ON sources(product_id)"
        )
        cursor.execute(
            "CREATE INDEX IF NOT EXISTS idx_custom_fields_product_id ON custom_fields(product_id)"
        )
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name)")
        cursor.execute(
            "CREATE INDEX IF NOT EXISTS idx_product_tags_product_id ON product_tags(product_id)"
        )
        cursor.execute(
            "CREATE INDEX IF NOT EXISTS idx_product_tags_tag_id ON product_tags(tag_id)"
        )

        self.connection.commit()

    def _add_missing_columns(self, cursor):
        """添加缺失的列（用于数据库升级）"""
        try:
            # 检查products表是否存在reference_url列
            cursor.execute("PRAGMA table_info(products)")
            product_columns = [column[1] for column in cursor.fetchall()]

            if "reference_url" not in product_columns:
                cursor.execute(
                    "ALTER TABLE products ADD COLUMN reference_url TEXT DEFAULT ''"
                )
                print("已添加 reference_url 列到 products 表")

            if "shipping_fee" not in product_columns:
                cursor.execute(
                    "ALTER TABLE products ADD COLUMN shipping_fee REAL DEFAULT 0.0"
                )
                print("已添加 shipping_fee 列到 products 表")

            if "free_shipping" not in product_columns:
                cursor.execute(
                    "ALTER TABLE products ADD COLUMN free_shipping BOOLEAN DEFAULT 1"
                )
                print("已添加 free_shipping 列到 products 表")

            # 检查sources表是否存在shipping_cost列
            cursor.execute("PRAGMA table_info(sources)")
            columns = [column[1] for column in cursor.fetchall()]

            if "shipping_cost" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN shipping_cost REAL DEFAULT 0.0"
                )
                print("已添加 shipping_cost 列到 sources 表")

            if "source_mode" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN source_mode TEXT DEFAULT 'wholesale'"
                )
                print("已添加 source_mode 列到 sources 表")

            # 新增字段
            if "min_order_quantity" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN min_order_quantity INTEGER DEFAULT 1"
                )
                print("已添加 min_order_quantity 列到 sources 表")

            if "shipping_cost_suffix" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN shipping_cost_suffix TEXT DEFAULT ''"
                )
                print("已添加 shipping_cost_suffix 列到 sources 表")

            if "shipping_location" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN shipping_location TEXT DEFAULT ''"
                )
                print("已添加 shipping_location 列到 sources 表")

            if "product_name" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN product_name TEXT DEFAULT ''"
                )
                print("已添加 product_name 列到 sources 表")

            if "sales_count" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN sales_count INTEGER DEFAULT 0"
                )
                print("已添加 sales_count 列到 sources 表")

            if "status" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN status TEXT DEFAULT 'active'"
                )
                print("已添加 status 列到 sources 表")

            if "return_policy" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN return_policy TEXT DEFAULT ''"
                )
                print("已添加 return_policy 列到 sources 表")

            if "pickup_rate_24h" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN pickup_rate_24h REAL DEFAULT 0.0"
                )
                print("已添加 pickup_rate_24h 列到 sources 表")

            if "pickup_rate_48h" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN pickup_rate_48h REAL DEFAULT 0.0"
                )
                print("已添加 pickup_rate_48h 列到 sources 表")

            if "monthly_dropship_orders" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN monthly_dropship_orders INTEGER DEFAULT 0"
                )
                print("已添加 monthly_dropship_orders 列到 sources 表")

            if "downstream_stores" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN downstream_stores INTEGER DEFAULT 0"
                )
                print("已添加 downstream_stores 列到 sources 表")

            if "distributor_count" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN distributor_count INTEGER DEFAULT 0"
                )
                print("已添加 distributor_count 列到 sources 表")

            if "product_publish_time" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN product_publish_time TIMESTAMP"
                )
                print("已添加 product_publish_time 列到 sources 表")

            if "available_products" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN available_products INTEGER DEFAULT 0"
                )
                print("已添加 available_products 列到 sources 表")

            if "monthly_new_products" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN monthly_new_products INTEGER DEFAULT 0"
                )
                print("已添加 monthly_new_products 列到 sources 表")

            if "modes" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN modes TEXT DEFAULT 'wholesale'"
                )
                print("已添加 modes 列到 sources 表")

            if "image_urls" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN image_urls TEXT DEFAULT '[]'"
                )
                print("已添加 image_urls 列到 sources 表")

            # 批发模式专属字段
            if "wholesale_min_order_quantity" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN wholesale_min_order_quantity INTEGER DEFAULT 1"
                )
                print("已添加 wholesale_min_order_quantity 列到 sources 表")

            if "wholesale_payment_terms" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN wholesale_payment_terms INTEGER DEFAULT 0"
                )
                print("已添加 wholesale_payment_terms 列到 sources 表")

            if "wholesale_rebate_rate" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN wholesale_rebate_rate REAL DEFAULT 0.0"
                )
                print("已添加 wholesale_rebate_rate 列到 sources 表")

            if "wholesale_price_tiers" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN wholesale_price_tiers TEXT DEFAULT ''"
                )
                print("已添加 wholesale_price_tiers 列到 sources 表")

            # 代发模式专属字段
            if "dropship_service_fee" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN dropship_service_fee REAL DEFAULT 0.0"
                )
                print("已添加 dropship_service_fee 列到 sources 表")

            if "dropship_processing_time" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN dropship_processing_time INTEGER DEFAULT 0"
                )
                print("已添加 dropship_processing_time 列到 sources 表")

            if "dropship_packaging" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN dropship_packaging TEXT DEFAULT ''"
                )
                print("已添加 dropship_packaging 列到 sources 表")

            if "dropship_inventory_sync" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN dropship_inventory_sync TEXT DEFAULT '实时同步'"
                )
                print("已添加 dropship_inventory_sync 列到 sources 表")

            if "dropship_min_quantity" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN dropship_min_quantity INTEGER DEFAULT 1"
                )
                print("已添加 dropship_min_quantity 列到 sources 表")

            if "dropship_shipping_location" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN dropship_shipping_location TEXT DEFAULT ''"
                )
                print("已添加 dropship_shipping_location 列到 sources 表")

            if "dropship_support_regions" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN dropship_support_regions TEXT DEFAULT ''"
                )
                print("已添加 dropship_support_regions 列到 sources 表")

            # 检查products表是否存在新字段
            cursor.execute("PRAGMA table_info(products)")
            product_columns = [column[1] for column in cursor.fetchall()]

            if "description" not in product_columns:
                cursor.execute(
                    "ALTER TABLE products ADD COLUMN description TEXT DEFAULT ''"
                )
                print("已添加 description 列到 products 表")

            if "sales_reference" not in product_columns:
                cursor.execute(
                    "ALTER TABLE products ADD COLUMN sales_reference INTEGER DEFAULT 0"
                )
                print("已添加 sales_reference 列到 products 表")

            if "featured_images" not in product_columns:
                cursor.execute(
                    "ALTER TABLE products ADD COLUMN featured_images TEXT DEFAULT '[]'"
                )
                print("已添加 featured_images 列到 products 表")

            # 添加货源表的主要图片字段
            if "featured_images" not in columns:
                cursor.execute(
                    "ALTER TABLE sources ADD COLUMN featured_images TEXT DEFAULT '[]'"
                )
                print("已添加 featured_images 列到 sources 表")

        except Exception as e:
            print(f"数据库升级失败：{e}")

    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class ProductService:
    """产品服务 - 业务逻辑层"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    # ===== 图片管理相关方法 =====

    def _get_product_images_dir(self, product_id: int) -> Path:
        """获取产品图片目录"""
        from config import IMAGES_DIR

        product_dir = Path(IMAGES_DIR) / "products" / f"product_{product_id}"
        product_dir.mkdir(parents=True, exist_ok=True)
        return product_dir

    def _get_source_images_dir(self, source_id: int) -> Path:
        """获取货源图片目录"""
        from config import IMAGES_DIR

        source_dir = Path(IMAGES_DIR) / "sources" / f"source_{source_id}"
        source_dir.mkdir(parents=True, exist_ok=True)
        return source_dir

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除特殊字符"""
        # 移除或替换特殊字符
        filename = re.sub(r'[<>:"/\\|?*]', "_", filename)
        # 移除多余的空格和点
        filename = re.sub(r"\s+", " ", filename).strip()
        filename = filename.replace("..", "_")
        return filename

    def _generate_image_filename(
        self, product_name: str, index: int, original_path: str
    ) -> str:
        """生成新的图片文件名"""
        # 获取原文件扩展名
        original_ext = Path(original_path).suffix.lower()

        # 清理产品名称
        clean_name = self._sanitize_filename(product_name)

        # 生成新文件名: 产品名_序号.扩展名
        new_filename = f"{clean_name}_{index:03d}{original_ext}"

        return new_filename

    def _find_next_available_index(self, product_dir: Path, product_name: str) -> int:
        """寻找下一个可用的图片编号（智能补位）"""
        # 获取产品目录中所有文件
        if not product_dir.exists():
            return 1

        existing_files = list(product_dir.glob("*"))
        if not existing_files:
            return 1

        # 清理产品名称，用于匹配文件名
        clean_name = self._sanitize_filename(product_name)

        # 提取现有文件的编号
        used_indices = set()
        for file_path in existing_files:
            filename = file_path.name
            # 检查文件名是否匹配产品名称模式
            if filename.startswith(clean_name + "_"):
                # 提取编号部分
                name_part = filename[len(clean_name) + 1 :]  # 去掉产品名和下划线
                if name_part:
                    # 找到文件扩展名的位置
                    dot_index = name_part.rfind(".")
                    if dot_index > 0:
                        number_part = name_part[:dot_index]
                        try:
                            index = int(number_part)
                            used_indices.add(index)
                        except ValueError:
                            continue

        # 寻找最小可用编号（从1开始）
        index = 1
        while index in used_indices:
            index += 1

        return index

    def _copy_images_to_product_dir(
        self, product_id: int, product_name: str, image_paths: List[str]
    ) -> List[str]:
        """将图片复制到产品目录并返回新的相对路径列表"""
        if not image_paths:
            return []

        product_dir = self._get_product_images_dir(product_id)
        copied_paths = []

        for original_path in image_paths:
            try:
                original_file = Path(original_path)

                # 检查原文件是否存在
                if not original_file.exists():
                    print(f"警告：原图片文件不存在：{original_path}")
                    continue

                # 寻找下一个可用的编号（智能补位）
                available_index = self._find_next_available_index(
                    product_dir, product_name
                )
                new_filename = self._generate_image_filename(
                    product_name, available_index, original_path
                )
                new_file_path = product_dir / new_filename

                # 确保文件名唯一，如果存在则递增序号
                while new_file_path.exists():
                    available_index += 1
                    new_filename = self._generate_image_filename(
                        product_name, available_index, original_path
                    )
                    new_file_path = product_dir / new_filename

                # 复制文件
                shutil.copy2(original_file, new_file_path)

                # 存储相对路径（使用正斜杠，跨平台兼容）
                relative_path = str(new_file_path.relative_to(Path.cwd())).replace(
                    "\\", "/"
                )
                copied_paths.append(relative_path)

                print(f"图片已复制：{original_path} -> {new_file_path}")

            except Exception as e:
                print(f"复制图片失败：{original_path}，错误：{e}")
                continue

        return copied_paths

    def _cleanup_product_images(self, product_id: int):
        """清理产品的所有图片文件"""
        try:
            product_dir = self._get_product_images_dir(product_id)
            if product_dir.exists():
                shutil.rmtree(product_dir)
                print(f"已清理产品 {product_id} 的图片目录")
        except Exception as e:
            print(f"清理图片目录失败：{e}")

    def _update_product_images(
        self,
        product_id: int,
        product_name: str,
        new_image_paths: List[str],
        replace_all: bool = True,
    ) -> List[str]:
        """更新产品图片：支持累积添加或完全替换"""
        if replace_all:
            # 替换模式：清理旧图片，复制新图片
            self._cleanup_product_images(product_id)
            return self._copy_images_to_product_dir(
                product_id, product_name, new_image_paths
            )
        else:
            # 累积模式：只复制新图片，不清理旧图片
            return self._copy_images_to_product_dir(
                product_id, product_name, new_image_paths
            )

    def update_product_images(
        self, product_id: int, image_paths: List[str], replace_all: bool = True
    ) -> List[str]:
        """
        更新产品图片（公有方法）

        Args:
            product_id: 产品ID
            image_paths: 新图片路径列表（应该是绝对路径）
            replace_all: 是否替换所有图片（True）还是累积添加（False）

        Returns:
            最终的图片相对路径列表
        """
        # 获取产品信息
        product = self.get_product(product_id)
        if not product:
            return []

        if replace_all:
            # 替换模式：清理旧图片，复制新图片
            self._cleanup_product_images(product_id)
            final_images = self._copy_images_to_product_dir(
                product_id, product.name, image_paths
            )
        else:
            # 累积模式：只复制新图片，合并现有图片
            existing_images = product.images or []

            # 只复制新图片（绝对路径）
            new_copied_images = self._copy_images_to_product_dir(
                product_id, product.name, image_paths
            )

            # 合并现有图片（相对路径）和新图片（相对路径）
            final_images = existing_images + new_copied_images

        # 更新数据库中的图片信息
        cursor = self.db.connection.cursor()
        cursor.execute(
            "UPDATE products SET images = ? WHERE id = ?",
            (json.dumps(final_images), product_id),
        )
        self.db.connection.commit()

        print(f"图片更新完成，最终图片数量: {len(final_images)}")
        return final_images

    # ===== 货源图片管理方法 =====

    def _copy_images_to_source_dir(
        self, source_id: int, source_name: str, image_paths: List[str]
    ) -> List[str]:
        """
        复制图片到货源目录

        Args:
            source_id: 货源ID
            source_name: 货源名称
            image_paths: 原图片路径列表

        Returns:
            复制后的图片路径列表
        """
        if not image_paths:
            return []

        source_dir = self._get_source_images_dir(source_id)
        copied_paths = []

        for image_path in image_paths:
            try:
                if not image_path:
                    continue

                source_path = Path(image_path)
                if not source_path.exists():
                    print(f"图片文件不存在: {image_path}")
                    continue

                # 寻找下一个可用的编号（智能补位）
                available_index = self._find_next_available_index(
                    source_dir, source_name
                )
                new_filename = self._generate_image_filename(
                    source_name, available_index, str(source_path)
                )
                dest_path = source_dir / new_filename

                # 确保文件名唯一，如果存在则递增序号
                while dest_path.exists():
                    available_index += 1
                    new_filename = self._generate_image_filename(
                        source_name, available_index, str(source_path)
                    )
                    dest_path = source_dir / new_filename

                # 复制文件
                import shutil

                shutil.copy2(source_path, dest_path)

                # 存储相对路径（使用正斜杠，跨平台兼容）
                relative_path = str(dest_path.relative_to(Path.cwd())).replace(
                    "\\", "/"
                )
                copied_paths.append(relative_path)

                print(f"图片已复制: {source_path} -> {dest_path}")

            except Exception as e:
                print(f"复制图片失败: {image_path}, 错误: {e}")

        return copied_paths

    def _cleanup_source_images(self, source_id: int):
        """清理货源图片目录"""
        source_dir = self._get_source_images_dir(source_id)
        if source_dir.exists():
            try:
                import shutil

                shutil.rmtree(source_dir)
                print(f"已清理货源图片目录: {source_dir}")
            except Exception as e:
                print(f"清理货源图片目录失败: {e}")

    def _update_source_images(
        self,
        source_id: int,
        source_name: str,
        new_image_paths: List[str],
        replace_all: bool = True,
    ) -> List[str]:
        """更新货源图片：支持累积添加或完全替换"""
        if replace_all:
            # 替换模式：清理旧图片，复制新图片
            self._cleanup_source_images(source_id)

        # 复制新图片
        if new_image_paths:
            return self._copy_images_to_source_dir(
                source_id, source_name, new_image_paths
            )

        return []

    def update_source_images(
        self, source_id: int, image_paths: List[str], replace_all: bool = True
    ) -> List[str]:
        """
        更新货源图片

        Args:
            source_id: 货源ID
            image_paths: 新图片路径列表
            replace_all: 是否替换所有图片（True）还是累积添加（False）

        Returns:
            复制后的图片路径列表
        """
        source = self.get_source(source_id)
        if not source:
            return []

        final_images = self._update_source_images(
            source_id, source.name, image_paths, replace_all
        )

        # 如果是累积模式，需要合并现有图片和新图片
        if not replace_all:
            existing_images = source.image_urls or []
            final_images = existing_images + final_images

        # 更新数据库中的图片信息
        import json

        cursor = self.db.connection.cursor()
        cursor.execute(
            "UPDATE sources SET image_urls = ? WHERE id = ?",
            (json.dumps(final_images), source_id),
        )
        self.db.connection.commit()

        return final_images

    # ===== 产品相关操作 =====

    def create_product(
        self,
        name: str,
        sku: str = "",
        selling_price: float = 0.0,
        shipping_fee: float = 0.0,
        free_shipping: bool = True,
        description: str = "",
        sales_reference: int = 0,
        reference_url: str = "",
        images: List[str] = None,
    ) -> Product:
        """创建产品"""
        cursor = self.db.connection.cursor()
        now = datetime.now()

        # 插入产品记录
        cursor.execute(
            """
            INSERT INTO products (name, sku, selling_price, shipping_fee, free_shipping, description, sales_reference, reference_url, images, featured_images, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                name,
                sku,
                selling_price,
                shipping_fee,
                free_shipping,
                description,
                sales_reference,
                reference_url,
                json.dumps(images or []),
                json.dumps([]),  # 新产品时主要图片为空
                now,
                now,
            ),
        )

        product_id = cursor.lastrowid
        self.db.connection.commit()

        # 处理图片
        if images:
            final_images = self._copy_images_to_product_dir(product_id, name, images)
            # 更新产品图片路径
            cursor.execute(
                "UPDATE products SET images = ? WHERE id = ?",
                (json.dumps(final_images), product_id),
            )
            self.db.connection.commit()

        return self.get_product(product_id)

    def get_product(self, product_id: int) -> Optional[Product]:
        """获取产品"""
        cursor = self.db.connection.cursor()
        cursor.execute("SELECT * FROM products WHERE id = ?", (product_id,))
        row = cursor.fetchone()

        if not row:
            return None

        product = self._row_to_product(row)
        product.sources = self.get_product_sources(product_id)
        product.custom_fields = self.get_product_custom_fields(product_id)

        return product

    def get_all_products(self) -> List[Product]:
        """获取所有产品"""
        cursor = self.db.connection.cursor()
        cursor.execute("SELECT * FROM products ORDER BY created_at DESC")
        rows = cursor.fetchall()

        products = []
        for row in rows:
            product = self._row_to_product(row)
            # 加载货源数据和标签数据，因为产品列表需要显示这些信息
            product.sources = self.get_product_sources(product.id)
            product.tags = self.get_product_tags(product.id)
            products.append(product)

        return products

    def update_product(self, product: Product) -> bool:
        """更新产品"""
        cursor = self.db.connection.cursor()
        now = datetime.now()

        # 获取旧的产品信息
        old_product = self.get_product(product.id)
        if not old_product:
            return False

        # 处理图片更新（如果图片列表发生变化且不是空列表才更新）
        # 注意：如果外部已经调用了update_product_images，这里会跳过图片处理
        if product.images != old_product.images and product.images:
            final_images = self._update_product_images(
                product.id, product.name, product.images
            )
            product.images = final_images

        # 更新产品记录
        cursor.execute(
            """
            UPDATE products 
            SET name = ?, sku = ?, selling_price = ?, shipping_fee = ?, free_shipping = ?, description = ?, sales_reference = ?, reference_url = ?, images = ?, featured_images = ?, updated_at = ?
            WHERE id = ?
            """,
            (
                product.name,
                product.sku,
                product.selling_price,
                product.shipping_fee,
                product.free_shipping,
                product.description,
                product.sales_reference,
                product.reference_url,
                json.dumps(product.images),
                json.dumps(product.featured_images),
                now,
                product.id,
            ),
        )

        self.db.connection.commit()
        return cursor.rowcount > 0

    def delete_product(self, product_id: int) -> bool:
        """删除产品"""
        # 删除产品图片
        self._cleanup_product_images(product_id)

        cursor = self.db.connection.cursor()
        cursor.execute("DELETE FROM products WHERE id = ?", (product_id,))
        self.db.connection.commit()

        return cursor.rowcount > 0

    def search_products(self, keyword: str) -> List[Product]:
        """搜索产品"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            """SELECT * FROM products 
               WHERE name LIKE ? OR sku LIKE ? 
               ORDER BY created_at DESC""",
            (f"%{keyword}%", f"%{keyword}%"),
        )
        rows = cursor.fetchall()

        products = []
        for row in rows:
            product = self._row_to_product(row)
            products.append(product)

        return products

    def search_products_by_tags(self, tag_ids: List[int]) -> List[Product]:
        """通过标签搜索产品"""
        if not tag_ids:
            return []

        cursor = self.db.connection.cursor()
        placeholders = ",".join(["?" for _ in tag_ids])
        cursor.execute(
            f"""
            SELECT DISTINCT p.* FROM products p
            JOIN product_tags pt ON p.id = pt.product_id
            WHERE pt.tag_id IN ({placeholders})
            ORDER BY p.name
            """,
            tag_ids,
        )
        rows = cursor.fetchall()

        products = []
        for row in rows:
            product = self._row_to_product(row)
            products.append(product)

        return products

    # ===== 货源相关操作 =====

    def add_source(self, product_id: int, name: str, price: float, **kwargs) -> Source:
        """添加货源"""
        cursor = self.db.connection.cursor()

        # 处理modes字段
        modes = kwargs.get("modes", ["wholesale"])
        if isinstance(modes, list):
            modes_str = ",".join(modes)
        else:
            modes_str = str(modes)

        # 处理image_urls字段
        image_urls = kwargs.get("image_urls", [])
        if isinstance(image_urls, list):
            import json

            image_urls_str = json.dumps(image_urls)
        else:
            image_urls_str = str(image_urls)

        # 处理featured_images字段
        featured_images = kwargs.get("featured_images", [])
        if isinstance(featured_images, list):
            featured_images_str = json.dumps(featured_images)
        else:
            featured_images_str = str(featured_images)

        # 处理product_publish_time字段
        product_publish_time = kwargs.get("product_publish_time")
        if product_publish_time:
            if hasattr(product_publish_time, "isoformat"):
                product_publish_time_str = product_publish_time.isoformat()
            else:
                product_publish_time_str = str(product_publish_time)
        else:
            product_publish_time_str = None

        cursor.execute(
            """INSERT INTO sources 
               (product_id, name, price, url, shop_info, quantity, min_order_quantity, 
                shipping_cost, shipping_cost_suffix, shipping_location, product_name, 
                sales_count, status, return_policy, pickup_rate_24h, pickup_rate_48h, 
                monthly_dropship_orders, downstream_stores, distributor_count, 
                product_publish_time, available_products, monthly_new_products, 
                modes, image_urls, featured_images, source_mode, contact_info, notes, is_active,
                wholesale_min_order_quantity, wholesale_payment_terms, wholesale_rebate_rate, wholesale_price_tiers,
                dropship_service_fee, dropship_processing_time, dropship_packaging, dropship_inventory_sync,
                dropship_min_quantity, dropship_shipping_location, dropship_support_regions) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            (
                product_id,
                name,
                price,
                kwargs.get("url", ""),
                kwargs.get("shop_info", ""),
                kwargs.get("quantity", 1),
                kwargs.get("min_order_quantity", 1),
                kwargs.get("shipping_cost", 0.0),
                kwargs.get("shipping_cost_suffix", ""),
                kwargs.get("shipping_location", ""),
                kwargs.get("product_name", ""),
                kwargs.get("sales_count", 0),
                kwargs.get("status", "active"),
                kwargs.get("return_policy", ""),
                kwargs.get("pickup_rate_24h", 0.0),
                kwargs.get("pickup_rate_48h", 0.0),
                kwargs.get("monthly_dropship_orders", 0),
                kwargs.get("downstream_stores", 0),
                kwargs.get("distributor_count", 0),
                product_publish_time_str,
                kwargs.get("available_products", 0),
                kwargs.get("monthly_new_products", 0),
                modes_str,
                image_urls_str,
                featured_images_str,
                kwargs.get("source_mode", "wholesale"),  # 保持向后兼容
                kwargs.get("contact_info", ""),
                kwargs.get("notes", ""),
                kwargs.get("is_active", True),
                # 批发模式专属字段
                kwargs.get("wholesale_min_order_quantity", 1),
                kwargs.get("wholesale_payment_terms", 0),
                kwargs.get("wholesale_rebate_rate", 0.0),
                kwargs.get("wholesale_price_tiers", ""),
                # 代发模式专属字段
                kwargs.get("dropship_service_fee", 0.0),
                kwargs.get("dropship_processing_time", 0),
                kwargs.get("dropship_packaging", ""),
                kwargs.get("dropship_inventory_sync", "实时同步"),
                kwargs.get("dropship_min_quantity", 1),
                kwargs.get("dropship_shipping_location", ""),
                kwargs.get("dropship_support_regions", ""),
            ),
        )
        self.db.connection.commit()

        source_id = cursor.lastrowid
        return self.get_source(source_id)

    def get_source(self, source_id: int) -> Optional[Source]:
        """获取货源"""
        cursor = self.db.connection.cursor()
        cursor.execute("SELECT * FROM sources WHERE id = ?", (source_id,))
        row = cursor.fetchone()

        if not row:
            return None

        return self._row_to_source(row)

    def get_product_sources(self, product_id: int) -> List[Source]:
        """获取产品的所有货源"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            "SELECT * FROM sources WHERE product_id = ? ORDER BY price ASC",
            (product_id,),
        )
        rows = cursor.fetchall()

        sources = []
        for row in rows:
            source = self._row_to_source(row)
            sources.append(source)

        return sources

    def update_source(self, source: Source) -> bool:
        """更新货源"""
        cursor = self.db.connection.cursor()

        # 处理modes字段
        modes_str = ",".join(source.modes) if source.modes else "wholesale"

        # 处理image_urls字段
        import json

        image_urls_str = json.dumps(source.image_urls) if source.image_urls else "[]"

        # 处理featured_images字段
        featured_images_str = (
            json.dumps(source.featured_images) if source.featured_images else "[]"
        )

        # 处理product_publish_time字段
        product_publish_time_str = None
        if source.product_publish_time:
            if hasattr(source.product_publish_time, "isoformat"):
                product_publish_time_str = source.product_publish_time.isoformat()
            else:
                product_publish_time_str = str(source.product_publish_time)

        cursor.execute(
            """UPDATE sources 
               SET name = ?, price = ?, url = ?, shop_info = ?, quantity = ?, 
                   min_order_quantity = ?, shipping_cost = ?, shipping_cost_suffix = ?, 
                   shipping_location = ?, product_name = ?, sales_count = ?, status = ?, 
                   return_policy = ?, pickup_rate_24h = ?, pickup_rate_48h = ?, 
                   monthly_dropship_orders = ?, downstream_stores = ?, distributor_count = ?, 
                   product_publish_time = ?, available_products = ?, monthly_new_products = ?, 
                   modes = ?, image_urls = ?, featured_images = ?, source_mode = ?, contact_info = ?, notes = ?, 
                   is_active = ?, wholesale_min_order_quantity = ?, wholesale_payment_terms = ?, 
                   wholesale_rebate_rate = ?, wholesale_price_tiers = ?, dropship_service_fee = ?, 
                   dropship_processing_time = ?, dropship_packaging = ?, dropship_inventory_sync = ?, 
                   dropship_min_quantity = ?, dropship_shipping_location = ?, dropship_support_regions = ?, 
                   updated_at = CURRENT_TIMESTAMP 
               WHERE id = ?""",
            (
                source.name,
                source.price,
                source.url,
                source.shop_info,
                source.quantity,
                source.min_order_quantity,
                source.shipping_cost,
                source.shipping_cost_suffix,
                source.shipping_location,
                source.product_name,
                source.sales_count,
                source.status,
                source.return_policy,
                source.pickup_rate_24h,
                source.pickup_rate_48h,
                source.monthly_dropship_orders,
                source.downstream_stores,
                source.distributor_count,
                product_publish_time_str,
                source.available_products,
                source.monthly_new_products,
                modes_str,
                image_urls_str,
                featured_images_str,
                source.modes[0] if source.modes else "wholesale",  # 保持向后兼容
                source.contact_info,
                source.notes,
                source.is_active,
                # 批发模式专属字段
                source.wholesale_min_order_quantity,
                source.wholesale_payment_terms,
                source.wholesale_rebate_rate,
                source.wholesale_price_tiers,
                # 代发模式专属字段
                source.dropship_service_fee,
                source.dropship_processing_time,
                source.dropship_packaging,
                source.dropship_inventory_sync,
                source.dropship_min_quantity,
                source.dropship_shipping_location,
                source.dropship_support_regions,
                source.id,
            ),
        )
        self.db.connection.commit()

        return cursor.rowcount > 0

    def delete_source(self, source_id: int) -> bool:
        """删除货源"""
        # 删除货源图片
        self._cleanup_source_images(source_id)

        cursor = self.db.connection.cursor()
        cursor.execute("DELETE FROM sources WHERE id = ?", (source_id,))
        self.db.connection.commit()

        return cursor.rowcount > 0

    # ===== 自定义字段相关操作 =====

    def add_custom_field(
        self,
        product_id: int,
        name: str,
        value: str = "",
        field_type: str = "text",
        **kwargs,
    ) -> CustomField:
        """添加自定义字段"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            """INSERT INTO custom_fields 
               (product_id, name, value, field_type, display_name, description, is_required, is_visible, order_index) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            (
                product_id,
                name,
                value,
                field_type,
                kwargs.get("display_name", name),
                kwargs.get("description", ""),
                kwargs.get("is_required", False),
                kwargs.get("is_visible", True),
                kwargs.get("order_index", 0),
            ),
        )
        self.db.connection.commit()

        field_id = cursor.lastrowid
        return self.get_custom_field(field_id)

    def get_custom_field(self, field_id: int) -> Optional[CustomField]:
        """获取自定义字段"""
        cursor = self.db.connection.cursor()
        cursor.execute("SELECT * FROM custom_fields WHERE id = ?", (field_id,))
        row = cursor.fetchone()

        if not row:
            return None

        return self._row_to_custom_field(row)

    def get_product_custom_fields(self, product_id: int) -> List[CustomField]:
        """获取产品的所有自定义字段"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            "SELECT * FROM custom_fields WHERE product_id = ? ORDER BY order_index ASC, name ASC",
            (product_id,),
        )
        rows = cursor.fetchall()

        fields = []
        for row in rows:
            field = self._row_to_custom_field(row)
            fields.append(field)

        return fields

    def update_custom_field(self, field: CustomField) -> bool:
        """更新自定义字段"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            """UPDATE custom_fields 
               SET name = ?, value = ?, field_type = ?, display_name = ?, description = ?, 
                   is_required = ?, is_visible = ?, order_index = ?, updated_at = CURRENT_TIMESTAMP 
               WHERE id = ?""",
            (
                field.name,
                field.value,
                field.field_type,
                field.display_name,
                field.description,
                field.is_required,
                field.is_visible,
                field.order_index,
                field.id,
            ),
        )
        self.db.connection.commit()

        return cursor.rowcount > 0

    def delete_custom_field(self, field_id: int) -> bool:
        """删除自定义字段"""
        cursor = self.db.connection.cursor()
        cursor.execute("DELETE FROM custom_fields WHERE id = ?", (field_id,))
        self.db.connection.commit()

        return cursor.rowcount > 0

    # ===== 对比分析相关操作 =====

    def compare_products(self, product_ids: List[int]) -> List[Dict]:
        """产品对比分析"""
        comparison_data = []

        for product_id in product_ids:
            product = self.get_product(product_id)
            if product:
                comparison_data.append(
                    {
                        "id": product.id,
                        "name": product.name,
                        "sku": product.sku,
                        "selling_price": product.selling_price,
                        "lowest_cost": product.lowest_cost,
                        "highest_cost": product.highest_cost,
                        "average_cost": product.average_cost,
                        "profit": product.profit,
                        "profit_margin": product.profit_margin,
                        "total_quantity": product.total_quantity,
                        "source_count": len(product.sources),
                        "best_source": (
                            product.best_source.name if product.best_source else "N/A"
                        ),
                        "worst_source": (
                            product.worst_source.name if product.worst_source else "N/A"
                        ),
                    }
                )

        return comparison_data

    def get_statistics(self) -> Dict:
        """获取统计数据"""
        cursor = self.db.connection.cursor()

        # 产品统计
        cursor.execute("SELECT COUNT(*) FROM products")
        total_products = cursor.fetchone()[0]

        # 货源统计
        cursor.execute("SELECT COUNT(*) FROM sources")
        total_sources = cursor.fetchone()[0]

        # 价格统计
        cursor.execute(
            "SELECT AVG(selling_price), MIN(selling_price), MAX(selling_price) FROM products"
        )
        price_stats = cursor.fetchone()

        # 利润统计
        cursor.execute(
            """
            SELECT p.id, p.selling_price, MIN(s.price) as min_cost
            FROM products p
            LEFT JOIN sources s ON p.id = s.product_id
            GROUP BY p.id
        """
        )
        profit_data = cursor.fetchall()

        profits = []
        for row in profit_data:
            if row["min_cost"] is not None:
                profit = row["selling_price"] - row["min_cost"]
                profits.append(profit)

        avg_profit = sum(profits) / len(profits) if profits else 0

        return {
            "total_products": total_products,
            "total_sources": total_sources,
            "avg_selling_price": price_stats[0] or 0,
            "min_selling_price": price_stats[1] or 0,
            "max_selling_price": price_stats[2] or 0,
            "avg_profit": avg_profit,
            "profitable_products": len([p for p in profits if p > 0]),
        }

    # ===== 私有辅助方法 =====

    def _row_to_product(self, row) -> Product:
        """将数据库行转换为产品对象"""
        images = json.loads(row["images"]) if row["images"] else []

        # 安全访问新添加的字段
        try:
            description = row["description"] if "description" in row.keys() else ""
        except (KeyError, TypeError):
            description = ""

        try:
            sales_reference = (
                row["sales_reference"] if "sales_reference" in row.keys() else 0
            )
        except (KeyError, TypeError):
            sales_reference = 0

        try:
            reference_url = (
                row["reference_url"] if "reference_url" in row.keys() else ""
            )
        except (KeyError, TypeError):
            reference_url = ""

        # 处理运费字段
        try:
            shipping_fee = row["shipping_fee"] if "shipping_fee" in row.keys() else 0.0
        except (KeyError, TypeError):
            shipping_fee = 0.0

        try:
            free_shipping = (
                bool(row["free_shipping"]) if "free_shipping" in row.keys() else True
            )
        except (KeyError, TypeError):
            free_shipping = True

        # 处理主要图片字段
        try:
            featured_images_str = (
                row["featured_images"] if "featured_images" in row.keys() else "[]"
            )
            featured_images = (
                json.loads(featured_images_str) if featured_images_str else []
            )
        except (KeyError, TypeError, json.JSONDecodeError):
            featured_images = []

        product = Product(
            id=row["id"],
            name=row["name"],
            sku=row["sku"] or "",
            selling_price=row["selling_price"],
            shipping_fee=shipping_fee,
            free_shipping=free_shipping,
            description=description,
            sales_reference=sales_reference,
            reference_url=reference_url,
            images=images,
            featured_images=featured_images,
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
        )

        # 加载产品标签
        product.tags = self.get_product_tags(product.id)

        return product

    def _row_to_source(self, row) -> Source:
        """数据库行转货源对象"""
        import json
        from datetime import datetime

        # 处理新字段的兼容性
        def get_field(field_name, default_value):
            try:
                return row[field_name] if field_name in row.keys() else default_value
            except (KeyError, TypeError):
                return default_value

        # 处理modes字段
        modes_str = get_field("modes", "wholesale")
        if isinstance(modes_str, str) and modes_str:
            modes = [mode.strip() for mode in modes_str.split(",") if mode.strip()]
        else:
            modes = ["wholesale"]

        # 处理image_urls字段
        image_urls_str = get_field("image_urls", "[]")
        try:
            image_urls = json.loads(image_urls_str) if image_urls_str else []
        except (json.JSONDecodeError, TypeError):
            image_urls = []

        # 处理featured_images字段
        featured_images_str = get_field("featured_images", "[]")
        try:
            featured_images = (
                json.loads(featured_images_str) if featured_images_str else []
            )
        except (json.JSONDecodeError, TypeError):
            featured_images = []

        # 处理product_publish_time字段
        product_publish_time_str = get_field("product_publish_time", None)
        product_publish_time = None
        if product_publish_time_str:
            try:
                product_publish_time = datetime.fromisoformat(product_publish_time_str)
            except (ValueError, TypeError):
                product_publish_time = None

        return Source(
            id=row["id"],
            product_id=row["product_id"],
            name=row["name"],
            price=row["price"],
            url=row["url"] or "",
            shop_info=row["shop_info"] or "",
            quantity=row["quantity"],
            modes=modes,
            min_order_quantity=get_field("min_order_quantity", 1),
            shipping_cost=get_field("shipping_cost", 0.0),
            shipping_cost_suffix=get_field("shipping_cost_suffix", ""),
            shipping_location=get_field("shipping_location", ""),
            product_name=get_field("product_name", ""),
            sales_count=get_field("sales_count", 0),
            status=get_field("status", "active"),
            return_policy=get_field("return_policy", ""),
            pickup_rate_24h=get_field("pickup_rate_24h", 0.0),
            pickup_rate_48h=get_field("pickup_rate_48h", 0.0),
            monthly_dropship_orders=get_field("monthly_dropship_orders", 0),
            downstream_stores=get_field("downstream_stores", 0),
            distributor_count=get_field("distributor_count", 0),
            product_publish_time=product_publish_time,
            available_products=get_field("available_products", 0),
            monthly_new_products=get_field("monthly_new_products", 0),
            image_urls=image_urls,
            featured_images=featured_images,
            # 批发模式专属字段
            wholesale_min_order_quantity=get_field("wholesale_min_order_quantity", 1),
            wholesale_payment_terms=get_field("wholesale_payment_terms", 0),
            wholesale_rebate_rate=get_field("wholesale_rebate_rate", 0.0),
            wholesale_price_tiers=get_field("wholesale_price_tiers", ""),
            # 代发模式专属字段
            dropship_service_fee=get_field("dropship_service_fee", 0.0),
            dropship_processing_time=get_field("dropship_processing_time", 0),
            dropship_packaging=get_field("dropship_packaging", ""),
            dropship_inventory_sync=get_field("dropship_inventory_sync", "实时同步"),
            dropship_min_quantity=get_field("dropship_min_quantity", 1),
            dropship_shipping_location=get_field("dropship_shipping_location", ""),
            dropship_support_regions=get_field("dropship_support_regions", ""),
            contact_info=row["contact_info"] or "",
            notes=row["notes"] or "",
            is_active=bool(row["is_active"]),
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
        )

    def _row_to_custom_field(self, row) -> CustomField:
        """数据库行转自定义字段对象"""
        return CustomField(
            id=row["id"],
            product_id=row["product_id"],
            name=row["name"],
            value=row["value"] or "",
            field_type=row["field_type"],
            display_name=row["display_name"] or row["name"],
            description=row["description"] or "",
            is_required=bool(row["is_required"]),
            is_visible=bool(row["is_visible"]),
            order_index=row["order_index"],
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
        )

    def _row_to_tag(self, row) -> Tag:
        """数据库行转标签对象"""
        return Tag(
            id=row["id"],
            name=row["name"],
            color=row["color"],
            description=row["description"] or "",
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
        )

    # ===== 标签相关操作 =====

    def get_product_tags(self, product_id: int) -> List[Tag]:
        """获取产品的所有标签"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            """
            SELECT t.* FROM tags t
            JOIN product_tags pt ON t.id = pt.tag_id
            WHERE pt.product_id = ?
            ORDER BY t.name
            """,
            (product_id,),
        )
        rows = cursor.fetchall()

        tags = []
        for row in rows:
            tag = self._row_to_tag(row)
            tags.append(tag)

        return tags

    def get_products_by_tag(self, tag_id: int) -> List[Product]:
        """获取包含指定标签的所有产品"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            """
            SELECT p.* FROM products p
            JOIN product_tags pt ON p.id = pt.product_id
            WHERE pt.tag_id = ?
            ORDER BY p.name
            """,
            (tag_id,),
        )
        rows = cursor.fetchall()

        products = []
        for row in rows:
            product = self._row_to_product(row)
            products.append(product)

        return products

    def add_tag_to_product(self, product_id: int, tag_id: int) -> bool:
        """为产品添加标签"""
        cursor = self.db.connection.cursor()
        try:
            cursor.execute(
                "INSERT INTO product_tags (product_id, tag_id) VALUES (?, ?)",
                (product_id, tag_id),
            )
            self.db.connection.commit()
            return True
        except sqlite3.IntegrityError:
            # 关联已存在
            return False

    def remove_tag_from_product(self, product_id: int, tag_id: int) -> bool:
        """从产品移除标签"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            "DELETE FROM product_tags WHERE product_id = ? AND tag_id = ?",
            (product_id, tag_id),
        )
        self.db.connection.commit()
        return cursor.rowcount > 0


class TagService:
    """标签服务 - 标签管理业务逻辑"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def create_tag(
        self, name: str, color: str = "#1976d2", description: str = ""
    ) -> Tag:
        """创建标签"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            "INSERT INTO tags (name, color, description) VALUES (?, ?, ?)",
            (name, color, description),
        )
        self.db.connection.commit()

        tag_id = cursor.lastrowid
        return Tag(id=tag_id, name=name, color=color, description=description)

    def get_tag(self, tag_id: int) -> Optional[Tag]:
        """获取单个标签"""
        cursor = self.db.connection.cursor()
        cursor.execute("SELECT * FROM tags WHERE id = ?", (tag_id,))
        row = cursor.fetchone()

        if row:
            return self._row_to_tag(row)
        return None

    def get_tag_by_name(self, name: str) -> Optional[Tag]:
        """通过名称获取标签"""
        cursor = self.db.connection.cursor()
        cursor.execute("SELECT * FROM tags WHERE name = ?", (name,))
        row = cursor.fetchone()

        if row:
            return self._row_to_tag(row)
        return None

    def get_all_tags(self) -> List[Tag]:
        """获取所有标签"""
        cursor = self.db.connection.cursor()
        cursor.execute("SELECT * FROM tags ORDER BY name")
        rows = cursor.fetchall()

        tags = []
        for row in rows:
            tag = self._row_to_tag(row)
            # 计算使用次数
            cursor.execute(
                "SELECT COUNT(*) FROM product_tags WHERE tag_id = ?", (tag.id,)
            )
            tag.usage_count = cursor.fetchone()[0]
            tags.append(tag)

        return tags

    def update_tag(self, tag: Tag) -> bool:
        """更新标签"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            "UPDATE tags SET name = ?, color = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            (tag.name, tag.color, tag.description, tag.id),
        )
        self.db.connection.commit()

        return cursor.rowcount > 0

    def delete_tag(self, tag_id: int) -> bool:
        """删除标签"""
        cursor = self.db.connection.cursor()

        # 删除标签会自动删除关联关系（因为外键约束）
        cursor.execute("DELETE FROM tags WHERE id = ?", (tag_id,))
        self.db.connection.commit()

        return cursor.rowcount > 0

    def get_popular_tags(self, limit: int = 10) -> List[Tag]:
        """获取热门标签（按使用次数排序）"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            """
            SELECT t.*, COUNT(pt.product_id) as usage_count
            FROM tags t
            LEFT JOIN product_tags pt ON t.id = pt.tag_id
            GROUP BY t.id
            ORDER BY usage_count DESC, t.name
            LIMIT ?
            """,
            (limit,),
        )
        rows = cursor.fetchall()

        tags = []
        for row in rows:
            tag = self._row_to_tag(row)
            tag.usage_count = row["usage_count"]
            tags.append(tag)

        return tags

    def search_tags(self, keyword: str) -> List[Tag]:
        """搜索标签"""
        cursor = self.db.connection.cursor()
        cursor.execute(
            """
            SELECT t.*, COUNT(pt.product_id) as usage_count
            FROM tags t
            LEFT JOIN product_tags pt ON t.id = pt.tag_id
            WHERE t.name LIKE ? OR t.description LIKE ?
            GROUP BY t.id
            ORDER BY usage_count DESC, t.name
            """,
            (f"%{keyword}%", f"%{keyword}%"),
        )
        rows = cursor.fetchall()

        tags = []
        for row in rows:
            tag = self._row_to_tag(row)
            tag.usage_count = row["usage_count"]
            tags.append(tag)

        return tags

    def _row_to_tag(self, row) -> Tag:
        """数据库行转标签对象"""
        return Tag(
            id=row["id"],
            name=row["name"],
            color=row["color"],
            description=row["description"] or "",
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
        )
