"""
智能网页抓取器
基于原版GetNews的Playwright技术，针对电商货源网站优化
集成验证绕过功能，完全复刻参考项目的成功方案
"""

import asyncio
import random
import time
from datetime import datetime
from typing import List, Dict, Optional, Any, Union
from dataclasses import dataclass, asdict
from urllib.parse import urljoin, urlparse
import json
import re
import hashlib
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON>, Browser
from fake_useragent import UserAgent
from bs4 import BeautifulSoup
import aiohttp
import aiofiles
import logging

from .auth_bypass import AuthBypass


@dataclass
class ScrapedContent:
    """抓取内容数据模型"""

    title: str = ""
    url: str = ""
    content: str = ""
    images: List[str] = None
    price: str = ""
    price_range: str = ""
    supplier: str = ""
    specifications: Dict[str, Any] = None
    description: str = ""
    success: bool = False
    error_message: str = ""
    scraped_at: str = ""
    source_type: str = ""  # 1688, ta<PERSON><PERSON>, alibaba等

    def __post_init__(self):
        if self.images is None:
            self.images = []
        if self.specifications is None:
            self.specifications = {}
        if not self.scraped_at:
            self.scraped_at = datetime.now().isoformat()


class WebScraper:
    """智能网页抓取器 - 集成验证绕过功能"""

    def __init__(self, headless: bool = True, timeout: int = 30):
        self.headless = headless
        self.timeout = timeout
        self.max_retries = 3
        self.delay_range = (1, 3)

        self.playwright = None
        self.browser = None
        self.context = None

        # 新增：验证绕过器
        self.auth_bypass = AuthBypass(headless=False)  # 验证时需要显示浏览器
        self.logger = logging.getLogger("web_scraper")

        self.ua = UserAgent()

        # 1688专用选择器（保持原版功能）
        self.selectors_1688 = {
            "title": [
                'h1[data-spm="title"]',
                ".product-title h1",
                ".product-name",
                "h1",
            ],
            "price": [
                ".price-now",
                ".price-original",
                ".ladder-price",
                ".price-range",
                '[class*="price"]',
            ],
            "images": [
                ".image-list img",
                ".detail-gallery img",
                ".main-image img",
                "img[data-src]",
            ],
            "supplier": [
                ".company-name",
                ".supplier-name",
                ".shop-name",
                '[class*="company"]',
            ],
            "content": [
                ".detail-content",
                ".product-desc",
                ".description",
                ".detail-info",
            ],
        }

        # 通用选择器
        self.selectors_generic = {
            "title": ["h1", ".title", ".product-title"],
            "price": ['[class*="price"]', ".price", ".cost"],
            "images": ["img[src]", "img[data-src]"],
            "content": [".content", ".description", ".details"],
        }

    async def __aenter__(self):
        """异步上下文管理器入口 - 完全复刻参考项目的反拦截策略"""
        self.playwright = await async_playwright().start()

        # 完全复刻参考项目的增强浏览器启动参数，强力绕过检测
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--no-first-run",
                "--no-zygote",
                "--disable-gpu",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor,TranslateUI",
                "--disable-background-networking",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-field-trial-config",
                "--disable-ipc-flooding-protection",
                "--enable-features=NetworkService,NetworkServiceLogging",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",  # 加快加载，避免图片验证码
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            ],
        )

        # 完全复刻参考项目的增强上下文配置
        self.context = await self.browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            viewport={"width": 1920, "height": 1080},
            locale="zh-CN",
            timezone_id="Asia/Shanghai",
            geolocation={"longitude": 116.3974, "latitude": 39.9093},  # 北京坐标
            permissions=["geolocation"],
            extra_http_headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "DNT": "1",
            },
        )

        # 完全复刻参考项目的反检测脚本注入
        await self.context.add_init_script(
            """
            // 隐藏webdriver特征
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪造plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 伪造languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            // 覆盖权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
            );
            
            // 伪造chrome属性
            window.chrome = {
                runtime: {},
                csi: function() {},
                loadTimes: function() { return {}; },
                app: {
                    isInstalled: false,
                    InstallState: {
                        DISABLED: 'disabled',
                        INSTALLED: 'installed',
                        NOT_INSTALLED: 'not_installed'
                    },
                    RunningState: {
                        CANNOT_RUN: 'cannot_run',
                        READY_TO_RUN: 'ready_to_run',
                        RUNNING: 'running'
                    }
                }
            };
            
            // 移除selenium标识
            delete navigator.__proto__.webdriver;
            
            // 伪造更多navigator属性
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 8,
            });
            
            // 伪造内存信息
            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => 8,
            });
            
            // 伪造网络连接信息
            Object.defineProperty(navigator, 'connection', {
                get: () => ({
                    effectiveType: '4g',
                    downlink: 10,
                    rtt: 50
                }),
            });
            
            // 覆盖console.debug以隐藏调试信息
            const originalDebug = console.debug;
            console.debug = function() {
                // 静默处理
            };
            
            // 伪造Battery API
            Object.defineProperty(navigator, 'getBattery', {
                get: () => () => Promise.resolve({
                    charging: true,
                    chargingTime: 0,
                    dischargingTime: Infinity,
                    level: 1
                }),
            });
            
            // 伪造Canvas指纹
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(type) {
                if (type === '2d') {
                    const context = getContext.call(this, type);
                    const originalFillText = context.fillText;
                    context.fillText = function(text, x, y, maxWidth) {
                        // 添加随机噪声来混淆指纹
                        const noise = Math.random() * 0.0001;
                        return originalFillText.call(this, text, x + noise, y + noise, maxWidth);
                    };
                    return context;
                }
                return getContext.call(this, type);
            };
            
            // 伪造WebGL指纹
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) {
                    return 'Intel Inc.';
                }
                if (parameter === 37446) {
                    return 'Intel Iris OpenGL Engine';
                }
                return getParameter.call(this, parameter);
            };
            
            // 伪造AudioContext指纹
            if (window.AudioContext) {
                const OriginalAudioContext = window.AudioContext;
                window.AudioContext = function() {
                    const context = new OriginalAudioContext();
                    const originalCreateOscillator = context.createOscillator;
                    context.createOscillator = function() {
                        const oscillator = originalCreateOscillator.call(this);
                        oscillator.frequency.value += Math.random() * 0.0001;
                        return oscillator;
                    };
                    return context;
                };
            }
            
            // 伪造屏幕分辨率变化
            Object.defineProperty(screen, 'availWidth', {
                get: () => 1920 + Math.floor(Math.random() * 100)
            });
            Object.defineProperty(screen, 'availHeight', {
                get: () => 1080 + Math.floor(Math.random() * 100)
            });
        """
        )

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

        # 清理验证绕过器
        await self.auth_bypass.close()

    async def scrape_url(self, url: str) -> Optional[ScrapedContent]:
        """
        抓取指定URL的内容 - 使用验证绕过增强版

        Args:
            url: 目标URL

        Returns:
            ScrapedContent对象或None
        """
        if not self._is_valid_url(url):
            self.logger.error(f"无效的URL: {url}")
            return None

        self.logger.info(f"开始抓取URL: {url}")

        # 对于1688等网站，使用验证绕过模式
        if "1688.com" in url or "alibaba.com" in url:
            return await self._scrape_with_auth_bypass(url)
        else:
            # 其他网站使用标准模式
            for attempt in range(self.max_retries):
                try:
                    return await self._scrape_with_playwright(url)
                except Exception as e:
                    self.logger.warning(
                        f"抓取失败 (尝试 {attempt + 1}/{self.max_retries}): {e}"
                    )
                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(2**attempt)

            self.logger.error(f"抓取失败，已达到最大重试次数: {url}")
            return None

    async def _scrape_with_auth_bypass(self, url: str) -> Optional[ScrapedContent]:
        """使用验证绕过模式抓取1688等网站 - 完全复刻参考项目的成功方案"""
        self.logger.info(f"使用验证绕过模式抓取: {url}")

        try:
            # 使用验证绕过器获取页面 - 使用增强的自动绕过功能
            # 注意：页面访问和验证绕过已经在auth_bypass中完成
            page = await self.auth_bypass.get_page_with_auto_bypass(url)
            self.logger.info("✅ 页面访问和验证绕过已完成")

            # 等待SKU内容加载 - 专门针对电商网站
            await self._wait_for_sku_content(page)

            # 最后检查一次是否有遗漏的验证码
            if await self._detect_captcha_or_block(page):
                self.logger.warning("检测到遗漏的验证码，等待人工处理...")

                # 等待人工验证
                verification_success = (
                    await self.auth_bypass.wait_for_human_verification(page, url)
                )

                if not verification_success:
                    await page.close()
                    return ScrapedContent(
                        url=url, success=False, error_message="验证码验证失败或超时"
                    )

            # 模拟人类行为
            await self._simulate_human_behavior_1688(page)

            # 提取内容
            content = await self._extract_content_from_page(page, url)
            content.success = True

            await page.close()
            return content

        except Exception as e:
            self.logger.error(f"验证绕过抓取失败: {e}")
            return ScrapedContent(url=url, success=False, error_message=str(e))

    async def _wait_for_sku_content(self, page: Page):
        """等待SKU内容加载 - 专门针对电商网站的动态内容"""
        sku_selectors = [
            ".sku-item-wrapper",
            ".sku-item",
            ".price-now",
            ".discountPrice",
            ".offer-price",
            ".product-sku",
            ".variant-item",
        ]

        self.logger.debug("等待SKU内容加载...")

        for selector in sku_selectors:
            try:
                await page.wait_for_selector(selector, timeout=5000)
                self.logger.debug(f"SKU内容已加载: {selector}")
                break
            except:
                continue

        # 额外等待动态内容
        await asyncio.sleep(3)

    async def _simulate_human_behavior_1688(self, page: Page):
        """专门针对1688的人类行为模拟 - 基于参考项目"""
        try:
            # 随机等待
            await asyncio.sleep(random.uniform(1, 3))

            # 模拟鼠标移动
            for _ in range(random.randint(2, 4)):
                x = random.randint(100, 1800)
                y = random.randint(100, 1000)
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.1, 0.5))

            # 模拟滚动 - 触发懒加载
            scroll_times = random.randint(3, 6)
            for i in range(scroll_times):
                scroll_y = (i + 1) * 300 + random.randint(-100, 100)
                await page.evaluate(f"window.scrollTo(0, {scroll_y})")
                await asyncio.sleep(random.uniform(0.5, 1.5))

            # 尝试点击价格相关元素触发数据加载
            await self._trigger_price_interactions(page)

        except Exception as e:
            self.logger.debug(f"人类行为模拟失败: {e}")

    async def _trigger_price_interactions(self, page: Page):
        """触发价格相关交互 - 激活动态价格显示"""
        try:
            # 尝试点击SKU选项
            sku_selectors = [
                ".sku-item-wrapper",
                ".sku-item",
                ".next-number-picker",
                ".quantity-selector",
            ]

            for selector in sku_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        # 随机点击一个元素
                        element = random.choice(elements)
                        if await element.is_visible():
                            await element.click()
                            await asyncio.sleep(0.5)
                            break
                except:
                    continue

        except Exception as e:
            self.logger.debug(f"价格交互触发失败: {e}")

    async def _scrape_with_playwright(self, url: str) -> Optional[ScrapedContent]:
        """使用Playwright抓取网页内容 - 增强多策略访问"""
        if not self.context:
            raise Exception("WebScraper未初始化，请使用async with语句")

        page = await self.context.new_page()

        try:
            # 设置超时
            page.set_default_timeout(self.timeout * 1000)

            # 完全复刻参考项目的多策略访问方式
            access_success = False
            strategies = [
                {"wait_until": "domcontentloaded", "timeout": 30000},
                {"wait_until": "load", "timeout": 20000},
                {"wait_until": "networkidle", "timeout": 15000},
                {"timeout": 10000},  # 最基本的访问
            ]

            for i, strategy in enumerate(strategies):
                try:
                    logging.info(f"尝试访问策略 {i+1}: {strategy}")
                    await page.goto(url, **strategy)
                    access_success = True
                    logging.info(f"访问策略 {i+1} 成功")
                    break
                except Exception as e:
                    logging.warning(f"访问策略 {i+1} 失败: {str(e)[:100]}")
                    continue

            if not access_success:
                return ScrapedContent(
                    url=url, success=False, error_message="页面访问失败"
                )

            # 等待页面稳定
            await asyncio.sleep(2)

            # 检测验证码或反爬页面
            if await self._detect_captcha_or_block(page):
                logging.warning("检测到验证码或反爬页面，尝试处理...")

                # 尝试等待更长时间让页面完全加载
                await asyncio.sleep(5)

                # 再次尝试人类行为模拟
                await self._simulate_human_behavior_enhanced(page)

                # 检查是否仍然是验证码页面
                if await self._detect_captcha_or_block(page):
                    return ScrapedContent(
                        url=url,
                        success=False,
                        error_message="遇到验证码拦截，无法继续抓取",
                    )

            # 完全复刻参考项目的人类行为模拟
            await self._simulate_human_behavior_enhanced(page)

            # 提取内容
            content = await self._extract_content_from_page(page, url)
            content.success = True

            return content

        except Exception as e:
            logging.error(f"Playwright抓取失败: {e}")
            return ScrapedContent(url=url, success=False, error_message=str(e))
        finally:
            await page.close()

    async def _extract_content_from_page(self, page: Page, url: str) -> ScrapedContent:
        """从页面提取内容 - 增强版，结合原GetNews的强大抓取能力"""
        content = ScrapedContent(url=url)

        # 检测网站类型
        content.source_type = self._detect_site_type(url)

        try:
            # === 1. 提取标题 - 多种策略 ===
            content.title = await self._extract_title_enhanced(page)

            # === 2. 提取供应商信息 ===
            content.supplier = await self._extract_supplier_enhanced(
                page, content.source_type
            )

            # === 3. 提取价格信息 ===
            content.price = await self._extract_price_enhanced(
                page, content.source_type
            )

            # === 4. 提取图片 ===
            content.images = await self._extract_images_enhanced(page, url)

            # === 5. 核心：提取全部页面内容 - 使用原GetNews的强大逻辑 ===
            content.content = await self._extract_all_page_content(page)

            # === 6. 提取规格信息 ===
            content.specifications = await self._extract_specifications(
                page, content.source_type
            )

            # === 7. 获取完整HTML用于AI分析 ===
            html_content = await page.content()
            content.description = html_content

            # === 8. 设置成功状态 ===
            content.success = True

            logging.info(
                f"成功提取内容: 标题={content.title[:50] if content.title else 'None'}..."
            )

        except Exception as e:
            logging.error(f"内容提取失败: {e}")
            content.error_message = str(e)
            content.success = False

        return content

    async def _extract_title_enhanced(self, page: Page) -> str:
        """增强的标题提取"""
        # 多种标题选择器，优先级从高到低
        title_selectors = [
            'h1[data-spm="title"]',  # 1688专用
            ".product-title h1",
            ".product-name",
            ".detail-title",
            ".item-title",
            ".goods-name",
            "h1",
            ".title",
            ".article-title",
            ".post-title",
            ".news-title",
        ]

        for selector in title_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    title_text = await element.inner_text()
                    if title_text and len(title_text.strip()) > 3:
                        # 清理标题
                        title = self._clean_title(title_text.strip())
                        if title:
                            return title
            except:
                continue

        # 如果还没找到标题，使用页面标题
        try:
            page_title = await page.title()
            if page_title:
                return self._clean_title(page_title)
        except:
            pass

        return ""

    async def _extract_supplier_enhanced(self, page: Page, source_type: str) -> str:
        """增强的供应商提取"""
        if source_type == "1688":
            supplier_selectors = [
                ".company-name a",
                ".company-name",
                ".supplier-name",
                ".shop-name",
                ".store-name",
                '[class*="company"]',
                '[data-spm="company"]',
            ]
        else:
            supplier_selectors = [
                ".seller-name",
                ".shop-name",
                ".store-name",
                ".brand-name",
                '[class*="seller"]',
                '[class*="shop"]',
            ]

        for selector in supplier_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and text.strip():
                        return text.strip()
            except:
                continue
        return ""

    async def _extract_price_enhanced(self, page: Page, source_type: str) -> str:
        """增强的价格提取"""
        if source_type == "1688":
            price_selectors = [
                ".price-now",
                ".price-original",
                ".ladder-price",
                ".price-range",
                ".unit-price",
                '[class*="price"]',
                '[data-spm*="price"]',
            ]
        else:
            price_selectors = [
                ".price",
                ".cost",
                ".amount",
                '[class*="price"]',
                '[class*="cost"]',
            ]

        for selector in price_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and text.strip():
                        # 清理价格文本
                        price_text = text.strip()
                        if any(
                            char in price_text
                            for char in ["¥", "$", "元", "价格", "起"]
                        ):
                            return price_text
            except:
                continue
        return ""

    async def _extract_images_enhanced(self, page: Page, base_url: str) -> List[str]:
        """增强的图片提取"""
        images = []

        # 多种图片选择器
        image_selectors = [
            ".image-list img",
            ".detail-gallery img",
            ".main-image img",
            ".product-images img",
            ".goods-gallery img",
            "img[data-src]",
            "img[src]",
            ".preview-img img",
        ]

        for selector in image_selectors:
            try:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    # 尝试多种属性获取图片URL
                    for attr in ["data-src", "data-original", "src", "data-lazy"]:
                        try:
                            img_url = await element.get_attribute(attr)
                            if img_url and img_url.startswith(("http", "//")):
                                # 转换为绝对URL
                                full_url = urljoin(base_url, img_url)
                                if (
                                    self._is_valid_image_url(full_url)
                                    and full_url not in images
                                ):
                                    images.append(full_url)
                                    if len(images) >= 10:  # 限制图片数量
                                        return images
                        except:
                            continue
            except:
                continue

        return images

    async def _extract_all_page_content(self, page: Page) -> str:
        """提取页面所有内容 - 使用原GetNews的强大逻辑"""
        try:
            # 移除不需要的元素
            await page.evaluate(
                """
                // 移除脚本、样式等无用元素
                const elementsToRemove = document.querySelectorAll(
                    'script, style, nav, header, footer, aside, .ad, .advertisement, ' +
                    '.sponsor, .related, .comment, .sidebar, .popup, .modal, ' +
                    '.breadcrumb, .pagination, .share, .social'
                );
                elementsToRemove.forEach(el => {
                    if (el) el.remove();
                });
            """
            )

            # 获取主要内容
            main_content = ""

            # 优先从文章/产品容器获取内容
            content_selectors = [
                # 电商专用
                ".detail-content",
                ".product-desc",
                ".description",
                ".detail-info",
                ".item-detail",
                ".goods-detail",
                # 通用内容
                "article",
                ".article",
                ".content",
                "#content",
                ".main-content",
                ".post-content",
                ".entry-content",
                ".article-content",
                ".article-body",
                ".news-content",
                ".rich_media_content",
                ".detail-wrap",
                ".product-detail",
            ]

            for selector in content_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        text = await element.inner_text()
                        if text and len(text.strip()) > 100:
                            main_content = text.strip()
                            break
                except:
                    continue

            # 如果没找到主要内容，获取body的全部文字（原GetNews的核心思路）
            if not main_content:
                try:
                    # 再次清理页面，移除更多干扰元素
                    await page.evaluate(
                        """
                        const elementsToHide = document.querySelectorAll(
                            '.header, .footer, .nav, .navigation, .menu, .toolbar, ' +
                            '.advert, .banner, .popup, .overlay, .mask'
                        );
                        elementsToHide.forEach(el => {
                            if (el) el.style.display = 'none';
                        });
                    """
                    )

                    body_element = await page.query_selector("body")
                    if body_element:
                        main_content = await body_element.inner_text()
                except:
                    main_content = ""

            # 清理内容
            if main_content:
                main_content = self._clean_content(main_content)

            return main_content

        except Exception as e:
            logging.error(f"全内容提取失败: {e}")
            return ""

    async def _extract_specifications(
        self, page: Page, source_type: str
    ) -> Dict[str, Any]:
        """提取规格信息"""
        specs = {}

        try:
            if source_type == "1688":
                # 1688规格选择器
                spec_selectors = [
                    ".detail-params",
                    ".product-params",
                    ".attributes",
                    ".spec-list",
                    ".property-list",
                ]
            else:
                spec_selectors = [
                    ".specifications",
                    ".specs",
                    ".attributes",
                    ".properties",
                    ".details-list",
                ]

            for selector in spec_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        # 尝试提取表格形式的规格
                        rows = await element.query_selector_all(
                            "tr, .spec-item, .attr-item"
                        )
                        for row in rows:
                            try:
                                text = await row.inner_text()
                                if ":" in text:
                                    parts = text.split(":", 1)
                                    if len(parts) == 2:
                                        key = parts[0].strip()
                                        value = parts[1].strip()
                                        if key and value:
                                            specs[key] = value
                            except:
                                continue

                        if specs:  # 如果找到了规格，就退出
                            break

                except:
                    continue

        except Exception as e:
            logging.debug(f"规格提取失败: {e}")

        return specs

    def _clean_title(self, title: str) -> str:
        """清洗产品标题"""
        if not title:
            return ""

        # 移除HTML标签
        title = re.sub(r"<[^>]+>", "", title)

        # 移除多余的空格和换行
        title = re.sub(r"\s+", " ", title).strip()

        # 移除常见的干扰词和网站名称
        noise_patterns = [
            r"[-_].*?(淘宝|天猫|1688|阿里巴巴|京东).*?$",  # 移除网站后缀
            r"^.*?(淘宝|天猫|1688|阿里巴巴|京东)[-_]",  # 移除网站前缀
        ]

        for pattern in noise_patterns:
            title = re.sub(pattern, "", title, flags=re.IGNORECASE)

        # 移除常见的营销词汇
        noise_words = [
            "包邮",
            "热销",
            "爆款",
            "限时",
            "特价",
            "促销",
            "直销",
            "批发",
            "现货",
        ]
        for word in noise_words:
            title = title.replace(word, "")

        return title.strip()

    def _clean_content(self, content: str) -> str:
        """清理内容"""
        if not content:
            return ""

        # 移除多余的空白字符
        content = re.sub(r"\s+", " ", content)
        content = re.sub(r"\n\s*\n", "\n\n", content)

        # 移除常见的无用文本
        patterns = [
            r"点击.*?查看.*?",
            r"更多.*?请.*?",
            r"版权所有.*?",
            r"转载请注明.*?",
            r"免责声明.*?",
            r"客服.*?联系.*?",
            r"QQ[:：]\s*\d+",
            r"微信[:：]\s*\w+",
            r"电话[:：]\s*[\d\-\s]+",
        ]

        for pattern in patterns:
            content = re.sub(pattern, "", content, flags=re.IGNORECASE)

        return content.strip()

    def _detect_site_type(self, url: str) -> str:
        """检测网站类型"""
        domain = urlparse(url).netloc.lower()

        if "1688.com" in domain:
            return "1688"
        elif "taobao.com" in domain:
            return "taobao"
        elif "tmall.com" in domain:
            return "tmall"
        elif "alibaba.com" in domain:
            return "alibaba"
        else:
            return "generic"

    def _is_valid_url(self, url: str) -> bool:
        """验证URL有效性"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

    def _is_valid_image_url(self, url: str) -> bool:
        """验证图片URL有效性"""
        if not url:
            return False

        # 检查是否为图片格式
        image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]
        url_lower = url.lower()

        return any(ext in url_lower for ext in image_extensions) or "image" in url_lower

    async def _simulate_human_behavior_enhanced(self, page: Page):
        """完全复刻参考项目的高级人类行为模拟"""
        try:
            # 更长的随机等待，模拟真实用户阅读时间
            await asyncio.sleep(random.uniform(2, 5))

            # 模拟多次鼠标移动，更真实的轨迹
            for _ in range(random.randint(3, 6)):
                x = random.randint(100, 1800)
                y = random.randint(100, 1000)
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.2, 0.8))

                # 随机点击空白区域
                if random.random() < 0.3:  # 30%概率点击
                    await page.mouse.click(x, y)
                    await asyncio.sleep(random.uniform(0.1, 0.3))

            # 更真实的滚动行为
            current_scroll = 0
            scroll_times = random.randint(3, 8)
            for i in range(scroll_times):
                # 模拟不同的滚动模式
                if random.random() < 0.7:  # 70%向下滚动
                    scroll_delta = random.randint(200, 600)
                    current_scroll += scroll_delta
                else:  # 30%向上滚动
                    scroll_delta = random.randint(-300, -100)
                    current_scroll = max(0, current_scroll + scroll_delta)

                await page.evaluate(f"window.scrollTo(0, {current_scroll})")
                await asyncio.sleep(random.uniform(0.8, 2.5))

                # 随机停顿，模拟阅读
                if random.random() < 0.4:  # 40%概率停顿
                    await asyncio.sleep(random.uniform(1, 3))

            # 模拟键盘操作（按空格、方向键等）
            if random.random() < 0.5:  # 50%概率进行键盘操作
                keys = ["Space", "ArrowDown", "ArrowUp", "PageDown"]
                for _ in range(random.randint(1, 3)):
                    key = random.choice(keys)
                    await page.keyboard.press(key)
                    await asyncio.sleep(random.uniform(0.5, 1.5))

            # 最后回到页面顶部，模拟重新查看
            await page.evaluate("window.scrollTo(0, 0)")
            await asyncio.sleep(random.uniform(1, 2))

        except Exception as e:
            logging.debug(f"人类行为模拟失败: {e}")

    async def _detect_captcha_or_block(self, page: Page) -> bool:
        """检测验证码或反爬页面"""
        try:
            # 获取页面标题和内容
            title = await page.title()
            content = await page.content()

            # 检测常见的验证码关键词
            captcha_keywords = [
                "验证码",
                "captcha",
                "verification",
                "robot",
                "安全验证",
                "请稍后再试",
                "访问被拒绝",
                "access denied",
                "blocked",
                "人机验证",
                "滑动验证",
                "点击验证",
                "输入验证码",
            ]

            # 检查标题
            if title:
                for keyword in captcha_keywords:
                    if keyword in title.lower():
                        return True

            # 检查页面内容
            if content:
                content_lower = content.lower()
                for keyword in captcha_keywords:
                    if keyword in content_lower:
                        return True

            # 检查是否有验证码相关的元素
            captcha_selectors = [
                ".captcha",
                "#captcha",
                "[id*='captcha']",
                "[class*='captcha']",
                ".verify",
                "#verify",
                "[id*='verify']",
                "[class*='verify']",
                ".security",
                "#security",
                "[id*='security']",
                "[class*='security']",
            ]

            for selector in captcha_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        return True
                except:
                    continue

            return False

        except Exception as e:
            logging.debug(f"验证码检测失败: {e}")
            return False
