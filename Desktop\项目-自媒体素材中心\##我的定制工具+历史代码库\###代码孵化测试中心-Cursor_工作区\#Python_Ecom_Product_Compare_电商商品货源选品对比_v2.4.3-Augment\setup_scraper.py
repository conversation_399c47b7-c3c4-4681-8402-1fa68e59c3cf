#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能抓取功能设置脚本
一键安装依赖和配置环境
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🤖 电商货源智能抓取功能 - 环境设置")
    print("=" * 60)
    print("基于原版网页抓取工具技术，集成qwen2.5系列AI模型")
    print("支持1688、淘宝、阿里巴巴等主流电商平台")
    print()


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")

    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.8或更高版本")
        return False

    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True


def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")

    try:
        # 升级pip
        print("升级pip...")
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
            check=True,
            capture_output=True,
        )

        # 安装依赖
        print("安装项目依赖...")
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            check=True,
        )

        print("✅ 依赖安装完成")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False


def install_playwright():
    """安装Playwright浏览器"""
    print("\n🌐 安装Playwright浏览器...")

    try:
        # 安装Chromium
        subprocess.run(
            [sys.executable, "-m", "playwright", "install", "chromium"], check=True
        )

        print("✅ Playwright浏览器安装完成")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Playwright安装失败: {e}")
        print("请手动运行: python -m playwright install chromium")
        return False


def check_ollama():
    """检查Ollama服务"""
    print("\n🔧 检查Ollama服务...")

    try:
        import aiohttp

        async def test_ollama():
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        "http://localhost:11434/api/tags",
                        timeout=aiohttp.ClientTimeout(total=5),
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            models = [model["name"] for model in data.get("models", [])]

                            print("✅ Ollama服务运行正常")
                            print(f"已安装模型: {models}")

                            # 检查指定模型
                            required_models = ["qwen2.5:14b", "qwen2.5vl:latest"]
                            missing_models = []

                            for model in required_models:
                                if not any(model in m for m in models):
                                    missing_models.append(model)

                            if missing_models:
                                print(f"⚠️  缺少以下模型: {missing_models}")
                                print("请使用以下命令安装:")
                                for model in missing_models:
                                    print(f"  ollama pull {model}")
                                return False
                            else:
                                print("✅ 所需AI模型已就绪")
                                return True
                        else:
                            print("❌ Ollama服务未响应")
                            return False
            except Exception as e:
                print(f"❌ 无法连接Ollama: {e}")
                return False

        # 运行异步检查
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(test_ollama())
            return result
        finally:
            loop.close()

    except ImportError:
        print("❌ aiohttp未安装，无法检查Ollama")
        return False


def create_directories():
    """创建必要目录"""
    print("\n📁 创建项目目录...")

    directories = ["scrapers", "data/scraped", "data/logs", "data/cache"]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ {directory}")

    print("✅ 目录创建完成")


def test_scraper():
    """测试抓取功能"""
    print("\n🧪 测试抓取功能...")

    try:
        # 测试导入
        from scrapers import WebScraper, AIAnalyzer, SourceDataMapper

        print("✅ 模块导入成功")

        # 测试基本功能
        analyzer = AIAnalyzer()
        mapper = SourceDataMapper()
        print("✅ 组件初始化成功")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def print_usage_guide():
    """打印使用指南"""
    print("\n" + "=" * 60)
    print("🎉 智能抓取功能设置完成！")
    print("=" * 60)
    print()
    print("📋 使用方法:")
    print("1. 启动程序: python main.py")
    print("2. 点击 '添加货源' 或编辑现有货源")
    print("3. 在货源对话框中切换到 '🤖 智能抓取' 选项卡")
    print("4. 输入货源网页URL（如1688商品页面）")
    print("5. 点击 '🚀 开始抓取和分析'")
    print("6. 等待AI分析完成，查看结果预览")
    print("7. 点击 '✅ 应用到货源' 自动填充表单")
    print()
    print("🔧 AI模型配置:")
    print("- 文本分析: qwen2.5:14b")
    print("- 视觉分析: qwen2.5vl:latest")
    print("- 确保Ollama服务运行在: http://localhost:11434")
    print()
    print("📝 支持的网站:")
    print("- 1688.com (阿里巴巴)")
    print("- taobao.com (淘宝)")
    print("- tmall.com (天猫)")
    print("- alibaba.com (阿里巴巴国际站)")
    print("- 其他电商网站")
    print()
    print("⚠️  注意事项:")
    print("- 首次使用建议先测试简单页面")
    print("- 确保网络连接稳定")
    print("- 遵守网站使用条款和机器人协议")
    print("- AI分析需要一定时间，请耐心等待")
    print()


def main():
    """主函数"""
    print_banner()

    # 检查Python版本
    if not check_python_version():
        return

    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，请检查网络连接或手动安装")
        return

    # 安装Playwright
    if not install_playwright():
        print("⚠️  Playwright安装可能失败，但可以继续")

    # 创建目录
    create_directories()

    # 检查Ollama
    ollama_ok = check_ollama()
    if not ollama_ok:
        print("\n⚠️  Ollama配置不完整，智能分析功能可能无法使用")
        print("请参考Ollama官方文档安装并配置所需模型")

    # 测试功能
    test_ok = test_scraper()
    if not test_ok:
        print("⚠️  功能测试失败，请检查代码和依赖")

    # 打印使用指南
    print_usage_guide()

    if ollama_ok and test_ok:
        print("🎯 所有功能就绪，可以开始使用智能抓取功能！")
    else:
        print("🔧 请根据上述提示完成剩余配置")


if __name__ == "__main__":
    main()
