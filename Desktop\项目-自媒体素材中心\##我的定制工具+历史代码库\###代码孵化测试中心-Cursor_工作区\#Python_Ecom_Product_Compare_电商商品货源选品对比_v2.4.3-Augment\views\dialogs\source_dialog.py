"""
货源编辑对话框
支持货源的添加和编辑，包含批发模式和代发模式的多选功能
"""

from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QFormLayout,
    QLineEdit,
    QTextEdit,
    QDoubleSpinBox,
    QSpinBox,
    QDialogButtonBox,
    QMessageBox,
    QCheckBox,
    QLabel,
    QTabWidget,
    QWidget,
    QComboBox,
    QGroupBox,
    QGridLayout,
    QScrollArea,
    QDateTimeEdit,
    QFrame,
    QSizePolicy,
    QPushButton,
    QListWidget,
    QFileDialog,
    QListWidgetItem,
    QSplitter,
    QProgressDialog,
    QSpacerItem,
    QApplication,
)
from PyQt6.QtCore import (
    Qt,
    pyqtSignal,
    QDateTime,
    QTimer,
    QPropertyAnimation,
    QEasingCurve,
)
from PyQt6.QtGui import QFont, QPixmap, QIcon, QWheelEvent, QColor, QPalette
from pathlib import Path
import logging
from utils.image_utils import load_image_safely

from models.source import Source, SourceMode
from models.database import ProductService
from utils import ImageDuplicateChecker, DuplicateResultDialog


class SourceDialog(QDialog):
    """货源编辑对话框"""

    # 信号定义
    source_saved = pyqtSignal(object)  # 货源保存完成

    def __init__(
        self,
        source: Source = None,
        product_id: int = None,
        product_service: ProductService = None,
        parent=None,
    ):
        try:
            super().__init__(parent)
            self.source = source
            self.product_id = product_id
            self.product_service = product_service
            self.is_edit_mode = source is not None

            # 设置窗口基本属性
            self.setWindowTitle("编辑货源" if self.is_edit_mode else "添加货源")
            self.setMinimumSize(900, 1000)
            self.resize(900, 1000)

            # 设置对话框字体
            font = QFont("Microsoft YaHei UI", 10)
            self.setFont(font)

            # 设置输入框样式（支持暗黑模式）
            self.setStyleSheet(
                """
                QLineEdit {
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    color: white;
                    padding: 6px;
                    border-radius: 3px;
                }
                QLineEdit:focus {
                    border: 2px solid #1976d2;
                }
                QTextEdit {
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    color: white;
                    padding: 6px;
                    border-radius: 3px;
                }
                QTextEdit:focus {
                    border: 2px solid #1976d2;
                }
                QSpinBox {
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    color: white;
                    padding: 6px;
                    border-radius: 3px;
                }
                QSpinBox:focus {
                    border: 2px solid #1976d2;
                }
                QDoubleSpinBox {
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    color: white;
                    padding: 6px;
                    border-radius: 3px;
                }
                QDoubleSpinBox:focus {
                    border: 2px solid #1976d2;
                }
                QComboBox {
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    color: white;
                    padding: 6px;
                    border-radius: 3px;
                }
                QComboBox:focus {
                    border: 2px solid #1976d2;
                }
                QComboBox::drop-down {
                    border: none;
                    background-color: #3d3d3d;
                }
                QComboBox::down-arrow {
                    image: none;
                    border: 1px solid #666666;
                    width: 10px;
                    height: 10px;
                }
                QComboBox QAbstractItemView {
                    background-color: #2b2b2b;
                    color: white;
                    border: 1px solid #555555;
                    selection-background-color: #1976d2;
                }
                QDateTimeEdit {
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    color: white;
                    padding: 6px;
                    border-radius: 3px;
                }
                QDateTimeEdit:focus {
                    border: 2px solid #1976d2;
                }
                QLabel {
                    color: white;
                }
                QGroupBox {
                    color: white;
                    font-weight: bold;
                    border: 2px solid #555555;
                    border-radius: 5px;
                    margin-top: 10px;
                    padding-top: 15px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    color: white;
                }
                QTabWidget::pane {
                    border: 1px solid #555555;
                    background-color: #3d3d3d;
                }
                QTabBar::tab {
                    background-color: #2b2b2b;
                    color: white;
                    padding: 8px 16px;
                    margin-right: 2px;
                }
                QTabBar::tab:selected {
                    background-color: #1976d2;
                    color: white;
                }
                QTabBar::tab:hover {
                    background-color: #444444;
                }
            """
            )

            layout = QVBoxLayout(self)

            # 创建选项卡
            self.tab_widget = QTabWidget()
            self.tab_widget.setStyleSheet(
                """
                QTabWidget::tab-bar { 
                    font-size: 11px; 
                } 
                QTabBar::tab { 
                    font-size: 11px; 
                    padding: 8px 16px; 
                    min-width: 80px;
                }
            """
            )

            # 创建选项卡内容
            print("开始创建选项卡...")

            # 基本信息选项卡
            try:
                basic_tab = self.create_basic_tab()
                self.tab_widget.addTab(basic_tab, "基本信息")
                print("基本信息选项卡创建成功")
            except Exception as e:
                print(f"创建基本信息选项卡失败: {str(e)}")
                import traceback

                traceback.print_exc()

            # 批发模式选项卡
            try:
                wholesale_tab = self.create_wholesale_tab()
                self.tab_widget.addTab(wholesale_tab, "批发模式")
                print("批发模式选项卡创建成功")
            except Exception as e:
                print(f"创建批发模式选项卡失败: {str(e)}")
                import traceback

                traceback.print_exc()

            # 代发模式选项卡
            try:
                dropship_tab = self.create_dropship_tab()
                self.tab_widget.addTab(dropship_tab, "代发模式")
                print("代发模式选项卡创建成功")
            except Exception as e:
                print(f"创建代发模式选项卡失败: {str(e)}")
                import traceback

                traceback.print_exc()

            # 图片管理选项卡
            try:
                image_tab = self.create_image_tab()
                self.tab_widget.addTab(image_tab, "图片管理")
                print("图片管理选项卡创建成功")
            except Exception as e:
                print(f"创建图片管理选项卡失败: {str(e)}")
                import traceback

                traceback.print_exc()

            # 计算结果选项卡
            try:
                calculation_tab = self.create_calculation_tab()
                self.tab_widget.addTab(calculation_tab, "成本计算")
                print("成本计算选项卡创建成功")
            except Exception as e:
                print(f"创建成本计算选项卡失败: {str(e)}")
                import traceback

                traceback.print_exc()

            # 智能抓取选项卡
            try:
                from .smart_scraper_tab import SmartScraperTab

                self.smart_scraper_tab = SmartScraperTab(self)
                self.tab_widget.addTab(self.smart_scraper_tab, "🤖 智能抓取")

                # 连接智能抓取的信号
                self.smart_scraper_tab.data_scraped.connect(self.apply_scraped_data)
                print("智能抓取选项卡创建成功")
            except Exception as e:
                print(f"创建智能抓取选项卡失败: {str(e)}")
                import traceback

                traceback.print_exc()

            # 商品详情选项卡（新增）
            try:
                product_details_tab = self.create_product_details_tab()
                self.tab_widget.addTab(product_details_tab, "📋 商品详情")
                print("商品详情选项卡创建成功")
            except Exception as e:
                print(f"创建商品详情选项卡失败: {str(e)}")
                import traceback

                traceback.print_exc()

            layout.addWidget(self.tab_widget)

            # 按钮
            button_box = QDialogButtonBox(
                QDialogButtonBox.StandardButton.Ok
                | QDialogButtonBox.StandardButton.Cancel
            )
            button_box.setStyleSheet(
                "QPushButton { font-size: 11px; padding: 8px 16px; }"
            )
            button_box.accepted.connect(self.accept)
            button_box.rejected.connect(self.reject)

            layout.addWidget(button_box)

            # 连接信号
            print("开始连接信号...")
            self.connect_signals()

            # 初始化模式显示
            print("开始初始化模式显示...")
            self.update_mode_visibility()

            # 如果是编辑模式，加载数据
            if self.is_edit_mode:
                print("开始加载货源数据...")
                try:
                    self.load_source_data()
                    print("货源数据加载成功")
                except Exception as e:
                    print(f"加载货源数据失败: {str(e)}")
                    import traceback

                    traceback.print_exc()

            # 设置焦点
            if hasattr(self, "name_edit") and self.name_edit:
                self.name_edit.setFocus()

            print("货源对话框初始化完成")

        except Exception as e:
            print(f"货源对话框初始化失败: {str(e)}")
            import traceback

            traceback.print_exc()
            # 显示错误信息但不阻止对话框显示
            QMessageBox.critical(
                self,
                "初始化错误",
                f"货源对话框初始化时发生错误: {str(e)}\n\n对话框将继续显示，但可能存在功能问题。",
            )

    def create_basic_tab(self):
        """创建基本信息选项卡"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)

        # 供应商名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入供应商名称")
        basic_layout.addRow("供应商名称*:", self.name_edit)

        # 产品名称
        self.product_name_edit = QLineEdit()
        self.product_name_edit.setPlaceholderText("请输入产品名称")
        basic_layout.addRow("产品名称:", self.product_name_edit)

        # 店铺信息
        self.shop_info_edit = QLineEdit()
        self.shop_info_edit.setPlaceholderText("如：金牌卖家、官方旗舰店等")
        basic_layout.addRow("店铺信息:", self.shop_info_edit)

        # 网址链接
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("https://...")
        basic_layout.addRow("网址链接:", self.url_edit)

        layout.addWidget(basic_group)

        # 兼容性参数组（用于信号连接和计算）
        compatibility_group = QGroupBox("兼容性参数")
        compatibility_layout = QFormLayout(compatibility_group)

        # 基本单价（用于兼容性计算）
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0, 999999.99)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" 元")
        self.price_spinbox.setToolTip("此价格与各模式价格自动同步，可在任一模式中修改")
        compatibility_layout.addRow("统一单价:", self.price_spinbox)

        # 基本库存数量
        self.quantity_spinbox = QSpinBox()
        self.quantity_spinbox.setRange(0, 999999)
        self.quantity_spinbox.setValue(1)
        self.quantity_spinbox.setToolTip(
            "此为兼容性参数，实际库存请在对应模式选项卡中设置"
        )
        compatibility_layout.addRow("基本库存数量:", self.quantity_spinbox)

        # 基本最小起批数量
        self.min_order_quantity_spinbox = QSpinBox()
        self.min_order_quantity_spinbox.setRange(1, 999999)
        self.min_order_quantity_spinbox.setValue(1)
        self.min_order_quantity_spinbox.setToolTip(
            "此为兼容性参数，实际起批数量请在对应模式选项卡中设置"
        )
        compatibility_layout.addRow(
            "基本最小起批数量:", self.min_order_quantity_spinbox
        )

        # 基本运费
        shipping_layout_h = QHBoxLayout()
        self.shipping_cost_spinbox = QDoubleSpinBox()
        self.shipping_cost_spinbox.setRange(0, 999999.99)
        self.shipping_cost_spinbox.setDecimals(2)
        self.shipping_cost_spinbox.setSuffix(" 元")
        self.shipping_cost_spinbox.setToolTip(
            "此为兼容性参数，实际运费请在对应模式选项卡中设置"
        )
        shipping_layout_h.addWidget(self.shipping_cost_spinbox)

        # 运费后缀
        self.shipping_suffix_combo = QComboBox()
        self.shipping_suffix_combo.addItems(["", "起", "+"])
        self.shipping_suffix_combo.setMaximumWidth(60)
        shipping_layout_h.addWidget(self.shipping_suffix_combo)

        shipping_widget = QWidget()
        shipping_widget.setLayout(shipping_layout_h)
        compatibility_layout.addRow("基本运费:", shipping_widget)

        # 基本发货地
        self.shipping_location_edit = QLineEdit()
        self.shipping_location_edit.setPlaceholderText("如：广东深圳")
        self.shipping_location_edit.setToolTip(
            "此为兼容性参数，实际发货地请在对应模式选项卡中设置"
        )
        compatibility_layout.addRow("基本发货地:", self.shipping_location_edit)

        # 基本销量
        self.sales_count_spinbox = QSpinBox()
        self.sales_count_spinbox.setRange(0, 999999999)
        self.sales_count_spinbox.setToolTip(
            "此为兼容性参数，实际销量请在对应模式选项卡中设置"
        )
        compatibility_layout.addRow("基本销量:", self.sales_count_spinbox)

        layout.addWidget(compatibility_group)

        # 模式选择组
        mode_group = QGroupBox("支持的模式")
        mode_layout = QVBoxLayout(mode_group)

        # 模式说明
        mode_desc = QLabel("请选择货源支持的模式：")
        mode_desc.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        mode_layout.addWidget(mode_desc)

        # 代发模式（默认选中）
        self.dropship_checkbox = QCheckBox("代发模式")
        self.dropship_checkbox.setChecked(True)
        self.dropship_checkbox.setToolTip("适合小批量订单，无需囤货，直接代发")
        mode_layout.addWidget(self.dropship_checkbox)

        # 批发模式
        self.wholesale_checkbox = QCheckBox("批发模式")
        self.wholesale_checkbox.setToolTip("适合大批量采购，支持批发价格阶梯和账期")
        mode_layout.addWidget(self.wholesale_checkbox)

        # 模式提示
        mode_hint = QLabel(
            "💡 可以同时选择两种模式，各模式的价格、库存等参数在对应选项卡中独立设置"
        )
        mode_hint.setStyleSheet(
            "color: #666; font-size: 10px; font-style: italic; margin-top: 5px;"
        )
        mode_hint.setWordWrap(True)
        mode_layout.addWidget(mode_hint)

        layout.addWidget(mode_group)

        # 状态信息组
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)

        # 状态选择说明
        status_desc = QLabel("请选择货源状态（可多选）:")
        status_desc.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        status_layout.addWidget(status_desc)

        # 状态选择区域
        status_checkboxes_layout = QGridLayout()

        # 创建四个状态复选框
        self.status_active_checkbox = QCheckBox("活跃")
        self.status_active_checkbox.setChecked(True)  # 默认选中
        status_checkboxes_layout.addWidget(self.status_active_checkbox, 0, 0)

        self.status_inactive_checkbox = QCheckBox("非活跃")
        status_checkboxes_layout.addWidget(self.status_inactive_checkbox, 0, 1)

        self.status_pending_checkbox = QCheckBox("待定")
        status_checkboxes_layout.addWidget(self.status_pending_checkbox, 1, 0)

        self.status_suspended_checkbox = QCheckBox("暂停")
        status_checkboxes_layout.addWidget(self.status_suspended_checkbox, 1, 1)

        # 存储状态值映射
        self.status_mapping = {
            "活跃": "active",
            "非活跃": "inactive",
            "待定": "pending",
            "暂停": "suspended",
        }
        self.reverse_status_mapping = {v: k for k, v in self.status_mapping.items()}

        status_layout.addLayout(status_checkboxes_layout)

        # 是否活跃（保持原有逻辑）
        self.is_active_checkbox = QCheckBox("货源活跃状态")
        self.is_active_checkbox.setChecked(True)
        status_layout.addWidget(self.is_active_checkbox)

        layout.addWidget(status_group)

        # 其他信息组
        other_group = QGroupBox("其他信息")
        other_layout = QVBoxLayout(other_group)

        # 联系方式
        contact_label = QLabel("联系方式:")
        other_layout.addWidget(contact_label)
        self.contact_info_edit = QTextEdit()
        self.contact_info_edit.setPlaceholderText(
            "请输入联系方式（电话、QQ、微信等）..."
        )
        self.contact_info_edit.setMaximumHeight(60)
        other_layout.addWidget(self.contact_info_edit)

        # 退货政策
        policy_label = QLabel("退货政策:")
        other_layout.addWidget(policy_label)
        self.return_policy_edit = QTextEdit()
        self.return_policy_edit.setPlaceholderText("请输入退货政策...")
        self.return_policy_edit.setMaximumHeight(60)
        other_layout.addWidget(self.return_policy_edit)

        # 备注信息
        notes_label = QLabel("备注信息:")
        other_layout.addWidget(notes_label)
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("请输入备注信息...")
        self.notes_edit.setMaximumHeight(80)
        other_layout.addWidget(self.notes_edit)

        layout.addWidget(other_group)

        # 快速图片管理组
        image_quick_group = QGroupBox("快速图片管理")
        image_quick_layout = QVBoxLayout(image_quick_group)

        # 图片管理说明
        image_desc = QLabel("管理此货源的图片资源")
        image_desc.setStyleSheet("color: #666; font-size: 11px; margin-bottom: 8px;")
        image_quick_layout.addWidget(image_desc)

        # 图片管理按钮
        image_management_btn = QPushButton("🖼️ 管理图片")
        image_management_btn.setMinimumHeight(35)
        image_management_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """
        )
        image_management_btn.clicked.connect(self.manage_source_images)
        image_quick_layout.addWidget(image_management_btn)

        layout.addWidget(image_quick_group)
        layout.addStretch()

        scroll_area.setWidget(widget)
        return scroll_area

    def create_wholesale_tab(self):
        """创建批发模式选项卡"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 批发模式说明
        info_label = QLabel("批发模式专属参数 - 仅在选择批发模式时生效")
        info_label.setStyleSheet(
            "font-weight: bold; color: #1976d2; margin-bottom: 10px;"
        )
        layout.addWidget(info_label)

        # 批发基本参数组
        wholesale_basic_group = QGroupBox("批发基本参数")
        wholesale_basic_layout = QFormLayout(wholesale_basic_group)

        # 批发单价
        self.wholesale_price_spinbox = QDoubleSpinBox()
        self.wholesale_price_spinbox.setRange(0, 999999.99)
        self.wholesale_price_spinbox.setDecimals(2)
        self.wholesale_price_spinbox.setSuffix(" 元")
        self.wholesale_price_spinbox.setToolTip("与基本信息中的统一单价同步")
        wholesale_basic_layout.addRow("批发单价*:", self.wholesale_price_spinbox)

        # 批发库存数量
        self.wholesale_quantity_spinbox = QSpinBox()
        self.wholesale_quantity_spinbox.setRange(0, 999999)
        self.wholesale_quantity_spinbox.setValue(1)
        wholesale_basic_layout.addRow("批发库存数量:", self.wholesale_quantity_spinbox)

        # 批发最小起批数量
        self.wholesale_min_order_quantity_spinbox = QSpinBox()
        self.wholesale_min_order_quantity_spinbox.setRange(1, 999999)
        self.wholesale_min_order_quantity_spinbox.setValue(1)
        wholesale_basic_layout.addRow(
            "批发最小起批数量*:", self.wholesale_min_order_quantity_spinbox
        )

        # 批发销量
        self.wholesale_sales_count_spinbox = QSpinBox()
        self.wholesale_sales_count_spinbox.setRange(0, 999999999)
        wholesale_basic_layout.addRow("批发销量:", self.wholesale_sales_count_spinbox)

        layout.addWidget(wholesale_basic_group)

        # 批发运费信息组
        wholesale_shipping_group = QGroupBox("批发运费信息")
        wholesale_shipping_layout = QFormLayout(wholesale_shipping_group)

        # 批发运费
        wholesale_shipping_layout_h = QHBoxLayout()
        self.wholesale_shipping_cost_spinbox = QDoubleSpinBox()
        self.wholesale_shipping_cost_spinbox.setRange(0, 999999.99)
        self.wholesale_shipping_cost_spinbox.setDecimals(2)
        self.wholesale_shipping_cost_spinbox.setSuffix(" 元")
        wholesale_shipping_layout_h.addWidget(self.wholesale_shipping_cost_spinbox)

        # 批发运费后缀
        self.wholesale_shipping_suffix_combo = QComboBox()
        self.wholesale_shipping_suffix_combo.addItems(["", "起", "+"])
        self.wholesale_shipping_suffix_combo.setMaximumWidth(60)
        wholesale_shipping_layout_h.addWidget(self.wholesale_shipping_suffix_combo)

        wholesale_shipping_widget = QWidget()
        wholesale_shipping_widget.setLayout(wholesale_shipping_layout_h)
        wholesale_shipping_layout.addRow("批发运费:", wholesale_shipping_widget)

        # 批发发货地
        self.wholesale_shipping_location_edit = QLineEdit()
        self.wholesale_shipping_location_edit.setPlaceholderText("如：广东深圳")
        wholesale_shipping_layout.addRow(
            "批发发货地:", self.wholesale_shipping_location_edit
        )

        layout.addWidget(wholesale_shipping_group)

        # 批发政策组
        wholesale_policy_group = QGroupBox("批发政策")
        wholesale_policy_layout = QFormLayout(wholesale_policy_group)

        # 批发账期
        self.wholesale_payment_terms_spinbox = QSpinBox()
        self.wholesale_payment_terms_spinbox.setRange(0, 365)
        self.wholesale_payment_terms_spinbox.setSuffix(" 天")
        wholesale_policy_layout.addRow(
            "批发账期:", self.wholesale_payment_terms_spinbox
        )

        # 批发返点比例
        self.wholesale_rebate_rate_spinbox = QDoubleSpinBox()
        self.wholesale_rebate_rate_spinbox.setRange(0, 100)
        self.wholesale_rebate_rate_spinbox.setDecimals(2)
        self.wholesale_rebate_rate_spinbox.setSuffix(" %")
        wholesale_policy_layout.addRow(
            "批发返点比例:", self.wholesale_rebate_rate_spinbox
        )

        # 批发价格阶梯
        self.wholesale_price_tiers_edit = QLineEdit()
        self.wholesale_price_tiers_edit.setPlaceholderText(
            "如：100件以上8折，500件以上7折"
        )
        wholesale_policy_layout.addRow("批发价格阶梯:", self.wholesale_price_tiers_edit)

        layout.addWidget(wholesale_policy_group)

        # 业务数据组
        business_group = QGroupBox("业务数据")
        business_layout = QFormLayout(business_group)

        # 揽收率
        self.pickup_rate_24h_spinbox = QDoubleSpinBox()
        self.pickup_rate_24h_spinbox.setRange(0, 100)
        self.pickup_rate_24h_spinbox.setDecimals(1)
        self.pickup_rate_24h_spinbox.setSuffix(" %")
        business_layout.addRow("24小时揽收率:", self.pickup_rate_24h_spinbox)

        self.pickup_rate_48h_spinbox = QDoubleSpinBox()
        self.pickup_rate_48h_spinbox.setRange(0, 100)
        self.pickup_rate_48h_spinbox.setDecimals(1)
        self.pickup_rate_48h_spinbox.setSuffix(" %")
        business_layout.addRow("48小时揽收率:", self.pickup_rate_48h_spinbox)

        # 分销商数据
        self.downstream_stores_spinbox = QSpinBox()
        self.downstream_stores_spinbox.setRange(0, 999999)
        business_layout.addRow("下游铺货数:", self.downstream_stores_spinbox)

        self.distributor_count_spinbox = QSpinBox()
        self.distributor_count_spinbox.setRange(0, 999999)
        business_layout.addRow("分销商数:", self.distributor_count_spinbox)

        layout.addWidget(business_group)

        # 商品信息组
        product_group = QGroupBox("商品信息")
        product_layout = QFormLayout(product_group)

        # 商品发布时间
        self.product_publish_time_edit = QDateTimeEdit()
        self.product_publish_time_edit.setDateTime(QDateTime.currentDateTime())
        self.product_publish_time_edit.setCalendarPopup(True)
        product_layout.addRow("商品发布时间:", self.product_publish_time_edit)

        # 可分销商品数
        self.available_products_spinbox = QSpinBox()
        self.available_products_spinbox.setRange(0, 999999)
        product_layout.addRow("可分销商品数:", self.available_products_spinbox)

        # 月上新商品数
        self.monthly_new_products_spinbox = QSpinBox()
        self.monthly_new_products_spinbox.setRange(0, 999999)
        product_layout.addRow("月上新商品数:", self.monthly_new_products_spinbox)

        layout.addWidget(product_group)

        layout.addStretch()

        scroll_area.setWidget(widget)
        return scroll_area

    def create_dropship_tab(self):
        """创建代发模式选项卡"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 代发模式说明
        info_label = QLabel("代发模式专属参数 - 仅在选择代发模式时生效")
        info_label.setStyleSheet(
            "font-weight: bold; color: #ff9800; margin-bottom: 10px;"
        )
        layout.addWidget(info_label)

        # 代发基本参数组
        dropship_basic_group = QGroupBox("代发基本参数")
        dropship_basic_layout = QFormLayout(dropship_basic_group)

        # 代发单价
        self.dropship_price_spinbox = QDoubleSpinBox()
        self.dropship_price_spinbox.setRange(0, 999999.99)
        self.dropship_price_spinbox.setDecimals(2)
        self.dropship_price_spinbox.setSuffix(" 元")
        self.dropship_price_spinbox.setToolTip("与基本信息中的统一单价同步")
        dropship_basic_layout.addRow("代发单价*:", self.dropship_price_spinbox)

        # 代发库存数量
        self.dropship_quantity_spinbox = QSpinBox()
        self.dropship_quantity_spinbox.setRange(0, 999999)
        self.dropship_quantity_spinbox.setValue(1)
        dropship_basic_layout.addRow("代发库存数量:", self.dropship_quantity_spinbox)

        # 代发最小起批数量
        self.dropship_min_quantity_spinbox = QSpinBox()
        self.dropship_min_quantity_spinbox.setRange(1, 999999)
        self.dropship_min_quantity_spinbox.setValue(1)
        dropship_basic_layout.addRow(
            "代发最小起批数量*:", self.dropship_min_quantity_spinbox
        )

        # 代发销量
        self.dropship_sales_count_spinbox = QSpinBox()
        self.dropship_sales_count_spinbox.setRange(0, 999999999)
        dropship_basic_layout.addRow("代发销量:", self.dropship_sales_count_spinbox)

        layout.addWidget(dropship_basic_group)

        # 代发运费信息组
        dropship_shipping_group = QGroupBox("代发运费信息")
        dropship_shipping_layout = QFormLayout(dropship_shipping_group)

        # 代发运费
        dropship_shipping_layout_h = QHBoxLayout()
        self.dropship_shipping_cost_spinbox = QDoubleSpinBox()
        self.dropship_shipping_cost_spinbox.setRange(0, 999999.99)
        self.dropship_shipping_cost_spinbox.setDecimals(2)
        self.dropship_shipping_cost_spinbox.setSuffix(" 元")
        dropship_shipping_layout_h.addWidget(self.dropship_shipping_cost_spinbox)

        # 代发运费后缀
        self.dropship_shipping_suffix_combo = QComboBox()
        self.dropship_shipping_suffix_combo.addItems(["", "起", "+"])
        self.dropship_shipping_suffix_combo.setMaximumWidth(60)
        dropship_shipping_layout_h.addWidget(self.dropship_shipping_suffix_combo)

        dropship_shipping_widget = QWidget()
        dropship_shipping_widget.setLayout(dropship_shipping_layout_h)
        dropship_shipping_layout.addRow("代发运费:", dropship_shipping_widget)

        # 代发发货地
        self.dropship_shipping_location_edit = QLineEdit()
        self.dropship_shipping_location_edit.setPlaceholderText("如：广东深圳")
        dropship_shipping_layout.addRow(
            "代发发货地:", self.dropship_shipping_location_edit
        )

        layout.addWidget(dropship_shipping_group)

        # 代发服务设置组
        dropship_service_group = QGroupBox("代发服务设置")
        dropship_service_layout = QFormLayout(dropship_service_group)

        # 代发服务费
        self.dropship_service_fee_spinbox = QDoubleSpinBox()
        self.dropship_service_fee_spinbox.setRange(0, 999999.99)
        self.dropship_service_fee_spinbox.setDecimals(2)
        self.dropship_service_fee_spinbox.setSuffix(" 元")
        dropship_service_layout.addRow("代发服务费:", self.dropship_service_fee_spinbox)

        # 代发处理时间
        self.dropship_processing_time_spinbox = QSpinBox()
        self.dropship_processing_time_spinbox.setRange(0, 999)
        self.dropship_processing_time_spinbox.setSuffix(" 小时")
        dropship_service_layout.addRow(
            "代发处理时间:", self.dropship_processing_time_spinbox
        )

        # 代发包装要求
        self.dropship_packaging_edit = QLineEdit()
        self.dropship_packaging_edit.setPlaceholderText("如：无标签包装、定制包装等")
        dropship_service_layout.addRow("包装要求:", self.dropship_packaging_edit)

        # 库存同步方式
        self.dropship_inventory_sync_combo = QComboBox()
        self.dropship_inventory_sync_combo.addItems(
            ["实时同步", "每日同步", "手动同步", "不同步"]
        )
        dropship_service_layout.addRow(
            "库存同步方式:", self.dropship_inventory_sync_combo
        )

        layout.addWidget(dropship_service_group)

        # 代发业务数据组
        dropship_business_group = QGroupBox("代发业务数据")
        dropship_business_layout = QFormLayout(dropship_business_group)

        # 月代发订单数
        self.monthly_dropship_orders_spinbox = QSpinBox()
        self.monthly_dropship_orders_spinbox.setRange(0, 999999)
        dropship_business_layout.addRow(
            "月代发订单数:", self.monthly_dropship_orders_spinbox
        )

        # 支持地区
        self.dropship_support_regions_edit = QLineEdit()
        self.dropship_support_regions_edit.setPlaceholderText("如：全国、华东、华南等")
        dropship_business_layout.addRow("支持地区:", self.dropship_support_regions_edit)

        layout.addWidget(dropship_business_group)

        layout.addStretch()

        scroll_area.setWidget(widget)
        return scroll_area

    def create_image_tab(self):
        """创建图片管理选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 图片管理说明
        info_label = QLabel("货源图片管理 - 独立于产品图片的图片系统")
        info_label.setStyleSheet(
            "font-weight: bold; color: #4caf50; margin-bottom: 10px;"
        )
        layout.addWidget(info_label)

        # 图片列表组
        image_group = QGroupBox("图片列表")
        image_layout = QVBoxLayout(image_group)

        # 图片操作按钮
        button_layout = QHBoxLayout()

        # 添加图片按钮
        add_image_btn = QPushButton("选择图片文件")
        add_image_btn.clicked.connect(self.select_image_files)
        button_layout.addWidget(add_image_btn)

        # 使用ImageViewer组件的管理图片按钮
        manage_images_btn = QPushButton("管理图片")
        manage_images_btn.clicked.connect(self.manage_source_images)
        button_layout.addWidget(manage_images_btn)

        button_layout.addStretch()
        image_layout.addLayout(button_layout)

        # 图片列表
        self.image_list_widget = QListWidget()
        self.image_list_widget.setMaximumHeight(200)
        self.image_list_widget.setSelectionMode(
            QListWidget.SelectionMode.ExtendedSelection
        )
        image_layout.addWidget(self.image_list_widget)

        # 图片操作按钮
        image_btn_layout = QHBoxLayout()
        remove_image_btn = QPushButton("移除选中")
        remove_image_btn.clicked.connect(self.remove_selected_image)
        image_btn_layout.addWidget(remove_image_btn)

        clear_images_btn = QPushButton("清空所有")
        clear_images_btn.clicked.connect(self.clear_all_images)
        image_btn_layout.addWidget(clear_images_btn)

        image_btn_layout.addStretch()
        image_layout.addLayout(image_btn_layout)

        layout.addWidget(image_group)

        # 图片预览
        preview_group = QGroupBox("图片预览")
        preview_layout = QVBoxLayout(preview_group)

        self.image_preview_label = QLabel("选择图片进行预览")
        self.image_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview_label.setMinimumHeight(200)
        self.image_preview_label.setStyleSheet(
            "border: 1px dashed #ccc; background-color: #f9f9f9;"
        )
        preview_layout.addWidget(self.image_preview_label)

        layout.addWidget(preview_group)

        layout.addStretch()

        return widget

    def create_calculation_tab(self):
        """创建计算结果选项卡"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 计算结果说明
        info_label = QLabel("成本计算结果 - 根据所选模式自动计算")
        info_label.setStyleSheet(
            "font-weight: bold; color: #9c27b0; margin-bottom: 10px;"
        )
        layout.addWidget(info_label)

        # 代发模式计算组
        self.dropship_calc_group = QGroupBox("🚚 代发模式计算")
        dropship_calc_layout = QFormLayout(self.dropship_calc_group)

        # 代发模式 - 单项成本
        self.dropship_unit_cost_label = QLabel("¥0.00")
        self.dropship_unit_cost_label.setStyleSheet(
            "color: #ff9800; font-weight: bold; font-size: 12px;"
        )
        dropship_calc_layout.addRow("单项成本:", self.dropship_unit_cost_label)

        # 代发模式 - 单项成本（含运费）
        self.dropship_unit_cost_with_shipping_label = QLabel("¥0.00")
        self.dropship_unit_cost_with_shipping_label.setStyleSheet(
            "color: #ff9800; font-weight: bold; font-size: 12px;"
        )
        dropship_calc_layout.addRow(
            "单项成本(含运费):", self.dropship_unit_cost_with_shipping_label
        )

        # 代发模式 - 预估利润
        self.dropship_profit_label = QLabel("¥0.00")
        self.dropship_profit_label.setStyleSheet(
            "color: #ff9800; font-weight: bold; font-size: 12px;"
        )
        dropship_calc_layout.addRow("预估利润:", self.dropship_profit_label)

        layout.addWidget(self.dropship_calc_group)

        # 批发模式计算组
        self.wholesale_calc_group = QGroupBox("📦 批发模式计算")
        wholesale_calc_layout = QFormLayout(self.wholesale_calc_group)

        # 批发模式 - 单项成本
        self.wholesale_unit_cost_label = QLabel("¥0.00")
        self.wholesale_unit_cost_label.setStyleSheet(
            "color: #1976d2; font-weight: bold; font-size: 12px;"
        )
        wholesale_calc_layout.addRow("单项成本:", self.wholesale_unit_cost_label)

        # 批发模式 - 单项成本（含运费）
        self.wholesale_unit_cost_with_shipping_label = QLabel("¥0.00")
        self.wholesale_unit_cost_with_shipping_label.setStyleSheet(
            "color: #1976d2; font-weight: bold; font-size: 12px;"
        )
        wholesale_calc_layout.addRow(
            "单项成本(含运费):", self.wholesale_unit_cost_with_shipping_label
        )

        # 批发模式 - 最小起批数量
        self.wholesale_min_order_info_label = QLabel("1件")
        self.wholesale_min_order_info_label.setStyleSheet(
            "color: #1976d2; font-weight: bold; font-size: 12px;"
        )
        wholesale_calc_layout.addRow(
            "最小起批数量:", self.wholesale_min_order_info_label
        )

        # 批发模式 - 最小起批总成本
        self.wholesale_min_order_total_cost_label = QLabel("¥0.00")
        self.wholesale_min_order_total_cost_label.setStyleSheet(
            "color: #1976d2; font-weight: bold; font-size: 12px;"
        )
        wholesale_calc_layout.addRow(
            "最小起批总成本:", self.wholesale_min_order_total_cost_label
        )

        # 批发模式 - 预估利润
        self.wholesale_profit_label = QLabel("¥0.00")
        self.wholesale_profit_label.setStyleSheet(
            "color: #1976d2; font-weight: bold; font-size: 12px;"
        )
        wholesale_calc_layout.addRow("预估利润:", self.wholesale_profit_label)

        layout.addWidget(self.wholesale_calc_group)

        # 基础信息组
        basic_info_group = QGroupBox("📋 基础信息")
        basic_info_layout = QFormLayout(basic_info_group)

        # 商品单价
        self.base_price_label = QLabel("¥0.00")
        self.base_price_label.setStyleSheet(
            "color: #4caf50; font-weight: bold; font-size: 12px;"
        )
        basic_info_layout.addRow("商品单价:", self.base_price_label)

        # 运费
        self.shipping_cost_label = QLabel("¥0.00")
        self.shipping_cost_label.setStyleSheet(
            "color: #4caf50; font-weight: bold; font-size: 12px;"
        )
        basic_info_layout.addRow("运费:", self.shipping_cost_label)

        # 库存数量
        self.stock_quantity_label = QLabel("1件")
        self.stock_quantity_label.setStyleSheet(
            "color: #4caf50; font-weight: bold; font-size: 12px;"
        )
        basic_info_layout.addRow("库存数量:", self.stock_quantity_label)

        layout.addWidget(basic_info_group)

        layout.addStretch()

        scroll_area.setWidget(widget)
        return scroll_area

    def create_product_details_tab(self):
        """创建商品详情选项卡（包含AI分析字段）"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 商品属性组
        product_attrs_group = QGroupBox("🏷️ 商品属性")
        attrs_layout = QFormLayout(product_attrs_group)

        # 材质
        self.material_edit = QLineEdit()
        self.material_edit.setPlaceholderText("请输入商品材质")
        attrs_layout.addRow("材质:", self.material_edit)

        # 产品类别
        self.product_category_edit = QLineEdit()
        self.product_category_edit.setPlaceholderText("请输入产品类别")
        attrs_layout.addRow("产品类别:", self.product_category_edit)

        # 货号
        self.product_code_edit = QLineEdit()
        self.product_code_edit.setPlaceholderText("请输入商品货号")
        attrs_layout.addRow("货号:", self.product_code_edit)

        # 净重
        self.net_weight_spinbox = QDoubleSpinBox()
        self.net_weight_spinbox.setRange(0, 999999.99)
        self.net_weight_spinbox.setDecimals(2)
        self.net_weight_spinbox.setSuffix(" 克")
        attrs_layout.addRow("净重:", self.net_weight_spinbox)

        # 毛重
        self.gross_weight_spinbox = QDoubleSpinBox()
        self.gross_weight_spinbox.setRange(0, 999999.99)
        self.gross_weight_spinbox.setDecimals(2)
        self.gross_weight_spinbox.setSuffix(" 克")
        attrs_layout.addRow("毛重:", self.gross_weight_spinbox)

        # 品牌
        self.brand_edit = QLineEdit()
        self.brand_edit.setPlaceholderText("请输入品牌名称")
        attrs_layout.addRow("品牌:", self.brand_edit)

        # 产地
        self.origin_edit = QLineEdit()
        self.origin_edit.setPlaceholderText("请输入产地")
        attrs_layout.addRow("产地:", self.origin_edit)

        # 是否进口
        self.is_import_checkbox = QCheckBox("是")
        attrs_layout.addRow("是否进口:", self.is_import_checkbox)

        # 是否专利货源
        self.is_patent_checkbox = QCheckBox("是")
        attrs_layout.addRow("专利货源:", self.is_patent_checkbox)

        layout.addWidget(product_attrs_group)

        # 包装信息组
        packaging_group = QGroupBox("📦 包装信息")
        packaging_layout = QFormLayout(packaging_group)

        # 外箱尺寸
        self.box_dimensions_edit = QLineEdit()
        self.box_dimensions_edit.setPlaceholderText("如：30×20×15cm")
        packaging_layout.addRow("外箱尺寸:", self.box_dimensions_edit)

        # 装箱数量
        self.box_quantity_spinbox = QSpinBox()
        self.box_quantity_spinbox.setRange(0, 999999)
        self.box_quantity_spinbox.setSuffix(" 个")
        packaging_layout.addRow("装箱数量:", self.box_quantity_spinbox)

        # 箱子重量
        self.box_weight_spinbox = QDoubleSpinBox()
        self.box_weight_spinbox.setRange(0, 999999.99)
        self.box_weight_spinbox.setDecimals(2)
        self.box_weight_spinbox.setSuffix(" kg")
        packaging_layout.addRow("箱子重量:", self.box_weight_spinbox)

        layout.addWidget(packaging_group)

        # 销售数据组
        sales_group = QGroupBox("📊 销售数据")
        sales_layout = QFormLayout(sales_group)

        # 年销量
        self.annual_sales_spinbox = QSpinBox()
        self.annual_sales_spinbox.setRange(0, 999999999)
        sales_layout.addRow("年销量:", self.annual_sales_spinbox)

        # 评价数量
        self.review_count_spinbox = QSpinBox()
        self.review_count_spinbox.setRange(0, 999999)
        sales_layout.addRow("评价数量:", self.review_count_spinbox)

        # 已售数量
        self.sold_quantity_spinbox = QSpinBox()
        self.sold_quantity_spinbox.setRange(0, 999999999)
        sales_layout.addRow("已售数量:", self.sold_quantity_spinbox)

        # 评价标签
        self.review_tags_edit = QLineEdit()
        self.review_tags_edit.setPlaceholderText("如：价格便宜(6), 质量好(10)")
        sales_layout.addRow("评价标签:", self.review_tags_edit)

        layout.addWidget(sales_group)

        # 代发服务扩展数据组
        dropship_ext_group = QGroupBox("🚚 代发服务扩展")
        dropship_ext_layout = QFormLayout(dropship_ext_group)

        # 退货率
        self.return_rate_spinbox = QDoubleSpinBox()
        self.return_rate_spinbox.setRange(0, 100)
        self.return_rate_spinbox.setDecimals(1)
        self.return_rate_spinbox.setSuffix(" %")
        dropship_ext_layout.addRow("退货率:", self.return_rate_spinbox)

        # 商品发布时间
        self.product_publish_datetime = QDateTimeEdit()
        self.product_publish_datetime.setDisplayFormat("yyyy-MM-dd hh:mm")
        self.product_publish_datetime.setCalendarPopup(True)
        dropship_ext_layout.addRow("发布时间:", self.product_publish_datetime)

        layout.addWidget(dropship_ext_group)

        # 服务保障组
        service_group = QGroupBox("🛡️ 服务保障")
        service_layout = QFormLayout(service_group)

        # 发货承诺
        self.shipping_promise_edit = QLineEdit()
        self.shipping_promise_edit.setPlaceholderText("如：48小时内发货")
        service_layout.addRow("发货承诺:", self.shipping_promise_edit)

        # 支持快递
        self.supported_couriers_edit = QLineEdit()
        self.supported_couriers_edit.setPlaceholderText("如：顺丰、圆通、申通等")
        service_layout.addRow("支持快递:", self.supported_couriers_edit)

        # 服务保障
        self.service_guarantees_edit = QTextEdit()
        self.service_guarantees_edit.setMaximumHeight(80)
        self.service_guarantees_edit.setPlaceholderText("请输入服务保障项目，每行一项")
        service_layout.addRow("服务保障:", self.service_guarantees_edit)

        layout.addWidget(service_group)

        # 规格变体组
        variant_group = QGroupBox("🎨 规格变体")
        variant_layout = QFormLayout(variant_group)

        # 总规格数量
        self.total_variants_spinbox = QSpinBox()
        self.total_variants_spinbox.setRange(0, 9999)
        variant_layout.addRow("总规格数:", self.total_variants_spinbox)

        # 颜色规格
        self.color_variants_edit = QLineEdit()
        self.color_variants_edit.setPlaceholderText("如：红色,蓝色,绿色")
        variant_layout.addRow("颜色规格:", self.color_variants_edit)

        # 款式类型
        self.variant_types_edit = QLineEdit()
        self.variant_types_edit.setPlaceholderText("如：基础款,豪华款,限量款")
        variant_layout.addRow("款式类型:", self.variant_types_edit)

        layout.addWidget(variant_group)

        layout.addStretch()

        scroll_area.setWidget(widget)
        return scroll_area

    def connect_signals(self):
        """连接信号"""
        try:
            # 连接计算相关的信号 - 基本控件
            if hasattr(self, "price_spinbox") and self.price_spinbox:
                self.price_spinbox.valueChanged.connect(self.update_calculations)
            if hasattr(self, "quantity_spinbox") and self.quantity_spinbox:
                self.quantity_spinbox.valueChanged.connect(self.update_calculations)
            if (
                hasattr(self, "min_order_quantity_spinbox")
                and self.min_order_quantity_spinbox
            ):
                self.min_order_quantity_spinbox.valueChanged.connect(
                    self.update_calculations
                )
            if hasattr(self, "shipping_cost_spinbox") and self.shipping_cost_spinbox:
                self.shipping_cost_spinbox.valueChanged.connect(
                    self.update_calculations
                )

            # 连接批发模式专属字段信号
            if (
                hasattr(self, "wholesale_min_order_quantity_spinbox")
                and self.wholesale_min_order_quantity_spinbox
            ):
                self.wholesale_min_order_quantity_spinbox.valueChanged.connect(
                    self.update_calculations
                )
            if (
                hasattr(self, "wholesale_rebate_rate_spinbox")
                and self.wholesale_rebate_rate_spinbox
            ):
                self.wholesale_rebate_rate_spinbox.valueChanged.connect(
                    self.update_calculations
                )
            if (
                hasattr(self, "wholesale_price_spinbox")
                and self.wholesale_price_spinbox
            ):
                self.wholesale_price_spinbox.valueChanged.connect(
                    self.update_calculations
                )
                # 连接价格同步信号
                self.wholesale_price_spinbox.valueChanged.connect(
                    self.sync_price_from_wholesale
                )
            if (
                hasattr(self, "wholesale_quantity_spinbox")
                and self.wholesale_quantity_spinbox
            ):
                self.wholesale_quantity_spinbox.valueChanged.connect(
                    self.update_calculations
                )
            if (
                hasattr(self, "wholesale_shipping_cost_spinbox")
                and self.wholesale_shipping_cost_spinbox
            ):
                self.wholesale_shipping_cost_spinbox.valueChanged.connect(
                    self.update_calculations
                )

            # 连接代发模式专属字段信号
            if (
                hasattr(self, "dropship_service_fee_spinbox")
                and self.dropship_service_fee_spinbox
            ):
                self.dropship_service_fee_spinbox.valueChanged.connect(
                    self.update_calculations
                )
            if hasattr(self, "dropship_price_spinbox") and self.dropship_price_spinbox:
                self.dropship_price_spinbox.valueChanged.connect(
                    self.update_calculations
                )
                # 连接价格同步信号
                self.dropship_price_spinbox.valueChanged.connect(
                    self.sync_price_from_dropship
                )
            if (
                hasattr(self, "dropship_quantity_spinbox")
                and self.dropship_quantity_spinbox
            ):
                self.dropship_quantity_spinbox.valueChanged.connect(
                    self.update_calculations
                )
            if (
                hasattr(self, "dropship_min_quantity_spinbox")
                and self.dropship_min_quantity_spinbox
            ):
                self.dropship_min_quantity_spinbox.valueChanged.connect(
                    self.update_calculations
                )
            if (
                hasattr(self, "dropship_shipping_cost_spinbox")
                and self.dropship_shipping_cost_spinbox
            ):
                self.dropship_shipping_cost_spinbox.valueChanged.connect(
                    self.update_calculations
                )

            # 连接模式切换信号
            if hasattr(self, "wholesale_checkbox") and self.wholesale_checkbox:
                self.wholesale_checkbox.toggled.connect(self.update_mode_visibility)
                self.wholesale_checkbox.toggled.connect(self.update_calculations)
            if hasattr(self, "dropship_checkbox") and self.dropship_checkbox:
                self.dropship_checkbox.toggled.connect(self.update_mode_visibility)
                self.dropship_checkbox.toggled.connect(self.update_calculations)

            # 连接图片列表信号
            if hasattr(self, "image_list_widget") and self.image_list_widget:
                self.image_list_widget.currentRowChanged.connect(
                    self.preview_selected_image
                )

            print("信号连接完成")

        except Exception as e:
            print(f"connect_signals() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            # 不要在这里抛出异常，让对话框继续初始化

    def sync_price_from_dropship(self, value):
        """从代发价格同步到基本价格"""
        try:
            if hasattr(self, "price_spinbox") and self.price_spinbox:
                # 临时断开信号连接，避免循环更新
                self.price_spinbox.blockSignals(True)
                self.price_spinbox.setValue(value)
                self.price_spinbox.blockSignals(False)

                # 同步批发价格（如果批发模式被启用）
                if (
                    hasattr(self, "wholesale_price_spinbox")
                    and self.wholesale_price_spinbox
                ):
                    self.wholesale_price_spinbox.blockSignals(True)
                    self.wholesale_price_spinbox.setValue(value)
                    self.wholesale_price_spinbox.blockSignals(False)
        except Exception as e:
            print(f"sync_price_from_dropship() 发生异常: {str(e)}")

    def sync_price_from_wholesale(self, value):
        """从批发价格同步到基本价格"""
        try:
            if hasattr(self, "price_spinbox") and self.price_spinbox:
                # 临时断开信号连接，避免循环更新
                self.price_spinbox.blockSignals(True)
                self.price_spinbox.setValue(value)
                self.price_spinbox.blockSignals(False)

                # 同步代发价格（如果代发模式被启用）
                if (
                    hasattr(self, "dropship_price_spinbox")
                    and self.dropship_price_spinbox
                ):
                    self.dropship_price_spinbox.blockSignals(True)
                    self.dropship_price_spinbox.setValue(value)
                    self.dropship_price_spinbox.blockSignals(False)
        except Exception as e:
            print(f"sync_price_from_wholesale() 发生异常: {str(e)}")

    def update_mode_visibility(self):
        """更新模式相关选项卡和计算结果的可见性"""
        try:
            # 获取模式状态
            wholesale_enabled = (
                getattr(self, "wholesale_checkbox", None)
                and self.wholesale_checkbox.isChecked()
            )
            dropship_enabled = (
                getattr(self, "dropship_checkbox", None)
                and self.dropship_checkbox.isChecked()
            )

            # 更新选项卡的启用状态
            if hasattr(self, "tab_widget") and self.tab_widget:
                self.tab_widget.setTabEnabled(1, wholesale_enabled)  # 批发模式选项卡
                self.tab_widget.setTabEnabled(2, dropship_enabled)  # 代发模式选项卡

                # 更新选项卡标题样式
                wholesale_title = (
                    "批发模式" if wholesale_enabled else "批发模式（未启用）"
                )
                dropship_title = (
                    "代发模式" if dropship_enabled else "代发模式（未启用）"
                )
                self.tab_widget.setTabText(1, wholesale_title)
                self.tab_widget.setTabText(2, dropship_title)

                # 如果当前选项卡被禁用，切换到基本信息选项卡
                current_index = self.tab_widget.currentIndex()
                if (current_index == 1 and not wholesale_enabled) or (
                    current_index == 2 and not dropship_enabled
                ):
                    self.tab_widget.setCurrentIndex(0)

            # 更新计算结果组的可见性
            if hasattr(self, "wholesale_calc_group") and self.wholesale_calc_group:
                self.wholesale_calc_group.setVisible(wholesale_enabled)
            if hasattr(self, "dropship_calc_group") and self.dropship_calc_group:
                self.dropship_calc_group.setVisible(dropship_enabled)

            print(
                f"模式可见性更新完成: 批发={wholesale_enabled}, 代发={dropship_enabled}"
            )

        except Exception as e:
            print(f"update_mode_visibility() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            # 不要在这里抛出异常，让对话框继续运行

    def select_image_files(self):
        """选择图片文件"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        file_dialog.setNameFilter("图片文件 (*.png *.jpg *.jpeg *.gif *.bmp *.tiff)")
        file_dialog.setViewMode(QFileDialog.ViewMode.Detail)

        if file_dialog.exec():
            file_paths = file_dialog.selectedFiles()
            for file_path in file_paths:
                if file_path not in [
                    self.image_list_widget.item(i).text()
                    for i in range(self.image_list_widget.count())
                ]:
                    item = QListWidgetItem(file_path)
                    item.setToolTip(file_path)  # 设置悬停提示
                    self.image_list_widget.addItem(item)
                else:
                    QMessageBox.warning(
                        self, "重复图片", f"图片已存在：{Path(file_path).name}"
                    )

    def manage_source_images(self):
        """管理货源图片"""
        if not self.source or not self.source.id:
            QMessageBox.warning(self, "提示", "请先保存货源后再管理图片")
            return

        print("=== manage_source_images: 开始创建图片管理对话框 ===")
        # 创建货源图片管理对话框
        dialog = SourceImageManagementDialog(self.source, self.product_service, self)
        print("=== manage_source_images: 图片管理对话框创建完成，开始执行 ===")

        result = dialog.exec()
        print(f"=== manage_source_images: 图片管理对话框执行结果: {result} ===")

        if result == QDialog.DialogCode.Accepted:
            print("=== manage_source_images: 开始调用refresh_image_list ===")
            # 更新图片列表显示
            self.refresh_image_list()
            print("=== manage_source_images: refresh_image_list 调用完成 ===")
        else:
            print("=== manage_source_images: 用户取消了图片管理对话框 ===")

        print("=== manage_source_images: 方法执行完成 ===")

    def refresh_image_list(self):
        """刷新图片列表显示"""
        print("=== refresh_image_list: 开始刷新图片列表 ===")

        if self.source and hasattr(self.source, "image_urls"):
            print(
                f"refresh_image_list: 货源有图片数据，共{len(self.source.image_urls)}张图片"
            )

            self.image_list_widget.clear()
            print("refresh_image_list: 已清空图片列表UI")

            for i, image_path in enumerate(self.source.image_urls):
                if image_path:  # 只添加非空路径
                    print(f"  处理图片 {i+1}: {image_path}")

                    # 检查文件是否存在
                    path_exists = Path(image_path).exists()
                    print(f"    文件存在: {path_exists}")

                    item = QListWidgetItem(
                        Path(image_path).name if path_exists else image_path
                    )
                    item.setToolTip(image_path)
                    self.image_list_widget.addItem(item)
                    print(f"    已添加到UI列表")
                else:
                    print(f"  跳过空图片路径 {i+1}")

            print(
                f"refresh_image_list: UI列表更新完成，共添加{self.image_list_widget.count()}项"
            )
        else:
            print("refresh_image_list: 货源没有图片数据或image_urls属性不存在")

        print("=== refresh_image_list: 刷新图片列表完成 ===")

    def remove_selected_image(self):
        """移除选中的图片"""
        selected_items = self.image_list_widget.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要删除的图片")
            return

        for item in selected_items:
            row = self.image_list_widget.row(item)
            self.image_list_widget.takeItem(row)

        # 如果没有图片了，重置预览
        if self.image_list_widget.count() == 0:
            self.image_preview_label.setText("选择图片进行预览")

    def clear_all_images(self):
        """清空所有图片"""
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空所有图片吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )
        if reply == QMessageBox.StandardButton.Yes:
            self.image_list_widget.clear()
            self.image_preview_label.setText("选择图片进行预览")

    def preview_selected_image(self, row):
        """预览选中的图片"""
        if row >= 0:
            item = self.image_list_widget.item(row)
            if item:
                image_path = item.text()
                self.load_image_preview(image_path)

    def load_image_preview(self, image_path: str):
        """加载图片预览"""
        try:
            # 本地文件预览
            if Path(image_path).exists():
                # 使用统一的图片加载函数
                pixmap = load_image_safely(image_path)
                if not pixmap.isNull():
                    # 缩放图片以适应预览区域
                    scaled_pixmap = pixmap.scaled(
                        self.image_preview_label.size(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation,
                    )
                    self.image_preview_label.setPixmap(scaled_pixmap)
                    self.image_preview_label.setText("")
                else:
                    self.image_preview_label.setText(
                        f"无法加载图片：\n{Path(image_path).name}"
                    )
                    # 只在DEBUG级别记录错误，避免控制台输出过多信息
                    logging.debug(f"图片加载失败: {image_path}")
            else:
                self.image_preview_label.setText(
                    f"文件不存在：\n{Path(image_path).name}"
                )
        except Exception as e:
            self.image_preview_label.setText(f"预览失败：\n{str(e)}")
            logging.debug(f"图片预览失败: {image_path} - {str(e)}")

        self.image_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

    def load_source_data(self):
        """加载货源数据"""
        if self.source:
            # 基本信息
            self.name_edit.setText(self.source.name)
            self.product_name_edit.setText(self.source.product_name)
            self.shop_info_edit.setText(self.source.shop_info)
            self.url_edit.setText(self.source.url)
            self.price_spinbox.setValue(self.source.price)
            self.quantity_spinbox.setValue(self.source.quantity)
            self.min_order_quantity_spinbox.setValue(self.source.min_order_quantity)
            self.sales_count_spinbox.setValue(self.source.sales_count)

            # 运费信息
            self.shipping_cost_spinbox.setValue(self.source.shipping_cost)
            self.shipping_suffix_combo.setCurrentText(self.source.shipping_cost_suffix)
            self.shipping_location_edit.setText(self.source.shipping_location)

            # 状态信息
            status_display = self.reverse_status_mapping.get(self.source.status, "活跃")
            # 重置所有状态复选框
            self.status_active_checkbox.setChecked(False)
            self.status_inactive_checkbox.setChecked(False)
            self.status_pending_checkbox.setChecked(False)
            self.status_suspended_checkbox.setChecked(False)

            # 根据当前状态选中对应的复选框
            if status_display == "活跃":
                self.status_active_checkbox.setChecked(True)
            elif status_display == "非活跃":
                self.status_inactive_checkbox.setChecked(True)
            elif status_display == "待定":
                self.status_pending_checkbox.setChecked(True)
            elif status_display == "暂停":
                self.status_suspended_checkbox.setChecked(True)

            self.is_active_checkbox.setChecked(self.source.is_active)

            # 其他信息
            self.contact_info_edit.setPlainText(self.source.contact_info)
            self.return_policy_edit.setPlainText(self.source.return_policy)
            self.notes_edit.setPlainText(self.source.notes)

            # 模式设置
            self.wholesale_checkbox.setChecked(self.source.supports_wholesale)
            self.dropship_checkbox.setChecked(self.source.supports_dropship)

            # 批发模式字段
            self.pickup_rate_24h_spinbox.setValue(self.source.pickup_rate_24h)
            self.pickup_rate_48h_spinbox.setValue(self.source.pickup_rate_48h)
            self.monthly_dropship_orders_spinbox.setValue(
                self.source.monthly_dropship_orders
            )
            self.downstream_stores_spinbox.setValue(self.source.downstream_stores)
            self.distributor_count_spinbox.setValue(self.source.distributor_count)

            if self.source.product_publish_time:
                self.product_publish_time_edit.setDateTime(
                    QDateTime.fromString(
                        self.source.product_publish_time.isoformat(),
                        Qt.DateFormat.ISODate,
                    )
                )

            self.available_products_spinbox.setValue(self.source.available_products)
            self.monthly_new_products_spinbox.setValue(self.source.monthly_new_products)

            # 批发模式专属字段
            self.wholesale_price_spinbox.setValue(
                self.source.price
            )  # 批发价格使用统一的price字段
            self.wholesale_quantity_spinbox.setValue(self.source.quantity)  # 批发库存
            self.wholesale_sales_count_spinbox.setValue(
                self.source.sales_count
            )  # 批发销量
            self.wholesale_shipping_cost_spinbox.setValue(
                self.source.shipping_cost
            )  # 批发运费
            self.wholesale_shipping_suffix_combo.setCurrentText(
                self.source.shipping_cost_suffix
            )  # 批发运费后缀
            self.wholesale_shipping_location_edit.setText(
                self.source.shipping_location
            )  # 批发发货地
            self.wholesale_min_order_quantity_spinbox.setValue(
                self.source.wholesale_min_order_quantity
            )
            self.wholesale_payment_terms_spinbox.setValue(
                self.source.wholesale_payment_terms
            )
            # 批发返点比例：数据库存储小数，界面显示百分比
            self.wholesale_rebate_rate_spinbox.setValue(
                self.source.wholesale_rebate_rate * 100
            )
            self.wholesale_price_tiers_edit.setText(self.source.wholesale_price_tiers)

            # 代发模式专属字段
            self.dropship_price_spinbox.setValue(
                self.source.price
            )  # 代发价格使用统一的price字段
            self.dropship_quantity_spinbox.setValue(self.source.quantity)  # 代发库存
            self.dropship_sales_count_spinbox.setValue(
                self.source.sales_count
            )  # 代发销量
            self.dropship_shipping_cost_spinbox.setValue(
                self.source.shipping_cost
            )  # 代发运费
            self.dropship_shipping_suffix_combo.setCurrentText(
                self.source.shipping_cost_suffix
            )  # 代发运费后缀
            self.dropship_service_fee_spinbox.setValue(self.source.dropship_service_fee)
            self.dropship_processing_time_spinbox.setValue(
                self.source.dropship_processing_time
            )
            self.dropship_packaging_edit.setText(self.source.dropship_packaging)
            self.dropship_inventory_sync_combo.setCurrentText(
                self.source.dropship_inventory_sync
            )
            self.dropship_min_quantity_spinbox.setValue(
                self.source.dropship_min_quantity
            )
            self.dropship_shipping_location_edit.setText(
                self.source.dropship_shipping_location
            )
            self.dropship_support_regions_edit.setText(
                self.source.dropship_support_regions
            )

            # 图片信息
            for image_url in self.source.image_urls:
                if image_url:  # 只添加非空URL
                    item = QListWidgetItem(image_url)
                    item.setToolTip(image_url)
                    self.image_list_widget.addItem(item)

            # 加载商品详情数据
            self.load_product_details_data()

            # 更新计算和可见性
            self.update_calculations()
            self.update_mode_visibility()

    def load_product_details_data(self):
        """加载商品详情数据（从自定义字段中）"""
        if not self.source or not hasattr(self.source, "custom_fields"):
            return

        try:
            # 从自定义字段中加载数据
            custom_fields = self.source.custom_fields or []

            # 创建字段映射字典
            field_dict = {}
            for field in custom_fields:
                if hasattr(field, "name") and hasattr(field, "value"):
                    field_dict[field.name] = field.value

            # 加载商品属性
            if hasattr(self, "material_edit"):
                self.material_edit.setText(field_dict.get("material", ""))
            if hasattr(self, "product_category_edit"):
                self.product_category_edit.setText(
                    field_dict.get("product_category", "")
                )
            if hasattr(self, "product_code_edit"):
                self.product_code_edit.setText(field_dict.get("product_code", ""))
            if hasattr(self, "net_weight_spinbox"):
                try:
                    self.net_weight_spinbox.setValue(
                        float(field_dict.get("net_weight", 0))
                    )
                except (ValueError, TypeError):
                    self.net_weight_spinbox.setValue(0)
            if hasattr(self, "gross_weight_spinbox"):
                try:
                    self.gross_weight_spinbox.setValue(
                        float(field_dict.get("gross_weight", 0))
                    )
                except (ValueError, TypeError):
                    self.gross_weight_spinbox.setValue(0)
            if hasattr(self, "brand_edit"):
                self.brand_edit.setText(field_dict.get("brand", ""))
            if hasattr(self, "origin_edit"):
                self.origin_edit.setText(field_dict.get("origin", ""))
            if hasattr(self, "is_import_checkbox"):
                self.is_import_checkbox.setChecked(
                    field_dict.get("is_import", "").lower() in ["true", "1", "yes"]
                )
            if hasattr(self, "is_patent_checkbox"):
                self.is_patent_checkbox.setChecked(
                    field_dict.get("is_patent", "").lower() in ["true", "1", "yes"]
                )

            # 加载包装信息
            if hasattr(self, "box_dimensions_edit"):
                self.box_dimensions_edit.setText(field_dict.get("box_dimensions", ""))
            if hasattr(self, "box_quantity_spinbox"):
                try:
                    self.box_quantity_spinbox.setValue(
                        int(field_dict.get("box_quantity", 0))
                    )
                except (ValueError, TypeError):
                    self.box_quantity_spinbox.setValue(0)
            if hasattr(self, "box_weight_spinbox"):
                try:
                    self.box_weight_spinbox.setValue(
                        float(field_dict.get("box_weight", 0))
                    )
                except (ValueError, TypeError):
                    self.box_weight_spinbox.setValue(0)

            # 加载销售数据
            if hasattr(self, "annual_sales_spinbox"):
                try:
                    self.annual_sales_spinbox.setValue(
                        int(field_dict.get("annual_sales", 0))
                    )
                except (ValueError, TypeError):
                    self.annual_sales_spinbox.setValue(0)
            if hasattr(self, "review_count_spinbox"):
                try:
                    self.review_count_spinbox.setValue(
                        int(field_dict.get("review_count", 0))
                    )
                except (ValueError, TypeError):
                    self.review_count_spinbox.setValue(0)
            if hasattr(self, "sold_quantity_spinbox"):
                try:
                    self.sold_quantity_spinbox.setValue(
                        int(field_dict.get("sold_quantity", 0))
                    )
                except (ValueError, TypeError):
                    self.sold_quantity_spinbox.setValue(0)
            if hasattr(self, "review_tags_edit"):
                self.review_tags_edit.setText(field_dict.get("review_tags", ""))

            # 加载代发服务扩展数据
            if hasattr(self, "return_rate_spinbox"):
                try:
                    self.return_rate_spinbox.setValue(
                        float(field_dict.get("return_rate", 0))
                    )
                except (ValueError, TypeError):
                    self.return_rate_spinbox.setValue(0)
            if hasattr(self, "product_publish_datetime"):
                publish_time = field_dict.get("listing_date", "")
                if publish_time:
                    try:
                        from PyQt6.QtCore import QDateTime

                        dt = QDateTime.fromString(publish_time, "yyyy-MM-dd hh:mm")
                        if dt.isValid():
                            self.product_publish_datetime.setDateTime(dt)
                    except:
                        pass

            # 加载服务保障
            if hasattr(self, "shipping_promise_edit"):
                self.shipping_promise_edit.setText(
                    field_dict.get("shipping_promise", "")
                )
            if hasattr(self, "supported_couriers_edit"):
                self.supported_couriers_edit.setText(
                    field_dict.get("supported_couriers", "")
                )
            if hasattr(self, "service_guarantees_edit"):
                guarantees = field_dict.get("service_guarantees", "")
                # 如果是JSON格式的列表，解析它
                try:
                    import json

                    if guarantees and guarantees.startswith("["):
                        guarantees_list = json.loads(guarantees)
                        guarantees = "\n".join(guarantees_list)
                except:
                    pass
                self.service_guarantees_edit.setText(guarantees)

            # 加载规格变体
            if hasattr(self, "total_variants_spinbox"):
                try:
                    self.total_variants_spinbox.setValue(
                        int(field_dict.get("total_variants", 0))
                    )
                except (ValueError, TypeError):
                    self.total_variants_spinbox.setValue(0)
            if hasattr(self, "color_variants_edit"):
                color_variants = field_dict.get("color_variants", "")
                # 如果是JSON格式的列表，转换为逗号分隔的字符串
                try:
                    import json

                    if color_variants and color_variants.startswith("["):
                        color_list = json.loads(color_variants)
                        color_variants = ",".join(color_list)
                except:
                    pass
                self.color_variants_edit.setText(color_variants)
            if hasattr(self, "variant_types_edit"):
                variant_types = field_dict.get("variant_types", "")
                # 如果是JSON格式的列表，转换为逗号分隔的字符串
                try:
                    import json

                    if variant_types and variant_types.startswith("["):
                        types_list = json.loads(variant_types)
                        variant_types = ",".join(types_list)
                except:
                    pass
                self.variant_types_edit.setText(variant_types)

        except Exception as e:
            logging.error(f"加载商品详情数据失败: {e}")

    def get_selected_status(self):
        """获取选中的状态"""
        # 优先返回选中的状态，如果多选则返回第一个选中的
        if self.status_active_checkbox.isChecked():
            return "active"
        elif self.status_inactive_checkbox.isChecked():
            return "inactive"
        elif self.status_pending_checkbox.isChecked():
            return "pending"
        elif self.status_suspended_checkbox.isChecked():
            return "suspended"
        else:
            return "active"  # 默认返回活跃状态

    def get_source_data(self):
        """获取货源数据"""
        try:
            # 获取支持的模式
            modes = []
            if self.wholesale_checkbox.isChecked():
                modes.append(SourceMode.WHOLESALE)
            if self.dropship_checkbox.isChecked():
                modes.append(SourceMode.DROPSHIP)

            # 获取图片路径列表
            image_urls = []
            # 检查image_list_widget是否存在
            if (
                hasattr(self, "image_list_widget")
                and self.image_list_widget is not None
            ):
                for i in range(self.image_list_widget.count()):
                    item = self.image_list_widget.item(i)
                    if item:
                        image_urls.append(item.text())

            # 根据选择的模式获取对应的参数
            # 默认值 - 如果两种模式都不选择，使用代发模式的默认值作为兼容
            price = 0
            quantity = 1
            min_order_quantity = 1
            sales_count = 0
            shipping_cost = 0
            shipping_cost_suffix = ""
            shipping_location = ""

            # 优先使用代发模式的参数（因为它通常是默认选择）
            if self.dropship_checkbox.isChecked() and hasattr(
                self, "dropship_price_spinbox"
            ):
                price = self.dropship_price_spinbox.value()
                quantity = self.dropship_quantity_spinbox.value()
                min_order_quantity = self.dropship_min_quantity_spinbox.value()
                sales_count = self.dropship_sales_count_spinbox.value()
                shipping_cost = self.dropship_shipping_cost_spinbox.value()
                shipping_cost_suffix = self.dropship_shipping_suffix_combo.currentText()
                shipping_location = self.dropship_shipping_location_edit.text().strip()

            # 如果只选择批发模式，或者两种都选择时作为备选
            elif self.wholesale_checkbox.isChecked() and hasattr(
                self, "wholesale_price_spinbox"
            ):
                price = self.wholesale_price_spinbox.value()
                quantity = self.wholesale_quantity_spinbox.value()
                min_order_quantity = self.wholesale_min_order_quantity_spinbox.value()
                sales_count = self.wholesale_sales_count_spinbox.value()
                shipping_cost = self.wholesale_shipping_cost_spinbox.value()
                shipping_cost_suffix = (
                    self.wholesale_shipping_suffix_combo.currentText()
                )
                shipping_location = self.wholesale_shipping_location_edit.text().strip()

            data = {
                # 基本信息
                "name": self.name_edit.text().strip(),
                "product_name": self.product_name_edit.text().strip(),
                "shop_info": self.shop_info_edit.text().strip(),
                "url": self.url_edit.text().strip(),
                # 兼容性参数 - 主要用于验证和保存
                "price": price,
                "quantity": quantity,
                "min_order_quantity": min_order_quantity,
                "sales_count": sales_count,
                "shipping_cost": shipping_cost,
                "shipping_cost_suffix": shipping_cost_suffix,
                "shipping_location": shipping_location,
                # 状态信息
                "status": self.get_selected_status(),
                "is_active": self.is_active_checkbox.isChecked(),
                # 其他信息
                "contact_info": self.contact_info_edit.toPlainText().strip(),
                "return_policy": self.return_policy_edit.toPlainText().strip(),
                "notes": self.notes_edit.toPlainText().strip(),
                # 模式信息
                "modes": modes,
                # 批发模式业务字段
                "pickup_rate_24h": (
                    self.pickup_rate_24h_spinbox.value()
                    if hasattr(self, "pickup_rate_24h_spinbox")
                    else 0
                ),
                "pickup_rate_48h": (
                    self.pickup_rate_48h_spinbox.value()
                    if hasattr(self, "pickup_rate_48h_spinbox")
                    else 0
                ),
                "downstream_stores": (
                    self.downstream_stores_spinbox.value()
                    if hasattr(self, "downstream_stores_spinbox")
                    else 0
                ),
                "distributor_count": (
                    self.distributor_count_spinbox.value()
                    if hasattr(self, "distributor_count_spinbox")
                    else 0
                ),
                "product_publish_time": (
                    self.product_publish_time_edit.dateTime().toPython()
                    if hasattr(self, "product_publish_time_edit")
                    and hasattr(self.product_publish_time_edit.dateTime(), "toPython")
                    else (
                        self.product_publish_time_edit.dateTime()
                        if hasattr(self, "product_publish_time_edit")
                        else QDateTime.currentDateTime()
                    )
                ),
                "available_products": (
                    self.available_products_spinbox.value()
                    if hasattr(self, "available_products_spinbox")
                    else 0
                ),
                "monthly_new_products": (
                    self.monthly_new_products_spinbox.value()
                    if hasattr(self, "monthly_new_products_spinbox")
                    else 0
                ),
                # 批发模式专属字段
                "wholesale_min_order_quantity": (
                    self.wholesale_min_order_quantity_spinbox.value()
                    if hasattr(self, "wholesale_min_order_quantity_spinbox")
                    else 1
                ),
                "wholesale_payment_terms": (
                    self.wholesale_payment_terms_spinbox.value()
                    if hasattr(self, "wholesale_payment_terms_spinbox")
                    else 0
                ),
                "wholesale_rebate_rate": (
                    (self.wholesale_rebate_rate_spinbox.value() / 100)
                    if hasattr(self, "wholesale_rebate_rate_spinbox")
                    else 0
                ),
                "wholesale_price_tiers": (
                    self.wholesale_price_tiers_edit.text().strip()
                    if hasattr(self, "wholesale_price_tiers_edit")
                    else ""
                ),
                # 代发模式专属字段
                "dropship_service_fee": (
                    self.dropship_service_fee_spinbox.value()
                    if hasattr(self, "dropship_service_fee_spinbox")
                    else 0
                ),
                "dropship_processing_time": (
                    self.dropship_processing_time_spinbox.value()
                    if hasattr(self, "dropship_processing_time_spinbox")
                    else 0
                ),
                "dropship_packaging": (
                    self.dropship_packaging_edit.text().strip()
                    if hasattr(self, "dropship_packaging_edit")
                    else ""
                ),
                "dropship_inventory_sync": (
                    self.dropship_inventory_sync_combo.currentText()
                    if hasattr(self, "dropship_inventory_sync_combo")
                    else "实时同步"
                ),
                "dropship_min_quantity": (
                    self.dropship_min_quantity_spinbox.value()
                    if hasattr(self, "dropship_min_quantity_spinbox")
                    else 1
                ),
                "dropship_shipping_location": (
                    self.dropship_shipping_location_edit.text().strip()
                    if hasattr(self, "dropship_shipping_location_edit")
                    else ""
                ),
                "dropship_support_regions": (
                    self.dropship_support_regions_edit.text().strip()
                    if hasattr(self, "dropship_support_regions_edit")
                    else ""
                ),
                "monthly_dropship_orders": (
                    self.monthly_dropship_orders_spinbox.value()
                    if hasattr(self, "monthly_dropship_orders_spinbox")
                    else 0
                ),
                # 图片信息
                "image_urls": image_urls,
            }

            return data

        except Exception as e:
            print(f"get_source_data() 异常: {str(e)}")
            import traceback

            traceback.print_exc()
            raise

    def update_calculations(self):
        """更新计算结果"""
        try:
            # 代发模式计算
            if self.dropship_checkbox.isChecked() and hasattr(
                self, "dropship_price_spinbox"
            ):
                dropship_price = self.dropship_price_spinbox.value()
                dropship_quantity = (
                    self.dropship_quantity_spinbox.value()
                    if hasattr(self, "dropship_quantity_spinbox")
                    else 1
                )
                dropship_min_order = (
                    self.dropship_min_quantity_spinbox.value()
                    if hasattr(self, "dropship_min_quantity_spinbox")
                    else 1
                )
                dropship_shipping = (
                    self.dropship_shipping_cost_spinbox.value()
                    if hasattr(self, "dropship_shipping_cost_spinbox")
                    else 0
                )

                dropship_unit_cost_with_shipping = dropship_price + dropship_shipping
                self.update_dropship_profit_calculation(
                    dropship_unit_cost_with_shipping
                )

            # 批发模式计算
            if self.wholesale_checkbox.isChecked() and hasattr(
                self, "wholesale_price_spinbox"
            ):
                wholesale_price = self.wholesale_price_spinbox.value()
                wholesale_quantity = (
                    self.wholesale_quantity_spinbox.value()
                    if hasattr(self, "wholesale_quantity_spinbox")
                    else 1
                )
                wholesale_min_order = (
                    self.wholesale_min_order_quantity_spinbox.value()
                    if hasattr(self, "wholesale_min_order_quantity_spinbox")
                    else 1
                )
                wholesale_shipping = (
                    self.wholesale_shipping_cost_spinbox.value()
                    if hasattr(self, "wholesale_shipping_cost_spinbox")
                    else 0
                )

                wholesale_unit_cost_with_shipping = wholesale_price + wholesale_shipping
                self.update_wholesale_profit_calculation(
                    wholesale_unit_cost_with_shipping
                )

        except Exception as e:
            print(f"update_calculations() 异常: {str(e)}")
            import traceback

            traceback.print_exc()

    def update_dropship_profit_calculation(self, unit_cost_with_shipping: float):
        """更新代发模式利润计算"""
        try:
            # 尝试获取产品售价
            selling_price = 0.0

            # 如果是编辑模式且有产品服务，尝试获取产品信息
            if self.product_id and self.product_service:
                product = self.product_service.get_product(self.product_id)
                if product:
                    selling_price = product.selling_price

            # 计算利润
            if selling_price > 0:
                profit = selling_price - unit_cost_with_shipping
                self.dropship_profit_label.setText(f"¥{profit:.2f}")

                # 根据利润情况设置颜色
                if profit > 0:
                    self.dropship_profit_label.setStyleSheet(
                        "color: #4caf50; font-weight: bold; font-size: 12px;"
                    )  # 绿色
                elif profit == 0:
                    self.dropship_profit_label.setStyleSheet(
                        "color: #FFA500; font-weight: bold; font-size: 12px;"
                    )  # 橙色
                else:
                    self.dropship_profit_label.setStyleSheet(
                        "color: #f44336; font-weight: bold; font-size: 12px;"
                    )  # 红色
            else:
                self.dropship_profit_label.setText("需要产品售价")
                self.dropship_profit_label.setStyleSheet(
                    "color: #666666; font-style: italic; font-size: 12px;"
                )

        except Exception as e:
            self.dropship_profit_label.setText("计算错误")
            self.dropship_profit_label.setStyleSheet(
                "color: #666666; font-style: italic; font-size: 12px;"
            )

    def update_wholesale_profit_calculation(self, unit_cost_with_shipping: float):
        """更新批发模式利润计算"""
        try:
            # 尝试获取产品售价
            selling_price = 0.0

            # 如果是编辑模式且有产品服务，尝试获取产品信息
            if self.product_id and self.product_service:
                product = self.product_service.get_product(self.product_id)
                if product:
                    selling_price = product.selling_price

            # 计算利润
            if selling_price > 0:
                profit = selling_price - unit_cost_with_shipping
                self.wholesale_profit_label.setText(f"¥{profit:.2f}")

                # 根据利润情况设置颜色
                if profit > 0:
                    self.wholesale_profit_label.setStyleSheet(
                        "color: #4caf50; font-weight: bold; font-size: 12px;"
                    )  # 绿色
                elif profit == 0:
                    self.wholesale_profit_label.setStyleSheet(
                        "color: #FFA500; font-weight: bold; font-size: 12px;"
                    )  # 橙色
                else:
                    self.wholesale_profit_label.setStyleSheet(
                        "color: #f44336; font-weight: bold; font-size: 12px;"
                    )  # 红色
            else:
                self.wholesale_profit_label.setText("需要产品售价")
                self.wholesale_profit_label.setStyleSheet(
                    "color: #666666; font-style: italic; font-size: 12px;"
                )

        except Exception as e:
            self.wholesale_profit_label.setText("计算错误")
            self.wholesale_profit_label.setStyleSheet(
                "color: #666666; font-style: italic; font-size: 12px;"
            )

    def validate_input(self):
        """验证输入"""
        try:
            data = self.get_source_data()

            # 验证必填字段
            if not data["name"]:
                QMessageBox.warning(self, "验证错误", "请输入供应商名称")
                self.name_edit.setFocus()
                return False

            # 验证至少选择一种模式
            if not data["modes"]:
                QMessageBox.warning(
                    self, "验证错误", "请至少选择一种模式（批发模式或代发模式）"
                )
                self.tab_widget.setCurrentIndex(0)  # 切换到基本信息选项卡
                return False

            # 根据选择的模式验证对应的参数
            if self.dropship_checkbox.isChecked():
                # 验证代发模式参数
                if (
                    hasattr(self, "dropship_price_spinbox")
                    and self.dropship_price_spinbox.value() <= 0
                ):
                    QMessageBox.warning(self, "验证错误", "代发单价必须大于0")
                    self.tab_widget.setCurrentIndex(2)  # 切换到代发模式选项卡
                    self.dropship_price_spinbox.setFocus()
                    return False

                if (
                    hasattr(self, "dropship_min_quantity_spinbox")
                    and self.dropship_min_quantity_spinbox.value() <= 0
                ):
                    QMessageBox.warning(self, "验证错误", "代发最小起批数量必须大于0")
                    self.tab_widget.setCurrentIndex(2)  # 切换到代发模式选项卡
                    self.dropship_min_quantity_spinbox.setFocus()
                    return False

            if self.wholesale_checkbox.isChecked():
                # 验证批发模式参数
                if (
                    hasattr(self, "wholesale_price_spinbox")
                    and self.wholesale_price_spinbox.value() <= 0
                ):
                    QMessageBox.warning(self, "验证错误", "批发单价必须大于0")
                    self.tab_widget.setCurrentIndex(1)  # 切换到批发模式选项卡
                    self.wholesale_price_spinbox.setFocus()
                    return False

                if (
                    hasattr(self, "wholesale_min_order_quantity_spinbox")
                    and self.wholesale_min_order_quantity_spinbox.value() <= 0
                ):
                    QMessageBox.warning(self, "验证错误", "批发最小起批数量必须大于0")
                    self.tab_widget.setCurrentIndex(1)  # 切换到批发模式选项卡
                    self.wholesale_min_order_quantity_spinbox.setFocus()
                    return False

            # 验证URL格式（如果填写了）
            if data["url"] and not data["url"].startswith(("http://", "https://")):
                reply = QMessageBox.question(
                    self,
                    "URL格式",
                    "网址链接格式可能不正确，是否继续保存？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No,
                )
                if reply == QMessageBox.StandardButton.No:
                    self.tab_widget.setCurrentIndex(0)  # 切换到基本信息选项卡
                    self.url_edit.setFocus()
                    return False

            return True

        except Exception as e:
            print(f"validate_input() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"验证输入时发生错误: {str(e)}")
            return False

    def save_source(self):
        """保存货源"""
        try:
            if not self.validate_input():
                return False

            data = self.get_source_data()

            if self.is_edit_mode and self.source:
                # 更新现有货源
                self.source.name = data["name"]
                self.source.product_name = data["product_name"]
                self.source.shop_info = data["shop_info"]
                self.source.url = data["url"]
                self.source.price = data["price"]
                self.source.quantity = data["quantity"]
                self.source.min_order_quantity = data["min_order_quantity"]
                self.source.sales_count = data["sales_count"]
                self.source.shipping_cost = data["shipping_cost"]
                self.source.shipping_cost_suffix = data["shipping_cost_suffix"]
                self.source.shipping_location = data["shipping_location"]
                self.source.status = data["status"]
                self.source.is_active = data["is_active"]
                self.source.contact_info = data["contact_info"]
                self.source.return_policy = data["return_policy"]
                self.source.notes = data["notes"]
                self.source.modes = data["modes"]
                self.source.pickup_rate_24h = data["pickup_rate_24h"]
                self.source.pickup_rate_48h = data["pickup_rate_48h"]
                self.source.monthly_dropship_orders = data["monthly_dropship_orders"]
                self.source.downstream_stores = data["downstream_stores"]
                self.source.distributor_count = data["distributor_count"]
                self.source.product_publish_time = data["product_publish_time"]
                self.source.available_products = data["available_products"]
                self.source.monthly_new_products = data["monthly_new_products"]
                # 批发模式专属字段
                self.source.wholesale_min_order_quantity = data[
                    "wholesale_min_order_quantity"
                ]
                self.source.wholesale_payment_terms = data["wholesale_payment_terms"]
                self.source.wholesale_rebate_rate = data["wholesale_rebate_rate"]
                self.source.wholesale_price_tiers = data["wholesale_price_tiers"]
                # 代发模式专属字段
                self.source.dropship_service_fee = data["dropship_service_fee"]
                self.source.dropship_processing_time = data["dropship_processing_time"]
                self.source.dropship_packaging = data["dropship_packaging"]
                self.source.dropship_inventory_sync = data["dropship_inventory_sync"]
                self.source.dropship_min_quantity = data["dropship_min_quantity"]
                self.source.dropship_shipping_location = data[
                    "dropship_shipping_location"
                ]
                self.source.dropship_support_regions = data["dropship_support_regions"]
                self.source.image_urls = data["image_urls"]

                # 处理图片文件（只支持本地文件）
                if data["image_urls"]:
                    # 更新图片文件（使用累积模式，不清理现有图片）
                    final_images = self.product_service.update_source_images(
                        self.source.id, data["image_urls"], replace_all=False
                    )
                    self.source.image_urls = final_images

                success = self.product_service.update_source(self.source)
                if success:
                    # 成功时不弹窗，直接发送信号
                    self.source_saved.emit(self.source)
                    return True
                else:
                    QMessageBox.warning(self, "错误", "货源更新失败")
                    return False
            else:
                # 创建新货源
                if not self.product_id:
                    # 如果没有product_id，说明是在ProductDialog中添加货源
                    # 此时不需要立即保存到数据库，只需要返回数据
                    return True

                source = self.product_service.add_source(
                    product_id=self.product_id,
                    name=data["name"],
                    url=data["url"],
                    shop_info=data["shop_info"],
                    quantity=data["quantity"],
                    min_order_quantity=data["min_order_quantity"],
                    shipping_cost=data["shipping_cost"],
                    shipping_cost_suffix=data["shipping_cost_suffix"],
                    shipping_location=data["shipping_location"],
                    product_name=data["product_name"],
                    sales_count=data["sales_count"],
                    status=data["status"],
                    return_policy=data["return_policy"],
                    pickup_rate_24h=data["pickup_rate_24h"],
                    pickup_rate_48h=data["pickup_rate_48h"],
                    monthly_dropship_orders=data["monthly_dropship_orders"],
                    downstream_stores=data["downstream_stores"],
                    distributor_count=data["distributor_count"],
                    product_publish_time=data["product_publish_time"],
                    available_products=data["available_products"],
                    monthly_new_products=data["monthly_new_products"],
                    modes=data["modes"],
                    image_urls=data["image_urls"],
                    # 批发模式专属字段
                    wholesale_min_order_quantity=data["wholesale_min_order_quantity"],
                    wholesale_payment_terms=data["wholesale_payment_terms"],
                    wholesale_rebate_rate=data["wholesale_rebate_rate"],
                    wholesale_price_tiers=data["wholesale_price_tiers"],
                    # 代发模式专属字段
                    dropship_service_fee=data["dropship_service_fee"],
                    dropship_processing_time=data["dropship_processing_time"],
                    dropship_packaging=data["dropship_packaging"],
                    dropship_inventory_sync=data["dropship_inventory_sync"],
                    dropship_min_quantity=data["dropship_min_quantity"],
                    dropship_shipping_location=data["dropship_shipping_location"],
                    dropship_support_regions=data["dropship_support_regions"],
                    contact_info=data["contact_info"],
                    notes=data["notes"],
                    is_active=data["is_active"],
                    price=data["price"],
                )

                if source:
                    # 处理图片文件（只支持本地文件）
                    if data["image_urls"]:
                        # 更新图片文件（使用累积模式，不清理现有图片）
                        final_images = self.product_service.update_source_images(
                            source.id, data["image_urls"], replace_all=False
                        )
                        source.image_urls = final_images
                        # 再次更新数据库
                        self.product_service.update_source(source)

                    # 成功时不弹窗，直接发送信号
                    self.source_saved.emit(source)
                    return True
                else:
                    QMessageBox.warning(self, "错误", "货源创建失败")
                    return False

        except Exception as e:
            print(f"save_source() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"保存货源失败: {str(e)}")
            return False

    def apply_scraped_data(self, scraped_data: dict):
        """应用抓取的数据到货源表单"""
        try:
            # 填充基本信息
            if scraped_data.get("name"):
                self.name_edit.setText(scraped_data["name"])

            if scraped_data.get("supplier_name"):
                # 如果有独立的供应商名称字段，使用它；否则使用产品名称
                if hasattr(self, "supplier_name_edit"):
                    self.supplier_name_edit.setText(scraped_data["supplier_name"])

            if scraped_data.get("url"):
                self.url_edit.setText(scraped_data["url"])

            # 填充价格信息
            if (
                scraped_data.get("wholesale_price")
                and scraped_data["wholesale_price"] > 0
            ):
                # 批发模式价格
                if hasattr(self, "wholesale_unit_price_spinbox"):
                    self.wholesale_unit_price_spinbox.setValue(
                        scraped_data["wholesale_price"]
                    )

                # 基本价格（兼容性）
                if hasattr(self, "price_spinbox"):
                    self.price_spinbox.setValue(scraped_data["wholesale_price"])

            if (
                scraped_data.get("dropship_price")
                and scraped_data["dropship_price"] > 0
            ):
                # 代发模式价格
                if hasattr(self, "dropship_unit_price_spinbox"):
                    self.dropship_unit_price_spinbox.setValue(
                        scraped_data["dropship_price"]
                    )

            # 填充最小起订量
            if (
                scraped_data.get("wholesale_min_quantity")
                and scraped_data["wholesale_min_quantity"] > 0
            ):
                if hasattr(self, "wholesale_min_quantity_spinbox"):
                    self.wholesale_min_quantity_spinbox.setValue(
                        scraped_data["wholesale_min_quantity"]
                    )

            # 填充运费
            if scraped_data.get("shipping_cost") and scraped_data["shipping_cost"] > 0:
                if hasattr(self, "wholesale_shipping_cost_spinbox"):
                    self.wholesale_shipping_cost_spinbox.setValue(
                        scraped_data["shipping_cost"]
                    )
                if hasattr(self, "dropship_shipping_cost_spinbox"):
                    self.dropship_shipping_cost_spinbox.setValue(
                        scraped_data["shipping_cost"]
                    )

            # 填充产品描述和规格
            description_parts = []
            if scraped_data.get("description"):
                description_parts.append(scraped_data["description"])

            if scraped_data.get("specifications"):
                description_parts.append(
                    f"\n规格信息:\n{scraped_data['specifications']}"
                )

            if scraped_data.get("notes"):
                description_parts.append(f"\n抓取信息:\n{scraped_data['notes']}")

            if description_parts and hasattr(self, "description_edit"):
                self.description_edit.setText("\n".join(description_parts))

            # 处理图片URL
            if scraped_data.get("image_urls") and len(scraped_data["image_urls"]) > 0:
                # 这里可以添加下载和保存图片的逻辑
                # 暂时将URL添加到备注中
                url_info = f"\n\n图片链接:\n" + "\n".join(
                    scraped_data["image_urls"][:5]
                )
                if hasattr(self, "description_edit"):
                    current_text = self.description_edit.toPlainText()
                    self.description_edit.setText(current_text + url_info)

            # 填充商品详情字段（来自AI分析的自定义字段）
            self.apply_product_details_data(scraped_data)

            # 切换到基本信息选项卡显示填充的数据
            self.tab_widget.setCurrentIndex(0)

            # 更新计算
            if hasattr(self, "update_calculations"):
                self.update_calculations()

            logging.info(
                f"智能抓取数据已应用到货源表单: {scraped_data.get('name', '未知产品')}"
            )

        except Exception as e:
            logging.error(f"应用抓取数据失败: {e}")
            QMessageBox.warning(
                self, "数据应用失败", f"应用抓取数据时发生错误：\n{str(e)}"
            )

    def apply_product_details_data(self, scraped_data: dict):
        """应用AI分析的商品详情数据到商品详情选项卡"""
        try:
            # 从抓取数据中获取自定义字段
            custom_fields = scraped_data.get('custom_fields', [])
            
            # 将自定义字段转换为字典以便查找
            field_dict = {}
            for field in custom_fields:
                if isinstance(field, dict):
                    field_dict[field.get('name', '')] = field.get('value', '')
            
            # 填充商品属性
            if hasattr(self, 'material_edit') and field_dict.get('material'):
                self.material_edit.setText(str(field_dict['material']))
            if hasattr(self, 'product_category_edit') and field_dict.get('product_category'):
                self.product_category_edit.setText(str(field_dict['product_category']))
            if hasattr(self, 'product_code_edit') and field_dict.get('product_code'):
                self.product_code_edit.setText(str(field_dict['product_code']))
            if hasattr(self, 'brand_edit') and field_dict.get('brand'):
                self.brand_edit.setText(str(field_dict['brand']))
            if hasattr(self, 'origin_edit') and field_dict.get('origin'):
                self.origin_edit.setText(str(field_dict['origin']))
            
            # 填充重量信息
            if hasattr(self, 'net_weight_spinbox') and field_dict.get('net_weight'):
                try:
                    net_weight = float(field_dict['net_weight'])
                    self.net_weight_spinbox.setValue(net_weight)
                except (ValueError, TypeError):
                    pass
            
            if hasattr(self, 'gross_weight_spinbox') and field_dict.get('gross_weight'):
                try:
                    gross_weight = float(field_dict['gross_weight'])
                    self.gross_weight_spinbox.setValue(gross_weight)
                except (ValueError, TypeError):
                    pass
            
            # 填充包装信息
            if hasattr(self, 'box_dimensions_edit') and field_dict.get('box_dimensions'):
                self.box_dimensions_edit.setText(str(field_dict['box_dimensions']))
            if hasattr(self, 'box_quantity_spinbox') and field_dict.get('box_quantity'):
                try:
                    box_quantity = int(field_dict['box_quantity'])
                    self.box_quantity_spinbox.setValue(box_quantity)
                except (ValueError, TypeError):
                    pass
            if hasattr(self, 'box_weight_spinbox') and field_dict.get('box_weight'):
                try:
                    box_weight = float(field_dict['box_weight'])
                    self.box_weight_spinbox.setValue(box_weight)
                except (ValueError, TypeError):
                    pass
            
            # 填充销售数据
            if hasattr(self, 'annual_sales_spinbox') and field_dict.get('annual_sales'):
                try:
                    annual_sales = int(field_dict['annual_sales'])
                    self.annual_sales_spinbox.setValue(annual_sales)
                except (ValueError, TypeError):
                    pass
            
            if hasattr(self, 'review_count_spinbox') and field_dict.get('review_count'):
                try:
                    review_count = int(field_dict['review_count'])
                    self.review_count_spinbox.setValue(review_count)
                except (ValueError, TypeError):
                    pass
            
            if hasattr(self, 'sold_quantity_spinbox') and field_dict.get('sold_quantity'):
                try:
                    sold_quantity = int(field_dict['sold_quantity'])
                    self.sold_quantity_spinbox.setValue(sold_quantity)
                except (ValueError, TypeError):
                    pass
            
            if hasattr(self, 'review_tags_edit') and field_dict.get('review_tags'):
                self.review_tags_edit.setText(str(field_dict['review_tags']))
            
            # 填充代发服务扩展数据
            if hasattr(self, 'return_rate_spinbox') and field_dict.get('return_rate'):
                try:
                    return_rate = float(field_dict['return_rate'])
                    self.return_rate_spinbox.setValue(return_rate)
                except (ValueError, TypeError):
                    pass
            
            # 填充服务保障
            if hasattr(self, 'shipping_promise_edit') and field_dict.get('shipping_promise'):
                self.shipping_promise_edit.setText(str(field_dict['shipping_promise']))
            
            if hasattr(self, 'supported_couriers_edit') and field_dict.get('supported_couriers'):
                self.supported_couriers_edit.setText(str(field_dict['supported_couriers']))
            
            if hasattr(self, 'service_guarantees_edit') and field_dict.get('service_guarantees'):
                guarantees = field_dict['service_guarantees']
                # 如果是JSON格式的数组，转换为每行一项的文本
                try:
                    import json
                    if isinstance(guarantees, str) and guarantees.startswith('['):
                        guarantees_list = json.loads(guarantees)
                        guarantees = '\n'.join(guarantees_list)
                    elif isinstance(guarantees, list):
                        guarantees = '\n'.join(guarantees)
                except:
                    pass
                self.service_guarantees_edit.setText(str(guarantees))
            
            # 填充规格变体
            if hasattr(self, 'total_variants_spinbox') and field_dict.get('total_variants'):
                try:
                    total_variants = int(field_dict['total_variants'])
                    self.total_variants_spinbox.setValue(total_variants)
                except (ValueError, TypeError):
                    pass
            
            if hasattr(self, 'color_variants_edit') and field_dict.get('color_variants'):
                color_variants = field_dict['color_variants']
                # 如果是JSON格式的数组，转换为逗号分隔的字符串
                try:
                    import json
                    if isinstance(color_variants, str) and color_variants.startswith('['):
                        color_list = json.loads(color_variants)
                        color_variants = ','.join(color_list)
                    elif isinstance(color_variants, list):
                        color_variants = ','.join(color_variants)
                except:
                    pass
                self.color_variants_edit.setText(str(color_variants))
            
            if hasattr(self, 'variant_types_edit') and field_dict.get('variant_types'):
                variant_types = field_dict['variant_types']
                # 如果是JSON格式的数组，转换为逗号分隔的字符串
                try:
                    import json
                    if isinstance(variant_types, str) and variant_types.startswith('['):
                        types_list = json.loads(variant_types)
                        variant_types = ','.join(types_list)
                    elif isinstance(variant_types, list):
                        variant_types = ','.join(variant_types)
                except:
                    pass
                self.variant_types_edit.setText(str(variant_types))
            
            logging.info("商品详情数据已应用到商品详情选项卡")
            
        except Exception as e:
            logging.error(f"应用商品详情数据失败: {e}")

    def accept(self):
        """确认对话框"""
        try:
            # 先验证输入
            if not self.validate_input():
                return

            if self.save_source():
                super().accept()

        except Exception as e:
            print(f"SourceDialog.accept() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"货源对话框操作失败: {str(e)}")


class SourceImageManagementDialog(QDialog):
    """货源图片管理对话框"""

    def __init__(self, source: Source, product_service: ProductService, parent=None):
        super().__init__(parent)
        self.source = source
        self.product_service = product_service

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle(f"管理货源图片 - {self.source.name}")
        self.setMinimumSize(1400, 900)  # 调整窗口尺寸以更好利用空间
        self.resize(1400, 900)  # 设置初始大小

        # 初始化图片列表属性
        self.updated_images = self.source.image_urls.copy()
        # 安全地获取主要图片列表
        if hasattr(self.source, "featured_images") and self.source.featured_images:
            self.updated_featured_images = self.source.featured_images.copy()
        else:
            self.updated_featured_images = []

        # 初始化选中删除状态追踪
        self.selected_for_deletion = set()

        layout = QVBoxLayout(self)

        # 说明文字
        info_label = QLabel(
            "您可以添加、删除或重新排序货源图片。勾选图片复选框可设置为主要图片（最多3个），主要图片将在货源详情页面优先显示。"
        )
        info_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 创建自定义的图片查看器
        self.create_custom_image_viewer(layout)

        # 按钮
        from PyQt6.QtWidgets import QDialogButtonBox

        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def create_custom_image_viewer(self, layout):
        """创建自定义的图片查看器，在图片预览区域集成复选框"""
        from PyQt6.QtWidgets import QSplitter, QListWidget, QListWidgetItem
        from PyQt6.QtCore import Qt

        # 创建分割窗口
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：图片列表（用于选择和管理）
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # 列表标题
        list_title = QLabel("图片列表")
        list_title.setStyleSheet(
            "font-weight: bold; margin-bottom: 5px; color: #1976d2;"
        )
        left_layout.addWidget(list_title)

        # 图片列表（支持多选）
        self.simple_image_list = QListWidget()
        self.simple_image_list.setMinimumWidth(250)  # 减少左侧列表宽度
        self.simple_image_list.setMaximumWidth(300)  # 设置最大宽度
        self.simple_image_list.setSelectionMode(
            QListWidget.SelectionMode.ExtendedSelection
        )
        self.simple_image_list.currentRowChanged.connect(self.on_simple_image_selected)
        left_layout.addWidget(self.simple_image_list)

        # 操作按钮 - 2行3列布局
        buttons_widget = QWidget()
        buttons_layout = QGridLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 10)
        buttons_layout.setSpacing(8)

        # 第一行按钮
        add_button = QPushButton("添加图片")
        add_button.clicked.connect(self.add_images)
        buttons_layout.addWidget(add_button, 0, 0)

        remove_button = QPushButton("删除选中")
        remove_button.clicked.connect(self.remove_selected_images)
        remove_button.setStyleSheet(
            """
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """
        )
        buttons_layout.addWidget(remove_button, 0, 1)

        # 打开目录按钮
        open_folder_button = QPushButton("📁 打开目录")
        open_folder_button.setToolTip("打开货源图片目录")
        open_folder_button.clicked.connect(self.open_image_folder)
        open_folder_button.setStyleSheet(
            """
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """
        )
        buttons_layout.addWidget(open_folder_button, 0, 2)

        # 第二行按钮
        select_all_button = QPushButton("全选")
        select_all_button.clicked.connect(self.select_all_images)
        buttons_layout.addWidget(select_all_button, 1, 0)

        select_none_button = QPushButton("取消选择")
        select_none_button.clicked.connect(self.select_none_images)
        buttons_layout.addWidget(select_none_button, 1, 1)

        # 查重按钮移到第二行
        duplicate_button = QPushButton("🔍 查重")
        duplicate_button.setToolTip("查找重复或相似的图片")
        duplicate_button.clicked.connect(self.check_duplicates)
        duplicate_button.setStyleSheet(
            """
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """
        )
        buttons_layout.addWidget(duplicate_button, 1, 2)

        left_layout.addWidget(buttons_widget)

        splitter.addWidget(left_widget)

        # 右侧：带复选框的图片预览区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # 标题
        preview_title = QLabel(
            "图片预览（勾选删除❌或设为主要图片⭐，最多3个主要图片）🖱️滚轮滑动浏览"
        )
        preview_title.setStyleSheet(
            "font-weight: bold; margin-bottom: 5px; color: #1976d2;"
        )
        right_layout.addWidget(preview_title)

        # 创建自定义的图片显示区域
        self.create_custom_image_display(right_layout)

        # 控制按钮
        self.create_image_controls(right_layout)

        splitter.addWidget(right_widget)

        # 设置分割比例 - 让右侧图片预览区域占更多空间
        splitter.setSizes([280, 1120])

        layout.addWidget(splitter)

        # 初始化显示
        self.current_group_index = 0
        self.update_simple_image_list()
        self.update_image_display()

    def create_custom_image_display(self, layout):
        """创建带复选框的图片显示区域"""
        from PyQt6.QtWidgets import QFrame, QCheckBox, QGridLayout
        from PyQt6.QtCore import Qt

        # 初始化存储列表
        self.image_frames = []
        self.image_labels = []
        self.featured_checkboxes = []
        self.delete_checkboxes = []

        # 主图片显示容器
        self.display_container = QWidget()
        self.display_container.setObjectName("displayContainer")
        self.display_container.setMinimumHeight(700)  # 增加容器高度
        self.display_container.setMaximumHeight(750)  # 设置最大高度

        display_layout = QGridLayout(self.display_container)
        display_layout.setContentsMargins(10, 10, 10, 10)  # 适当的边距
        display_layout.setSpacing(15)  # 适当的间距

        # 显示当前组的图片 (最多6张，2行3列)
        for i in range(6):
            row = i // 3  # 行号 (0或1)
            col = i % 3  # 列号 (0, 1, 2)

            # 图片框架 - 大幅增大尺寸以更好利用空间
            frame = QFrame()
            frame.setObjectName(f"imageFrame{i}")
            frame.setMinimumSize(300, 350)  # 大幅增大框架尺寸
            frame.setMaximumSize(350, 400)  # 大幅增大最大尺寸

            frame_layout = QVBoxLayout(frame)
            frame_layout.setContentsMargins(8, 8, 8, 25)  # 适当的边距给复选框空间
            frame_layout.setSpacing(5)

            # 图片标签 - 大幅增大图片显示区域高度
            image_label = QLabel()
            image_label.setObjectName(f"imageLabel{i}")
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label.setMinimumHeight(280)  # 大幅增大图片区域高度
            image_label.setMaximumHeight(310)
            image_label.setStyleSheet(
                """
                QLabel {
                    border: 1px solid #444;
                    border-radius: 4px;
                    background-color: #2a2a2a;
                }
            """
            )

            frame_layout.addWidget(image_label)

            # 图片信息标签
            info_label = QLabel()
            info_label.setObjectName(f"infoLabel{i}")
            info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            info_label.setStyleSheet(
                "font-size: 10px; color: #888; padding: 2px; max-height: 15px;"
            )
            frame_layout.addWidget(info_label)

            # 复选框区域 - 确保可见且不重叠
            checkbox_container = QWidget()
            checkbox_container.setFixedHeight(20)  # 固定高度确保可见
            checkbox_layout = QHBoxLayout(checkbox_container)
            checkbox_layout.setContentsMargins(2, 0, 2, 0)
            checkbox_layout.setSpacing(8)

            # 主要图片复选框（左侧）
            featured_checkbox = QCheckBox("⭐")
            featured_checkbox.setObjectName(f"featuredCheckbox{i}")
            featured_checkbox.setToolTip("设为主要图片")
            featured_checkbox.setStyleSheet(
                """
                QCheckBox {
                    font-size: 12px;
                    font-weight: bold;
                    color: #4CAF50;
                    padding: 1px;
                }
                QCheckBox::indicator {
                    width: 14px;
                    height: 14px;
                    border: 2px solid #4CAF50;
                    border-radius: 3px;
                    background-color: #2a2a2a;
                }
                QCheckBox::indicator:checked {
                    background-color: #4CAF50;
                    border: 2px solid #4CAF50;
                }
                QCheckBox::indicator:hover {
                    border: 2px solid #66BB6A;
                    background-color: #2e2e2e;
                }
                QCheckBox::indicator:checked:hover {
                    background-color: #66BB6A;
                    border: 2px solid #66BB6A;
                }
            """
            )
            checkbox_layout.addWidget(featured_checkbox)

            checkbox_layout.addStretch()

            # 删除复选框（右侧）
            delete_checkbox = QCheckBox("🗑️")
            delete_checkbox.setObjectName(f"deleteCheckbox{i}")
            delete_checkbox.setToolTip("标记删除")
            delete_checkbox.setStyleSheet(
                """
                QCheckBox {
                    font-size: 12px;
                    font-weight: bold;
                    color: #f44336;
                    padding: 1px;
                }
                QCheckBox::indicator {
                    width: 14px;
                    height: 14px;
                    border: 2px solid #f44336;
                    border-radius: 3px;
                    background-color: #2a2a2a;
                }
                QCheckBox::indicator:checked {
                    background-color: #f44336;
                    border: 2px solid #f44336;
                }
                QCheckBox::indicator:hover {
                    border: 2px solid #ef5350;
                    background-color: #2e2e2e;
                }
                QCheckBox::indicator:checked:hover {
                    background-color: #ef5350;
                    border: 2px solid #ef5350;
                }
            """
            )
            checkbox_layout.addWidget(delete_checkbox)

            frame_layout.addWidget(checkbox_container)

            # 应用样式到框架
            frame.setStyleSheet(
                """
                QFrame {
                    border: 1px solid #555;
                    border-radius: 6px;
                    background-color: #333;
                    margin: 2px;
                }
                QFrame:hover {
                    border: 1px solid #777;
                    background-color: #383838;
                }
            """
            )

            # 添加到网格布局，确保不重叠
            display_layout.addWidget(frame, row, col)

            # 存储到列表中供后续使用
            self.image_frames.append(frame)
            self.image_labels.append(image_label)
            self.featured_checkboxes.append(featured_checkbox)
            self.delete_checkboxes.append(delete_checkbox)

        # 重写滚轮事件
        def wheelEvent(event):
            """处理鼠标滚轮事件"""
            from PyQt6.QtCore import Qt

            # 获取滚轮角度
            angle_delta = event.angleDelta().y()

            if angle_delta > 0:
                # 向上滚动，显示上一组
                self.previous_group()
            elif angle_delta < 0:
                # 向下滚动，显示下一组
                self.next_group()

            # 接受事件，防止传递给父控件
            event.accept()

        # 将滚轮事件绑定到容器
        self.display_container.wheelEvent = wheelEvent

        layout.addWidget(self.display_container)

    def create_image_controls(self, layout):
        """创建图片控制按钮区域"""
        widget = QWidget()
        widget.setObjectName("imageControlsWidget")
        controls_layout = QHBoxLayout(widget)
        controls_layout.setContentsMargins(0, 10, 0, 0)

        self.prev_button = QPushButton("◀ 上一组")
        self.prev_button.setObjectName("prevButton")
        self.prev_button.clicked.connect(self.previous_group)
        controls_layout.addWidget(self.prev_button)

        self.info_label = QLabel("暂无图片")
        self.info_label.setObjectName("infoLabel")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        controls_layout.addWidget(self.info_label)

        self.next_button = QPushButton("下一组 ▶")
        self.next_button.setObjectName("nextButton")
        self.next_button.clicked.connect(self.next_group)
        controls_layout.addWidget(self.next_button)

        layout.addWidget(widget)

    def _is_ui_initialized(self):
        """检查UI组件是否已完全初始化"""
        required_attrs = [
            "image_frames",
            "image_labels",
            "featured_checkboxes",
            "delete_checkboxes",
            "info_label",
            "prev_button",
            "next_button",
        ]

        for attr in required_attrs:
            if not hasattr(self, attr):
                return False

        # 检查列表是否为空
        list_attrs = [
            "image_frames",
            "image_labels",
            "featured_checkboxes",
            "delete_checkboxes",
        ]
        for attr in list_attrs:
            if not getattr(self, attr):
                return False

        return True

    def update_simple_image_list(self):
        """更新简洁版图片列表"""
        try:
            print("=== 开始更新简洁版图片列表 ===")
            from pathlib import Path

            # 清空现有列表
            old_count = self.simple_image_list.count()
            self.simple_image_list.clear()
            print(f"清空列表，原有项目数量: {old_count}")

            print(f"准备添加 {len(self.updated_images)} 张图片到列表")

            for i, image_path in enumerate(self.updated_images):
                # 创建列表项
                item_text = f"{i+1}. {Path(image_path).name}"
                print(f"处理图片 {i+1}: {Path(image_path).name}")

                # 添加状态标记
                status_icons = []
                if image_path in self.updated_featured_images:
                    status_icons.append("⭐")
                    print(f"  ⭐ 标记为主要图片")
                if image_path in self.selected_for_deletion:
                    status_icons.append("🗑️")
                    print(f"  🗑️ 标记为待删除")

                if status_icons:
                    item_text += " " + " ".join(status_icons)

                item = QListWidgetItem(item_text)

                # 如果标记删除，使用不同的颜色
                if image_path in self.selected_for_deletion:
                    item.setForeground(Qt.GlobalColor.red)
                elif image_path in self.updated_featured_images:
                    item.setForeground(Qt.GlobalColor.blue)

                self.simple_image_list.addItem(item)
                print(f"  ✓ 添加到列表: {item_text}")

            final_count = self.simple_image_list.count()
            print(f"列表更新完成，最终项目数量: {final_count}")

            if final_count != len(self.updated_images):
                print(
                    f"⚠️ 警告：列表项目数量({final_count})与图片数量({len(self.updated_images)})不匹配"
                )
            else:
                print("✅ 列表项目数量与图片数量匹配")

            print("=== 简洁版图片列表更新完成 ===")

        except Exception as e:
            print(f"❌ 更新简洁版图片列表失败: {e}")
            import traceback

            traceback.print_exc()

    def update_image_display(self):
        """更新图片显示"""
        try:
            # 全面的安全检查：确保必要的UI组件已初始化
            if not self._is_ui_initialized():
                print("货源图片查看器UI组件未完全初始化，跳过显示更新")
                return

            from PyQt6.QtGui import QPixmap
            from PyQt6.QtCore import Qt, QTimer
            import os

            if not hasattr(self, "updated_images") or not self.updated_images:
                # 显示中间的框架，显示"暂无图片"
                for i, frame in enumerate(self.image_frames):
                    if i == 1:  # 中间框架
                        frame.show()
                        self.image_labels[i].clear()
                        self.image_labels[i].setText("暂无图片")
                        self.featured_checkboxes[i].hide()
                        self.delete_checkboxes[i].hide()
                    else:
                        frame.hide()
                        self.image_labels[i].clear()
                        self.featured_checkboxes[i].hide()
                        self.delete_checkboxes[i].hide()
                return

            # 计算当前组的图片索引范围
            start_idx = self.current_group_index * 6
            end_idx = min(start_idx + 6, len(self.updated_images))

            # 更新每个图片位置
            for i in range(6):
                img_idx = start_idx + i
                label = self.image_labels[i]
                featured_checkbox = self.featured_checkboxes[i]
                delete_checkbox = self.delete_checkboxes[i]
                frame = self.image_frames[i]

                if img_idx < len(self.updated_images):
                    image_path = self.updated_images[img_idx]

                    # 显示图片
                    if os.path.exists(image_path):
                        # 使用统一的图片加载函数
                        pixmap = load_image_safely(image_path)
                        if not pixmap.isNull():
                            # 缩放图片以适应标签
                            target_size = label.size()
                            if target_size.width() > 50 and target_size.height() > 50:
                                scaled_pixmap = pixmap.scaled(
                                    target_size.width() - 10,
                                    target_size.height() - 10,
                                    Qt.AspectRatioMode.KeepAspectRatio,
                                    Qt.TransformationMode.SmoothTransformation,
                                )
                                label.setPixmap(scaled_pixmap)
                            else:
                                # 使用默认大小 - 大幅增大默认尺寸以匹配新框架
                                scaled_pixmap = pixmap.scaled(
                                    280,
                                    220,
                                    Qt.AspectRatioMode.KeepAspectRatio,
                                    Qt.TransformationMode.SmoothTransformation,
                                )
                                label.setPixmap(scaled_pixmap)
                        else:
                            label.setText("无法加载图片")
                            # 只在DEBUG级别记录错误，避免控制台输出过多信息
                            logging.debug(f"图片加载失败: {image_path}")
                            label.setStyleSheet(
                                """
                                QLabel {
                                    border: 1px solid #666;
                                    border-radius: 4px;
                                    background-color: #2a2a2a;
                                    color: #999;
                                }
                            """
                            )
                    else:
                        label.setText("图片文件不存在")
                        label.setStyleSheet(
                            """
                            QLabel {
                                border: 1px solid #666;
                                border-radius: 4px;
                                background-color: #2a2a2a;
                                color: #999;
                            }
                        """
                        )

                    # 设置复选框状态
                    is_featured = image_path in self.updated_featured_images
                    featured_checkbox.setChecked(is_featured)

                    # 检查是否选中删除
                    is_selected_for_deletion = image_path in self.selected_for_deletion
                    delete_checkbox.setChecked(is_selected_for_deletion)

                    # 连接信号 - 避免重复连接
                    try:
                        featured_checkbox.stateChanged.disconnect()
                        delete_checkbox.stateChanged.disconnect()
                    except:
                        pass

                    featured_checkbox.stateChanged.connect(
                        lambda state, idx=img_idx: self.on_featured_checkbox_changed(
                            state, idx
                        )
                    )
                    delete_checkbox.stateChanged.connect(
                        lambda state, idx=img_idx: self.on_delete_checkbox_changed(
                            state, idx
                        )
                    )

                    # 设置标签工具提示
                    label.setToolTip(f"图片 {img_idx + 1}: {Path(image_path).name}")

                    # 显示组件
                    frame.show()
                    featured_checkbox.show()
                    delete_checkbox.show()
                else:
                    # 隐藏空位的组件
                    frame.hide()
                    label.clear()
                    featured_checkbox.hide()
                    delete_checkbox.hide()

            # 更新控制信息
            total_images = len(self.updated_images)
            if total_images > 0:
                current_start = self.current_group_index * 6 + 1
                current_end = min((self.current_group_index + 1) * 6, total_images)
                total_groups = (total_images + 5) // 6
                info_text = f"显示 {current_start}-{current_end} / {total_images} 张图片 (第 {self.current_group_index + 1} 组 / 共 {total_groups} 组)"
            else:
                info_text = "暂无图片"

            if hasattr(self, "info_label"):
                self.info_label.setText(info_text)

            # 更新导航按钮状态
            if hasattr(self, "prev_button"):
                self.prev_button.setEnabled(self.current_group_index > 0)
            if hasattr(self, "next_button"):
                total_groups = (len(self.updated_images) + 5) // 6
                self.next_button.setEnabled(self.current_group_index < total_groups - 1)

        except Exception as e:
            print(f"更新图片显示失败: {e}")
            import traceback

            traceback.print_exc()

    def on_featured_checkbox_changed(self, state, img_idx):
        """主要图片复选框状态改变"""
        try:
            from PyQt6.QtCore import Qt
            from PyQt6.QtWidgets import QMessageBox

            if img_idx >= len(self.updated_images):
                return

            image_path = self.updated_images[img_idx]

            if state == Qt.CheckState.Checked.value:
                # 检查是否已经有3个主要图片
                if len(self.updated_featured_images) >= 3:
                    # 取消选中这个复选框
                    position_index = img_idx - self.current_group_index * 6
                    if 0 <= position_index < len(self.featured_checkboxes):
                        self.featured_checkboxes[position_index].setChecked(False)
                    QMessageBox.warning(self, "警告", "最多只能选择3个主要图片")
                    return

                # 添加到主要图片列表
                if image_path not in self.updated_featured_images:
                    self.updated_featured_images.append(image_path)
            else:
                # 从主要图片列表中移除
                if image_path in self.updated_featured_images:
                    self.updated_featured_images.remove(image_path)

            # 更新显示
            self.update_simple_image_list()
            self.update_image_display()

        except Exception as e:
            print(f"货源主要图片复选框状态改变异常: {e}")
            import traceback

            traceback.print_exc()

    def on_delete_checkbox_changed(self, state, img_idx):
        """删除复选框状态改变"""
        try:
            from PyQt6.QtCore import Qt

            if img_idx >= len(self.updated_images):
                return

            image_path = self.updated_images[img_idx]

            if state == Qt.CheckState.Checked.value:
                # 添加到删除列表
                self.selected_for_deletion.add(image_path)
            else:
                # 从删除列表中移除
                self.selected_for_deletion.discard(image_path)

            # 更新显示
            self.update_simple_image_list()

        except Exception as e:
            print(f"货源删除复选框状态改变异常: {e}")
            import traceback

            traceback.print_exc()

    def on_image_checkbox_changed(self, state, position_index):
        """图片复选框状态改变（旧版本兼容）"""
        # 计算实际的图片索引
        img_idx = self.current_group_index * 6 + position_index
        self.on_featured_checkbox_changed(state, img_idx)

    def on_simple_image_selected(self, row):
        """选中图片时同步预览"""
        if 0 <= row < len(self.updated_images):
            # 计算要显示的图片组
            group_index = row // 6
            self.current_group_index = group_index
            self.update_image_display()

    def previous_group(self):
        """上一组图片"""
        if self.current_group_index > 0:
            self.current_group_index -= 1
            self.update_image_display()

    def next_group(self):
        """下一组图片"""
        total_groups = (len(self.updated_images) + 5) // 6
        if self.current_group_index < total_groups - 1:
            self.current_group_index += 1
            self.update_image_display()

    def add_images(self):
        """添加图片"""
        try:
            from PyQt6.QtWidgets import QFileDialog, QMessageBox
            import os
            from pathlib import Path

            print("=== 开始添加货源图片 ===")

            # 设置默认路径为用户下载目录
            default_path = os.path.expanduser("~/Downloads")
            if not os.path.exists(default_path):
                default_path = ""

            file_paths, _ = QFileDialog.getOpenFileNames(
                self,
                "选择图片文件",
                default_path,  # 使用下载目录作为默认路径
                "图片文件 (*.jpg *.jpeg *.png *.bmp *.gif *.webp);;所有文件 (*.*)",
            )

            if not file_paths:
                print("用户取消了图片选择")
                return

            print(f"用户选择了 {len(file_paths)} 张图片：")
            for i, path in enumerate(file_paths):
                print(f"  {i+1}. {path}")
                # 验证文件是否存在
                if not os.path.exists(path):
                    print(f"    ⚠️ 警告：文件不存在！")
                else:
                    file_size = os.path.getsize(path)
                    print(f"    ✓ 文件存在，大小：{file_size} bytes")

            # 检查是否已保存货源
            if not self.source or not self.source.id:
                print("❌ 错误：货源未保存，无法添加图片")
                QMessageBox.warning(self, "提示", "请先保存货源后再添加图片")
                return

            print(f"货源ID: {self.source.id}, 货源名称: {self.source.name}")
            print(f"当前图片列表长度: {len(self.updated_images)}")

            # 检查重复文件
            new_files = []
            duplicate_files = []
            for file_path in file_paths:
                if file_path not in self.updated_images:
                    new_files.append(file_path)
                else:
                    duplicate_files.append(file_path)

            print(f"新图片数量: {len(new_files)}")
            print(f"重复图片数量: {len(duplicate_files)}")

            # 复制新图片到货源目录
            if new_files:
                try:
                    print("--- 开始复制图片文件 ---")

                    # 使用ProductService复制图片文件
                    copied_images = self.product_service.update_source_images(
                        self.source.id, new_files, replace_all=False
                    )

                    print(f"ProductService返回的复制结果: {len(copied_images)} 张图片")
                    for i, img_path in enumerate(copied_images):
                        print(f"  复制结果 {i+1}: {img_path}")

                        # 验证文件是否真的存在
                        if Path(img_path).is_absolute():
                            full_path = Path(img_path)
                        else:
                            full_path = Path.cwd() / img_path

                        if full_path.exists():
                            file_size = full_path.stat().st_size
                            print(f"    ✓ 文件确实存在，大小：{file_size} bytes")
                        else:
                            print(f"    ❌ 文件不存在！路径：{full_path}")

                    if copied_images:
                        print("--- 开始更新内存中的图片列表 ---")
                        old_count = len(self.updated_images)

                        # 将复制后的相对路径添加到图片列表
                        self.updated_images.extend(copied_images)

                        new_count = len(self.updated_images)
                        print(f"图片列表更新：{old_count} → {new_count}")

                        # 立即保存到数据库
                        print("--- 开始保存到数据库 ---")
                        self.save_changes_to_database()

                        # 验证数据库中的记录
                        print("--- 验证数据库记录 ---")
                        fresh_source = self.product_service.get_source(self.source.id)
                        if fresh_source:
                            db_image_count = (
                                len(fresh_source.image_urls)
                                if fresh_source.image_urls
                                else 0
                            )
                            print(f"数据库中图片数量: {db_image_count}")
                            print(f"数据库中图片列表: {fresh_source.image_urls}")
                        else:
                            print("❌ 无法从数据库获取货源信息")

                        print("--- 开始更新UI显示 ---")
                        # 更新显示
                        self.update_simple_image_list()
                        print(
                            f"简单图片列表更新完成，列表项数量: {self.simple_image_list.count()}"
                        )

                        self.update_image_display()
                        print("图片显示更新完成")

                        # 发出图片变化信号
                        self.on_images_changed(self.updated_images)
                        print("图片变化信号发出完成")

                        print(f"✅ 成功添加并复制了 {len(copied_images)} 张新图片")

                        # 显示成功消息
                        QMessageBox.information(
                            self,
                            "添加成功",
                            f"成功添加 {len(copied_images)} 张图片\n"
                            f"图片已复制到货源目录\n"
                            f"当前总图片数量: {len(self.updated_images)}",
                        )
                    else:
                        print("❌ 没有图片被成功复制")
                        QMessageBox.warning(self, "警告", "没有图片被成功复制")

                except Exception as copy_error:
                    print(f"❌ 复制图片失败: {copy_error}")
                    import traceback

                    traceback.print_exc()
                    QMessageBox.critical(
                        self, "错误", f"复制图片失败: {str(copy_error)}"
                    )
                    return

            # 提示重复文件
            if duplicate_files:
                print(f"提示用户跳过了 {len(duplicate_files)} 个重复文件")
                QMessageBox.information(
                    self, "提示", f"已跳过 {len(duplicate_files)} 个重复的图片文件"
                )

            print("=== 添加货源图片完成 ===")

        except Exception as e:
            print(f"❌ 添加货源图片失败: {e}")
            import traceback

            traceback.print_exc()
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"添加图片失败: {str(e)}")

    def remove_selected_image(self):
        """删除选中的图片"""
        current_row = self.simple_image_list.currentRow()
        if 0 <= current_row < len(self.updated_images):
            removed_image = self.updated_images.pop(current_row)

            # 如果被删除的图片是主要图片，也要从主要图片列表中移除
            if removed_image in self.updated_featured_images:
                self.updated_featured_images.remove(removed_image)

            # 调整当前显示的组索引
            total_groups = max(1, (len(self.updated_images) + 5) // 6)
            if self.current_group_index >= total_groups:
                self.current_group_index = max(0, total_groups - 1)

            # 更新显示
            self.update_simple_image_list()
            self.update_image_display()

            # 发出图片变化信号
            self.on_images_changed(self.updated_images)

    def on_images_changed(self, image_paths):
        """图片列表改变"""
        try:
            print("=== 图片列表改变事件 ===")
            print(f"新的图片路径数量: {len(image_paths)}")

            # 更新内存中的图片列表
            old_count = len(self.updated_images)
            self.updated_images = image_paths
            new_count = len(self.updated_images)

            print(f"内存图片列表更新: {old_count} → {new_count}")

            # 显示图片路径（限制输出数量避免过多log）
            if len(image_paths) <= 10:
                for i, path in enumerate(image_paths):
                    print(f"  图片 {i+1}: {path}")
            else:
                for i in range(5):
                    print(f"  图片 {i+1}: {image_paths[i]}")
                print(f"  ... 省略 {len(image_paths) - 10} 张图片 ...")
                for i in range(len(image_paths) - 5, len(image_paths)):
                    print(f"  图片 {i+1}: {image_paths[i]}")

            print(f"货源图片列表改变: {len(image_paths)} 张图片")

            # 清理主要图片列表，移除不存在的图片
            if (
                hasattr(self, "updated_featured_images")
                and self.updated_featured_images
            ):
                old_featured_count = len(self.updated_featured_images)
                self.updated_featured_images = [
                    img for img in self.updated_featured_images if img in image_paths
                ]
                new_featured_count = len(self.updated_featured_images)

                print(f"主要图片列表清理: {old_featured_count} → {new_featured_count}")

                if old_featured_count != new_featured_count:
                    print(
                        f"  移除了 {old_featured_count - new_featured_count} 张不存在的主要图片"
                    )
            else:
                self.updated_featured_images = []
                print("初始化主要图片列表为空")

            # 更新显示
            print("--- 开始更新UI显示 ---")
            self.update_simple_image_list()
            self.update_image_display()
            print("--- UI显示更新完成 ---")

            print("=== 图片列表改变事件处理完成 ===")

        except Exception as e:
            print(f"❌ 处理图片列表改变事件失败: {e}")
            import traceback

            traceback.print_exc()

    def remove_selected_images(self):
        """批量删除选中的图片"""
        try:
            print("=== 开始删除选中的货源图片 ===")
            from PyQt6.QtWidgets import QMessageBox
            from pathlib import Path

            print(f"删除前图片数量: {len(self.updated_images)}")

            # 获取选中的图片（从列表选择和删除复选框）
            selected_indices = []
            selected_items = self.simple_image_list.selectedItems()

            print(f"从列表选择的项目数量: {len(selected_items)}")

            # 从列表选择获取索引
            for item in selected_items:
                for i in range(self.simple_image_list.count()):
                    if self.simple_image_list.item(i) == item:
                        selected_indices.append(i)
                        break

            print(f"选中的索引: {selected_indices}")

            # 合并从删除复选框选择的图片
            selected_paths = set()
            for idx in selected_indices:
                if 0 <= idx < len(self.updated_images):
                    selected_paths.add(self.updated_images[idx])

            print(f"从复选框选择的图片数量: {len(self.selected_for_deletion)}")
            print(f"复选框选择的图片: {self.selected_for_deletion}")

            # 添加通过删除复选框选择的图片
            selected_paths.update(self.selected_for_deletion)

            print(f"总共要删除的图片数量: {len(selected_paths)}")
            for i, path in enumerate(selected_paths):
                print(f"  待删除 {i+1}: {path}")

            if not selected_paths:
                print("没有选中任何图片")
                QMessageBox.information(self, "提示", "请先选择要删除的图片")
                return

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {len(selected_paths)} 张图片吗？\n\n注意：这将永久删除图片文件！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                print("用户确认删除")

                # 记录删除前的数量
                before_count = len(self.updated_images)

                # 删除选中的图片
                removed_images = []
                removed_count = 0
                for image_path in selected_paths:
                    if image_path in self.updated_images:
                        self.updated_images.remove(image_path)
                        removed_images.append(image_path)  # 记录被删除的图片路径
                        removed_count += 1
                        print(f"从内存列表删除: {image_path}")

                    # 如果被删除的图片是主要图片，也要从主要图片列表中移除
                    if image_path in self.updated_featured_images:
                        self.updated_featured_images.remove(image_path)
                        print(f"从主要图片列表删除: {image_path}")

                print(f"内存中删除的图片数量: {removed_count}")

                # 删除物理文件
                if removed_images:
                    print("--- 开始删除物理文件 ---")
                    deleted_files = 0
                    for image_path in removed_images:
                        try:
                            if Path(image_path).is_absolute():
                                file_path = Path(image_path)
                            else:
                                file_path = Path.cwd() / image_path

                            print(f"尝试删除文件: {file_path}")

                            if file_path.exists() and file_path.is_file():
                                file_path.unlink()
                                deleted_files += 1
                                print(f"✓ 文件删除成功: {file_path}")
                            else:
                                print(f"⚠️ 文件不存在或不是文件: {file_path}")

                        except Exception as file_error:
                            print(f"❌ 删除文件失败 {image_path}: {file_error}")

                    print(f"成功删除 {deleted_files}/{len(removed_images)} 个物理文件")

                # 清空选择状态
                self.selected_for_deletion.clear()
                print("已清空删除选择状态")

                # 调整当前显示的组索引
                if self.updated_images:
                    total_groups = max(1, (len(self.updated_images) + 5) // 6)
                    if self.current_group_index >= total_groups:
                        self.current_group_index = max(0, total_groups - 1)
                        print(f"调整显示组索引到: {self.current_group_index}")
                else:
                    self.current_group_index = 0
                    print("没有图片了，重置显示组索引为0")

                # 立即保存到数据库
                print("--- 开始保存删除后的状态到数据库 ---")
                self.save_changes_to_database()

                print("--- 开始更新UI显示 ---")
                # 更新显示
                self.update_simple_image_list()
                print(
                    f"简单图片列表更新完成，当前项目数量: {self.simple_image_list.count()}"
                )

                self.update_image_display()
                print("图片显示更新完成")

                # 发出图片变化信号
                self.on_images_changed(self.updated_images)
                print("图片变化信号发出完成")

                # 显示删除结果
                after_count = len(self.updated_images)
                print(f"删除完成 - 删除前: {before_count}, 删除后: {after_count}")

                QMessageBox.information(
                    self,
                    "删除成功",
                    f"已删除 {removed_count} 张图片\n"
                    f"删除前：{before_count} 张\n"
                    f"删除后：{after_count} 张\n"
                    f"✓ 已同步到数据库\n"
                    f"✓ 已删除物理文件",
                )
            else:
                print("用户取消了删除操作")

            print("=== 删除选中的货源图片完成 ===")

        except Exception as e:
            print(f"❌ 删除图片失败: {e}")
            import traceback

            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"删除图片失败: {str(e)}")

    def open_image_folder(self):
        """打开货源图片目录"""
        from pathlib import Path
        import os
        import platform

        if not self.source or not self.source.id:
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.warning(self, "提示", "请先保存货源后再打开目录")
            return

        # 构建货源图片目录路径
        source_dir = Path(f"data/images/sources/source_{self.source.id}")

        # 如果目录不存在，创建它
        if not source_dir.exists():
            source_dir.mkdir(parents=True, exist_ok=True)

        # 使用系统文件管理器打开目录
        try:
            if platform.system() == "Windows":
                os.startfile(str(source_dir))
            elif platform.system() == "Darwin":  # macOS
                os.system(f"open '{source_dir}'")
            else:  # Linux
                os.system(f"xdg-open '{source_dir}'")
        except Exception as e:
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.warning(self, "错误", f"无法打开目录: {str(e)}")

    def select_all_images(self):
        """全选图片"""
        self.simple_image_list.selectAll()

    def select_none_images(self):
        """取消选择图片"""
        self.simple_image_list.clearSelection()

    def check_duplicates(self):
        """检查图片重复"""
        if not self.updated_images:
            QMessageBox.information(self, "提示", "没有图片可以进行查重")
            return

        if len(self.updated_images) < 2:
            QMessageBox.information(self, "提示", "至少需要2张图片才能进行查重")
            return

        try:
            # 创建查重器
            checker = ImageDuplicateChecker()

            # 查找重复图片
            duplicate_groups = checker.find_duplicates(self.updated_images, self)

            # 显示结果对话框
            result_dialog = DuplicateResultDialog(duplicate_groups, self)

            # 连接删除信号
            result_dialog.images_to_delete.connect(self.on_delete_duplicate_images)

            result_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"图片查重失败: {str(e)}")
            print(f"图片查重失败: {e}")
            import traceback

            traceback.print_exc()

    def save_changes_to_database(self):
        """立即保存更改到数据库"""
        try:
            print("=== 开始保存货源图片到数据库 ===")
            print(f"当前内存中图片数量: {len(self.updated_images)}")
            print(f"当前内存中主要图片数量: {len(self.updated_featured_images)}")

            # 显示当前图片列表
            for i, img in enumerate(self.updated_images):
                print(f"  图片 {i+1}: {img}")

            # 更新货源的图片信息
            self.source.image_urls = self.updated_images.copy()
            self.source.featured_images = self.updated_featured_images.copy()

            print("已更新货源对象的图片信息")

            # 保存到数据库
            if self.product_service:
                print("调用ProductService.update_source()...")
                success = self.product_service.update_source(self.source)

                if success:
                    print("✅ ProductService.update_source() 返回成功")

                    # 验证数据库中的实际内容
                    fresh_source = self.product_service.get_source(self.source.id)
                    if fresh_source:
                        db_image_count = (
                            len(fresh_source.image_urls)
                            if fresh_source.image_urls
                            else 0
                        )
                        print(f"数据库验证 - 图片数量: {db_image_count}")
                        print(f"数据库验证 - 图片列表: {fresh_source.image_urls}")

                        if db_image_count == len(self.updated_images):
                            print("✅ 数据库同步成功，数量匹配")
                        else:
                            print(
                                f"❌ 数据库同步异常，数量不匹配: 内存{len(self.updated_images)} vs 数据库{db_image_count}"
                            )
                    else:
                        print("❌ 无法从数据库重新获取货源信息进行验证")
                else:
                    print("❌ ProductService.update_source() 返回失败")

                print(f"货源图片已同步到数据库：{len(self.updated_images)} 张图片")
            else:
                print("❌ product_service 为空，无法保存")

            print("=== 保存货源图片到数据库完成 ===")

        except Exception as e:
            print(f"❌ 保存货源图片到数据库失败: {e}")
            import traceback

            traceback.print_exc()

    def cleanup_removed_images(self, removed_images):
        """清理被删除的图片文件"""
        try:
            from pathlib import Path

            deleted_count = 0
            for image_path in removed_images:
                try:
                    if Path(image_path).is_absolute():
                        file_path = Path(image_path)
                    else:
                        file_path = Path.cwd() / image_path

                    if file_path.exists() and file_path.is_file():
                        file_path.unlink()
                        print(f"✓ 删除货源图片文件: {file_path}")
                        deleted_count += 1
                    else:
                        print(f"⚠ 货源图片文件不存在或不是文件: {file_path}")

                except Exception as file_error:
                    print(f"删除单个货源图片文件失败 {image_path}: {file_error}")

            print(
                f"货源图片清理完成：成功删除 {deleted_count}/{len(removed_images)} 个图片文件"
            )

        except Exception as e:
            print(f"清理货源图片文件失败: {e}")
            import traceback

            traceback.print_exc()

    def on_delete_duplicate_images(self, images_to_delete):
        """删除重复图片"""
        try:
            # 记录删除前的数量
            before_count = len(self.updated_images)
            removed_count = 0

            # 从图片列表中移除
            removed_images = []
            for image_path in images_to_delete:
                if image_path in self.updated_images:
                    self.updated_images.remove(image_path)
                    removed_images.append(image_path)
                    removed_count += 1

                # 如果是主要图片，也要移除
                if image_path in self.updated_featured_images:
                    self.updated_featured_images.remove(image_path)

            # 删除物理文件
            if removed_images:
                self.cleanup_removed_images(removed_images)
                print(f"查重删除：已删除 {len(removed_images)} 个物理图片文件")

            # 调整当前显示的组索引
            total_groups = max(1, (len(self.updated_images) + 5) // 6)
            if self.current_group_index >= total_groups:
                self.current_group_index = max(0, total_groups - 1)

            # 立即保存到数据库
            self.save_changes_to_database()

            # 更新显示
            self.update_simple_image_list()
            self.update_image_display()

            # 发出图片变化信号
            self.on_images_changed(self.updated_images)

            # 显示删除结果
            after_count = len(self.updated_images)
            QMessageBox.information(
                self,
                "查重删除成功",
                f"已删除 {removed_count} 张重复图片\n"
                f"删除前：{before_count} 张\n"
                f"删除后：{after_count} 张\n"
                f"✓ 已同步到数据库\n"
                f"✓ 已删除物理文件",
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除图片失败: {str(e)}")
            print(f"删除图片失败: {e}")
            import traceback

            traceback.print_exc()

    def accept(self):
        """确认对话框"""
        try:
            print("=== 开始执行SourceImageManagementDialog.accept() ===")

            # 确保属性存在
            if not hasattr(self, "updated_featured_images"):
                self.updated_featured_images = []

            print(f"1. 准备最终保存 {len(self.updated_images)} 张货源图片")
            print(f"2. 准备最终保存 {len(self.updated_featured_images)} 张主要图片")

            # 分析图片变化（仅用于日志）
            original_images = (
                set(self.source.image_urls) if self.source.image_urls else set()
            )
            current_images = set(self.updated_images)

            removed_images = [
                img for img in original_images if img not in current_images
            ]

            print(f"3. 最终图片列表: {len(self.updated_images)} 张")
            print(f"4. 删除的图片: {len(removed_images)} 张")
            print(f"5. 原始图片列表: {list(original_images)}")
            print(f"6. 当前图片列表: {list(current_images)}")

            if removed_images:
                print(f"7. 需要删除的图片: {removed_images}")

            # 删除被移除的图片文件（如果有的话）
            if removed_images:
                print("8. 开始清理被删除的图片文件...")
                self.cleanup_removed_images(removed_images)
                print(f"9. 清理了 {len(removed_images)} 个被删除的图片文件")
            else:
                print("8. 没有需要删除的图片，跳过清理步骤")

            print("9. 开始更新货源对象...")
            # 更新货源的图片和主要图片信息
            old_images = self.source.image_urls.copy() if self.source.image_urls else []
            old_featured = (
                self.source.featured_images.copy()
                if self.source.featured_images
                else []
            )

            self.source.image_urls = self.updated_images.copy()
            self.source.featured_images = self.updated_featured_images.copy()

            print(f"10. 货源对象更新完成:")
            print(f"    图片: {len(old_images)} -> {len(self.source.image_urls)}")
            print(
                f"    主要图片: {len(old_featured)} -> {len(self.source.featured_images)}"
            )
            print(f"    主要图片详情: {self.source.featured_images}")
            print(f"    勾选的主要图片: {self.updated_featured_images}")

            print("11. 开始调用ProductService.update_source()...")
            # 最终保存到数据库
            success = self.product_service.update_source(self.source)
            print(f"12. ProductService.update_source() 返回: {success}")

            if success:
                print(f"13. 货源图片最终保存成功，图片数量: {len(self.updated_images)}")

                print("14. 货源图片保存成功，不显示弹窗")

                # 立即通知父窗口刷新显示
                if self.parent() and hasattr(self.parent(), "refresh"):
                    self.parent().refresh()

                print("16. 开始调用super().accept()...")
                super().accept()
                print("17. super().accept() 调用完成")
            else:
                print("13. 货源图片最终保存失败")
                QMessageBox.warning(self, "错误", "货源图片保存失败")

            print("=== SourceImageManagementDialog.accept() 执行完成 ===")

        except Exception as e:
            print(f"❌ SourceImageManagementDialog最终保存货源图片失败: {e}")
            import traceback

            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"保存货源图片失败: {str(e)}")
