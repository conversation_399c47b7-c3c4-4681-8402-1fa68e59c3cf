"""
货源模型
管理产品的货源信息
"""

from datetime import datetime
from typing import Optional, List
from dataclasses import dataclass, field


class SourceMode:
    """货源模式枚举"""

    WHOLESALE = "wholesale"  # 批发模式
    DROPSHIP = "dropship"  # 代发模式

    @classmethod
    def get_display_name(cls, mode: str) -> str:
        """获取模式的显示名称"""
        display_names = {cls.WHOLESALE: "批发模式", cls.DROPSHIP: "代发模式"}
        return display_names.get(mode, "未知模式")

    @classmethod
    def get_all_modes(cls) -> list:
        """获取所有模式"""
        return [
            (cls.WHOLESALE, cls.get_display_name(cls.WHOLESALE)),
            (cls.DROPSHIP, cls.get_display_name(cls.DROPSHIP)),
        ]


@dataclass
class Source:
    """货源模型"""

    # 基本属性
    id: int = 0
    product_id: int = 0
    name: str = ""  # 供应商名称
    price: float = 0.0  # 单价
    url: str = ""  # 网址链接
    shop_info: str = ""  # 店铺信息
    quantity: int = 1  # 库存数量
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    # 模式相关字段
    modes: List[str] = field(
        default_factory=lambda: [SourceMode.WHOLESALE]
    )  # 支持的模式列表
    min_order_quantity: int = 1  # 最小起批数量

    # 基础字段
    shipping_cost: float = 0.0  # 运费
    shipping_cost_suffix: str = ""  # 运费后缀（如"起", "+"等）
    shipping_location: str = ""  # 发货地
    product_name: str = ""  # 产品名称
    sales_count: int = 0  # 销量
    status: str = "active"  # 状态
    return_policy: str = ""  # 退货政策

    # 批发模式专属字段
    pickup_rate_24h: float = 0.0  # 24小时揽收率
    pickup_rate_48h: float = 0.0  # 48小时揽收率
    monthly_dropship_orders: int = 0  # 月代发订单数
    downstream_stores: int = 0  # 下游铺货数
    distributor_count: int = 0  # 分销商数
    product_publish_time: datetime = None  # 商品发布时间
    available_products: int = 0  # 可分销商品数
    monthly_new_products: int = 0  # 月上新商品数

    # 图片系统
    image_urls: List[str] = field(default_factory=list)  # 图片URL列表
    featured_images: List[str] = field(default_factory=list)  # 主要图片（最多3个）

    # 批发模式专属字段
    wholesale_min_order_quantity: int = 1  # 批发最小起订量
    wholesale_payment_terms: int = 0  # 批发账期（天）
    wholesale_rebate_rate: float = 0.0  # 批发返点比例
    wholesale_price_tiers: str = ""  # 批发价格阶梯

    # 代发模式专属字段
    dropship_service_fee: float = 0.0  # 代发服务费
    dropship_processing_time: int = 0  # 代发处理时间（小时）
    dropship_packaging: str = ""  # 代发包装要求
    dropship_inventory_sync: str = "实时同步"  # 库存同步方式
    dropship_min_quantity: int = 1  # 最小代发数量
    dropship_shipping_location: str = ""  # 代发发货地
    dropship_support_regions: str = ""  # 代发支持地区

    # 其他字段
    contact_info: str = ""  # 联系方式
    notes: str = ""  # 备注
    is_active: bool = True  # 是否活跃

    @property
    def supports_wholesale(self) -> bool:
        """是否支持批发模式"""
        return SourceMode.WHOLESALE in self.modes

    @property
    def supports_dropship(self) -> bool:
        """是否支持代发模式"""
        return SourceMode.DROPSHIP in self.modes

    @property
    def modes_display(self) -> str:
        """模式显示名称"""
        if not self.modes:
            return "未设置"
        return ", ".join([SourceMode.get_display_name(mode) for mode in self.modes])

    @property
    def total_cost(self) -> float:
        """总成本（不含运费）"""
        return self.price * self.quantity

    @property
    def total_cost_by_min_order(self) -> float:
        """按最小起批数量计算的总成本（不含运费）"""
        return self.price * self.min_order_quantity

    @property
    def unit_cost_with_shipping(self) -> float:
        """包含运费的单个成本"""
        if self.quantity <= 0:
            return self.price
        return self.price + (self.shipping_cost / self.quantity)

    @property
    def unit_cost_with_shipping_by_min_order(self) -> float:
        """按最小起批数量包含运费的单个成本"""
        if self.min_order_quantity <= 0:
            return self.price
        return self.price + (self.shipping_cost / self.min_order_quantity)

    @property
    def total_cost_with_shipping(self) -> float:
        """包含运费的总成本"""
        return self.total_cost + self.shipping_cost

    @property
    def total_cost_with_shipping_by_min_order(self) -> float:
        """按最小起批数量包含运费的总成本"""
        return self.total_cost_by_min_order + self.shipping_cost

    @property
    def shipping_cost_display(self) -> str:
        """运费显示文本"""
        if self.shipping_cost_suffix:
            return f"¥{self.shipping_cost:.2f}{self.shipping_cost_suffix}"
        return f"¥{self.shipping_cost:.2f}"

    @property
    def is_valid_url(self) -> bool:
        """URL有效性检查"""
        if not self.url:
            return False
        return self.url.startswith(("http://", "https://"))

    @property
    def display_name(self) -> str:
        """显示名称"""
        if self.product_name:
            return self.product_name
        if self.shop_info:
            return f"{self.name} - {self.shop_info}"
        return self.name

    @property
    def has_images(self) -> bool:
        """是否有图片"""
        return bool(self.image_urls)

    @property
    def image_count(self) -> int:
        """图片数量"""
        return len(self.image_urls)

    def add_mode(self, mode: str):
        """添加模式"""
        if mode not in self.modes and mode in [
            SourceMode.WHOLESALE,
            SourceMode.DROPSHIP,
        ]:
            self.modes.append(mode)
            self.update_timestamp()

    def remove_mode(self, mode: str):
        """移除模式"""
        if mode in self.modes:
            self.modes.remove(mode)
            self.update_timestamp()

    def set_modes(self, modes: List[str]):
        """设置模式列表"""
        valid_modes = [
            mode
            for mode in modes
            if mode in [SourceMode.WHOLESALE, SourceMode.DROPSHIP]
        ]
        if valid_modes:
            self.modes = valid_modes
            self.update_timestamp()

    def add_image(self, image_url: str):
        """添加图片"""
        if image_url and image_url not in self.image_urls:
            self.image_urls.append(image_url)
            self.update_timestamp()

    def remove_image(self, image_url: str):
        """移除图片"""
        if image_url in self.image_urls:
            self.image_urls.remove(image_url)
            self.update_timestamp()
            # 如果图片是主要图片，也要从主要图片列表中移除
            if image_url in self.featured_images:
                self.featured_images.remove(image_url)

    def clear_images(self):
        """清空图片"""
        self.image_urls.clear()
        self.featured_images.clear()
        self.update_timestamp()

    def add_featured_image(self, image_url: str):
        """添加主要图片"""
        if image_url not in self.featured_images and len(self.featured_images) < 3:
            self.featured_images.append(image_url)
            self.update_timestamp()

    def remove_featured_image(self, image_url: str):
        """移除主要图片"""
        if image_url in self.featured_images:
            self.featured_images.remove(image_url)
            self.update_timestamp()

    def set_featured_images(self, image_urls: List[str]):
        """设置主要图片（最多3个）"""
        # 确保所有图片都在图片列表中
        valid_images = [img for img in image_urls if img in self.image_urls]
        # 最多3个
        self.featured_images = valid_images[:3]
        self.update_timestamp()

    def get_display_images(self) -> List[str]:
        """获取用于显示的图片（优先显示主要图片）"""
        if self.featured_images:
            return self.featured_images
        else:
            # 如果没有主要图片，返回前3个图片
            return self.image_urls[:3]

    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()

    def update_price(self, new_price: float):
        """更新价格"""
        if new_price >= 0:
            self.price = new_price
            self.update_timestamp()

    def update_quantity(self, new_quantity: int):
        """更新库存数量"""
        if new_quantity >= 0:
            self.quantity = new_quantity
            self.update_timestamp()

    def update_min_order_quantity(self, new_min_order_quantity: int):
        """更新最小起批数量"""
        if new_min_order_quantity > 0:
            self.min_order_quantity = new_min_order_quantity
            self.update_timestamp()

    def set_inactive(self):
        """设为非活跃状态"""
        self.is_active = False
        self.status = "inactive"
        self.update_timestamp()

    def set_active(self):
        """设为活跃状态"""
        self.is_active = True
        self.status = "active"
        self.update_timestamp()

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "product_id": self.product_id,
            "name": self.name,
            "price": self.price,
            "url": self.url,
            "shop_info": self.shop_info,
            "quantity": self.quantity,
            "modes": self.modes,
            "min_order_quantity": self.min_order_quantity,
            "shipping_cost": self.shipping_cost,
            "shipping_cost_suffix": self.shipping_cost_suffix,
            "shipping_location": self.shipping_location,
            "product_name": self.product_name,
            "sales_count": self.sales_count,
            "status": self.status,
            "return_policy": self.return_policy,
            "pickup_rate_24h": self.pickup_rate_24h,
            "pickup_rate_48h": self.pickup_rate_48h,
            "monthly_dropship_orders": self.monthly_dropship_orders,
            "downstream_stores": self.downstream_stores,
            "distributor_count": self.distributor_count,
            "product_publish_time": (
                self.product_publish_time.isoformat()
                if self.product_publish_time
                and hasattr(self.product_publish_time, "isoformat")
                else (
                    str(self.product_publish_time)
                    if self.product_publish_time
                    else None
                )
            ),
            "available_products": self.available_products,
            "monthly_new_products": self.monthly_new_products,
            "image_urls": self.image_urls,
            "featured_images": self.featured_images,
            # 批发模式专属字段
            "wholesale_min_order_quantity": self.wholesale_min_order_quantity,
            "wholesale_payment_terms": self.wholesale_payment_terms,
            "wholesale_rebate_rate": self.wholesale_rebate_rate,
            "wholesale_price_tiers": self.wholesale_price_tiers,
            # 代发模式专属字段
            "dropship_service_fee": self.dropship_service_fee,
            "dropship_processing_time": self.dropship_processing_time,
            "dropship_packaging": self.dropship_packaging,
            "dropship_inventory_sync": self.dropship_inventory_sync,
            "dropship_min_quantity": self.dropship_min_quantity,
            "dropship_shipping_location": self.dropship_shipping_location,
            "dropship_support_regions": self.dropship_support_regions,
            "contact_info": self.contact_info,
            "notes": self.notes,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            # 计算属性
            "supports_wholesale": self.supports_wholesale,
            "supports_dropship": self.supports_dropship,
            "modes_display": self.modes_display,
            "total_cost": self.total_cost,
            "total_cost_by_min_order": self.total_cost_by_min_order,
            "unit_cost_with_shipping": self.unit_cost_with_shipping,
            "unit_cost_with_shipping_by_min_order": self.unit_cost_with_shipping_by_min_order,
            "total_cost_with_shipping": self.total_cost_with_shipping,
            "total_cost_with_shipping_by_min_order": self.total_cost_with_shipping_by_min_order,
            "shipping_cost_display": self.shipping_cost_display,
            "is_valid_url": self.is_valid_url,
            "display_name": self.display_name,
            "has_images": self.has_images,
            "image_count": self.image_count,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "Source":
        """从字典创建货源"""
        # 处理product_publish_time
        product_publish_time = None
        if data.get("product_publish_time"):
            try:
                product_publish_time = datetime.fromisoformat(
                    data["product_publish_time"]
                )
            except (ValueError, TypeError):
                product_publish_time = None

        return cls(
            id=data.get("id", 0),
            product_id=data.get("product_id", 0),
            name=data.get("name", ""),
            price=data.get("price", 0.0),
            url=data.get("url", ""),
            shop_info=data.get("shop_info", ""),
            quantity=data.get("quantity", 1),
            modes=data.get("modes", [SourceMode.WHOLESALE]),
            min_order_quantity=data.get("min_order_quantity", 1),
            shipping_cost=data.get("shipping_cost", 0.0),
            shipping_cost_suffix=data.get("shipping_cost_suffix", ""),
            shipping_location=data.get("shipping_location", ""),
            product_name=data.get("product_name", ""),
            sales_count=data.get("sales_count", 0),
            status=data.get("status", "active"),
            return_policy=data.get("return_policy", ""),
            pickup_rate_24h=data.get("pickup_rate_24h", 0.0),
            pickup_rate_48h=data.get("pickup_rate_48h", 0.0),
            monthly_dropship_orders=data.get("monthly_dropship_orders", 0),
            downstream_stores=data.get("downstream_stores", 0),
            distributor_count=data.get("distributor_count", 0),
            product_publish_time=product_publish_time,
            available_products=data.get("available_products", 0),
            monthly_new_products=data.get("monthly_new_products", 0),
            image_urls=data.get("image_urls", []),
            featured_images=data.get("featured_images", []),
            # 批发模式专属字段
            wholesale_min_order_quantity=data.get("wholesale_min_order_quantity", 1),
            wholesale_payment_terms=data.get("wholesale_payment_terms", 0),
            wholesale_rebate_rate=data.get("wholesale_rebate_rate", 0.0),
            wholesale_price_tiers=data.get("wholesale_price_tiers", ""),
            # 代发模式专属字段
            dropship_service_fee=data.get("dropship_service_fee", 0.0),
            dropship_processing_time=data.get("dropship_processing_time", 0),
            dropship_packaging=data.get("dropship_packaging", ""),
            dropship_inventory_sync=data.get("dropship_inventory_sync", "实时同步"),
            dropship_min_quantity=data.get("dropship_min_quantity", 1),
            dropship_shipping_location=data.get("dropship_shipping_location", ""),
            dropship_support_regions=data.get("dropship_support_regions", ""),
            contact_info=data.get("contact_info", ""),
            notes=data.get("notes", ""),
            is_active=data.get("is_active", True),
            created_at=datetime.fromisoformat(
                data.get("created_at", datetime.now().isoformat())
            ),
            updated_at=datetime.fromisoformat(
                data.get("updated_at", datetime.now().isoformat())
            ),
        )

    def __str__(self) -> str:
        return f"Source(id={self.id}, name='{self.name}', price={self.price}, min_order={self.min_order_quantity})"

    def __repr__(self) -> str:
        return self.__str__()


# 示例货源数据
SAMPLE_SOURCES = [
    {
        "name": "淘宝店铺A",
        "price": 6999.0,
        "url": "https://taobao.com/item/123",
        "shop_info": "金牌卖家",
        "quantity": 100,
        "modes": [SourceMode.WHOLESALE, SourceMode.DROPSHIP],
        "min_order_quantity": 10,
        "shipping_cost": 15.0,
        "shipping_cost_suffix": "起",
        "shipping_location": "广东深圳",
        "product_name": "高端智能手机",
        "sales_count": 1205,
        "status": "active",
        "return_policy": "7天无理由退换货",
        "pickup_rate_24h": 95.5,
        "pickup_rate_48h": 98.9,
        "monthly_dropship_orders": 850,
        "downstream_stores": 45,
        "distributor_count": 12,
        "available_products": 256,
        "monthly_new_products": 15,
        "contact_info": "客服QQ: 123456",
        "notes": "质量可靠，发货快",
    },
    {
        "name": "京东自营",
        "price": 7299.0,
        "url": "https://jd.com/item/456",
        "shop_info": "京东自营",
        "quantity": 50,
        "modes": [SourceMode.DROPSHIP],
        "min_order_quantity": 1,
        "shipping_cost": 0.0,
        "shipping_location": "北京",
        "product_name": "官方正品手机",
        "sales_count": 5689,
        "status": "active",
        "return_policy": "30天无理由退换货",
        "contact_info": "400-606-5500",
        "notes": "正品保证",
    },
    {
        "name": "天猫旗舰店",
        "price": 7199.0,
        "url": "https://tmall.com/item/789",
        "shop_info": "官方旗舰",
        "quantity": 200,
        "modes": [SourceMode.WHOLESALE],
        "min_order_quantity": 5,
        "shipping_cost": 10.0,
        "shipping_cost_suffix": "+",
        "shipping_location": "上海",
        "product_name": "品牌旗舰机",
        "sales_count": 3456,
        "status": "active",
        "return_policy": "15天无理由退换货",
        "pickup_rate_24h": 89.2,
        "pickup_rate_48h": 96.7,
        "monthly_dropship_orders": 1200,
        "downstream_stores": 78,
        "distributor_count": 23,
        "available_products": 512,
        "monthly_new_products": 25,
        "contact_info": "在线客服",
        "notes": "官方授权",
    },
]
