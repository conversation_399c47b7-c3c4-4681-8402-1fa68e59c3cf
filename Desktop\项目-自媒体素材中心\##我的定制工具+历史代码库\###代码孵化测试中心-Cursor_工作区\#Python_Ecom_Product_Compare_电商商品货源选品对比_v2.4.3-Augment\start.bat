@echo off
echo ===========================================
echo   电商产品对比选品工具 - 简化版 v2.0.7
echo ===========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 正在检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 正在启动应用程序...
python main.py

if errorlevel 1 (
    echo.
    echo 应用程序启动失败，请检查错误信息
    pause
) 