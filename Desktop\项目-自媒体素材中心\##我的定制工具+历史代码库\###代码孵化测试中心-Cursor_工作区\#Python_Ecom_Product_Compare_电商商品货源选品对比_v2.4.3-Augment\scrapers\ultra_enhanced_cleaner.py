#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超级增强版数据清理器模块
基于原始数据完整分析的200+保护规则
专门针对1688等电商平台的商品信息保护
"""

import re
import time
from typing import Dict, List, Any, Optional
import logging


class UltraEnhancedDataCleaner:
    """超级增强版数据清理器 - 基于200+保护规则"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 统计数据
        self.stats = {
            "original_length": 0,
            "cleaned_length": 0,
            "compression_ratio": 0.0,
            "protected_info_count": 0,
            "processing_time": 0.0,
        }

        # 初始化所有保护模式
        self._init_protection_patterns()

    def _init_protection_patterns(self):
        """初始化所有保护模式"""

        # 1. 基础商品信息保护（25个规则）
        self.basic_patterns = [
            r"跨境[\u4e00-\u9fff\w\s]*玩具",  # 商品标题核心
            r"猫咪玩具球",  # 产品名称
            r"宠物用品",  # 产品类别
            r"自嗨解闷神器",  # 产品特色
            r"耐咬",  # 产品特性
            r"带绳",  # 产品规格
            r"跳跳",  # 产品功能
            r"￥[\d.]+起",  # 价格信息
            r"≥\d+件",  # 起订量
            r"detail\.1688\.com/offer/\d+\.html",  # 商品URL
            r"商品ID[\s:：]*\d+",  # 商品ID
            r"货号[\s:：]*[^>\n]*",  # 货号信息
            r"中国大陆",  # 地区标识
            r"跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳",  # 完整标题
            r"阿里巴巴为您提供了[^。]*等产品",  # 产品描述
            r"产品类别[\s:：]*[^>\n]*",  # 产品分类
            r"840138940155",  # 具体商品ID
            r'meta.*description.*content="[^"]*"',  # meta描述
            r"title>([^<]*)</title>",  # 页面标题
            r"温州辛宠宠物用品有限公司",  # 公司名称
            r"2\.80",  # 具体价格
            r"自动逗猫玩具",  # 核心功能
            r"解闷神器",  # 功能描述
            r"球猫咪",  # 产品关键词
            r"宠物玩具",  # 产品分类
        ]

        # 2. 供应商信息保护（20个规则）
        self.supplier_patterns = [
            r"温州辛宠宠物用品有限公司",  # 公司名称
            r"经营\s*\d+\s*年",  # 经营年限
            r"综合服务",  # 服务评级
            r"中国\s+浙江\s+温州",  # 公司地址
            r"瑞安市南滨街道林北村九弄\d+号",  # 详细地址
            r"价格很便宜\s*\d*",  # 评价标签
            r"客服很热情\s*\d*",  # 评价标签
            r"入手推荐\s*\d*",  # 评价标签
            r"颜色与图片一致\s*\d*",  # 评价标签
            r"浙江温州",  # 发货地
            r"公司头像",  # 公司信息
            r"联系方式",  # 联系信息
            r"经营年限[\s:：]*\d+年",  # 经营年限
            r"服务认证",  # 服务认证
            r"企业认证",  # 企业认证
            r"公司简介",  # 公司介绍
            r"主营产品",  # 主营产品
            r"供应商评级",  # 供应商评级
            r"店铺信息",  # 店铺信息
            r"商家信息",  # 商家信息
        ]

        # 3. 商品属性保护（30个规则）
        self.attributes_patterns = [
            r"材质[\s:：]*PC\+硅胶",  # 材质信息
            r"是否进口[\s:：]*否",  # 进口标识
            r"产品类别[\s:：]*啃咬玩具",  # 产品类别
            r"货号[\s:：]*猫咪滚滚球",  # 货号
            r"箱装数量[\s:：]*\d+",  # 包装数量
            r"重量[\s:：]*\d+克",  # 重量
            r"品牌[\s:：]*其他",  # 品牌
            r"是否专利货源[\s:：]*否",  # 专利信息
            r"是否跨境出口专供货源[\s:：]*否",  # 跨境信息
            r"是否属于礼品[\s:：]*否",  # 礼品标识
            r"是否IP授权[\s:：]*否",  # IP授权
            r"产品净重[\s:：]*\d+\s*克",  # 净重
            r"产品毛重[\s:：]*\d+\s*克",  # 毛重
            r"外箱箱规[\s:：]*[\d*\.x×]+",  # 外箱尺寸
            r"装箱数[\s:：]*\d+\s*PCS",  # 装箱数
            r"总重量[\s:：]*[\d.]+\s*kg",  # 总重量
            r"颜色[\s:：]*[^>\n]*",  # 颜色信息
            r"规格[\s:：]*[^>\n]*",  # 规格信息
            r"\d+\s*克",  # 重量数值
            r"PC\+硅胶",  # 材质
            r"啃咬玩具",  # 产品类别
            r"猫咪滚滚球",  # 产品型号
            r"\d+\s*PCS",  # 包装单位
            r"[\d.]+\s*kg",  # 重量单位
            r"59\*34\*34\.5",  # 具体尺寸
            r"200\s*PCS",  # 具体包装
            r"16\.2\s*kg",  # 具体重量
            r"57\s*g",  # 净重
            r"75\s*g",  # 毛重
            r"其他品牌",  # 品牌信息
        ]

        # 4. 销售数据保护（15个规则）
        self.sales_patterns = [
            r"4\.5\s*星",  # 评价星级
            r"\d+\s*满星",  # 满星数量
            r"\d+\+?\s*条评价",  # 评价数量
            r"一年内\s*\d+万?\+?\s*个成交",  # 年度销量
            r"已售\s*\d+\s*件",  # 当前销量
            r"成交\s*\d+\s*件",  # 成交数量
            r"30\+",  # 评价数量
            r"3267",  # 具体销量
            r"10万\+",  # 年销量
            r"TOP\d+",  # 销售排行
            r"同款产品",  # 同款信息
            r"多个商品ID",  # 商品关联
            r"销售排行榜",  # 排行信息
            r"累计销量",  # 销量统计
            r"月销量",  # 月度销量
        ]

        # 5. 物流服务保护（25个规则）
        self.logistics_patterns = [
            r"浙江温州",  # 发货地
            r"承诺\s*\d+\s*小时发货",  # 发货承诺
            r"48小时发货",  # 具体承诺
            r'data-name="limitTimeDesc"[^>]*>承诺\d+小时发货',  # HTML属性承诺
            r"运费[\s:：]*选择收货地",  # 运费信息
            r"7天无理由退货",  # 退货政策
            r"晚发必赔",  # 服务保障
            r"服务评级",  # 服务等级
            r"综合服务认证",  # 服务认证
            r"支持\s*\d+\s*种快递面单",  # 快递支持
            r"面单支持",  # 面单信息
            r"物流承诺",  # 物流承诺
            r"发货信息",  # 发货信息
            r"服务保障",  # 服务保障
            r"物流[\s:：]*[^>\n]*",  # 物流相关
            r"快递[\s:：]*[^>\n]*",  # 快递信息
            r"发货[\s:：]*[^>\n]*",  # 发货信息
            r"收货[\s:：]*[^>\n]*",  # 收货信息
            r"运费",  # 运费
            r"包邮",  # 包邮
            r"免邮",  # 免邮
            r"邮费",  # 邮费
            r"快递费",  # 快递费
            r"运输",  # 运输
            r"配送",  # 配送
        ]

        # 6. 代发参谋数据保护（20个规则）
        self.dropship_patterns = [
            r"代发参谋",  # 代发标识
            r"24小时揽收率[\s:：]*\d+\.\d+%",  # 24小时揽收率
            r"48小时揽收率[\s:：]*\d+\.\d+%",  # 48小时揽收率
            r"月代发订单数[\s:：]*\d+以内",  # 月订单数
            r"下游铺货数[\s:：]*\d+以内",  # 铺货数
            r"商品退货率[\s:：]*\d+\.\d+%",  # 退货率
            r"商品发布时间[\s:：]*\d+年\d+月",  # 发布时间
            r"可分销商品数[\s:：]*\d+以内",  # 分销商品数
            r"月上新商品数[\s:：]*\d+以内",  # 上新数量
            r"分销商数[\s:：]*\d+\+",  # 分销商数量
            r"揽收率",  # 揽收率相关
            r"\d+\.\d+%",  # 百分比数据
            r"82\.00%",  # 具体揽收率
            r"98\.00%",  # 具体揽收率
            r"100以内",  # 具体数量
            r"400\+",  # 具体数量
            r"0\.00%",  # 退货率
            r"2024年9月",  # 发布时间
            r"10以内",  # 具体数量
            r"代发服务",  # 代发服务
        ]

        # 7. 图片和视频资源保护（10个规则）
        self.media_patterns = [
            r'https?://cbu01\.alicdn\.com/img/ibank/[^"\'>\s]+',  # 商品图片
            r'https?://cloud\.video\.taobao\.com/play/[^"\'>\s]+',  # 商品视频
            r"video封面",  # 视频封面
            r"主图\d*",  # 主图标识
            r"详情页图片",  # 详情图片
            r"产品展示图",  # 展示图
            r"商品缩略图",  # 缩略图
            r"图片链接",  # 图片链接
            r"\.jpg",  # 图片格式
            r"\.png",  # 图片格式
        ]

        # 8. 规格变体保护（30个规则）
        self.variant_patterns = [
            r"蜻蜓带绳款带唤醒",  # 具体规格
            r"带绳款猫球红色【非唤醒模式】",  # 颜色规格
            r"带绳款绿色猫球【非唤醒模式】",  # 颜色规格
            r"带绳款猫球蓝色【非唤醒模式】",  # 颜色规格
            r"带绳款猫球黄色【非唤醒模式】",  # 颜色规格
            r"带绳款猫球粉色【非唤醒模式】",  # 颜色规格
            r"带绳款灰色灰绳子【非唤醒模式】",  # 颜色规格
            r"毛绒款\w*色",  # 毛绒款规格
            r"魔尾款\w*色",  # 魔尾款规格
            r"新款横纹凸凸球\w*色",  # 新款规格
            r"齿轮款凸凸球\w*色",  # 齿轮款规格
            r"鸟鸣猫爪款绳子球\w*色",  # 鸟鸣款规格
            r"长续航一小时[^>]*",  # 续航规格
            r"智能\w*模式",  # 智能模式
            r"唤醒模式",  # 唤醒功能
            r"非唤醒模式",  # 非唤醒模式
            r"替换\w*",  # 替换配件
            r"捕猎罩\d+cm",  # 配件规格
            r"\d+种颜色规格",  # 颜色数量
            r"\d+\+种规格",  # 规格数量
            r"红色",  # 具体颜色
            r"绿色",  # 具体颜色
            r"蓝色",  # 具体颜色
            r"黄色",  # 具体颜色
            r"粉色",  # 具体颜色
            r"灰色",  # 具体颜色
            r"白色",  # 具体颜色
            r"黑色",  # 具体颜色
            r"彩色",  # 具体颜色
            r"混色",  # 具体颜色
        ]

        # 9. 价格相关保护（20个规则）
        self.price_patterns = [
            r"￥\d+\.\d+起",  # 起价
            r"价格[\s:：]*￥\d+\.\d+",  # 价格标识
            r"批发价[\s:：]*￥\d+\.\d+",  # 批发价
            r"代发价[\s:：]*￥\d+\.\d+",  # 代发价
            r"最小起订量[\s:：]*\d+件",  # 起订量
            r"价格区间[\s:：]*￥\d+\.\d+\-￥\d+\.\d+",  # 价格区间
            r"阶梯价格",  # 阶梯价格
            r"L会员价",  # 会员价格
            r"发布价格",  # 发布价格
            r"活动价格",  # 活动价格
            r"单价",  # 单价
            r"折扣",  # 折扣信息
            r"￥2\.80",  # 具体价格
            r"含L会员价折扣",  # 价格说明
            r"人民币",  # 货币单位
            r"起订量",  # 起订量
            r"批发",  # 批发
            r"零售",  # 零售
            r"成本",  # 成本
            r"利润",  # 利润
        ]

        # 合并所有保护模式
        self.all_patterns = (
            self.basic_patterns
            + self.supplier_patterns
            + self.attributes_patterns
            + self.sales_patterns
            + self.logistics_patterns
            + self.dropship_patterns
            + self.media_patterns
            + self.variant_patterns
            + self.price_patterns
        )

        self.logger.debug(f"初始化完成，共{len(self.all_patterns)}个保护规则")

    def clean_html_content(self, html_content: str) -> str:
        """清理HTML内容，保护重要商品信息"""
        if not html_content:
            return ""

        start_time = time.time()
        original_length = len(html_content)

        # 1. 提取并保护重要信息
        protected_info = self._extract_protected_info(html_content)

        # 2. 清理HTML标签但保留重要内容
        cleaned_content = self._clean_html_tags(html_content)

        # 3. 清理无用字符和格式
        cleaned_content = self._clean_formatting(cleaned_content)

        # 4. 重新注入保护的信息
        final_content = self._inject_protected_info(cleaned_content, protected_info)

        # 5. 最终优化
        final_content = self._final_optimization(final_content)

        # 更新统计信息
        processing_time = time.time() - start_time
        self.stats.update(
            {
                "original_length": original_length,
                "cleaned_length": len(final_content),
                "compression_ratio": (
                    (1 - len(final_content) / original_length) * 100
                    if original_length > 0
                    else 0
                ),
                "protected_info_count": len(protected_info),
                "processing_time": processing_time,
            }
        )

        return final_content

    def _extract_protected_info(self, content: str) -> List[str]:
        """提取需要保护的重要信息"""
        protected_info = []

        for pattern in self.all_patterns:
            try:
                matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if isinstance(match, tuple):
                        # 如果匹配返回的是元组，取第一个元素
                        match = (
                            match[0]
                            if match[0]
                            else (match[1] if len(match) > 1 else "")
                        )
                    if match and match.strip():
                        protected_info.append(match.strip())
            except Exception as e:
                continue

        # 去重并保持顺序
        unique_info = []
        seen = set()
        for info in protected_info:
            if info not in seen:
                seen.add(info)
                unique_info.append(info)

        return unique_info

    def _clean_html_tags(self, content: str) -> str:
        """清理HTML标签"""
        # 保留重要的data属性中的文本
        data_attr_pattern = r'data-name="limitTimeDesc"[^>]*>([^<]+)'
        data_matches = re.findall(data_attr_pattern, content, re.IGNORECASE)

        # 移除脚本和样式
        content = re.sub(
            r"<script[^>]*>.*?</script>", "", content, flags=re.DOTALL | re.IGNORECASE
        )
        content = re.sub(
            r"<style[^>]*>.*?</style>", "", content, flags=re.DOTALL | re.IGNORECASE
        )

        # 移除HTML标签
        content = re.sub(r"<[^>]+>", " ", content)

        # 重新添加data属性中的重要文本
        for match in data_matches:
            content += f" {match} "

        return content

    def _clean_formatting(self, content: str) -> str:
        """清理格式和无用字符"""
        # 清理HTML实体
        html_entities = {
            "&nbsp;": " ",
            "&amp;": "&",
            "&lt;": "<",
            "&gt;": ">",
            "&quot;": '"',
            "&#39;": "'",
            "&hellip;": "...",
        }
        for entity, replacement in html_entities.items():
            content = content.replace(entity, replacement)

        # 清理多余的空白字符
        content = re.sub(r"\s+", " ", content)
        content = re.sub(r"\n+", "\n", content)

        return content.strip()

    def _inject_protected_info(
        self, cleaned_content: str, protected_info: List[str]
    ) -> str:
        """重新注入保护的信息"""
        if not protected_info:
            return cleaned_content

        # 将保护信息添加到清理后的内容中
        protected_section = "\n\n【重要商品信息】\n"
        for info in protected_info:
            protected_section += f"• {info}\n"

        return cleaned_content + protected_section

    def _final_optimization(self, content: str) -> str:
        """最终优化"""
        # 清理重复的空行
        content = re.sub(r"\n{3,}", "\n\n", content)

        # 清理重复的空格
        content = re.sub(r" {3,}", " ", content)

        # 移除首尾空白
        content = content.strip()

        return content

    def get_cleaning_stats(
        self, original_content: str = None, cleaned_content: str = None
    ) -> Dict[str, Any]:
        """获取清理统计信息"""
        if original_content and cleaned_content:
            self.stats.update(
                {
                    "original_length": len(original_content),
                    "cleaned_length": len(cleaned_content),
                    "compression_ratio": (
                        (1 - len(cleaned_content) / len(original_content)) * 100
                        if len(original_content) > 0
                        else 0
                    ),
                }
            )

        # 计算额外统计
        if cleaned_content:
            self.stats.update(
                {
                    "chinese_chars": len(
                        [c for c in cleaned_content if "\u4e00" <= c <= "\u9fff"]
                    ),
                    "english_words": len(cleaned_content.split()),
                    "numbers": len([c for c in cleaned_content if c.isdigit()]),
                    "lines": len(cleaned_content.split("\n")),
                }
            )

        return self.stats.copy()
