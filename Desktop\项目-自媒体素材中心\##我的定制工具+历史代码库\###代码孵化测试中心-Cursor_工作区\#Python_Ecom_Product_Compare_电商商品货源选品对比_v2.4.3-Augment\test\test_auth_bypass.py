#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证绕过功能测试脚本
测试新的抓取器是否能够成功绕过1688验证码
"""

import asyncio
import logging
from scrapers import WebScraper

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)


async def test_auth_bypass():
    """测试验证绕过功能"""

    # 测试URL - 可以替换为实际遇到验证码的1688页面
    test_urls = [
        "https://detail.1688.com/offer/743079095948.html",  # 示例1688商品页面
        "https://detail.1688.com/offer/743079095948.html?spm=a2615.7691456.co_1_0_wangpu_score_0_0_0_0_0_0_0000_0.0",
    ]

    print("🚀 开始测试增强版验证绕过功能...")
    print("✨ 新增：自动刷新绕过机制 - 基于参考项目的成功方案")
    print("⚠️  注意：系统会首先尝试自动绕过，如果失败才需要手动处理")
    print("=" * 60)

    async with WebScraper(headless=False) as scraper:  # 显示浏览器便于处理验证码
        for i, url in enumerate(test_urls, 1):
            print(f"\n📋 测试 {i}/{len(test_urls)}: {url}")

            try:
                result = await scraper.scrape_url(url)

                if result and result.success:
                    print("✅ 抓取成功!")
                    print(
                        f"   标题: {result.title[:100] if result.title else 'None'}..."
                    )
                    print(f"   价格: {result.price[:50] if result.price else 'None'}")
                    print(
                        f"   供应商: {result.supplier[:50] if result.supplier else 'None'}"
                    )
                    print(f"   内容长度: {len(result.content)} 字符")
                    print(f"   图片数量: {len(result.images)}")
                else:
                    print("❌ 抓取失败!")
                    if result:
                        print(f"   错误信息: {result.error_message}")
                    else:
                        print("   返回结果为None")

            except Exception as e:
                print(f"❌ 抓取异常: {e}")

            print("-" * 40)

    print("\n🎉 测试完成!")


async def test_standard_site():
    """测试标准网站（非1688）的抓取"""

    print("\n🔍 测试标准网站抓取...")

    test_url = "https://www.baidu.com"

    async with WebScraper(headless=True) as scraper:
        result = await scraper.scrape_url(test_url)

        if result and result.success:
            print("✅ 标准网站抓取成功!")
            print(f"   标题: {result.title}")
        else:
            print("❌ 标准网站抓取失败!")


def main():
    """主函数"""
    print("🧪 增强版验证绕过功能测试")
    print("基于参考项目的成功方案，新增自动刷新绕过机制")
    print("专门处理1688滑动验证码，无需手动操作！")
    print("=" * 60)

    try:
        # 测试验证绕过功能
        asyncio.run(test_auth_bypass())

        # 测试标准网站
        asyncio.run(test_standard_site())

    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试发生异常: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
