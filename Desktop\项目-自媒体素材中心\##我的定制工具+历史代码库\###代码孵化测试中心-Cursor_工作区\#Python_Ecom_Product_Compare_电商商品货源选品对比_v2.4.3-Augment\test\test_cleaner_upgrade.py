#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版数据清洗器 - 验证73个业务保护模式
重点验证"48小时发货"等关键信息的保护效果
"""

import logging
import json
from scrapers.enhanced_data_cleaner import EnhancedDataCleaner

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)


def test_cleaner_with_sample_data():
    """使用示例数据测试清洗器"""
    print("=" * 80)
    print("🧪 测试增强版数据清洗器 - 73个业务保护模式")
    print("=" * 80)

    # 创建增强清洗器
    cleaner = EnhancedDataCleaner()

    # 构造包含关键信息的测试HTML
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试页面</title>
        <style>
            .header { background: #000; }
            .useless { display: none; }
        </style>
        <script>
            console.log("无用脚本");
            var tracking = "abc123456789";
        </script>
    </head>
    <body>
        <div class="header-nav">导航垃圾内容</div>
        <div class="ad-banner">广告内容</div>
        
        <!-- 重要的业务信息 -->
        <div class="product-info">
            <span class="logistics-text" data-name="limitTimeDesc">承诺48小时发货</span>
            <div class="daifa-info">
                代发参谋数据：
                <span>24小时揽收率：82.00%</span>
                <span>48小时揽收率：98.00%</span>
                <span>月代发订单数：100以内</span>
                <span>下游铺货数：400+</span>
            </div>
            <div class="price-info">
                价格：¥9.8起
                重量：500克
                材质：优质塑料+金属
                供应商：义乌玩具厂
            </div>
            <div class="service-info">
                综合服务：4.8分
                支持7天退换货
                实地验厂
                一件代发
            </div>
        </div>
        
        <div class="footer-tracking">
            <!-- 大量垃圾跟踪代码 -->
            <script>
                var spm = "a2g0o.productlist.0.0.1234abcd";
                var pvid = "12345678901234567890";
            </script>
        </div>
    </body>
    </html>
    """

    print(f"📊 原始HTML长度: {len(test_html):,} 字符")
    print("\n🔍 原始内容中的关键信息：")

    # 检查原始内容中的关键信息
    key_checks = [
        ("48小时发货", "承诺48小时发货"),
        ("24小时揽收率", "24小时揽收率：82.00%"),
        ("代发参谋", "代发参谋数据"),
        ("价格信息", "价格：¥9.8起"),
        ("供应商", "供应商：义乌玩具厂"),
    ]

    for name, keyword in key_checks:
        if keyword in test_html:
            print(f"   ✅ {name}: {keyword}")
        else:
            print(f"   ❌ {name}: 未找到")

    print("\n" + "🔧 开始清洗测试..." + "\n")

    # 清洗内容
    cleaned_content = cleaner.clean_html_content(test_html)

    print(f"\n📊 清洗后内容长度: {len(cleaned_content):,} 字符")
    print(f"📈 压缩率: {(1 - len(cleaned_content) / len(test_html)) * 100:.1f}%")

    print("\n🔍 清洗后保护的关键信息：")

    # 检查清洗后的关键信息保护情况
    protection_results = {}
    for name, keyword in key_checks:
        # 检查完整关键词
        if keyword in cleaned_content:
            protection_results[name] = "完整保护"
            print(f"   ✅ {name}: 完整保护 - {keyword}")
        # 检查部分关键词
        elif any(part in cleaned_content for part in keyword.split("：")):
            protection_results[name] = "部分保护"
            print(f"   🟡 {name}: 部分保护")
        else:
            protection_results[name] = "未保护"
            print(f"   ❌ {name}: 未保护")

    # 获取清洗统计
    stats = cleaner.get_cleaning_stats(test_html, cleaned_content)

    print("\n📈 详细清洗统计：")
    for key, value in stats.items():
        if key == "key_info_status":
            print(f"   🔍 关键信息状态:")
            for info_name, status in value.items():
                status_icon = "✅" if status else "❌"
                print(f"      {status_icon} {info_name}: {status}")
        elif key == "protected_patterns_sample":
            print(f"   🛡️ 保护模式示例:")
            for pattern_info in value:
                print(f"      └─ {pattern_info['pattern']}: {pattern_info['matches']}")
        else:
            print(f"   📊 {key}: {value}")

    # 提取关键段落
    sections = cleaner.extract_key_sections(cleaned_content)

    print("\n📑 提取的关键业务段落：")
    for section_name, content in sections.items():
        print(f"   📝 {section_name}: {content}")

    print("\n🎯 最终清洗结果预览（前500字符）：")
    print("-" * 60)
    print(cleaned_content[:500])
    if len(cleaned_content) > 500:
        print(f"\n... (还有 {len(cleaned_content) - 500} 个字符)")
    print("-" * 60)

    # 重点验证"48小时发货"保护
    print("\n🚨 重点验证：48小时发货信息保护")
    shipping_patterns = [
        r"承诺.*?发货",
        r"\d+小时.*?发货",
        r"发货.*?\d+小时",
        r"48小时",
        r"承诺48小时发货",
    ]

    import re

    for pattern in shipping_patterns:
        matches = re.findall(pattern, cleaned_content, re.IGNORECASE)
        if matches:
            print(f"   ✅ 模式 '{pattern}' 匹配: {matches}")
        else:
            print(f"   ❌ 模式 '{pattern}' 无匹配")

    # 评估清洗质量
    print("\n🏆 清洗质量评估：")

    success_rate = sum(
        1 for result in protection_results.values() if result != "未保护"
    ) / len(protection_results)

    if success_rate >= 0.8 and stats.get("compression_ratio", 0) >= 80:
        quality = "优秀"
        icon = "🎉"
    elif success_rate >= 0.6 and stats.get("compression_ratio", 0) >= 70:
        quality = "良好"
        icon = "👍"
    else:
        quality = "需要改进"
        icon = "⚠️"

    print(f"   {icon} 总体质量: {quality}")
    print(f"   📊 关键信息保护率: {success_rate * 100:.1f}%")
    print(f"   📈 内容压缩率: {stats.get('compression_ratio', 0):.1f}%")
    print(f"   🛡️ 保护的业务指标数: {stats.get('protected_business_indicators', 0)}")

    return {
        "cleaned_content": cleaned_content,
        "protection_results": protection_results,
        "stats": stats,
        "sections": sections,
        "quality": quality,
    }


def test_with_original_data():
    """使用原始数据文件测试"""
    print("\n" + "=" * 80)
    print("🧪 使用原始数据文件测试")
    print("=" * 80)

    try:
        cleaner = EnhancedDataCleaner()

        # 读取原始数据
        with open("原始数据.txt", "r", encoding="utf-8") as f:
            original_data = json.load(f)

        raw_content = original_data.get("content", "")

        print(f"📊 原始数据长度: {len(raw_content):,} 字符")

        # 检查原始数据中的发货信息
        if "48小时发货" in raw_content:
            print("✅ 原始数据包含'48小时发货'信息")
        else:
            print("❌ 原始数据不包含'48小时发货'信息")

        # 清洗数据
        cleaned_content = cleaner.clean_html_content(raw_content)

        print(f"📊 清洗后长度: {len(cleaned_content):,} 字符")

        # 检查清洗后的发货信息
        if "48小时发货" in cleaned_content or "48小时" in cleaned_content:
            print("✅ 清洗后保护了发货信息")
        else:
            print("❌ 清洗后丢失了发货信息")

        # 获取统计信息
        stats = cleaner.get_cleaning_stats(raw_content, cleaned_content)
        print(f"📈 压缩率: {stats.get('compression_ratio', 0):.1f}%")

        return cleaned_content

    except FileNotFoundError:
        print("❌ 未找到原始数据.txt文件，跳过此测试")
        return None
    except Exception as e:
        print(f"❌ 测试原始数据时出错: {e}")
        return None


def main():
    """主函数"""
    print("🚀 启动增强版数据清洗器测试")

    # 测试1：使用示例数据
    result1 = test_cleaner_with_sample_data()

    # 测试2：使用原始数据文件
    result2 = test_with_original_data()

    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)

    if result1["quality"] in ["优秀", "良好"]:
        print("✅ 示例数据测试通过")
    else:
        print("❌ 示例数据测试需要改进")

    if result2:
        print("✅ 原始数据测试完成")
    else:
        print("⚠️ 原始数据测试跳过")

    print("\n🎯 关键发现：")
    print("1. 清洗器已集成73个业务保护模式")
    print("2. 特别加强了发货承诺信息的保护")
    print("3. 增加了详细的监控日志")
    print("4. 实现了多阶段清洗流程")

    print("\n🚀 增强版清洗器准备就绪！")


if __name__ == "__main__":
    main()
