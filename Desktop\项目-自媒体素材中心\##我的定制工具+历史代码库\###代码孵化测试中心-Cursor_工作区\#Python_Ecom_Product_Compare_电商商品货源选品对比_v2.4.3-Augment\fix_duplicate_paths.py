import sqlite3
import json
from pathlib import Path


def fix_duplicate_image_paths():
    """修复数据库中的重复图片路径"""
    conn = sqlite3.connect("data/database.db")
    cursor = conn.cursor()

    # 查询所有产品的图片信息
    cursor.execute("SELECT id, name, images, featured_images FROM products")
    products = cursor.fetchall()

    fixed_count = 0

    for product_id, name, images_json, featured_images_json in products:
        if images_json and images_json != "[]":
            images = json.loads(images_json)
            original_count = len(images)

            # 去重，保持顺序
            unique_images = []
            seen = set()
            for img in images:
                if img not in seen:
                    unique_images.append(img)
                    seen.add(img)

            # 如果有重复，更新数据库
            if len(unique_images) != original_count:
                print(f"产品 {product_id} ({name}):")
                print(f"  修复前: {original_count} 张图片")
                print(f"  修复后: {len(unique_images)} 张图片")
                print(f"  移除了 {original_count - len(unique_images)} 个重复路径")

                # 更新主图片列表
                featured_images = []
                if featured_images_json and featured_images_json != "[]":
                    featured_images = json.loads(featured_images_json)

                # 清理主图片列表，确保只包含存在的图片
                unique_featured_images = []
                seen_featured = set()
                for img in featured_images:
                    if img in unique_images and img not in seen_featured:
                        unique_featured_images.append(img)
                        seen_featured.add(img)

                # 更新数据库
                cursor.execute(
                    "UPDATE products SET images = ?, featured_images = ? WHERE id = ?",
                    (
                        json.dumps(unique_images),
                        json.dumps(unique_featured_images),
                        product_id,
                    ),
                )
                fixed_count += 1
                print(f"  ✓ 数据库已更新")
                print()

    conn.commit()
    conn.close()

    print(f"修复完成！共修复了 {fixed_count} 个产品的重复图片路径")


if __name__ == "__main__":
    fix_duplicate_image_paths()
