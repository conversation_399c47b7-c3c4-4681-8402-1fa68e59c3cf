"""
图片查重结果展示对话框
显示重复图片组和详细信息
"""

from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QScrollArea,
    QWidget,
    QFrame,
    QGridLayout,
    QMessageBox,
    QGroupBox,
    QCheckBox,
    QDialogButtonBox,
    QTextEdit,
    QSplitter,
    QListWidget,
    QListWidgetItem,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QFont, QIcon
from pathlib import Path
from typing import List, Dict, Optional
import os

from .image_utils import ImageDuplicateChecker


class DuplicateResultDialog(QDialog):
    """图片查重结果展示对话框"""

    # 信号：请求删除图片
    images_to_delete = pyqtSignal(list)

    def __init__(self, duplicate_groups: List[List[str]], parent=None):
        super().__init__(parent)
        self.duplicate_groups = duplicate_groups
        self.checker = ImageDuplicateChecker()
        self.selected_for_deletion = []

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("图片查重结果")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)

        # 设置样式
        self.setStyleSheet(
            """
            QDialog {
                background-color: #2b2b2b;
                color: white;
            }
            QLabel {
                color: white;
            }
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: white;
            }
            QFrame {
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #3d3d3d;
            }
            QCheckBox {
                color: white;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:checked {
                background-color: #f44336;
                border: 2px solid #f44336;
            }
            QCheckBox::indicator:unchecked {
                background-color: #666666;
                border: 2px solid #666666;
            }
            QPushButton {
                background-color: #1976d2;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
            QPushButton[objectName="deleteButton"] {
                background-color: #f44336;
            }
            QPushButton[objectName="deleteButton"]:hover {
                background-color: #d32f2f;
            }
            QTextEdit {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                color: white;
                border-radius: 4px;
            }
            QListWidget {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                color: white;
                border-radius: 4px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #555555;
            }
            QListWidget::item:selected {
                background-color: #1976d2;
            }
        """
        )

        layout = QVBoxLayout(self)

        # 标题和统计信息
        self.create_header(layout)

        # 结果展示区域
        if self.duplicate_groups:
            self.create_results_area(layout)
        else:
            self.create_no_duplicates_message(layout)

        # 按钮区域
        self.create_button_area(layout)

    def create_header(self, layout):
        """创建标题和统计信息"""
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)

        # 标题
        title_label = QLabel("🔍 图片查重结果")
        title_label.setFont(QFont("Microsoft YaHei UI", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)

        # 统计信息
        if self.duplicate_groups:
            total_duplicates = sum(len(group) for group in self.duplicate_groups)
            stats_text = f"发现 {len(self.duplicate_groups)} 个重复组，共 {total_duplicates} 张重复图片"
        else:
            stats_text = "未发现重复图片"

        stats_label = QLabel(stats_text)
        stats_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        stats_label.setStyleSheet("color: #4CAF50; font-size: 14px; margin: 10px;")
        header_layout.addWidget(stats_label)

        layout.addWidget(header_widget)

    def create_no_duplicates_message(self, layout):
        """创建无重复图片的消息"""
        message_widget = QWidget()
        message_layout = QVBoxLayout(message_widget)

        # 图标和消息
        icon_label = QLabel("✅")
        icon_label.setFont(QFont("Microsoft YaHei UI", 48))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        message_layout.addWidget(icon_label)

        message_label = QLabel("恭喜！未发现重复图片")
        message_label.setFont(QFont("Microsoft YaHei UI", 18))
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        message_label.setStyleSheet("color: #4CAF50; margin: 20px;")
        message_layout.addWidget(message_label)

        desc_label = QLabel("您的图片库中没有重复或相似的图片。")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #999; font-size: 14px;")
        message_layout.addWidget(desc_label)

        message_layout.addStretch()

        layout.addWidget(message_widget)

    def create_results_area(self, layout):
        """创建结果展示区域"""
        # 创建分割窗口
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：重复组列表
        left_widget = self.create_groups_list()
        splitter.addWidget(left_widget)

        # 右侧：图片详情
        right_widget = self.create_image_details()
        splitter.addWidget(right_widget)

        # 设置分割比例
        splitter.setSizes([300, 700])

        layout.addWidget(splitter)

    def create_groups_list(self):
        """创建重复组列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标题
        title_label = QLabel("重复图片组")
        title_label.setFont(QFont("Microsoft YaHei UI", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1976d2; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 列表
        self.groups_list = QListWidget()
        for i, group in enumerate(self.duplicate_groups):
            item_text = f"组 {i+1} ({len(group)} 张图片)"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, i)  # 存储组索引
            self.groups_list.addItem(item)

        self.groups_list.currentRowChanged.connect(self.on_group_selected)
        layout.addWidget(self.groups_list)

        return widget

    def create_image_details(self):
        """创建图片详情区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标题
        self.details_title = QLabel("选择一个重复组查看详情")
        self.details_title.setFont(QFont("Microsoft YaHei UI", 14, QFont.Weight.Bold))
        self.details_title.setStyleSheet("color: #1976d2; margin-bottom: 10px;")
        layout.addWidget(self.details_title)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(500)

        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)

        scroll_area.setWidget(self.details_widget)
        layout.addWidget(scroll_area)

        # 批量选择按钮
        buttons_layout = QHBoxLayout()

        select_all_button = QPushButton("全选此组")
        select_all_button.clicked.connect(self.select_all_in_group)
        buttons_layout.addWidget(select_all_button)

        select_none_button = QPushButton("取消选择")
        select_none_button.clicked.connect(self.select_none_in_group)
        buttons_layout.addWidget(select_none_button)

        # 智能保留按钮
        keep_best_button = QPushButton("🏆 保留最佳")
        keep_best_button.setToolTip(
            "保留此组中文件最大、分辨率最高的图片，删除其他重复图片"
        )
        keep_best_button.clicked.connect(self.keep_best_in_group)
        keep_best_button.setStyleSheet(
            """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        )
        buttons_layout.addWidget(keep_best_button)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        return widget

    def on_group_selected(self, row):
        """选择重复组时的处理"""
        if row < 0 or row >= len(self.duplicate_groups):
            return

        group = self.duplicate_groups[row]
        self.current_group = group

        # 更新标题
        self.details_title.setText(f"重复组 {row+1} - {len(group)} 张图片")

        # 清空之前的内容
        self.clear_layout(self.details_layout)

        # 显示图片详情
        for i, image_path in enumerate(group):
            self.add_image_item(image_path, i)

    def add_image_item(self, image_path: str, index: int):
        """添加图片项"""
        # 创建图片项容器
        item_frame = QFrame()
        item_frame.setMinimumHeight(120)
        item_frame.setMaximumHeight(150)
        item_layout = QHBoxLayout(item_frame)

        # 图片预览
        image_label = QLabel()
        image_label.setFixedSize(100, 100)
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        if os.path.exists(image_path):
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(
                    100,
                    100,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation,
                )
                image_label.setPixmap(scaled_pixmap)
            else:
                image_label.setText("无法加载")
        else:
            image_label.setText("文件不存在")

        image_label.setStyleSheet(
            """
            QLabel {
                border: 2px solid #555555;
                border-radius: 4px;
                background-color: #444444;
            }
        """
        )
        item_layout.addWidget(image_label)

        # 图片信息
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)

        # 文件名
        filename_label = QLabel(Path(image_path).name)
        filename_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        filename_label.setStyleSheet("color: #1976d2;")
        info_layout.addWidget(filename_label)

        # 获取图片信息
        img_info = self.checker.get_image_info(image_path)
        if "error" not in img_info:
            info_text = f"尺寸: {img_info['width']}×{img_info['height']}\n"
            info_text += f"大小: {img_info['size_mb']} MB\n"
            info_text += f"格式: {img_info['format']}"
        else:
            info_text = f"错误: {img_info['error']}"

        info_label = QLabel(info_text)
        info_label.setStyleSheet("color: #ccc; font-size: 10px;")
        info_layout.addWidget(info_label)

        # 路径
        path_label = QLabel(f"路径: {image_path}")
        path_label.setStyleSheet("color: #999; font-size: 9px;")
        path_label.setWordWrap(True)
        info_layout.addWidget(path_label)

        info_layout.addStretch()
        item_layout.addWidget(info_widget)

        # 删除复选框
        delete_checkbox = QCheckBox("删除此图片")
        delete_checkbox.setStyleSheet("color: #f44336; font-weight: bold;")

        # 设置复选框的初始状态
        if image_path in self.selected_for_deletion:
            delete_checkbox.setChecked(True)

        # 存储图片路径作为属性，便于后续查找和更新
        delete_checkbox.setProperty("image_path", image_path)

        delete_checkbox.stateChanged.connect(
            lambda state, path=image_path: self.on_delete_checkbox_changed(state, path)
        )
        item_layout.addWidget(delete_checkbox)

        self.details_layout.addWidget(item_frame)

    def on_delete_checkbox_changed(self, state, image_path):
        """删除复选框状态改变"""
        if state == Qt.CheckState.Checked.value:
            if image_path not in self.selected_for_deletion:
                self.selected_for_deletion.append(image_path)
        else:
            if image_path in self.selected_for_deletion:
                self.selected_for_deletion.remove(image_path)

    def select_all_in_group(self):
        """全选当前组"""
        if not hasattr(self, "current_group"):
            return

        for image_path in self.current_group:
            if image_path not in self.selected_for_deletion:
                self.selected_for_deletion.append(image_path)

        # 更新复选框状态
        self.update_checkboxes()

    def select_none_in_group(self):
        """取消选择当前组"""
        if not hasattr(self, "current_group"):
            return

        for image_path in self.current_group:
            if image_path in self.selected_for_deletion:
                self.selected_for_deletion.remove(image_path)

        # 更新复选框状态
        self.update_checkboxes()

    def keep_best_in_group(self):
        """保留当前组中的最佳图片，删除其他图片"""
        if not hasattr(self, "current_group") or len(self.current_group) < 2:
            QMessageBox.information(self, "提示", "请先选择一个重复组")
            return

        try:
            # 分析每张图片的质量
            best_image = self.find_best_image_in_group(self.current_group)

            if not best_image:
                QMessageBox.warning(self, "错误", "无法确定最佳图片")
                return

            # 确认操作
            from pathlib import Path

            reply = QMessageBox.question(
                self,
                "确认保留最佳图片",
                f"即将保留最佳图片：\n{Path(best_image).name}\n\n"
                f"⚠️ 警告：将永久删除其他 {len(self.current_group) - 1} 张重复图片！\n"
                f"删除的文件无法恢复。\n\n是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,  # 默认选择"否"，更安全
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 收集要删除的图片
                images_to_delete = []
                for image_path in self.current_group:
                    if image_path != best_image:
                        images_to_delete.append(image_path)

                # 将图片添加到删除列表
                for image_path in images_to_delete:
                    if image_path not in self.selected_for_deletion:
                        self.selected_for_deletion.append(image_path)

                # 确保最佳图片不在删除列表中
                if best_image in self.selected_for_deletion:
                    self.selected_for_deletion.remove(best_image)

                # 更新界面
                self.update_checkboxes()

                # 直接执行删除操作
                print(f"单组智能保留：准备删除 {len(images_to_delete)} 张重复图片")

                # 发出删除信号，让父对话框处理删除
                self.images_to_delete.emit(self.selected_for_deletion.copy())

                QMessageBox.information(
                    self,
                    "操作完成",
                    f"已保留最佳图片：{Path(best_image).name}\n"
                    f"删除重复图片：{len(images_to_delete)} 张\n\n"
                    "✓ 已从图片列表中移除\n"
                    "✓ 已删除硬盘上的物理文件\n"
                    "✓ 已同步到数据库",
                )

                # 清空选中列表
                self.selected_for_deletion.clear()

                # 关闭对话框
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"智能保留失败：{str(e)}")
            print(f"单组智能保留失败: {e}")
            import traceback

            traceback.print_exc()

    def find_best_image_in_group(self, image_group):
        """找出组中的最佳图片（基于文件大小和分辨率）"""
        best_image = None
        best_score = -1

        for image_path in image_group:
            try:
                img_info = self.checker.get_image_info(image_path)
                if "error" in img_info:
                    continue

                # 计算图片质量分数（文件大小 + 分辨率）
                file_size_score = img_info["size"] / (1024 * 1024)  # 转换为MB
                resolution_score = (img_info["width"] * img_info["height"]) / (
                    1024 * 1024
                )  # 转换为MP

                # 综合分数：分辨率权重70%，文件大小权重30%
                total_score = resolution_score * 0.7 + file_size_score * 0.3

                if total_score > best_score:
                    best_score = total_score
                    best_image = image_path

            except Exception as e:
                print(f"分析图片质量失败 {image_path}: {e}")
                continue

        return best_image

    def keep_best_in_all_groups(self):
        """为所有重复组保留最佳图片，删除其他图片"""
        if not self.duplicate_groups:
            QMessageBox.information(self, "提示", "没有重复组可以处理")
            return

        try:
            # 分析所有组并找出最佳图片
            total_kept = 0
            total_deleted = 0
            images_to_delete = []

            # 预先分析所有组
            for group_index, group in enumerate(self.duplicate_groups):
                if len(group) < 2:
                    continue

                # 找出最佳图片
                best_image = self.find_best_image_in_group(group)

                if not best_image:
                    print(f"无法确定组 {group_index + 1} 的最佳图片")
                    continue

                # 收集要删除的图片
                for image_path in group:
                    if image_path != best_image:
                        images_to_delete.append(image_path)
                        total_deleted += 1

                total_kept += 1

            if not images_to_delete:
                QMessageBox.information(self, "提示", "没有需要删除的重复图片")
                return

            # 确认操作
            reply = QMessageBox.question(
                self,
                "确认智能保留",
                f"即将为所有 {len(self.duplicate_groups)} 个重复组自动选择最佳图片保留。\n\n"
                f"保留最佳图片：{total_kept} 张\n"
                f"删除重复图片：{total_deleted} 张\n\n"
                "系统将基于图片分辨率和文件大小自动选择最佳图片。\n\n"
                "⚠️ 警告：这将永久删除硬盘上的重复图片文件！\n"
                "删除后无法恢复，请谨慎操作。\n\n"
                "是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,  # 默认选择"否"，更安全
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 添加图片到删除列表
            for image_path in images_to_delete:
                if image_path not in self.selected_for_deletion:
                    self.selected_for_deletion.append(image_path)

            # 更新界面显示
            self.update_checkboxes()

            # 直接执行删除操作
            print(f"智能保留：准备删除 {len(images_to_delete)} 张重复图片")

            # 发出删除信号，让父对话框处理删除
            self.images_to_delete.emit(self.selected_for_deletion.copy())

            # 显示完成消息
            QMessageBox.information(
                self,
                "智能保留完成",
                f"智能保留处理完成！\n\n"
                f"保留最佳图片：{total_kept} 张\n"
                f"删除重复图片：{total_deleted} 张\n\n"
                "✓ 已从图片列表中移除\n"
                "✓ 已删除硬盘上的物理文件\n"
                "✓ 已同步到数据库",
            )

            # 清空选中列表
            self.selected_for_deletion.clear()

            # 关闭对话框
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"智能保留失败：{str(e)}")
            print(f"智能保留失败: {e}")
            import traceback

            traceback.print_exc()

    def update_checkboxes(self):
        """更新复选框状态"""
        # 遍历details_layout中的所有项目
        for i in range(self.details_layout.count()):
            item = self.details_layout.itemAt(i)
            if item and item.widget():
                frame = item.widget()
                # 查找框架中的复选框
                checkbox = frame.findChild(QCheckBox)
                if checkbox:
                    # 获取图片路径
                    image_path = checkbox.property("image_path")
                    if image_path:
                        # 阻止信号触发，避免递归调用
                        checkbox.blockSignals(True)
                        # 根据选择状态设置复选框
                        checkbox.setChecked(image_path in self.selected_for_deletion)
                        # 恢复信号
                        checkbox.blockSignals(False)

        print(
            f"已更新复选框状态，当前选中删除：{len(self.selected_for_deletion)} 张图片"
        )

    def clear_layout(self, layout):
        """清空布局"""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def create_button_area(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()

        # 智能保留所有组按钮
        smart_keep_all_button = QPushButton("🤖 智能保留所有组")
        smart_keep_all_button.setToolTip(
            "为所有重复组自动选择最佳图片保留，永久删除其他重复图片文件"
        )
        smart_keep_all_button.clicked.connect(self.keep_best_in_all_groups)
        smart_keep_all_button.setStyleSheet(
            """
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """
        )
        button_layout.addWidget(smart_keep_all_button)

        # 删除选中的图片按钮
        delete_button = QPushButton("🗑️ 删除选中的图片")
        delete_button.setObjectName("deleteButton")
        delete_button.setToolTip("永久删除选中的重复图片文件（无法恢复）")
        delete_button.clicked.connect(self.delete_selected_images)
        delete_button.setStyleSheet(
            """
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #666;
                color: #999;
            }
        """
        )
        button_layout.addWidget(delete_button)

        button_layout.addStretch()

        # 标准按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.close)
        button_layout.addWidget(button_box)

        layout.addLayout(button_layout)

    def delete_selected_images(self):
        """删除选中的图片"""
        if not self.selected_for_deletion:
            QMessageBox.information(self, "提示", "请先选择要删除的图片")
            return

        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(self.selected_for_deletion)} 张图片吗？\n\n"
            "⚠️ 警告：这将永久删除硬盘上的图片文件！\n"
            "删除后无法恢复，请谨慎操作。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,  # 默认选择"否"，更安全
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 发出删除信号
            self.images_to_delete.emit(self.selected_for_deletion.copy())

            # 显示成功消息
            QMessageBox.information(
                self,
                "删除成功",
                f"已永久删除 {len(self.selected_for_deletion)} 张重复图片\n\n"
                "✓ 已从图片列表中移除\n"
                "✓ 已删除硬盘上的物理文件\n"
                "✓ 已同步到数据库",
            )

            # 清空选中列表
            self.selected_for_deletion.clear()

            # 关闭对话框
            self.accept()
