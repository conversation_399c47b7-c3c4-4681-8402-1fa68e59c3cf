#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析器测试脚本
用于验证AI分析器是否能正确提取代发参谋数据
"""

import asyncio
import json
from scrapers.ai_analyzer import AIAnalyzer

async def test_ai_analyzer():
    """测试AI分析器对清洗数据的处理能力"""
    
    # 用户提供的清洗后数据样本
    test_content = """
跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫一扫进入手机旺铺 TOP1 销量 TOP2 销量 本店 找货源 找工厂 搜索 首页 全部商品 所有产品 （15） 宠物梳子 （3） 宠物玩具 （13） 宠物清洁用品 （7） 猫玩具 （15） 未分类 （22） 店铺动态 加工专区 工厂档案 联系方式 咨询 客服 全网比价 手机 下单 铺货 工具 铺货单 问题 反馈 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 30+ 条评价 一年内 10万+ 个成交 举报 批发 代发 价格 ¥ 2 .80 起 ≥1件 服务 7天无理由退货 晚发必赔 代发参谋 24小时揽收率 82.00% 48小时揽收率 98.00% 月代发订单数 100以内 下游铺货数 100以内 商品退货率 0.00% 商品发布时间 2024年9月 可分销商品数 10以内 月上新商品数 10以内 分销商数 400+ 面单支持 物流 浙江温州 至 请选择 运费 : 选择收货地 承诺48小时发货 更多 商品分类 所有产品 宠物梳子 宠物玩具 宠物清洁用品 猫玩具 未分类 店铺商品销售排行榜 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 ¥ 2.8 已售3267件 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 ¥ 2.8 已售1973件 跨境宠物电动引力跳跳球猫狗玩具耐咬解闷神器大小型犬宠物牵引球 ¥ 2.8 已售28件 新款宠物玩具电动智能逗猫老鼠车自嗨解闷猫咪玩具跑跑车猫玩具 ¥ 6 已售250件 跨境宠物电动引力跳跳球猫狗玩具耐咬解闷神器大小型犬宠物牵引球 ¥ 3.8 已售27件 跨境宠物电动引力跳跳球猫狗玩具耐咬解闷神器大小型犬宠物牵引球 ¥ 2.8 已售119件 跨境宠物逗猫球引力智能滚滚球宠物用品猫玩具自嗨解闷充电逗猫 ¥ 4.2 已售167件 跨境耐咬自动逗猫球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳球 ¥ 2.8 已售20件 跨境引力跳跳球自嗨解闷狗玩具宠物智能滚滚球电动玩具自动遛狗球 ¥ 2.8 已售311件 厂家新款宠物逗猫球自嗨解闷神器引力滚滚球啃咬自动猫咪玩具球 ¥ 4.2 已售25件 查看更多 商品详情 买家评价（30+） 订购说明 商品属性 商品描述 价格说明 商品属性 材质 PC+硅胶 是否进口 否 产品类别 啃咬玩具 货号 猫咪滚滚球 箱装数量 200 重量 75克 品牌 其他 是否专利货源 否 颜色 蜻蜓带绳款带唤醒,带绳款猫球红色【非唤醒模式】,带绳款绿色猫球【非唤醒模式】,带绳款猫球蓝色【非唤醒模式】,带绳款猫球黄色【非唤醒模式】,带绳款猫球粉色【非唤醒模式】,带绳款灰色灰绳子【非唤醒模式】,带绳款灰色红绳子【非唤醒模式】,带绳款猫球紫色【非唤醒模式】,（鸟叫声）带绳款猫球红色智能,鸟叫声）毛绒款猫球红色智能（可备注颜色）,带绳款猫球红色,带绳款猫球绿色,带绳款猫球蓝色,带绳款猫球粉色,带绳款猫球黄色,带绳款猫球灰色红绳子,带绳款猫球灰色灰绳子,带绳款猫球紫色,毛绒款红色,毛绒款绿色,毛绒款灰色,魔尾款红色,魔尾款绿色,魔尾款灰色,新款横纹凸凸球蓝色,新款横纹凸凸球橙色,毛绒款猫球红色（智能）+捕猎罩,（鸟叫声）带绳款猫球绿色智能,（鸟叫声）带绳款猫球灰色智能,带绳款猫球红色+绿色,替换绳,替换毛绒,捕猎罩70cm,蜻蜓带绳款红色（带唤醒）,蜻蜓带绳款绿色（带唤醒）,蜻蜓带绳款灰色（带唤醒）,蜻蜓带绳款蓝色（带唤醒）,蜻蜓带绳款黄色（带唤醒）,蜻蜓带绳款粉色（带唤醒）,齿轮款凸凸球蓝色,齿轮款凸凸球橙色,鸟鸣猫爪款绳子球红色,鸟鸣猫爪款绳子球灰色,鸟鸣猫爪款绳子球绿色,长续航一小时绳子红带唤醒300毫安+保护板 是否跨境出口专供货源 否 是否属于礼品 否 是否IP授权 否 商品描述 带绳款逗猫球 产品净重：57克 产品毛重：75克 外箱箱规：59*34*34.5 装箱数：200 PCS 重量：16.2kg 价格说明 【平台活动下价格】 活动前价格： （1）非分销场景下，指平台活动（不含分销场景的活动）预热期（若无预热期，则为爆发期）前的商品销售价格；（2）分销场景下，专指分销活动预热期（若无预热期，则为爆发期）前的商品销售价格。 前述价格未计算平台发放的各种采购津贴、跨店券、红包等优惠，未计算商家设置的满减优惠、优惠券、店铺返利金、店铺会员折扣以及L会员价、商家设置的指定人群单品优惠活动等各种优惠；该价格会计算其他非平台活动下的各种优惠（包括商家自行设置的非指定人群的单品优惠等，最终以商家的自行设置为准）。前述商品销售价格指商品页面当日展示的标价；若当日商家多次调整销售价格的，取当日最后展示的销售价格。 【非平台活动下价格】 划线价格： 指商家自营销活动场景下的商品价格，该价格不包含平台的各种优惠活动。可能是商品的销售指导价或该商品的曾经展示过的销售价等，并非原价，仅供参考。 未划线价格： 商品在1688平台上由商家自行设置的销售标价，具体的成交价格根据商家设置的各种优惠活动发生变化，最终以订单结算页价格为准。 【发布价】 指商品在1688平台上由商家自行设置的销售标价并叠加L会员价折扣后的价格，并非原价，仅供参考。 *注： 1、前述说明仅适用于价格比较下的场景。 2、活动前的时间通常为预热期/爆发期的前一天，但因数据的延迟性，取价时间可能会发生改变，最终以前述展示的具体时间和所对应的商品价格为准。 同行还在看 跨境宠物电动引力跳跳球猫狗玩具耐咬解闷神器大小型犬宠物牵引球 ¥ 2.8 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 ¥ 2.8 跨境耐咬自动逗猫球猫咪玩具球自嗨解闷神器带绳宠物用品逗猫球 ¥ 1.8 新款宠物玩具电动智能逗猫老鼠车自嗨解闷猫咪玩具跑跑车猫玩具 ¥ 6.0 跨境宠物逗猫球引力智能滚滚球宠物用品猫玩具自嗨解闷充电逗猫 ¥ 4.2 跨境引力跳跳球自嗨解闷狗玩具宠物智能滚滚球电动玩具自动遛狗球 ¥ 2.8 跨境宠物电动引力跳跳球猫狗玩具耐咬解闷神器大小型犬宠物牵引球 ¥ 2.8 跨境耐咬自动逗猫球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳球 ¥ 2.8 宠物洗澡刷宠物清洁用品沐浴按摩刷搓澡按摩梳子猫咪狗狗洗澡刷子 ¥ 2.29 跨境耐咬自动逗猫球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳球 ¥ 2.8 内容声明：阿里巴巴中国站为第三方交易平台及互联网信息服务提供者，阿里巴巴中国站（含网站、客户端等）所展示的商品/服务的标题、价格、详情等信息内容系由店铺经营者发布，其真实性、准确性和合法性均由店铺经营者负责。阿里巴巴提醒您购买商品/服务前注意谨慎核实，如您对商品/服务的标题、价格、详情等任何信息有任何疑问的，请在购买前通过阿里旺旺与店铺经营者沟通确认；阿里巴巴中国站存在海量店铺，如您发现店铺内有任何违法/侵权信息，请立即向阿里巴巴举报并提供有效线索。 新手指南 买家入门 开通支付宝 绑定支付宝 安装阿里旺旺 交易安全 买家防骗 交易纠纷 投诉举报 采购商服务 找公司 找产品 进货单 1688服务 诚e赊 1688商学院 商友圈 在线客服 温州辛宠宠物用品有限公司 地址：中国 浙江 温州 瑞安市南滨街道林北村九弄9号 技术支持： 旺铺管理入口 | 免责声明 | 客服中心 阿里巴巴集团 | 阿里巴巴国际站 | 1688 | 全球速卖通 | 淘宝网 | 天猫 | 聚划算 | 一淘 | 阿里旅行·去阿 | 阿里妈妈 | 酷盘 | 虾米 | 阿里云计算 | YunOS | 阿里通信 | 万网 | 支付宝 | 来往 | 11 Main | 钉钉 @2010-2020 1688.com 版权所有 著作权与商标声明 | 法律声明 | 服务条款 | 隐私声明 | 关于阿里巴巴 | 联系我们 | 网站导航 承诺48小时发货
"""

    title = "跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳"
    
    print("🔬 AI分析器测试开始...")
    print(f"📝 商品标题: {title}")
    print(f"📊 内容长度: {len(test_content):,} 字符")
    print(f"📋 预期代发参谋数据:")
    print("   - 24小时揽收率: 82.00%")
    print("   - 48小时揽收率: 98.00%")
    print("   - 月代发订单数: 100以内")
    print("   - 分销商数: 400+")
    print("   - 商品退货率: 0.00%")
    print("\n" + "="*60)
    
    # 初始化AI分析器
    analyzer = AIAnalyzer()
    
    try:
        # 测试连接
        print("🔗 测试AI连接...")
        connection_ok = await analyzer.test_connection()
        if not connection_ok:
            print("❌ AI连接失败！请检查Ollama服务是否运行。")
            return
        print("✅ AI连接正常")
        
        # 进行分析
        print("🧠 开始AI分析...")
        
        # 创建模拟的抓取内容对象
        class MockScrapedContent:
            def __init__(self):
                self.title = title
                self.content = test_content
                self.description = test_content
                self.cleaned_content = test_content
                self.url = "https://detail.1688.com/offer/840138940155.html"
                self.source_type = "1688"
                self.images = []  # 添加缺失的属性
                self.price = "¥2.80"
                self.price_range = "≥1件"
                self.supplier = "温州辛宠宠物用品有限公司"
                self.specifications = {}
                self.success = True
                self.error_message = ""
                self.scraped_at = ""
                self.original_content = test_content
                
        scraped_content = MockScrapedContent()
        result = await analyzer.analyze_scraped_content(scraped_content)
        
        if result.success:
            print("✅ AI分析成功！")
            print(f"⏱️  处理时间: {result.processing_time:.2f}秒")
            
            # 检查代发参谋数据
            dropship_data = result.dropship_data
            print(f"\n🚚 代发参谋数据分析结果:")
            
            if dropship_data:
                for key, value in dropship_data.items():
                    if value:
                        print(f"   ✅ {key}: {value}")
                    else:
                        print(f"   ❌ {key}: 未识别")
            else:
                print("   ❌ 代发参谋数据为空！")
            
            # 检查其他重要数据
            print(f"\n📦 其他数据分析结果:")
            print(f"   价格信息: {len(result.price_info)} 个字段")
            print(f"   商品属性: {len(result.product_attributes)} 个字段")
            print(f"   销售数据: {len(result.sales_data)} 个字段")
            print(f"   包装信息: {len(result.packaging_info)} 个字段")
            print(f"   服务信息: {len(result.service_info)} 个字段")
            
            # 输出完整结果到文件
            output_data = {
                "title": result.title,
                "cleaned_content": result.cleaned_content,
                "price_info": result.price_info,
                "product_attributes": result.product_attributes,
                "sales_data": result.sales_data,
                "dropship_data": result.dropship_data,
                "service_info": result.service_info,
                "packaging_info": result.packaging_info,
                "variant_info": result.variant_info,
                "processing_time": result.processing_time
            }
            
            with open("AI分析测试结果.json", "w", encoding="utf-8") as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 完整分析结果已保存到: AI分析测试结果.json")
            
        else:
            print(f"❌ AI分析失败: {result.error_message}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ai_analyzer()) 