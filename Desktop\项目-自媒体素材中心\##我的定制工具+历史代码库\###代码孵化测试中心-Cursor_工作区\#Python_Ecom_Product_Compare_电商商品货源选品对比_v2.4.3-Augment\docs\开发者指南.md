# 开发者指南

## 项目概述

### 项目结构
```
Python_Ecom_Product_Source_Compare_v2.3.6/
├── main.py                     # 应用程序入口
├── config.py                   # 配置文件
├── setup_scraper.py            # AI环境配置脚本
├── requirements.txt            # 依赖包列表
├── start.bat                   # Windows启动脚本
├── 
├── models/                     # 数据模型层
│   ├── __init__.py
│   ├── database.py             # 数据库管理
│   ├── product.py              # 产品模型
│   └── source.py               # 货源模型
├── 
├── views/                      # 视图层
│   ├── __init__.py
│   ├── main_window.py          # 主窗口
│   ├── product_list.py         # 产品列表
│   ├── product_detail.py       # 产品详情
│   └── dialogs/                # 对话框
│       ├── product_dialog.py   # 产品编辑对话框
│       ├── source_dialog.py    # 货源编辑对话框
│       └── smart_scraper_tab.py # 智能抓取选项卡
├── 
├── scrapers/                   # 智能抓取模块 (v2.3.6新增)
│   ├── __init__.py
│   ├── web_scraper.py          # 网页抓取器
│   ├── ai_analyzer.py          # AI分析引擎
│   └── data_mapper.py          # 数据映射器
├── 
├── utils/                      # 工具函数
│   ├── __init__.py
│   ├── image_utils.py          # 图片处理工具
│   ├── export_utils.py         # 导入导出工具
│   └── ui_utils.py             # UI工具函数
├── 
├── data/                       # 数据目录
│   ├── database.db             # SQLite数据库
│   ├── images/                 # 图片存储
│   └── logs/                   # 日志文件
├── 
└── docs/                       # 文档目录
    ├── AI智能抓取技术文档.md
    ├── 开发者指南.md
    └── API文档.md
```

### 技术栈
- **UI框架**：PyQt6
- **数据库**：SQLite
- **网页抓取**：Playwright
- **AI模型**：qwen2.5系列 (通过Ollama)
- **图片处理**：Pillow
- **异步编程**：asyncio

## 开发环境配置

### 1. 基础环境
```bash
# Python版本要求
Python 3.8+

# 安装基础依赖
pip install -r requirements.txt

# 配置AI环境（可选）
python setup_scraper.py
```

### 2. 开发工具配置
```bash
# 推荐IDE：VS Code / PyCharm
# 代码格式化：black
pip install black

# 类型检查：mypy
pip install mypy

# 测试框架：pytest
pip install pytest pytest-qt
```

### 3. 调试配置
```python
# main.py 中启用调试模式
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用Qt调试信息
import os
os.environ['QT_DEBUG_PLUGINS'] = '1'
```

## 核心架构解析

### MVC架构模式

#### Models (数据模型层)
```python
# models/product.py
class Product:
    """产品模型"""
    def __init__(self, id=None, name="", code="", price=0.0, ...):
        self.id = id
        self.name = name
        # ... 其他属性
    
    def save(self):
        """保存到数据库"""
        pass
    
    @classmethod
    def find_by_id(cls, product_id):
        """根据ID查找产品"""
        pass
```

#### Views (视图层)
```python
# views/main_window.py
class MainWindow(QMainWindow):
    """主窗口视图"""
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI布局"""
        pass
    
    def setup_connections(self):
        """设置信号槽连接"""
        pass
```

#### Controllers (控制层)
```python
# controllers/product_controller.py
class ProductController:
    """产品控制器"""
    def __init__(self, view, model):
        self.view = view
        self.model = model
    
    def handle_product_save(self, product_data):
        """处理产品保存逻辑"""
        pass
```

### 信号槽机制
```python
# 定义信号
class CustomWidget(QWidget):
    # 自定义信号
    data_changed = pyqtSignal(dict)
    progress_updated = pyqtSignal(str, int)
    
    def emit_data_change(self, data):
        """发射数据变化信号"""
        self.data_changed.emit(data)

# 连接信号槽
widget.data_changed.connect(self.handle_data_change)
widget.progress_updated.connect(self.update_progress)
```

## 智能抓取系统开发

### 1. 网页抓取器扩展

#### 添加新平台支持
```python
# scrapers/web_scraper.py
class WebScraper:
    def __init__(self):
        self.platform_selectors = {
            '1688': {...},
            'taobao': {...},
            # 添加新平台
            'jd': {
                'title': '.sku-name',
                'price': '.price .p-price',
                'images': '.spec-list img',
                'description': '.product-intro'
            }
        }
    
    def _detect_platform(self, url: str) -> str:
        """检测电商平台"""
        if 'jd.com' in url:
            return 'jd'
        # ... 其他平台检测
        return 'unknown'
```

#### 自定义抓取选择器
```python
def add_custom_selectors(self, platform: str, selectors: dict):
    """添加自定义选择器"""
    self.platform_selectors[platform] = selectors

# 使用示例
scraper = WebScraper()
scraper.add_custom_selectors('custom_platform', {
    'title': '.product-title',
    'price': '.price-value',
    'images': '.gallery img'
})
```

### 2. AI分析引擎扩展

#### 自定义AI提示词
```python
# scrapers/ai_analyzer.py
class AIAnalyzer:
    def __init__(self):
        self.prompts = {
            'text_analysis': self._default_text_prompt,
            'image_analysis': self._default_image_prompt
        }
    
    def set_custom_prompt(self, prompt_type: str, prompt: str):
        """设置自定义提示词"""
        self.prompts[prompt_type] = prompt
    
    def _build_prompt(self, prompt_type: str, content: dict) -> str:
        """构建提示词"""
        prompt_template = self.prompts[prompt_type]
        return prompt_template.format(**content)
```

#### 添加新的AI模型支持
```python
def add_model_support(self, model_name: str, model_config: dict):
    """添加新模型支持"""
    self.supported_models[model_name] = model_config

# 配置示例
analyzer.add_model_support('custom_model:latest', {
    'type': 'text',
    'max_tokens': 4096,
    'temperature': 0.7
})
```

### 3. 数据映射器定制

#### 自定义字段映射
```python
# scrapers/data_mapper.py
class DataMapper:
    def __init__(self):
        self.field_mappings = self._default_mappings()
    
    def add_custom_mapping(self, field_name: str, mapping_config: dict):
        """添加自定义字段映射"""
        self.field_mappings[field_name] = mapping_config
    
    def _default_mappings(self):
        return {
            'supplier_name': {
                'source': 'ai_analysis.supplier_name',
                'fallback': 'scraped_data.title',
                'processor': 'clean_supplier_name'
            }
            # ... 其他映射
        }

# 使用示例
mapper = DataMapper()
mapper.add_custom_mapping('custom_field', {
    'source': 'ai_analysis.custom_data',
    'processor': 'custom_processor'
})
```

#### 自定义数据处理器
```python
def register_processor(self, name: str, processor_func):
    """注册自定义数据处理器"""
    self.processors[name] = processor_func

# 处理器示例
def custom_price_processor(value):
    """自定义价格处理器"""
    # 移除货币符号，转换为浮点数
    import re
    price_str = re.sub(r'[^\d.]', '', str(value))
    return float(price_str) if price_str else 0.0

mapper.register_processor('custom_price', custom_price_processor)
```

## UI组件开发

### 1. 自定义对话框
```python
from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout
from PyQt6.QtCore import pyqtSignal

class CustomDialog(QDialog):
    """自定义对话框基类"""
    data_accepted = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("自定义对话框")
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI布局"""
        layout = QVBoxLayout()
        
        # 添加控件
        self.content_widget = self.create_content_widget()
        layout.addWidget(self.content_widget)
        
        # 添加按钮
        button_layout = self.create_button_layout()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_content_widget(self):
        """创建内容控件 - 子类重写"""
        return QWidget()
    
    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        
        layout.addStretch()
        layout.addWidget(self.ok_button)
        layout.addWidget(self.cancel_button)
        
        return layout
    
    def setup_connections(self):
        """设置信号连接"""
        self.ok_button.clicked.connect(self.accept_data)
        self.cancel_button.clicked.connect(self.reject)
    
    def accept_data(self):
        """接受数据并发射信号"""
        data = self.get_data()
        if self.validate_data(data):
            self.data_accepted.emit(data)
            self.accept()
    
    def get_data(self) -> dict:
        """获取对话框数据 - 子类重写"""
        return {}
    
    def validate_data(self, data: dict) -> bool:
        """验证数据 - 子类重写"""
        return True
```

### 2. 自定义工作线程
```python
from PyQt6.QtCore import QThread, pyqtSignal

class CustomWorker(QThread):
    """自定义工作线程"""
    progress_updated = pyqtSignal(str, int)
    result_ready = pyqtSignal(object)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, task_data):
        super().__init__()
        self.task_data = task_data
        self.is_cancelled = False
    
    def run(self):
        """执行后台任务"""
        try:
            self.progress_updated.emit("开始处理...", 0)
            
            # 执行具体任务
            result = self.execute_task()
            
            if not self.is_cancelled:
                self.progress_updated.emit("处理完成", 100)
                self.result_ready.emit(result)
                
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def execute_task(self):
        """执行具体任务 - 子类重写"""
        pass
    
    def cancel(self):
        """取消任务"""
        self.is_cancelled = True
```

### 3. 响应式布局设计
```python
class ResponsiveWidget(QWidget):
    """响应式控件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """设置响应式布局"""
        self.main_layout = QVBoxLayout()
        
        # 使用伸缩布局
        self.content_layout = QHBoxLayout()
        self.content_layout.setStretchFactor(0, 1)  # 左侧面板
        self.content_layout.setStretchFactor(1, 2)  # 右侧面板
        
        self.main_layout.addLayout(self.content_layout)
        self.setLayout(self.main_layout)
    
    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        self.adjust_layout_for_size(event.size())
    
    def adjust_layout_for_size(self, size):
        """根据窗口大小调整布局"""
        if size.width() < 800:
            # 小屏幕：垂直布局
            self.switch_to_vertical_layout()
        else:
            # 大屏幕：水平布局
            self.switch_to_horizontal_layout()
```

## 数据库操作

### 1. 数据模型扩展
```python
# models/base.py
class BaseModel:
    """数据模型基类"""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def save(self):
        """保存到数据库"""
        if hasattr(self, 'id') and self.id:
            return self._update()
        else:
            return self._insert()
    
    def _insert(self):
        """插入新记录"""
        # 实现插入逻辑
        pass
    
    def _update(self):
        """更新已有记录"""
        # 实现更新逻辑
        pass
    
    @classmethod
    def find_by_id(cls, record_id):
        """根据ID查找记录"""
        # 实现查找逻辑
        pass
    
    @classmethod
    def find_all(cls, conditions=None):
        """查找所有记录"""
        # 实现查找逻辑
        pass
```

### 2. 数据库迁移
```python
# models/migrations.py
class DatabaseMigration:
    """数据库迁移管理"""
    
    def __init__(self, db_connection):
        self.db = db_connection
        self.current_version = self.get_db_version()
    
    def migrate_to_latest(self):
        """迁移到最新版本"""
        migrations = self.get_pending_migrations()
        
        for migration in migrations:
            print(f"执行迁移: {migration['name']}")
            migration['function'](self.db)
            self.update_db_version(migration['version'])
    
    def get_pending_migrations(self):
        """获取待执行的迁移"""
        all_migrations = [
            {
                'version': '2.3.6',
                'name': 'add_scraping_tables',
                'function': self.add_scraping_tables
            }
            # 添加更多迁移
        ]
        
        return [m for m in all_migrations if m['version'] > self.current_version]
    
    def add_scraping_tables(self, db):
        """添加抓取相关表"""
        db.execute('''
            CREATE TABLE IF NOT EXISTS scraping_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT NOT NULL,
                platform TEXT,
                status TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
```

### 3. 数据导入导出
```python
# utils/export_utils.py
class DataExporter:
    """数据导出工具"""
    
    def export_to_excel(self, data, filename):
        """导出到Excel"""
        import pandas as pd
        
        df = pd.DataFrame(data)
        df.to_excel(filename, index=False)
    
    def export_to_json(self, data, filename):
        """导出到JSON"""
        import json
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def import_from_excel(self, filename):
        """从Excel导入"""
        import pandas as pd
        
        df = pd.read_excel(filename)
        return df.to_dict('records')
```

## 测试开发

### 1. 单元测试
```python
# tests/test_models.py
import unittest
from models.product import Product

class TestProduct(unittest.TestCase):
    """产品模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.product = Product(
            name="测试产品",
            code="TEST001",
            price=100.0
        )
    
    def test_product_creation(self):
        """测试产品创建"""
        self.assertEqual(self.product.name, "测试产品")
        self.assertEqual(self.product.code, "TEST001")
        self.assertEqual(self.product.price, 100.0)
    
    def test_product_save(self):
        """测试产品保存"""
        result = self.product.save()
        self.assertIsNotNone(result)
    
    def tearDown(self):
        """测试后清理"""
        # 清理测试数据
        pass
```

### 2. UI测试
```python
# tests/test_ui.py
import pytest
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt
from views.main_window import MainWindow

@pytest.fixture
def app():
    """测试应用程序夹具"""
    app = QApplication([])
    yield app
    app.quit()

@pytest.fixture
def main_window(app):
    """主窗口夹具"""
    window = MainWindow()
    return window

def test_main_window_title(main_window):
    """测试主窗口标题"""
    assert "电商商品货源选品对比工具" in main_window.windowTitle()

def test_product_list_interaction(main_window, qtbot):
    """测试产品列表交互"""
    # 模拟点击添加产品按钮
    qtbot.mouseClick(main_window.add_product_btn, Qt.MouseButton.LeftButton)
    
    # 验证对话框是否打开
    # assert dialog is opened
```

### 3. 集成测试
```python
# tests/test_scraping.py
import asyncio
import pytest
from scrapers.web_scraper import WebScraper
from scrapers.ai_analyzer import AIAnalyzer
from scrapers.data_mapper import DataMapper

@pytest.mark.asyncio
async def test_full_scraping_pipeline():
    """测试完整抓取流程"""
    # 准备测试数据
    test_url = "https://example.com/product/123"
    
    # 初始化组件
    scraper = WebScraper()
    analyzer = AIAnalyzer()
    mapper = DataMapper()
    
    # 执行抓取流程
    scraped_data = await scraper.scrape_product_info(test_url)
    ai_analysis = await analyzer.analyze(scraped_data)
    mapped_data = mapper.map_data(scraped_data, ai_analysis)
    
    # 验证结果
    assert 'supplier_name' in mapped_data
    assert 'wholesale_price' in mapped_data
    assert mapped_data['wholesale_price'] > 0
```

## 性能优化

### 1. 数据库优化
```python
# 数据库连接池
class DatabasePool:
    """数据库连接池"""
    
    def __init__(self, max_connections=10):
        self.max_connections = max_connections
        self.connections = []
        self.active_connections = 0
    
    def get_connection(self):
        """获取数据库连接"""
        if self.connections:
            return self.connections.pop()
        elif self.active_connections < self.max_connections:
            self.active_connections += 1
            return sqlite3.connect('data/database.db')
        else:
            # 等待可用连接
            time.sleep(0.1)
            return self.get_connection()
    
    def return_connection(self, conn):
        """返回数据库连接"""
        self.connections.append(conn)
```

### 2. 内存管理
```python
# 大对象缓存
import weakref
from functools import lru_cache

class ObjectCache:
    """对象缓存管理"""
    
    def __init__(self):
        self._cache = weakref.WeakValueDictionary()
    
    def get_or_create(self, key, factory):
        """获取或创建对象"""
        obj = self._cache.get(key)
        if obj is None:
            obj = factory()
            self._cache[key] = obj
        return obj

# LRU缓存装饰器
@lru_cache(maxsize=128)
def expensive_computation(param):
    """耗时计算函数"""
    # 执行复杂计算
    pass
```

### 3. 异步优化
```python
# 批量操作
async def batch_scrape_urls(urls, batch_size=5):
    """批量抓取URL"""
    semaphore = asyncio.Semaphore(batch_size)
    
    async def scrape_single(url):
        async with semaphore:
            # 执行单个抓取
            return await scraper.scrape_product_info(url)
    
    tasks = [scrape_single(url) for url in urls]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return results
```

## 部署和发布

### 1. 打包配置
```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="python-ecom-product-source-compare",
    version="2.3.6",
    packages=find_packages(),
    install_requires=[
        "PyQt6>=6.0.0",
        "Pillow>=10.0.0",
        "pandas>=1.5.0",
        # AI功能依赖（可选）
        "playwright>=1.40.0",
        "fake-useragent>=1.4.0",
        "aiohttp>=3.9.0",
    ],
    extras_require={
        "ai": [
            "playwright>=1.40.0",
            "fake-useragent>=1.4.0",
            "aiohttp>=3.9.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "ecom-compare=main:main",
        ],
    },
)
```

### 2. Docker部署
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libxcb1 \
    && rm -rf /var/lib/apt/lists/*

# 复制代码
COPY . .

# 安装Python依赖
RUN pip install -r requirements.txt

# 配置AI环境（可选）
RUN python setup_scraper.py

# 启动应用
CMD ["python", "main.py"]
```

### 3. CI/CD配置
```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-qt
    
    - name: Run tests
      run: pytest tests/
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

## 扩展开发

### 1. 插件系统
```python
# plugins/base.py
class PluginBase:
    """插件基类"""
    
    def __init__(self):
        self.name = "Base Plugin"
        self.version = "1.0.0"
    
    def initialize(self, app_context):
        """初始化插件"""
        pass
    
    def get_menu_items(self):
        """获取菜单项"""
        return []
    
    def get_toolbar_items(self):
        """获取工具栏项"""
        return []

# 插件管理器
class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        self.plugins = []
    
    def load_plugin(self, plugin_class):
        """加载插件"""
        plugin = plugin_class()
        plugin.initialize(self.app_context)
        self.plugins.append(plugin)
    
    def get_all_menu_items(self):
        """获取所有插件菜单项"""
        items = []
        for plugin in self.plugins:
            items.extend(plugin.get_menu_items())
        return items
```

### 2. API接口
```python
# api/server.py
from fastapi import FastAPI
from fastapi.responses import JSONResponse

app = FastAPI(title="电商货源API", version="2.3.6")

@app.get("/api/products")
async def get_products():
    """获取产品列表"""
    products = Product.find_all()
    return [product.to_dict() for product in products]

@app.post("/api/products")
async def create_product(product_data: dict):
    """创建新产品"""
    product = Product(**product_data)
    product.save()
    return product.to_dict()

@app.post("/api/scrape")
async def scrape_product(url: str):
    """抓取商品信息"""
    scraper = WebScraper()
    result = await scraper.scrape_product_info(url)
    return result
```

### 3. 国际化支持
```python
# utils/i18n.py
import gettext
import os

class I18nManager:
    """国际化管理器"""
    
    def __init__(self):
        self.current_locale = 'zh_CN'
        self.translations = {}
    
    def load_translations(self, locale):
        """加载翻译文件"""
        locale_dir = os.path.join('locales', locale, 'LC_MESSAGES')
        translation = gettext.translation(
            'messages', 
            localedir=locale_dir,
            fallback=True
        )
        self.translations[locale] = translation
    
    def get_text(self, message):
        """获取翻译文本"""
        translation = self.translations.get(self.current_locale)
        if translation:
            return translation.gettext(message)
        return message

# 使用示例
i18n = I18nManager()
i18n.load_translations('en_US')
print(i18n.get_text("添加产品"))  # "Add Product"
```

## 最佳实践

### 1. 代码规范
```python
# 遵循PEP 8代码风格
# 使用类型注解
def process_data(data: dict) -> list[dict]:
    """处理数据函数"""
    results: list[dict] = []
    
    for item in data.get('items', []):
        processed_item = {
            'id': item.get('id'),
            'name': item.get('name', '').strip()
        }
        results.append(processed_item)
    
    return results

# 使用文档字符串
class ProductService:
    """产品服务类
    
    提供产品相关的业务逻辑处理功能。
    
    Attributes:
        db_manager: 数据库管理器
        cache_manager: 缓存管理器
    """
    
    def __init__(self, db_manager, cache_manager):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
    
    def get_product_by_id(self, product_id: int) -> Product | None:
        """根据ID获取产品
        
        Args:
            product_id: 产品ID
            
        Returns:
            Product实例或None
            
        Raises:
            ValueError: 当product_id无效时
        """
        if product_id <= 0:
            raise ValueError("Product ID must be positive")
        
        # 先从缓存获取
        cached_product = self.cache_manager.get(f"product:{product_id}")
        if cached_product:
            return cached_product
        
        # 从数据库获取
        product = self.db_manager.get_product(product_id)
        if product:
            self.cache_manager.set(f"product:{product_id}", product)
        
        return product
```

### 2. 错误处理
```python
# 自定义异常类
class ApplicationError(Exception):
    """应用程序错误基类"""
    pass

class DataValidationError(ApplicationError):
    """数据验证错误"""
    pass

class NetworkConnectionError(ApplicationError):
    """网络连接错误"""
    pass

# 统一错误处理装饰器
def handle_exceptions(func):
    """异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DataValidationError as e:
            logging.warning(f"数据验证错误: {e}")
            show_warning_message(str(e))
        except NetworkConnectionError as e:
            logging.error(f"网络连接错误: {e}")
            show_error_message("网络连接失败，请检查网络设置")
        except Exception as e:
            logging.exception(f"未知错误: {e}")
            show_error_message("发生未知错误，请联系技术支持")
    
    return wrapper
```

### 3. 日志管理
```python
# 配置日志系统
import logging
from logging.handlers import RotatingFileHandler

def setup_logging():
    """配置日志系统"""
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # 文件处理器（轮转日志）
    file_handler = RotatingFileHandler(
        'data/logs/app.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    
    # 格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
```

## 故障排除

### 常见问题解决

1. **UI卡顿问题**
   ```python
   # 使用QTimer延迟执行
   from PyQt6.QtCore import QTimer
   
   def heavy_operation(self):
       """耗时操作"""
       QTimer.singleShot(0, self._do_heavy_work)
   
   def _do_heavy_work(self):
       # 执行耗时操作
       pass
   ```

2. **内存泄漏问题**
   ```python
   # 及时断开信号连接
   def cleanup(self):
       self.worker.finished.disconnect()
       self.worker.deleteLater()
   ```

3. **数据库锁定问题**
   ```python
   # 使用连接上下文管理器
   class DatabaseConnection:
       def __enter__(self):
           self.conn = sqlite3.connect('database.db')
           return self.conn
       
       def __exit__(self, exc_type, exc_val, exc_tb):
           self.conn.close()
   ```

---

**文档版本**：v2.3.6  
**最后更新**：2025-01-07  
**维护团队**：AI Assistant & 开发者社区 