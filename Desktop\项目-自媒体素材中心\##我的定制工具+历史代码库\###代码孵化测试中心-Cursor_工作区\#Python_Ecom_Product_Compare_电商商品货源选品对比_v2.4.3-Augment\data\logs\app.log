2025-07-06 02:33:03 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 02:33:03 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 02:33:03 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 02:33:06 - root - INFO - 应用程序初始化完成
2025-07-06 02:33:06 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 02:33:08 - root - INFO - 主窗口创建完成
2025-07-06 02:33:09 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 02:33:09 - root - INFO - 数据库路径: data/database.db
2025-07-06 02:33:09 - root - INFO - 应用程序正在运行...
2025-07-06 02:33:27 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_022315
2025-07-06 02:33:30 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_022046
2025-07-06 02:33:32 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_023319
2025-07-06 02:33:32 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 2, 标签数量: 1
2025-07-06 02:33:32 - views.main_window - INFO - 开始导入标签...
2025-07-06 02:33:32 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 02:33:32 - views.main_window - INFO - 开始导入产品...
2025-07-06 02:33:32 - views.main_window - INFO - 导入产品: 狗粮
2025-07-06 02:33:32 - views.main_window - INFO - 处理产品图片: 狗粮
2025-07-06 02:33:32 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 02:33:32 - views.main_window - ERROR - 导入数据失败: ProductService.add_source() takes 4 positional arguments but 12 were given
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\views\main_window.py", line 899, in import_data
    self.product_service.add_source(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        product.id,
        ^^^^^^^^^^^
    ...<9 lines>...
        source_data.get("is_active", True),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
TypeError: ProductService.add_source() takes 4 positional arguments but 12 were given
2025-07-06 02:33:47 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 02:33:47 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 02:33:47 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 02:33:47 - root - INFO - 应用程序初始化完成
2025-07-06 02:33:47 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 02:33:47 - root - INFO - 主窗口创建完成
2025-07-06 02:33:48 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 02:33:48 - root - INFO - 数据库路径: data/database.db
2025-07-06 02:33:48 - root - INFO - 应用程序正在运行...
2025-07-06 02:33:48 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 02:34:05 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_023319
2025-07-06 02:34:06 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_023355
2025-07-06 02:34:06 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 2, 标签数量: 1
2025-07-06 02:34:06 - views.main_window - INFO - 开始导入标签...
2025-07-06 02:34:06 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 02:34:06 - views.main_window - INFO - 开始导入产品...
2025-07-06 02:34:06 - views.main_window - INFO - 导入产品: 狗粮
2025-07-06 02:34:06 - views.main_window - INFO - 处理产品图片: 狗粮
2025-07-06 02:34:06 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 02:34:06 - views.main_window - ERROR - 导入数据失败: ProductService.add_source() takes 4 positional arguments but 12 were given
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\views\main_window.py", line 899, in import_data
    self.product_service.add_source(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        product.id,
        ^^^^^^^^^^^
    ...<9 lines>...
        source_data.get("is_active", True),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
TypeError: ProductService.add_source() takes 4 positional arguments but 12 were given
2025-07-06 02:34:29 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 02:35:03 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 02:35:03 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 02:35:03 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 02:35:03 - root - INFO - 应用程序初始化完成
2025-07-06 02:35:03 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 02:35:03 - root - INFO - 主窗口创建完成
2025-07-06 02:35:04 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 02:35:04 - root - INFO - 数据库路径: data/database.db
2025-07-06 02:35:04 - root - INFO - 应用程序正在运行...
2025-07-06 02:35:14 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_023355
2025-07-06 02:35:15 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_023508
2025-07-06 02:35:15 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 2, 标签数量: 1
2025-07-06 02:35:15 - views.main_window - INFO - 开始导入标签...
2025-07-06 02:35:15 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 02:35:15 - views.main_window - INFO - 开始导入产品...
2025-07-06 02:35:15 - views.main_window - INFO - 导入产品: 狗粮
2025-07-06 02:35:15 - views.main_window - INFO - 处理产品图片: 狗粮
2025-07-06 02:35:15 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 02:35:15 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 02:35:15 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 02:35:15 - views.main_window - ERROR - 导入数据失败: 'TagService' object has no attribute 'add_tag_to_product'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\views\main_window.py", line 930, in import_data
    self.tag_service.add_tag_to_product(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TagService' object has no attribute 'add_tag_to_product'
2025-07-06 02:35:22 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_023508
2025-07-06 02:35:22 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 2, 标签数量: 1
2025-07-06 02:35:22 - views.main_window - INFO - 开始导入标签...
2025-07-06 02:35:22 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 02:35:22 - views.main_window - INFO - 开始导入产品...
2025-07-06 02:35:22 - views.main_window - INFO - 导入产品: 狗粮
2025-07-06 02:35:22 - views.main_window - INFO - 处理产品图片: 狗粮
2025-07-06 02:35:22 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 02:35:22 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 02:35:22 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 02:35:22 - views.main_window - ERROR - 导入数据失败: 'TagService' object has no attribute 'add_tag_to_product'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\views\main_window.py", line 930, in import_data
    self.tag_service.add_tag_to_product(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TagService' object has no attribute 'add_tag_to_product'
2025-07-06 02:35:38 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 02:36:35 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 02:36:35 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 02:36:35 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 02:36:36 - root - INFO - 应用程序初始化完成
2025-07-06 02:36:36 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 02:36:36 - root - INFO - 主窗口创建完成
2025-07-06 02:36:36 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 02:36:36 - root - INFO - 数据库路径: data/database.db
2025-07-06 02:36:36 - root - INFO - 应用程序正在运行...
2025-07-06 02:36:50 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_023508
2025-07-06 02:36:51 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_023642
2025-07-06 02:36:51 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 2, 标签数量: 1
2025-07-06 02:36:51 - views.main_window - INFO - 开始导入标签...
2025-07-06 02:36:51 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 02:36:51 - views.main_window - INFO - 开始导入产品...
2025-07-06 02:36:51 - views.main_window - INFO - 导入产品: 狗粮
2025-07-06 02:36:51 - views.main_window - INFO - 处理产品图片: 狗粮
2025-07-06 02:36:51 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 02:36:51 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 02:36:51 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 02:36:51 - views.main_window - INFO - 导入产品: 高跟鞋
2025-07-06 02:36:51 - views.main_window - INFO - 处理产品图片: 高跟鞋
2025-07-06 02:36:51 - views.main_window - INFO - 导入货源数量: 1
2025-07-06 02:36:51 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 02:36:51 - views.main_window - INFO - 关联标签数量: 0
2025-07-06 02:36:51 - views.main_window - INFO - 数据导入完成
2025-07-06 02:37:08 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 02:38:33 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 02:38:33 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 02:38:33 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 02:38:35 - root - INFO - 应用程序初始化完成
2025-07-06 02:38:35 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 02:38:35 - root - INFO - 主窗口创建完成
2025-07-06 02:38:36 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 02:38:36 - root - INFO - 数据库路径: data/database.db
2025-07-06 02:38:36 - root - INFO - 应用程序正在运行...
2025-07-06 02:38:45 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_023642
2025-07-06 02:38:47 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_023839
2025-07-06 02:38:47 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 2, 标签数量: 1
2025-07-06 02:38:47 - views.main_window - INFO - 开始导入标签...
2025-07-06 02:38:47 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 02:38:47 - views.main_window - INFO - 开始导入产品...
2025-07-06 02:38:47 - views.main_window - INFO - 导入产品: 狗粮
2025-07-06 02:38:47 - views.main_window - INFO - 处理产品图片: 狗粮
2025-07-06 02:38:47 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 02:38:47 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 02:38:47 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 02:38:47 - views.main_window - INFO - 导入产品: 狗粮
2025-07-06 02:38:47 - views.main_window - INFO - 处理产品图片: 狗粮
2025-07-06 02:38:47 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 02:38:47 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 02:38:47 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 02:38:47 - views.main_window - INFO - 数据导入完成
2025-07-06 02:39:35 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_023839
2025-07-06 02:39:36 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_023925
2025-07-06 02:39:36 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 1, 标签数量: 1
2025-07-06 02:39:36 - views.main_window - INFO - 开始导入标签...
2025-07-06 02:39:36 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 02:39:36 - views.main_window - INFO - 开始导入产品...
2025-07-06 02:39:36 - views.main_window - INFO - 导入产品: 狗粮
2025-07-06 02:39:36 - views.main_window - INFO - 处理产品图片: 狗粮
2025-07-06 02:39:36 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 02:39:36 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 02:39:36 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 02:39:36 - views.main_window - INFO - 数据导入完成
2025-07-06 02:39:42 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 02:42:58 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 02:42:58 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 02:42:58 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 02:42:59 - root - INFO - 应用程序初始化完成
2025-07-06 02:42:59 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 02:43:00 - root - INFO - 主窗口创建完成
2025-07-06 02:43:00 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 02:43:00 - root - INFO - 数据库路径: data/database.db
2025-07-06 02:43:00 - root - INFO - 应用程序正在运行...
2025-07-06 02:44:09 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 02:44:35 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 02:44:35 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 02:44:35 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 02:44:35 - root - INFO - 应用程序初始化完成
2025-07-06 02:44:35 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 02:44:36 - root - INFO - 主窗口创建完成
2025-07-06 02:44:36 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 02:44:36 - root - INFO - 数据库路径: data/database.db
2025-07-06 02:44:36 - root - INFO - 应用程序正在运行...
2025-07-06 02:45:06 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 02:50:30 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 02:50:30 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 02:50:30 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 02:50:32 - root - INFO - 应用程序初始化完成
2025-07-06 02:50:32 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 02:50:32 - root - INFO - 主窗口创建完成
2025-07-06 02:50:33 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 02:50:33 - root - INFO - 数据库路径: data/database.db
2025-07-06 02:50:33 - root - INFO - 应用程序正在运行...
2025-07-06 02:51:57 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_023925
2025-07-06 02:52:00 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_025145
2025-07-06 02:52:00 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 1, 标签数量: 1
2025-07-06 02:52:00 - views.main_window - INFO - 开始导入标签...
2025-07-06 02:52:00 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 02:52:00 - views.main_window - INFO - 开始导入产品...
2025-07-06 02:52:00 - views.main_window - INFO - 导入产品: 高跟鞋
2025-07-06 02:52:00 - views.main_window - INFO - 处理产品图片: 高跟鞋
2025-07-06 02:52:00 - views.main_window - INFO - 导入货源数量: 0
2025-07-06 02:52:00 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 02:52:00 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 02:52:00 - views.main_window - INFO - 数据导入完成
2025-07-06 02:53:32 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:02:37 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:02:37 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:02:37 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:02:39 - root - INFO - 应用程序初始化完成
2025-07-06 03:02:39 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:02:39 - root - INFO - 主窗口创建完成
2025-07-06 03:02:40 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:02:40 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:02:40 - root - INFO - 应用程序正在运行...
2025-07-06 03:02:45 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_025145
2025-07-06 03:02:48 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_025311
2025-07-06 03:03:25 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_030308
2025-07-06 03:03:25 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 1, 标签数量: 1
2025-07-06 03:03:25 - views.main_window - INFO - 开始导入标签...
2025-07-06 03:03:25 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 03:03:25 - views.main_window - INFO - 开始导入产品...
2025-07-06 03:03:25 - views.main_window - INFO - 导入产品: 高跟鞋-1
2025-07-06 03:03:25 - views.main_window - INFO - 处理产品图片: 高跟鞋-1
2025-07-06 03:03:25 - views.main_window - INFO - 导入货源数量: 0
2025-07-06 03:03:25 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 03:03:25 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 03:03:25 - views.main_window - INFO - 数据导入完成
2025-07-06 03:03:35 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:06:19 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:06:19 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:06:19 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:06:19 - root - INFO - 应用程序初始化完成
2025-07-06 03:06:19 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:06:20 - root - INFO - 主窗口创建完成
2025-07-06 03:06:20 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:06:20 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:06:20 - root - INFO - 应用程序正在运行...
2025-07-06 03:06:31 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_030308
2025-07-06 03:06:35 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:06:47 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:06:47 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:06:47 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:06:48 - root - INFO - 应用程序初始化完成
2025-07-06 03:06:48 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:06:49 - root - INFO - 主窗口创建完成
2025-07-06 03:06:49 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:06:49 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:06:49 - root - INFO - 应用程序正在运行...
2025-07-06 03:07:18 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_030654
2025-07-06 03:07:18 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 1, 标签数量: 1
2025-07-06 03:07:18 - views.main_window - INFO - 开始导入标签...
2025-07-06 03:07:18 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 03:07:18 - views.main_window - INFO - 开始导入产品...
2025-07-06 03:07:18 - views.main_window - INFO - 导入产品: 高跟鞋-1
2025-07-06 03:07:18 - views.main_window - INFO - 处理产品图片: 高跟鞋-1
2025-07-06 03:07:18 - views.main_window - INFO - 复制图片: 高跟鞋-1_001.png
2025-07-06 03:07:18 - views.main_window - INFO - 复制图片: 高跟鞋-1_002.png
2025-07-06 03:07:18 - views.main_window - INFO - 复制图片: 高跟鞋-1_003.png
2025-07-06 03:07:18 - views.main_window - INFO - 更新产品图片信息: 3张图片
2025-07-06 03:07:18 - views.main_window - INFO - 导入货源数量: 0
2025-07-06 03:07:18 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 03:07:18 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 03:07:18 - views.main_window - INFO - 数据导入完成
2025-07-06 03:07:37 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:11:05 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:11:05 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:11:05 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:11:07 - root - INFO - 应用程序初始化完成
2025-07-06 03:11:07 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:11:08 - root - INFO - 主窗口创建完成
2025-07-06 03:11:08 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:11:08 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:11:08 - root - INFO - 应用程序正在运行...
2025-07-06 03:11:18 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_030654
2025-07-06 03:11:30 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_031121
2025-07-06 03:11:30 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 1, 标签数量: 1
2025-07-06 03:11:30 - views.main_window - INFO - 开始导入标签...
2025-07-06 03:11:30 - views.main_window - INFO - 标签已存在，跳过创建: 宠物
2025-07-06 03:11:30 - views.main_window - INFO - 开始导入产品...
2025-07-06 03:11:30 - views.main_window - INFO - 导入产品: 高跟鞋-1
2025-07-06 03:11:30 - views.main_window - INFO - 处理产品图片: 高跟鞋-1
2025-07-06 03:11:30 - views.main_window - INFO - 复制图片: 高跟鞋-1_001.png -> data/images/products/product_33/高跟鞋-1_001.png
2025-07-06 03:11:30 - views.main_window - INFO - 复制图片: 高跟鞋-1_002.png -> data/images/products/product_33/高跟鞋-1_002.png
2025-07-06 03:11:30 - views.main_window - INFO - 复制图片: 高跟鞋-1_003.png -> data/images/products/product_33/高跟鞋-1_003.png
2025-07-06 03:11:30 - views.main_window - INFO - 更新产品图片信息: 3张图片
2025-07-06 03:11:30 - views.main_window - INFO - 导入货源数量: 0
2025-07-06 03:11:30 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 03:11:30 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 03:11:30 - views.main_window - INFO - 数据导入完成
2025-07-06 03:11:53 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:11:53 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:11:53 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:11:54 - root - INFO - 应用程序初始化完成
2025-07-06 03:11:54 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:11:54 - root - INFO - 主窗口创建完成
2025-07-06 03:11:54 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:11:54 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:11:54 - root - INFO - 应用程序正在运行...
2025-07-06 03:14:40 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:23:52 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:23:52 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:23:52 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:23:53 - root - INFO - 应用程序初始化完成
2025-07-06 03:23:54 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:23:54 - root - INFO - 主窗口创建完成
2025-07-06 03:23:55 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:23:55 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:23:55 - root - INFO - 应用程序正在运行...
2025-07-06 03:24:24 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:25:42 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:25:42 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:25:42 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:25:43 - root - INFO - 应用程序初始化完成
2025-07-06 03:25:43 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:25:45 - root - INFO - 主窗口创建完成
2025-07-06 03:25:45 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:25:45 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:25:45 - root - INFO - 应用程序正在运行...
2025-07-06 03:26:29 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:27:27 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:27:27 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:27:27 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:27:28 - root - INFO - 应用程序初始化完成
2025-07-06 03:27:28 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:27:29 - root - INFO - 主窗口创建完成
2025-07-06 03:27:29 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:27:29 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:27:29 - root - INFO - 应用程序正在运行...
2025-07-06 03:27:38 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:29:40 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:29:40 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:29:40 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:29:40 - root - INFO - 应用程序初始化完成
2025-07-06 03:29:40 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:29:41 - root - INFO - 主窗口创建完成
2025-07-06 03:29:42 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:29:42 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:29:42 - root - INFO - 应用程序正在运行...
2025-07-06 03:31:30 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:31:30 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:31:30 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:31:31 - root - INFO - 应用程序初始化完成
2025-07-06 03:31:31 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:31:31 - root - INFO - 主窗口创建完成
2025-07-06 03:31:32 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:31:32 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:31:32 - root - INFO - 应用程序正在运行...
2025-07-06 03:31:50 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:33:29 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:33:29 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:33:29 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:33:31 - root - INFO - 应用程序初始化完成
2025-07-06 03:33:31 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:33:31 - root - INFO - 主窗口创建完成
2025-07-06 03:33:32 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:33:32 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:33:32 - root - INFO - 应用程序正在运行...
2025-07-06 03:33:44 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:34:57 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:34:57 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:34:57 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:34:58 - root - INFO - 应用程序初始化完成
2025-07-06 03:34:58 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:34:59 - root - INFO - 主窗口创建完成
2025-07-06 03:34:59 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:34:59 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:34:59 - root - INFO - 应用程序正在运行...
2025-07-06 03:35:28 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:36:32 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:36:32 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:36:32 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:36:33 - root - INFO - 应用程序初始化完成
2025-07-06 03:36:33 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:36:34 - root - INFO - 主窗口创建完成
2025-07-06 03:36:34 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:36:34 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:36:34 - root - INFO - 应用程序正在运行...
2025-07-06 03:37:00 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:39:22 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:39:22 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:39:22 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:39:24 - root - INFO - 应用程序初始化完成
2025-07-06 03:39:24 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:39:24 - root - INFO - 主窗口创建完成
2025-07-06 03:39:24 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:39:24 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:39:24 - root - INFO - 应用程序正在运行...
2025-07-06 03:39:43 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:41:01 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:41:01 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:41:01 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:41:03 - root - INFO - 应用程序初始化完成
2025-07-06 03:41:03 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:41:03 - root - INFO - 主窗口创建完成
2025-07-06 03:41:04 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:41:04 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:41:04 - root - INFO - 应用程序正在运行...
2025-07-06 03:41:16 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:41:18 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:41:18 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:41:18 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:41:18 - root - INFO - 应用程序初始化完成
2025-07-06 03:41:18 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:41:19 - root - INFO - 主窗口创建完成
2025-07-06 03:41:19 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:41:19 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:41:19 - root - INFO - 应用程序正在运行...
2025-07-06 03:41:42 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:45:50 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:45:50 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:45:50 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:45:52 - root - INFO - 应用程序初始化完成
2025-07-06 03:45:52 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:45:53 - root - INFO - 主窗口创建完成
2025-07-06 03:45:53 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:45:53 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:45:53 - root - INFO - 应用程序正在运行...
2025-07-06 03:46:17 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:48:15 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:48:15 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:48:15 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:48:16 - root - INFO - 应用程序初始化完成
2025-07-06 03:48:16 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:48:17 - root - INFO - 主窗口创建完成
2025-07-06 03:48:17 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:48:17 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:48:17 - root - INFO - 应用程序正在运行...
2025-07-06 03:49:57 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 03:55:21 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 03:55:21 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 03:55:21 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.7
2025-07-06 03:55:23 - root - INFO - 应用程序初始化完成
2025-07-06 03:55:23 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 03:55:23 - root - INFO - 主窗口创建完成
2025-07-06 03:55:24 - root - INFO - === 电商产品对比选品工具 v2.1.7 启动成功 ===
2025-07-06 03:55:24 - root - INFO - 数据库路径: data/database.db
2025-07-06 03:55:24 - root - INFO - 应用程序正在运行...
2025-07-06 03:55:35 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 04:09:09 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 04:09:09 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.4--\data\logs\app.log
2025-07-06 04:09:09 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.8
2025-07-06 04:09:10 - root - INFO - 应用程序初始化完成
2025-07-06 04:09:11 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 04:09:11 - root - INFO - 主窗口创建完成
2025-07-06 04:09:12 - root - INFO - === 电商产品对比选品工具 v2.1.8 启动成功 ===
2025-07-06 04:09:12 - root - INFO - 数据库路径: data/database.db
2025-07-06 04:09:12 - root - INFO - 应用程序正在运行...
2025-07-06 04:09:28 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 12:36:44 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 12:36:44 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 12:36:44 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.8
2025-07-06 12:36:45 - root - INFO - 应用程序初始化完成
2025-07-06 12:36:45 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 12:46:23 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 12:46:23 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 12:46:23 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.8
2025-07-06 12:46:23 - root - INFO - 应用程序初始化完成
2025-07-06 12:46:23 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 12:46:32 - root - INFO - 主窗口创建完成
2025-07-06 12:46:32 - root - INFO - === 电商产品对比选品工具 v2.1.8 启动成功 ===
2025-07-06 12:46:32 - root - INFO - 数据库路径: data/database.db
2025-07-06 12:46:32 - root - INFO - 应用程序正在运行...
2025-07-06 12:53:53 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 13:01:33 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:01:33 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:01:33 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.8
2025-07-06 13:01:34 - root - INFO - 应用程序初始化完成
2025-07-06 13:01:34 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:01:34 - root - INFO - 主窗口创建完成
2025-07-06 13:01:35 - root - INFO - === 电商产品对比选品工具 v2.1.8 启动成功 ===
2025-07-06 13:01:35 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:01:35 - root - INFO - 应用程序正在运行...
2025-07-06 13:02:25 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 13:11:20 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:11:20 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:11:20 - root - INFO - 程序启动 - 电商产品对比选品工具 v2.1.8
2025-07-06 13:11:21 - root - INFO - 应用程序初始化完成
2025-07-06 13:11:21 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:11:21 - root - INFO - 主窗口创建完成
2025-07-06 13:11:22 - root - INFO - === 电商产品对比选品工具 v2.1.8 启动成功 ===
2025-07-06 13:11:22 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:11:22 - root - INFO - 应用程序正在运行...
2025-07-06 13:42:35 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:42:35 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:42:35 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 13:42:36 - root - INFO - 应用程序初始化完成
2025-07-06 13:42:36 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:42:38 - root - INFO - 主窗口创建完成
2025-07-06 13:42:39 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 13:42:39 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:42:39 - root - INFO - 应用程序正在运行...
2025-07-06 13:42:53 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:42:53 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:42:53 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 13:42:54 - root - INFO - 应用程序初始化完成
2025-07-06 13:42:54 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:42:54 - root - INFO - 主窗口创建完成
2025-07-06 13:42:54 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 13:42:54 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:42:54 - root - INFO - 应用程序正在运行...
2025-07-06 13:43:02 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:43:02 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:43:02 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 13:43:02 - root - INFO - 应用程序初始化完成
2025-07-06 13:43:02 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:43:03 - root - INFO - 主窗口创建完成
2025-07-06 13:43:03 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 13:43:03 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:43:03 - root - INFO - 应用程序正在运行...
2025-07-06 13:43:17 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:43:17 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:43:17 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 13:43:17 - root - INFO - 应用程序初始化完成
2025-07-06 13:43:17 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:43:18 - root - INFO - 主窗口创建完成
2025-07-06 13:43:18 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 13:43:18 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:43:18 - root - INFO - 应用程序正在运行...
2025-07-06 13:43:49 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 13:46:28 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:46:28 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:46:28 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 13:46:28 - root - INFO - 应用程序初始化完成
2025-07-06 13:46:28 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:46:28 - root - INFO - 主窗口创建完成
2025-07-06 13:46:29 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 13:46:29 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:46:29 - root - INFO - 应用程序正在运行...
2025-07-06 13:57:50 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:57:50 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:57:50 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 13:57:52 - root - INFO - 应用程序初始化完成
2025-07-06 13:57:52 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:57:52 - root - INFO - 主窗口创建完成
2025-07-06 13:57:52 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 13:57:52 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:57:52 - root - INFO - 应用程序正在运行...
2025-07-06 13:57:58 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 13:57:58 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 13:57:58 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 13:57:59 - root - INFO - 应用程序初始化完成
2025-07-06 13:57:59 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 13:57:59 - root - INFO - 主窗口创建完成
2025-07-06 13:57:59 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 13:57:59 - root - INFO - 数据库路径: data/database.db
2025-07-06 13:57:59 - root - INFO - 应用程序正在运行...
2025-07-06 14:01:03 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:01:03 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:01:03 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 14:01:04 - root - INFO - 应用程序初始化完成
2025-07-06 14:01:04 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:01:05 - root - INFO - 主窗口创建完成
2025-07-06 14:01:05 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 14:01:05 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:01:05 - root - INFO - 应用程序正在运行...
2025-07-06 14:08:49 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:08:49 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:08:49 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.1.9
2025-07-06 14:08:50 - root - INFO - 应用程序初始化完成
2025-07-06 14:08:50 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:08:50 - root - INFO - 主窗口创建完成
2025-07-06 14:08:50 - root - INFO - === Python_Ecom_Product_Source_Compare v2.1.9 启动成功 ===
2025-07-06 14:08:50 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:08:50 - root - INFO - 应用程序正在运行...
2025-07-06 14:10:06 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 14:13:57 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:13:57 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:13:57 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 14:13:58 - root - INFO - 应用程序初始化完成
2025-07-06 14:13:58 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:13:58 - root - INFO - 主窗口创建完成
2025-07-06 14:13:59 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 14:13:59 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:13:59 - root - INFO - 应用程序正在运行...
2025-07-06 14:14:18 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 14:14:22 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:14:22 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:14:22 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 14:14:23 - root - INFO - 应用程序初始化完成
2025-07-06 14:14:23 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:14:24 - root - INFO - 主窗口创建完成
2025-07-06 14:14:24 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 14:14:24 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:14:24 - root - INFO - 应用程序正在运行...
2025-07-06 14:22:30 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:22:30 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:22:30 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 14:22:32 - root - INFO - 应用程序初始化完成
2025-07-06 14:22:32 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:22:32 - root - INFO - 主窗口创建完成
2025-07-06 14:22:33 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 14:22:33 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:22:33 - root - INFO - 应用程序正在运行...
2025-07-06 14:27:50 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:27:50 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:27:50 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 14:27:51 - root - INFO - 应用程序初始化完成
2025-07-06 14:27:51 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:27:52 - root - INFO - 主窗口创建完成
2025-07-06 14:27:54 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 14:27:54 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:27:54 - root - INFO - 应用程序正在运行...
2025-07-06 14:33:42 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:33:42 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:33:42 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 14:33:43 - root - INFO - 应用程序初始化完成
2025-07-06 14:33:43 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:33:43 - root - INFO - 主窗口创建完成
2025-07-06 14:33:44 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 14:33:44 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:33:44 - root - INFO - 应用程序正在运行...
2025-07-06 14:40:05 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:40:05 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:40:05 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 14:40:06 - root - INFO - 应用程序初始化完成
2025-07-06 14:40:06 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:40:07 - root - INFO - 主窗口创建完成
2025-07-06 14:40:08 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 14:40:08 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:40:08 - root - INFO - 应用程序正在运行...
2025-07-06 14:40:46 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:40:46 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:40:46 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 14:40:46 - root - INFO - 应用程序初始化完成
2025-07-06 14:40:46 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:40:47 - root - INFO - 主窗口创建完成
2025-07-06 14:40:47 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 14:40:47 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:40:47 - root - INFO - 应用程序正在运行...
2025-07-06 14:41:19 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 14:55:42 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 14:55:42 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 14:55:42 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 14:55:43 - root - INFO - 应用程序初始化完成
2025-07-06 14:55:43 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 14:55:43 - root - INFO - 主窗口创建完成
2025-07-06 14:55:43 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 14:55:43 - root - INFO - 数据库路径: data/database.db
2025-07-06 14:55:43 - root - INFO - 应用程序正在运行...
2025-07-06 15:00:21 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:02:40 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:02:40 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:02:40 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:02:41 - root - INFO - 应用程序初始化完成
2025-07-06 15:02:41 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:02:41 - root - INFO - 主窗口创建完成
2025-07-06 15:02:41 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:02:41 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:02:41 - root - INFO - 应用程序正在运行...
2025-07-06 15:03:24 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:03:24 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:03:24 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:03:25 - root - INFO - 应用程序初始化完成
2025-07-06 15:03:25 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:03:26 - root - INFO - 主窗口创建完成
2025-07-06 15:03:26 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:03:26 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:03:26 - root - INFO - 应用程序正在运行...
2025-07-06 15:05:19 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:12:21 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:12:21 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:12:21 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:12:21 - root - INFO - 应用程序初始化完成
2025-07-06 15:12:21 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:12:22 - root - INFO - 主窗口创建完成
2025-07-06 15:12:22 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:12:22 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:12:22 - root - INFO - 应用程序正在运行...
2025-07-06 15:12:35 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:12:35 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:12:35 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:12:36 - root - INFO - 应用程序初始化完成
2025-07-06 15:12:36 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:12:37 - root - INFO - 主窗口创建完成
2025-07-06 15:12:37 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:12:37 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:12:37 - root - INFO - 应用程序正在运行...
2025-07-06 15:14:45 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_151437
2025-07-06 15:14:45 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 4, 标签数量: 1
2025-07-06 15:14:45 - views.main_window - INFO - 开始导入标签...
2025-07-06 15:14:45 - views.main_window - INFO - 标签已存在，跳过创建: 宠物-狗狗
2025-07-06 15:14:45 - views.main_window - INFO - 开始导入产品...
2025-07-06 15:14:45 - views.main_window - INFO - 导入产品: 地方
2025-07-06 15:14:45 - views.main_window - INFO - 处理产品图片: 地方
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 地方_001.png -> data/images/products/product_5/地方_001.png
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 地方_002.png -> data/images/products/product_5/地方_002.png
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 地方_003.png -> data/images/products/product_5/地方_003.png
2025-07-06 15:14:45 - views.main_window - INFO - 更新产品图片信息: 3张图片
2025-07-06 15:14:45 - views.main_window - INFO - 导入货源数量: 1
2025-07-06 15:14:45 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 15:14:45 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 15:14:45 - views.main_window - INFO - 导入产品: 电饭锅发的
2025-07-06 15:14:45 - views.main_window - INFO - 处理产品图片: 电饭锅发的
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 电饭锅发的_001.png -> data/images/products/product_6/电饭锅发的_001.png
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 电饭锅发的_002.png -> data/images/products/product_6/电饭锅发的_002.png
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 电饭锅发的_003.png -> data/images/products/product_6/电饭锅发的_003.png
2025-07-06 15:14:45 - views.main_window - INFO - 更新产品图片信息: 3张图片
2025-07-06 15:14:45 - views.main_window - INFO - 导入货源数量: 0
2025-07-06 15:14:45 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 15:14:45 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 15:14:45 - views.main_window - INFO - 导入产品: 神鼎飞丹砂
2025-07-06 15:14:45 - views.main_window - INFO - 处理产品图片: 神鼎飞丹砂
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 神鼎飞丹砂_001.jpeg -> data/images/products/product_7/神鼎飞丹砂_001.jpeg
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 神鼎飞丹砂_002.jpeg -> data/images/products/product_7/神鼎飞丹砂_002.jpeg
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 神鼎飞丹砂_003.jpeg -> data/images/products/product_7/神鼎飞丹砂_003.jpeg
2025-07-06 15:14:45 - views.main_window - INFO - 更新产品图片信息: 3张图片
2025-07-06 15:14:45 - views.main_window - INFO - 导入货源数量: 1
2025-07-06 15:14:45 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 15:14:45 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 15:14:45 - views.main_window - INFO - 导入产品: 宠物洁牙纸
2025-07-06 15:14:45 - views.main_window - INFO - 处理产品图片: 宠物洁牙纸
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 宠物洁牙纸_001.jpeg -> data/images/products/product_8/宠物洁牙纸_001.jpeg
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 宠物洁牙纸_002.jpeg -> data/images/products/product_8/宠物洁牙纸_002.jpeg
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 宠物洁牙纸_003.jpeg -> data/images/products/product_8/宠物洁牙纸_003.jpeg
2025-07-06 15:14:45 - views.main_window - INFO - 复制图片: 宠物洁牙纸_004.jpeg -> data/images/products/product_8/宠物洁牙纸_004.jpeg
2025-07-06 15:14:45 - views.main_window - INFO - 更新产品图片信息: 4张图片
2025-07-06 15:14:45 - views.main_window - INFO - 导入货源数量: 0
2025-07-06 15:14:45 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 15:14:45 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 15:14:45 - views.main_window - INFO - 数据导入完成
2025-07-06 15:15:01 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:19:20 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:19:20 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:19:20 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:19:21 - root - INFO - 应用程序初始化完成
2025-07-06 15:19:21 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:19:21 - root - INFO - 主窗口创建完成
2025-07-06 15:19:22 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:19:22 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:19:22 - root - INFO - 应用程序正在运行...
2025-07-06 15:19:30 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:19:49 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:21:03 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:21:03 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:21:03 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:21:04 - root - INFO - 应用程序初始化完成
2025-07-06 15:21:04 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:21:04 - root - INFO - 主窗口创建完成
2025-07-06 15:21:05 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:21:05 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:21:05 - root - INFO - 应用程序正在运行...
2025-07-06 15:22:07 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:24:07 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:24:07 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:24:07 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:24:08 - root - INFO - 应用程序初始化完成
2025-07-06 15:24:08 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:24:08 - root - INFO - 主窗口创建完成
2025-07-06 15:24:09 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:24:09 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:24:09 - root - INFO - 应用程序正在运行...
2025-07-06 15:26:10 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:27:31 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:27:31 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:27:31 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:27:33 - root - INFO - 应用程序初始化完成
2025-07-06 15:27:33 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:27:34 - root - INFO - 主窗口创建完成
2025-07-06 15:27:34 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:27:34 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:27:34 - root - INFO - 应用程序正在运行...
2025-07-06 15:28:02 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:39:21 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:39:21 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:39:21 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:39:23 - root - INFO - 应用程序初始化完成
2025-07-06 15:39:23 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:39:23 - root - INFO - 主窗口创建完成
2025-07-06 15:39:24 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:39:24 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:39:24 - root - INFO - 应用程序正在运行...
2025-07-06 15:43:20 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:48:42 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:48:42 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:48:42 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:48:42 - root - INFO - 应用程序初始化完成
2025-07-06 15:48:42 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:48:43 - root - INFO - 主窗口创建完成
2025-07-06 15:48:43 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:48:43 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:48:43 - root - INFO - 应用程序正在运行...
2025-07-06 15:51:40 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 15:58:04 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 15:58:04 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 15:58:04 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 15:58:04 - root - INFO - 应用程序初始化完成
2025-07-06 15:58:04 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 15:58:05 - root - INFO - 主窗口创建完成
2025-07-06 15:58:05 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 15:58:05 - root - INFO - 数据库路径: data/database.db
2025-07-06 15:58:05 - root - INFO - 应用程序正在运行...
2025-07-06 15:58:35 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 16:03:52 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:03:52 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 16:03:52 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 16:03:53 - root - INFO - 应用程序初始化完成
2025-07-06 16:03:53 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:03:54 - root - INFO - 主窗口创建完成
2025-07-06 16:03:54 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 16:03:54 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:03:54 - root - INFO - 应用程序正在运行...
2025-07-06 16:04:16 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:04:16 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 16:04:16 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 16:04:16 - root - INFO - 应用程序初始化完成
2025-07-06 16:04:16 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:04:17 - root - INFO - 主窗口创建完成
2025-07-06 16:04:17 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 16:04:17 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:04:17 - root - INFO - 应用程序正在运行...
2025-07-06 16:04:31 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:04:31 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 16:04:31 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 16:04:32 - root - INFO - 应用程序初始化完成
2025-07-06 16:04:32 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:04:34 - root - INFO - 主窗口创建完成
2025-07-06 16:04:34 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 16:04:34 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:04:34 - root - INFO - 应用程序正在运行...
2025-07-06 16:13:00 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:13:00 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 16:13:00 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 16:13:01 - root - INFO - 应用程序初始化完成
2025-07-06 16:13:01 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:13:02 - root - INFO - 主窗口创建完成
2025-07-06 16:13:02 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 16:13:02 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:13:02 - root - INFO - 应用程序正在运行...
2025-07-06 16:16:45 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:16:45 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 16:16:45 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 16:16:47 - root - INFO - 应用程序初始化完成
2025-07-06 16:16:47 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:16:48 - root - INFO - 主窗口创建完成
2025-07-06 16:16:48 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 16:16:48 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:16:48 - root - INFO - 应用程序正在运行...
2025-07-06 16:16:58 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 16:19:39 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 16:20:49 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:20:49 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 16:20:49 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.0
2025-07-06 16:20:51 - root - INFO - 应用程序初始化完成
2025-07-06 16:20:51 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:20:51 - root - INFO - 主窗口创建完成
2025-07-06 16:20:52 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.0 启动成功 ===
2025-07-06 16:20:52 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:20:52 - root - INFO - 应用程序正在运行...
2025-07-06 16:20:58 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 16:30:50 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:30:50 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 16:30:50 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.2
2025-07-06 16:30:50 - root - INFO - 应用程序初始化完成
2025-07-06 16:30:50 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:30:51 - root - INFO - 主窗口创建完成
2025-07-06 16:30:51 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.2 启动成功 ===
2025-07-06 16:30:51 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:30:51 - root - INFO - 应用程序正在运行...
2025-07-06 16:35:41 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 16:37:03 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:37:03 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.1.9-\data\logs\app.log
2025-07-06 16:37:03 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.3
2025-07-06 16:37:04 - root - INFO - 应用程序初始化完成
2025-07-06 16:37:04 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:37:04 - root - INFO - 主窗口创建完成
2025-07-06 16:37:05 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.3 启动成功 ===
2025-07-06 16:37:05 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:37:05 - root - INFO - 应用程序正在运行...
2025-07-06 16:39:44 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_151437
2025-07-06 16:39:46 - views.main_window - INFO - 删除导出版本: export\产品数据导出_20250706_031121
2025-07-06 16:39:48 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250706_163937
2025-07-06 16:39:48 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 1, 标签数量: 1
2025-07-06 16:39:48 - views.main_window - INFO - 开始导入标签...
2025-07-06 16:39:48 - views.main_window - INFO - 标签已存在，跳过创建: 宠物-狗狗
2025-07-06 16:39:48 - views.main_window - INFO - 开始导入产品...
2025-07-06 16:39:48 - views.main_window - INFO - 导入产品: 宠物刷牙指套
2025-07-06 16:39:48 - views.main_window - INFO - 处理产品图片: 宠物刷牙指套
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_001.webp -> data/images/products/product_2/宠物刷牙指套_001.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_002.webp -> data/images/products/product_2/宠物刷牙指套_002.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_003.webp -> data/images/products/product_2/宠物刷牙指套_003.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_004.webp -> data/images/products/product_2/宠物刷牙指套_004.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_005.webp -> data/images/products/product_2/宠物刷牙指套_005.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_006.webp -> data/images/products/product_2/宠物刷牙指套_006.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_007.webp -> data/images/products/product_2/宠物刷牙指套_007.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_008.webp -> data/images/products/product_2/宠物刷牙指套_008.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_009.webp -> data/images/products/product_2/宠物刷牙指套_009.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_010.webp -> data/images/products/product_2/宠物刷牙指套_010.webp
2025-07-06 16:39:48 - views.main_window - INFO - 复制图片: 宠物刷牙指套_011.webp -> data/images/products/product_2/宠物刷牙指套_011.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_012.webp -> data/images/products/product_2/宠物刷牙指套_012.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_013.webp -> data/images/products/product_2/宠物刷牙指套_013.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_014.webp -> data/images/products/product_2/宠物刷牙指套_014.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_015.webp -> data/images/products/product_2/宠物刷牙指套_015.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_016.webp -> data/images/products/product_2/宠物刷牙指套_016.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_017.webp -> data/images/products/product_2/宠物刷牙指套_017.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_018.webp -> data/images/products/product_2/宠物刷牙指套_018.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_019.webp -> data/images/products/product_2/宠物刷牙指套_019.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_020.webp -> data/images/products/product_2/宠物刷牙指套_020.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_021.webp -> data/images/products/product_2/宠物刷牙指套_021.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_022.webp -> data/images/products/product_2/宠物刷牙指套_022.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_023.webp -> data/images/products/product_2/宠物刷牙指套_023.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_024.webp -> data/images/products/product_2/宠物刷牙指套_024.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_025.webp -> data/images/products/product_2/宠物刷牙指套_025.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_026.webp -> data/images/products/product_2/宠物刷牙指套_026.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_027.webp -> data/images/products/product_2/宠物刷牙指套_027.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_028.webp -> data/images/products/product_2/宠物刷牙指套_028.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_029.webp -> data/images/products/product_2/宠物刷牙指套_029.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_030.webp -> data/images/products/product_2/宠物刷牙指套_030.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_031.webp -> data/images/products/product_2/宠物刷牙指套_031.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_032.webp -> data/images/products/product_2/宠物刷牙指套_032.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_033.webp -> data/images/products/product_2/宠物刷牙指套_033.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_034.webp -> data/images/products/product_2/宠物刷牙指套_034.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_035.jpg -> data/images/products/product_2/宠物刷牙指套_035.jpg
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_036.webp -> data/images/products/product_2/宠物刷牙指套_036.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_037.webp -> data/images/products/product_2/宠物刷牙指套_037.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_038.webp -> data/images/products/product_2/宠物刷牙指套_038.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_039.webp -> data/images/products/product_2/宠物刷牙指套_039.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_040.webp -> data/images/products/product_2/宠物刷牙指套_040.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_041.webp -> data/images/products/product_2/宠物刷牙指套_041.webp
2025-07-06 16:39:49 - views.main_window - INFO - 复制图片: 宠物刷牙指套_042.webp -> data/images/products/product_2/宠物刷牙指套_042.webp
2025-07-06 16:39:49 - views.main_window - INFO - 更新产品图片信息: 42张图片
2025-07-06 16:39:49 - views.main_window - INFO - 导入货源数量: 2
2025-07-06 16:39:49 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-06 16:39:49 - views.main_window - INFO - 关联标签数量: 1
2025-07-06 16:39:49 - views.main_window - INFO - 数据导入完成
2025-07-06 16:41:22 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 16:58:07 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 16:58:07 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 16:58:07 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.4
2025-07-06 16:58:08 - root - INFO - 应用程序初始化完成
2025-07-06 16:58:09 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 16:58:10 - root - INFO - 主窗口创建完成
2025-07-06 16:58:10 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.4 启动成功 ===
2025-07-06 16:58:10 - root - INFO - 数据库路径: data/database.db
2025-07-06 16:58:10 - root - INFO - 应用程序正在运行...
2025-07-06 16:58:40 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 17:09:18 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 17:09:18 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 17:09:18 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.4
2025-07-06 17:09:20 - root - INFO - 应用程序初始化完成
2025-07-06 17:09:20 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 17:09:22 - root - INFO - 主窗口创建完成
2025-07-06 17:09:22 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.4 启动成功 ===
2025-07-06 17:09:22 - root - INFO - 数据库路径: data/database.db
2025-07-06 17:09:22 - root - INFO - 应用程序正在运行...
2025-07-06 17:27:39 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 17:27:39 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 17:27:39 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.4
2025-07-06 17:27:40 - root - INFO - 应用程序初始化完成
2025-07-06 17:27:40 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 17:27:41 - root - INFO - 主窗口创建完成
2025-07-06 17:27:41 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.4 启动成功 ===
2025-07-06 17:27:41 - root - INFO - 数据库路径: data/database.db
2025-07-06 17:27:41 - root - INFO - 应用程序正在运行...
2025-07-06 17:28:11 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 17:46:52 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 17:46:52 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 17:46:52 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.4
2025-07-06 17:46:53 - root - INFO - 应用程序初始化完成
2025-07-06 17:46:53 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 17:46:55 - root - INFO - 主窗口创建完成
2025-07-06 17:46:55 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.4 启动成功 ===
2025-07-06 17:46:55 - root - INFO - 数据库路径: data/database.db
2025-07-06 17:46:55 - root - INFO - 应用程序正在运行...
2025-07-06 17:48:50 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 18:06:25 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:06:25 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:06:25 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.4
2025-07-06 18:06:26 - root - INFO - 应用程序初始化完成
2025-07-06 18:06:26 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:06:26 - root - INFO - 主窗口创建完成
2025-07-06 18:06:26 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.4 启动成功 ===
2025-07-06 18:06:26 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:06:26 - root - INFO - 应用程序正在运行...
2025-07-06 18:09:15 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 18:18:11 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:18:11 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:18:11 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.4
2025-07-06 18:18:12 - root - INFO - 应用程序初始化完成
2025-07-06 18:18:12 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:18:12 - root - INFO - 主窗口创建完成
2025-07-06 18:18:13 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.4 启动成功 ===
2025-07-06 18:18:13 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:18:13 - root - INFO - 应用程序正在运行...
2025-07-06 18:18:52 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 18:25:28 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:25:28 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:25:28 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.5
2025-07-06 18:25:28 - root - INFO - 应用程序初始化完成
2025-07-06 18:25:28 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:25:29 - root - INFO - 主窗口创建完成
2025-07-06 18:25:29 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.5 启动成功 ===
2025-07-06 18:25:29 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:25:29 - root - INFO - 应用程序正在运行...
2025-07-06 18:26:04 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 18:26:23 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:26:23 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:26:23 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.5
2025-07-06 18:26:25 - root - INFO - 应用程序初始化完成
2025-07-06 18:26:25 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:26:26 - root - INFO - 主窗口创建完成
2025-07-06 18:26:26 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.5 启动成功 ===
2025-07-06 18:26:26 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:26:26 - root - INFO - 应用程序正在运行...
2025-07-06 18:26:36 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 18:31:40 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:31:40 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:31:40 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.5
2025-07-06 18:31:41 - root - INFO - 应用程序初始化完成
2025-07-06 18:31:41 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:31:41 - root - INFO - 主窗口创建完成
2025-07-06 18:31:41 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.5 启动成功 ===
2025-07-06 18:31:41 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:31:41 - root - INFO - 应用程序正在运行...
2025-07-06 18:32:39 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 18:36:28 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:36:28 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:36:28 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.5
2025-07-06 18:36:28 - root - INFO - 应用程序初始化完成
2025-07-06 18:36:28 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:36:29 - root - INFO - 主窗口创建完成
2025-07-06 18:36:29 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.5 启动成功 ===
2025-07-06 18:36:29 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:36:29 - root - INFO - 应用程序正在运行...
2025-07-06 18:37:40 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 18:42:21 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:42:21 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:42:21 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.6
2025-07-06 18:42:21 - root - INFO - 应用程序初始化完成
2025-07-06 18:42:21 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:42:22 - root - INFO - 主窗口创建完成
2025-07-06 18:42:22 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.6 启动成功 ===
2025-07-06 18:42:22 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:42:22 - root - INFO - 应用程序正在运行...
2025-07-06 18:50:16 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:50:16 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:50:16 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.6
2025-07-06 18:50:17 - root - INFO - 应用程序初始化完成
2025-07-06 18:50:17 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:50:18 - root - INFO - 主窗口创建完成
2025-07-06 18:50:18 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.6 启动成功 ===
2025-07-06 18:50:18 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:50:18 - root - INFO - 应用程序正在运行...
2025-07-06 18:51:15 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 18:53:41 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 18:53:41 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 18:53:41 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.6
2025-07-06 18:53:43 - root - INFO - 应用程序初始化完成
2025-07-06 18:53:43 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 18:53:43 - root - INFO - 主窗口创建完成
2025-07-06 18:53:44 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.6 启动成功 ===
2025-07-06 18:53:44 - root - INFO - 数据库路径: data/database.db
2025-07-06 18:53:44 - root - INFO - 应用程序正在运行...
2025-07-06 18:55:36 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 21:27:46 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 21:27:46 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 21:27:46 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 21:27:47 - root - INFO - 应用程序初始化完成
2025-07-06 21:27:47 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 21:27:47 - root - INFO - 主窗口创建完成
2025-07-06 21:27:49 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 21:27:50 - root - INFO - 数据库路径: data/database.db
2025-07-06 21:27:50 - root - INFO - 应用程序正在运行...
2025-07-06 21:29:45 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 21:32:11 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 21:32:11 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 21:32:11 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 21:32:11 - root - INFO - 应用程序初始化完成
2025-07-06 21:32:11 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 21:32:12 - root - INFO - 主窗口创建完成
2025-07-06 21:32:12 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 21:32:12 - root - INFO - 数据库路径: data/database.db
2025-07-06 21:32:12 - root - INFO - 应用程序正在运行...
2025-07-06 21:33:55 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 21:33:55 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 21:33:55 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 21:33:56 - root - INFO - 应用程序初始化完成
2025-07-06 21:33:56 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 21:33:57 - root - INFO - 主窗口创建完成
2025-07-06 21:33:58 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 21:33:58 - root - INFO - 数据库路径: data/database.db
2025-07-06 21:33:58 - root - INFO - 应用程序正在运行...
2025-07-06 21:34:39 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 21:35:58 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 21:35:58 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 21:35:58 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 21:35:58 - root - INFO - 应用程序初始化完成
2025-07-06 21:35:58 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 21:35:59 - root - INFO - 主窗口创建完成
2025-07-06 21:35:59 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 21:35:59 - root - INFO - 数据库路径: data/database.db
2025-07-06 21:35:59 - root - INFO - 应用程序正在运行...
2025-07-06 21:36:42 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 21:38:51 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 21:38:51 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 21:38:51 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 21:38:53 - root - INFO - 应用程序初始化完成
2025-07-06 21:38:53 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 21:38:54 - root - INFO - 主窗口创建完成
2025-07-06 21:38:54 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 21:38:54 - root - INFO - 数据库路径: data/database.db
2025-07-06 21:38:54 - root - INFO - 应用程序正在运行...
2025-07-06 21:39:35 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 21:42:46 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 21:42:46 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 21:42:46 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 21:42:47 - root - INFO - 应用程序初始化完成
2025-07-06 21:42:47 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 21:42:48 - root - INFO - 主窗口创建完成
2025-07-06 21:42:49 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 21:42:49 - root - INFO - 数据库路径: data/database.db
2025-07-06 21:42:49 - root - INFO - 应用程序正在运行...
2025-07-06 21:43:33 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 21:46:03 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 21:46:03 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 21:46:03 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 21:46:05 - root - INFO - 应用程序初始化完成
2025-07-06 21:46:05 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 21:46:06 - root - INFO - 主窗口创建完成
2025-07-06 21:46:07 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 21:46:07 - root - INFO - 数据库路径: data/database.db
2025-07-06 21:46:07 - root - INFO - 应用程序正在运行...
2025-07-06 21:47:00 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 21:54:57 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 21:54:57 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 21:54:57 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 21:54:58 - root - INFO - 应用程序初始化完成
2025-07-06 21:54:58 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 21:54:59 - root - INFO - 主窗口创建完成
2025-07-06 21:55:00 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 21:55:00 - root - INFO - 数据库路径: data/database.db
2025-07-06 21:55:00 - root - INFO - 应用程序正在运行...
2025-07-06 21:55:01 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 21:55:35 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 23:46:16 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 23:46:16 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 23:46:16 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 23:46:17 - root - INFO - 应用程序初始化完成
2025-07-06 23:46:17 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 23:46:17 - root - INFO - 主窗口创建完成
2025-07-06 23:46:18 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 23:46:18 - root - INFO - 数据库路径: data/database.db
2025-07-06 23:46:18 - root - INFO - 应用程序正在运行...
2025-07-06 23:48:27 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 23:49:50 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 23:49:50 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 23:49:50 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 23:49:51 - root - INFO - 应用程序初始化完成
2025-07-06 23:49:51 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 23:49:52 - root - INFO - 主窗口创建完成
2025-07-06 23:49:52 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 23:49:52 - root - INFO - 数据库路径: data/database.db
2025-07-06 23:49:52 - root - INFO - 应用程序正在运行...
2025-07-06 23:50:04 - root - INFO - 应用程序退出，返回值: 0
2025-07-06 23:54:31 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-06 23:54:31 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-06 23:54:31 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-06 23:54:31 - root - INFO - 应用程序初始化完成
2025-07-06 23:54:31 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-06 23:54:32 - root - INFO - 主窗口创建完成
2025-07-06 23:54:32 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-06 23:54:32 - root - INFO - 数据库路径: data/database.db
2025-07-06 23:54:32 - root - INFO - 应用程序正在运行...
2025-07-06 23:55:34 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 00:03:44 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 00:03:44 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 00:03:44 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-07 00:03:45 - root - INFO - 应用程序初始化完成
2025-07-07 00:03:45 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 00:03:45 - root - INFO - 主窗口创建完成
2025-07-07 00:03:45 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-07 00:03:45 - root - INFO - 数据库路径: data/database.db
2025-07-07 00:03:45 - root - INFO - 应用程序正在运行...
2025-07-07 00:04:31 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 00:05:18 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 00:05:18 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 00:05:18 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-07 00:05:21 - root - INFO - 应用程序初始化完成
2025-07-07 00:05:21 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 00:05:22 - root - INFO - 主窗口创建完成
2025-07-07 00:05:24 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-07 00:05:24 - root - INFO - 数据库路径: data/database.db
2025-07-07 00:05:24 - root - INFO - 应用程序正在运行...
2025-07-07 00:07:04 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 00:09:59 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 00:09:59 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 00:09:59 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-07 00:09:59 - root - INFO - 应用程序初始化完成
2025-07-07 00:09:59 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 00:10:00 - root - INFO - 主窗口创建完成
2025-07-07 00:10:00 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-07 00:10:00 - root - INFO - 数据库路径: data/database.db
2025-07-07 00:10:00 - root - INFO - 应用程序正在运行...
2025-07-07 00:11:27 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 00:15:52 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 00:15:52 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 00:15:52 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.7
2025-07-07 00:15:53 - root - INFO - 应用程序初始化完成
2025-07-07 00:15:53 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 00:15:54 - root - INFO - 主窗口创建完成
2025-07-07 00:15:54 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.7 启动成功 ===
2025-07-07 00:15:54 - root - INFO - 数据库路径: data/database.db
2025-07-07 00:15:54 - root - INFO - 应用程序正在运行...
2025-07-07 00:16:19 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 02:21:03 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 02:21:03 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 02:21:03 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 02:21:03 - root - INFO - 应用程序初始化完成
2025-07-07 02:21:03 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 02:21:05 - root - INFO - 主窗口创建完成
2025-07-07 02:21:06 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 02:21:06 - root - INFO - 数据库路径: data/database.db
2025-07-07 02:21:06 - root - INFO - 应用程序正在运行...
2025-07-07 02:22:13 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 02:22:43 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 02:22:43 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 02:22:43 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 02:22:45 - root - INFO - 应用程序初始化完成
2025-07-07 02:22:45 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 02:22:46 - root - INFO - 主窗口创建完成
2025-07-07 02:22:47 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 02:22:47 - root - INFO - 数据库路径: data/database.db
2025-07-07 02:22:47 - root - INFO - 应用程序正在运行...
2025-07-07 02:23:51 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 02:27:48 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 02:27:48 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 02:27:48 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 02:27:49 - root - INFO - 应用程序初始化完成
2025-07-07 02:27:49 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 02:27:49 - root - INFO - 主窗口创建完成
2025-07-07 02:27:50 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 02:27:50 - root - INFO - 数据库路径: data/database.db
2025-07-07 02:27:50 - root - INFO - 应用程序正在运行...
2025-07-07 02:28:59 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 02:29:20 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 02:29:20 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 02:29:20 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 02:29:22 - root - INFO - 应用程序初始化完成
2025-07-07 02:29:23 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 02:29:25 - root - INFO - 主窗口创建完成
2025-07-07 02:29:25 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 02:29:25 - root - INFO - 数据库路径: data/database.db
2025-07-07 02:29:25 - root - INFO - 应用程序正在运行...
2025-07-07 02:29:34 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 02:34:24 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 02:34:24 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 02:34:24 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 02:34:24 - root - INFO - 应用程序初始化完成
2025-07-07 02:34:24 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 02:34:25 - root - INFO - 主窗口创建完成
2025-07-07 02:34:26 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 02:34:26 - root - INFO - 数据库路径: data/database.db
2025-07-07 02:34:26 - root - INFO - 应用程序正在运行...
2025-07-07 02:35:08 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 02:39:58 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 02:39:58 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 02:39:58 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 02:39:59 - root - INFO - 应用程序初始化完成
2025-07-07 02:39:59 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 02:40:00 - root - INFO - 主窗口创建完成
2025-07-07 02:40:00 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 02:40:00 - root - INFO - 数据库路径: data/database.db
2025-07-07 02:40:00 - root - INFO - 应用程序正在运行...
2025-07-07 02:44:12 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 02:47:54 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 02:47:54 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 02:47:54 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 02:47:54 - root - INFO - 应用程序初始化完成
2025-07-07 02:47:54 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 02:47:55 - root - INFO - 主窗口创建完成
2025-07-07 02:47:55 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 02:47:55 - root - INFO - 数据库路径: data/database.db
2025-07-07 02:47:55 - root - INFO - 应用程序正在运行...
2025-07-07 02:48:44 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 02:54:07 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 02:54:07 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 02:54:07 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 02:54:07 - root - INFO - 应用程序初始化完成
2025-07-07 02:54:07 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 02:54:08 - root - INFO - 主窗口创建完成
2025-07-07 02:54:08 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 02:54:08 - root - INFO - 数据库路径: data/database.db
2025-07-07 02:54:08 - root - INFO - 应用程序正在运行...
2025-07-07 02:55:08 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:00:31 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:00:31 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:00:31 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 03:00:32 - root - INFO - 应用程序初始化完成
2025-07-07 03:00:32 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:00:34 - root - INFO - 主窗口创建完成
2025-07-07 03:00:35 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 03:00:35 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:00:35 - root - INFO - 应用程序正在运行...
2025-07-07 03:00:52 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:02:24 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:02:24 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:02:24 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 03:02:25 - root - INFO - 应用程序初始化完成
2025-07-07 03:02:25 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:02:27 - root - INFO - 主窗口创建完成
2025-07-07 03:02:28 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 03:02:28 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:02:28 - root - INFO - 应用程序正在运行...
2025-07-07 03:02:31 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:09:52 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:09:53 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:09:53 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 03:09:54 - root - INFO - 应用程序初始化完成
2025-07-07 03:09:54 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:09:55 - root - INFO - 主窗口创建完成
2025-07-07 03:09:55 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 03:09:55 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:09:55 - root - INFO - 应用程序正在运行...
2025-07-07 03:11:10 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:17:45 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:17:45 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:17:45 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.2.8
2025-07-07 03:17:47 - root - INFO - 应用程序初始化完成
2025-07-07 03:17:47 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:17:49 - root - INFO - 主窗口创建完成
2025-07-07 03:17:49 - root - INFO - === Python_Ecom_Product_Source_Compare v2.2.8 启动成功 ===
2025-07-07 03:17:49 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:17:49 - root - INFO - 应用程序正在运行...
2025-07-07 03:22:50 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:25:37 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:25:37 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:25:37 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.0
2025-07-07 03:25:37 - root - INFO - 应用程序初始化完成
2025-07-07 03:25:37 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:25:38 - root - INFO - 主窗口创建完成
2025-07-07 03:25:38 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.0 启动成功 ===
2025-07-07 03:25:38 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:25:38 - root - INFO - 应用程序正在运行...
2025-07-07 03:26:25 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:27:31 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:27:31 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:27:31 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.0
2025-07-07 03:27:32 - root - INFO - 应用程序初始化完成
2025-07-07 03:27:32 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:27:35 - root - INFO - 主窗口创建完成
2025-07-07 03:27:35 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.0 启动成功 ===
2025-07-07 03:27:35 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:27:35 - root - INFO - 应用程序正在运行...
2025-07-07 03:29:47 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:33:43 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:33:43 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:33:43 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.0
2025-07-07 03:33:43 - root - INFO - 应用程序初始化完成
2025-07-07 03:33:43 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:33:44 - root - INFO - 主窗口创建完成
2025-07-07 03:33:44 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.0 启动成功 ===
2025-07-07 03:33:44 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:33:44 - root - INFO - 应用程序正在运行...
2025-07-07 03:34:46 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:38:59 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:38:59 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:38:59 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.0
2025-07-07 03:39:00 - root - INFO - 应用程序初始化完成
2025-07-07 03:39:00 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:39:00 - root - INFO - 主窗口创建完成
2025-07-07 03:39:01 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.0 启动成功 ===
2025-07-07 03:39:01 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:39:01 - root - INFO - 应用程序正在运行...
2025-07-07 03:40:00 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 03:43:31 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 03:43:31 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 03:43:31 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.0
2025-07-07 03:43:32 - root - INFO - 应用程序初始化完成
2025-07-07 03:43:32 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 03:43:35 - root - INFO - 主窗口创建完成
2025-07-07 03:43:37 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.0 启动成功 ===
2025-07-07 03:43:38 - root - INFO - 数据库路径: data/database.db
2025-07-07 03:43:38 - root - INFO - 应用程序正在运行...
2025-07-07 03:44:54 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 10:28:00 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 10:28:00 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 10:28:00 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.0
2025-07-07 10:28:01 - root - INFO - 应用程序初始化完成
2025-07-07 10:28:01 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 10:28:02 - root - INFO - 主窗口创建完成
2025-07-07 10:28:02 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.0 启动成功 ===
2025-07-07 10:28:02 - root - INFO - 数据库路径: data/database.db
2025-07-07 10:28:02 - root - INFO - 应用程序正在运行...
2025-07-07 10:28:42 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 10:48:30 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 10:48:30 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 10:48:30 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.2
2025-07-07 10:48:30 - root - INFO - 应用程序初始化完成
2025-07-07 10:48:30 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 10:48:32 - root - INFO - 主窗口创建完成
2025-07-07 10:48:33 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.2 启动成功 ===
2025-07-07 10:48:33 - root - INFO - 数据库路径: data/database.db
2025-07-07 10:48:33 - root - INFO - 应用程序正在运行...
2025-07-07 10:48:59 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 10:59:00 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 10:59:00 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 10:59:00 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.2
2025-07-07 10:59:01 - root - INFO - 应用程序初始化完成
2025-07-07 10:59:01 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 10:59:02 - root - INFO - 主窗口创建完成
2025-07-07 10:59:02 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.2 启动成功 ===
2025-07-07 10:59:02 - root - INFO - 数据库路径: data/database.db
2025-07-07 10:59:02 - root - INFO - 应用程序正在运行...
2025-07-07 11:01:31 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 11:01:37 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 11:01:37 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 11:01:37 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.2
2025-07-07 11:01:37 - root - INFO - 应用程序初始化完成
2025-07-07 11:01:37 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 11:01:38 - root - INFO - 主窗口创建完成
2025-07-07 11:01:38 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.2 启动成功 ===
2025-07-07 11:01:38 - root - INFO - 数据库路径: data/database.db
2025-07-07 11:01:38 - root - INFO - 应用程序正在运行...
2025-07-07 11:03:32 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 11:03:35 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 11:03:35 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 11:03:35 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.2
2025-07-07 11:03:37 - root - INFO - 应用程序初始化完成
2025-07-07 11:03:37 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 11:03:38 - root - INFO - 主窗口创建完成
2025-07-07 11:03:38 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.2 启动成功 ===
2025-07-07 11:03:38 - root - INFO - 数据库路径: data/database.db
2025-07-07 11:03:38 - root - INFO - 应用程序正在运行...
2025-07-07 11:03:43 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 11:11:44 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 11:11:44 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 11:11:44 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.3
2025-07-07 11:11:46 - root - INFO - 应用程序初始化完成
2025-07-07 11:11:46 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 11:11:49 - root - INFO - 主窗口创建完成
2025-07-07 11:11:51 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.3 启动成功 ===
2025-07-07 11:11:51 - root - INFO - 数据库路径: data/database.db
2025-07-07 11:11:51 - root - INFO - 应用程序正在运行...
2025-07-07 11:12:46 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 11:18:38 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 11:18:38 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 11:18:38 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.4
2025-07-07 11:18:39 - root - INFO - 应用程序初始化完成
2025-07-07 11:18:39 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 11:18:41 - root - INFO - 主窗口创建完成
2025-07-07 11:18:42 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.4 启动成功 ===
2025-07-07 11:18:42 - root - INFO - 数据库路径: data/database.db
2025-07-07 11:18:42 - root - INFO - 应用程序正在运行...
2025-07-07 11:19:35 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 11:28:20 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 11:28:20 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 11:28:20 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.3
2025-07-07 11:28:21 - root - INFO - 应用程序初始化完成
2025-07-07 11:28:21 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 11:28:23 - root - INFO - 主窗口创建完成
2025-07-07 11:28:25 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.3 启动成功 ===
2025-07-07 11:28:25 - root - INFO - 数据库路径: data/database.db
2025-07-07 11:28:25 - root - INFO - 应用程序正在运行...
2025-07-07 11:30:08 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 11:30:08 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 11:30:08 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.3
2025-07-07 11:30:08 - root - INFO - 应用程序初始化完成
2025-07-07 11:30:08 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 11:30:10 - root - INFO - 主窗口创建完成
2025-07-07 11:30:10 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.3 启动成功 ===
2025-07-07 11:30:10 - root - INFO - 数据库路径: data/database.db
2025-07-07 11:30:10 - root - INFO - 应用程序正在运行...
2025-07-07 11:31:16 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 11:38:44 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 11:38:44 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 11:38:44 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.3
2025-07-07 11:38:46 - root - INFO - 应用程序初始化完成
2025-07-07 11:38:46 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 11:38:47 - root - INFO - 主窗口创建完成
2025-07-07 11:38:47 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.3 启动成功 ===
2025-07-07 11:38:47 - root - INFO - 数据库路径: data/database.db
2025-07-07 11:38:47 - root - INFO - 应用程序正在运行...
2025-07-07 11:39:43 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 11:55:06 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 11:55:06 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 11:55:06 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.3
2025-07-07 11:55:07 - root - INFO - 应用程序初始化完成
2025-07-07 11:55:07 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 11:55:08 - root - INFO - 主窗口创建完成
2025-07-07 11:55:08 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.3 启动成功 ===
2025-07-07 11:55:08 - root - INFO - 数据库路径: data/database.db
2025-07-07 11:55:08 - root - INFO - 应用程序正在运行...
2025-07-07 11:56:16 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 12:02:16 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 13:48:12 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 13:48:12 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 13:48:12 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.3
2025-07-07 13:48:13 - root - INFO - 应用程序初始化完成
2025-07-07 13:48:13 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 13:48:17 - root - INFO - 主窗口创建完成
2025-07-07 13:48:18 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.3 启动成功 ===
2025-07-07 13:48:18 - root - INFO - 数据库路径: data/database.db
2025-07-07 13:48:18 - root - INFO - 应用程序正在运行...
2025-07-07 13:48:41 - views.main_window - INFO - 开始导入数据 - 文件夹: export\产品数据导出_20250707_134054
2025-07-07 13:48:41 - views.main_window - INFO - 数据文件读取成功 - 产品数量: 2, 标签数量: 2
2025-07-07 13:48:41 - views.main_window - INFO - 开始导入标签...
2025-07-07 13:48:41 - views.main_window - INFO - 标签已存在，跳过创建: 宠物-狗狗
2025-07-07 13:48:41 - views.main_window - INFO - 创建标签: 宠物-猫咪
2025-07-07 13:48:41 - views.main_window - INFO - 开始导入产品...
2025-07-07 13:48:41 - views.main_window - INFO - 导入产品: 自动小狗跳跳球
2025-07-07 13:48:41 - views.main_window - INFO - 处理产品图片: 自动小狗跳跳球
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_001.jpg -> data/images/products/product_4/自动小狗跳跳球_001.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_002.jpg -> data/images/products/product_4/自动小狗跳跳球_002.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_003.webp -> data/images/products/product_4/自动小狗跳跳球_003.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_004.webp -> data/images/products/product_4/自动小狗跳跳球_004.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_005.webp -> data/images/products/product_4/自动小狗跳跳球_005.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_007.jpg -> data/images/products/product_4/自动小狗跳跳球_007.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_008.webp -> data/images/products/product_4/自动小狗跳跳球_008.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_009.jpg -> data/images/products/product_4/自动小狗跳跳球_009.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_010.webp -> data/images/products/product_4/自动小狗跳跳球_010.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_011.jpg -> data/images/products/product_4/自动小狗跳跳球_011.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_012.jpg -> data/images/products/product_4/自动小狗跳跳球_012.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_013.jpg -> data/images/products/product_4/自动小狗跳跳球_013.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_014.jpg -> data/images/products/product_4/自动小狗跳跳球_014.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_015.jpg -> data/images/products/product_4/自动小狗跳跳球_015.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_006.webp -> data/images/products/product_4/自动小狗跳跳球_006.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_016.webp -> data/images/products/product_4/自动小狗跳跳球_016.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_017.webp -> data/images/products/product_4/自动小狗跳跳球_017.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_018.webp -> data/images/products/product_4/自动小狗跳跳球_018.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_019.webp -> data/images/products/product_4/自动小狗跳跳球_019.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_020.webp -> data/images/products/product_4/自动小狗跳跳球_020.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_021.webp -> data/images/products/product_4/自动小狗跳跳球_021.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_022.webp -> data/images/products/product_4/自动小狗跳跳球_022.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_023.webp -> data/images/products/product_4/自动小狗跳跳球_023.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_024.webp -> data/images/products/product_4/自动小狗跳跳球_024.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_025.webp -> data/images/products/product_4/自动小狗跳跳球_025.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_026.webp -> data/images/products/product_4/自动小狗跳跳球_026.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_027.webp -> data/images/products/product_4/自动小狗跳跳球_027.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_028.webp -> data/images/products/product_4/自动小狗跳跳球_028.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_029.webp -> data/images/products/product_4/自动小狗跳跳球_029.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_030.webp -> data/images/products/product_4/自动小狗跳跳球_030.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_031.webp -> data/images/products/product_4/自动小狗跳跳球_031.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_032.webp -> data/images/products/product_4/自动小狗跳跳球_032.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_033.jpg -> data/images/products/product_4/自动小狗跳跳球_033.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_034.jpg -> data/images/products/product_4/自动小狗跳跳球_034.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_035.webp -> data/images/products/product_4/自动小狗跳跳球_035.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_036.webp -> data/images/products/product_4/自动小狗跳跳球_036.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_037.webp -> data/images/products/product_4/自动小狗跳跳球_037.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_038.jpg -> data/images/products/product_4/自动小狗跳跳球_038.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_039.jpg -> data/images/products/product_4/自动小狗跳跳球_039.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_040.webp -> data/images/products/product_4/自动小狗跳跳球_040.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_041.jpg -> data/images/products/product_4/自动小狗跳跳球_041.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_042.webp -> data/images/products/product_4/自动小狗跳跳球_042.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_043.jpg -> data/images/products/product_4/自动小狗跳跳球_043.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_044.jpg -> data/images/products/product_4/自动小狗跳跳球_044.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_045.jpg -> data/images/products/product_4/自动小狗跳跳球_045.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_046.jpg -> data/images/products/product_4/自动小狗跳跳球_046.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 自动小狗跳跳球_047.jpg -> data/images/products/product_4/自动小狗跳跳球_047.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 更新产品图片信息: 47张图片
2025-07-07 13:48:41 - views.main_window - INFO - 导入货源数量: 0
2025-07-07 13:48:41 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-07 13:48:41 - views.main_window - INFO - 关联标签数量: 1
2025-07-07 13:48:41 - views.main_window - INFO - 导入产品: 宠物刷牙指套
2025-07-07 13:48:41 - views.main_window - INFO - 处理产品图片: 宠物刷牙指套
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_002.webp -> data/images/products/product_5/宠物刷牙指套_002.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_003.webp -> data/images/products/product_5/宠物刷牙指套_003.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_005.webp -> data/images/products/product_5/宠物刷牙指套_005.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_004.webp -> data/images/products/product_5/宠物刷牙指套_004.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_006.jpg -> data/images/products/product_5/宠物刷牙指套_006.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_001.webp -> data/images/products/product_5/宠物刷牙指套_001.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_007.webp -> data/images/products/product_5/宠物刷牙指套_007.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_008.webp -> data/images/products/product_5/宠物刷牙指套_008.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_009.webp -> data/images/products/product_5/宠物刷牙指套_009.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_010.webp -> data/images/products/product_5/宠物刷牙指套_010.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_011.webp -> data/images/products/product_5/宠物刷牙指套_011.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_012.webp -> data/images/products/product_5/宠物刷牙指套_012.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_013.webp -> data/images/products/product_5/宠物刷牙指套_013.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_014.jpg -> data/images/products/product_5/宠物刷牙指套_014.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_015.jpg -> data/images/products/product_5/宠物刷牙指套_015.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_016.jpg -> data/images/products/product_5/宠物刷牙指套_016.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_017.jpg -> data/images/products/product_5/宠物刷牙指套_017.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_018.jpg -> data/images/products/product_5/宠物刷牙指套_018.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_019.jpg -> data/images/products/product_5/宠物刷牙指套_019.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_020.jpg -> data/images/products/product_5/宠物刷牙指套_020.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_021.jpg -> data/images/products/product_5/宠物刷牙指套_021.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_022.jpg -> data/images/products/product_5/宠物刷牙指套_022.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_023.jpg -> data/images/products/product_5/宠物刷牙指套_023.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_024.jpg -> data/images/products/product_5/宠物刷牙指套_024.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_025.jpg -> data/images/products/product_5/宠物刷牙指套_025.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_026.jpg -> data/images/products/product_5/宠物刷牙指套_026.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_027.jpg -> data/images/products/product_5/宠物刷牙指套_027.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_028.jpg -> data/images/products/product_5/宠物刷牙指套_028.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_029.jpg -> data/images/products/product_5/宠物刷牙指套_029.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_030.jpg -> data/images/products/product_5/宠物刷牙指套_030.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_031.jpg -> data/images/products/product_5/宠物刷牙指套_031.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_032.jpg -> data/images/products/product_5/宠物刷牙指套_032.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_033.jpg -> data/images/products/product_5/宠物刷牙指套_033.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_034.jpg -> data/images/products/product_5/宠物刷牙指套_034.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_035.jpg -> data/images/products/product_5/宠物刷牙指套_035.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_036.jpg -> data/images/products/product_5/宠物刷牙指套_036.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_037.jpg -> data/images/products/product_5/宠物刷牙指套_037.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_038.jpg -> data/images/products/product_5/宠物刷牙指套_038.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_039.webp -> data/images/products/product_5/宠物刷牙指套_039.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_040.webp -> data/images/products/product_5/宠物刷牙指套_040.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_041.jpg -> data/images/products/product_5/宠物刷牙指套_041.jpg
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_042.webp -> data/images/products/product_5/宠物刷牙指套_042.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_043.webp -> data/images/products/product_5/宠物刷牙指套_043.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_044.webp -> data/images/products/product_5/宠物刷牙指套_044.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_045.webp -> data/images/products/product_5/宠物刷牙指套_045.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_046.webp -> data/images/products/product_5/宠物刷牙指套_046.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_047.webp -> data/images/products/product_5/宠物刷牙指套_047.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_048.webp -> data/images/products/product_5/宠物刷牙指套_048.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_049.webp -> data/images/products/product_5/宠物刷牙指套_049.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_050.webp -> data/images/products/product_5/宠物刷牙指套_050.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_051.webp -> data/images/products/product_5/宠物刷牙指套_051.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_052.webp -> data/images/products/product_5/宠物刷牙指套_052.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_053.webp -> data/images/products/product_5/宠物刷牙指套_053.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_054.webp -> data/images/products/product_5/宠物刷牙指套_054.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_055.webp -> data/images/products/product_5/宠物刷牙指套_055.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_056.webp -> data/images/products/product_5/宠物刷牙指套_056.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_057.webp -> data/images/products/product_5/宠物刷牙指套_057.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_058.webp -> data/images/products/product_5/宠物刷牙指套_058.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_059.webp -> data/images/products/product_5/宠物刷牙指套_059.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_060.webp -> data/images/products/product_5/宠物刷牙指套_060.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_061.webp -> data/images/products/product_5/宠物刷牙指套_061.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_062.webp -> data/images/products/product_5/宠物刷牙指套_062.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_063.webp -> data/images/products/product_5/宠物刷牙指套_063.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_064.webp -> data/images/products/product_5/宠物刷牙指套_064.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_065.webp -> data/images/products/product_5/宠物刷牙指套_065.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_066.webp -> data/images/products/product_5/宠物刷牙指套_066.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_067.webp -> data/images/products/product_5/宠物刷牙指套_067.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_068.webp -> data/images/products/product_5/宠物刷牙指套_068.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_069.webp -> data/images/products/product_5/宠物刷牙指套_069.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_070.webp -> data/images/products/product_5/宠物刷牙指套_070.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_071.webp -> data/images/products/product_5/宠物刷牙指套_071.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_072.webp -> data/images/products/product_5/宠物刷牙指套_072.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_073.webp -> data/images/products/product_5/宠物刷牙指套_073.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_074.webp -> data/images/products/product_5/宠物刷牙指套_074.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_075.webp -> data/images/products/product_5/宠物刷牙指套_075.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_076.webp -> data/images/products/product_5/宠物刷牙指套_076.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_077.webp -> data/images/products/product_5/宠物刷牙指套_077.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_078.webp -> data/images/products/product_5/宠物刷牙指套_078.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_079.webp -> data/images/products/product_5/宠物刷牙指套_079.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_080.webp -> data/images/products/product_5/宠物刷牙指套_080.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_081.webp -> data/images/products/product_5/宠物刷牙指套_081.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_082.webp -> data/images/products/product_5/宠物刷牙指套_082.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_083.webp -> data/images/products/product_5/宠物刷牙指套_083.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_084.webp -> data/images/products/product_5/宠物刷牙指套_084.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_085.webp -> data/images/products/product_5/宠物刷牙指套_085.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_086.webp -> data/images/products/product_5/宠物刷牙指套_086.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_087.webp -> data/images/products/product_5/宠物刷牙指套_087.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_088.webp -> data/images/products/product_5/宠物刷牙指套_088.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_089.webp -> data/images/products/product_5/宠物刷牙指套_089.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_090.webp -> data/images/products/product_5/宠物刷牙指套_090.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_091.webp -> data/images/products/product_5/宠物刷牙指套_091.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_092.webp -> data/images/products/product_5/宠物刷牙指套_092.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_093.webp -> data/images/products/product_5/宠物刷牙指套_093.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_094.webp -> data/images/products/product_5/宠物刷牙指套_094.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_095.webp -> data/images/products/product_5/宠物刷牙指套_095.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_096.webp -> data/images/products/product_5/宠物刷牙指套_096.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_097.webp -> data/images/products/product_5/宠物刷牙指套_097.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_098.webp -> data/images/products/product_5/宠物刷牙指套_098.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_099.webp -> data/images/products/product_5/宠物刷牙指套_099.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_100.webp -> data/images/products/product_5/宠物刷牙指套_100.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_101.webp -> data/images/products/product_5/宠物刷牙指套_101.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_102.webp -> data/images/products/product_5/宠物刷牙指套_102.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_103.webp -> data/images/products/product_5/宠物刷牙指套_103.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_104.webp -> data/images/products/product_5/宠物刷牙指套_104.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_105.webp -> data/images/products/product_5/宠物刷牙指套_105.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_106.webp -> data/images/products/product_5/宠物刷牙指套_106.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_107.webp -> data/images/products/product_5/宠物刷牙指套_107.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_108.webp -> data/images/products/product_5/宠物刷牙指套_108.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_109.webp -> data/images/products/product_5/宠物刷牙指套_109.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_110.webp -> data/images/products/product_5/宠物刷牙指套_110.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_111.webp -> data/images/products/product_5/宠物刷牙指套_111.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_112.webp -> data/images/products/product_5/宠物刷牙指套_112.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_113.webp -> data/images/products/product_5/宠物刷牙指套_113.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_114.webp -> data/images/products/product_5/宠物刷牙指套_114.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_115.webp -> data/images/products/product_5/宠物刷牙指套_115.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_116.webp -> data/images/products/product_5/宠物刷牙指套_116.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_117.webp -> data/images/products/product_5/宠物刷牙指套_117.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_118.webp -> data/images/products/product_5/宠物刷牙指套_118.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_119.webp -> data/images/products/product_5/宠物刷牙指套_119.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_120.webp -> data/images/products/product_5/宠物刷牙指套_120.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_121.webp -> data/images/products/product_5/宠物刷牙指套_121.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_122.webp -> data/images/products/product_5/宠物刷牙指套_122.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_123.webp -> data/images/products/product_5/宠物刷牙指套_123.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_124.webp -> data/images/products/product_5/宠物刷牙指套_124.webp
2025-07-07 13:48:41 - views.main_window - INFO - 复制图片: 宠物刷牙指套_125.webp -> data/images/products/product_5/宠物刷牙指套_125.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_126.webp -> data/images/products/product_5/宠物刷牙指套_126.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_127.webp -> data/images/products/product_5/宠物刷牙指套_127.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_128.webp -> data/images/products/product_5/宠物刷牙指套_128.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_129.webp -> data/images/products/product_5/宠物刷牙指套_129.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_130.webp -> data/images/products/product_5/宠物刷牙指套_130.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_131.webp -> data/images/products/product_5/宠物刷牙指套_131.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_132.webp -> data/images/products/product_5/宠物刷牙指套_132.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_133.webp -> data/images/products/product_5/宠物刷牙指套_133.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_134.webp -> data/images/products/product_5/宠物刷牙指套_134.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_135.webp -> data/images/products/product_5/宠物刷牙指套_135.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_136.webp -> data/images/products/product_5/宠物刷牙指套_136.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_137.webp -> data/images/products/product_5/宠物刷牙指套_137.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_138.webp -> data/images/products/product_5/宠物刷牙指套_138.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_139.webp -> data/images/products/product_5/宠物刷牙指套_139.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_140.webp -> data/images/products/product_5/宠物刷牙指套_140.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_141.webp -> data/images/products/product_5/宠物刷牙指套_141.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_142.webp -> data/images/products/product_5/宠物刷牙指套_142.webp
2025-07-07 13:48:42 - views.main_window - INFO - 复制图片: 宠物刷牙指套_143.jpg -> data/images/products/product_5/宠物刷牙指套_143.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 更新产品图片信息: 143张图片
2025-07-07 13:48:42 - views.main_window - INFO - 导入货源数量: 3
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_001.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_001.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_002.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_002.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_003.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_003.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_004.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_004.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_005.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_005.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_006.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_006.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_007.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_007.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_008.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_008.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_009.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_009.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_010.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_010.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_011.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_011.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_012.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_012.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_013.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_013.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_014.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_014.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_015.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_015.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_016.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_016.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_017.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_017.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_018.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_018.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_019.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_019.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_020.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_020.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_021.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_021.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_022.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_022.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_023.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_023.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_024.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_024.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_025.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_025.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_026.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_026.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_027.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_027.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_028.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_028.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_029.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_029.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_030.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_030.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_031.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_031.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 张家港朝之秀防护用品有限公司_032.jpg -> data/images/sources/source_5/张家港朝之秀防护用品有限公司_032.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_001.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_001.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_002.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_002.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_003.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_003.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_004.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_004.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_005.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_005.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_006.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_006.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_007.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_007.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_008.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_008.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_009.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_009.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_010.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_010.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_011.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_011.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_012.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_012.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_013.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_013.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_014.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_014.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_015.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_015.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_016.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_016.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_017.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_017.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_018.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_018.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_019.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_019.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_020.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_020.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_021.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_021.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_022.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_022.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市骏骏宠物用品有限公司_023.jpg -> data/images/sources/source_13/义乌市骏骏宠物用品有限公司_023.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_001.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_001.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_002.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_002.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_003.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_003.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_004.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_004.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_005.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_005.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_006.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_006.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_007.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_007.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_008.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_008.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_009.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_009.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_010.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_010.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_011.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_011.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_012.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_012.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 复制货源图片: 义乌市锦和宠物用品有限公司_013.jpg -> data/images/sources/source_12/义乌市锦和宠物用品有限公司_013.jpg
2025-07-07 13:48:42 - views.main_window - INFO - 导入自定义字段数量: 0
2025-07-07 13:48:42 - views.main_window - INFO - 关联标签数量: 1
2025-07-07 13:48:42 - views.main_window - INFO - 数据导入完成
2025-07-07 13:50:15 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 14:03:42 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 14:03:42 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 14:03:42 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.3
2025-07-07 14:03:44 - root - INFO - 应用程序初始化完成
2025-07-07 14:03:44 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 14:03:46 - root - INFO - 主窗口创建完成
2025-07-07 14:03:47 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.3 启动成功 ===
2025-07-07 14:03:47 - root - INFO - 数据库路径: data/database.db
2025-07-07 14:03:47 - root - INFO - 应用程序正在运行...
2025-07-07 14:04:18 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 14:31:07 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 14:31:07 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 14:31:07 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.5
2025-07-07 14:31:09 - root - INFO - 应用程序初始化完成
2025-07-07 14:31:09 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 14:31:13 - root - INFO - 主窗口创建完成
2025-07-07 14:31:15 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.5 启动成功 ===
2025-07-07 14:31:15 - root - INFO - 数据库路径: data/database.db
2025-07-07 14:31:15 - root - INFO - 应用程序正在运行...
2025-07-07 14:31:27 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 14:31:27 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 14:31:27 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.5
2025-07-07 14:31:29 - root - INFO - 应用程序初始化完成
2025-07-07 14:31:30 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 14:31:32 - root - INFO - 主窗口创建完成
2025-07-07 14:31:32 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.5 启动成功 ===
2025-07-07 14:31:32 - root - INFO - 数据库路径: data/database.db
2025-07-07 14:31:32 - root - INFO - 应用程序正在运行...
2025-07-07 14:31:55 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 14:32:02 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 20:32:30 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 20:32:30 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 20:32:30 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.5
2025-07-07 20:32:31 - root - INFO - 应用程序初始化完成
2025-07-07 20:32:31 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 20:32:31 - root - INFO - 主窗口创建完成
2025-07-07 20:32:32 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.5 启动成功 ===
2025-07-07 20:32:32 - root - INFO - 数据库路径: data/database.db
2025-07-07 20:32:32 - root - INFO - 应用程序正在运行...
2025-07-07 20:32:45 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 20:32:51 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 20:32:51 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 20:32:51 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.5
2025-07-07 20:32:52 - root - INFO - 应用程序初始化完成
2025-07-07 20:32:52 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 20:32:53 - root - INFO - 主窗口创建完成
2025-07-07 20:32:53 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.5 启动成功 ===
2025-07-07 20:32:53 - root - INFO - 数据库路径: data/database.db
2025-07-07 20:32:53 - root - INFO - 应用程序正在运行...
2025-07-07 20:34:02 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 20:36:32 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 20:36:32 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 20:36:32 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.5
2025-07-07 20:36:32 - root - INFO - 应用程序初始化完成
2025-07-07 20:36:32 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 20:36:32 - root - INFO - 主窗口创建完成
2025-07-07 20:36:33 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.5 启动成功 ===
2025-07-07 20:36:33 - root - INFO - 数据库路径: data/database.db
2025-07-07 20:36:33 - root - INFO - 应用程序正在运行...
2025-07-07 21:20:05 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 21:20:05 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 21:20:05 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.5
2025-07-07 21:20:09 - root - INFO - 应用程序初始化完成
2025-07-07 21:20:09 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 21:20:11 - root - INFO - 主窗口创建完成
2025-07-07 21:20:12 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.5 启动成功 ===
2025-07-07 21:20:12 - root - INFO - 数据库路径: data/database.db
2025-07-07 21:20:12 - root - INFO - 应用程序正在运行...
2025-07-07 21:22:21 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 21:22:21 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 21:22:21 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 21:22:23 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 21:22:35 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 21:22:42 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 21:22:42 - scrapers.data_mapper - INFO - 数据映射完成: 未知产品
2025-07-07 21:23:46 - scrapers.data_mapper - INFO - 数据映射完成: 未知产品
2025-07-07 21:23:46 - root - INFO - 智能抓取数据已应用到货源表单: 未知产品
2025-07-07 21:24:24 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 21:34:09 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 21:55:44 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 21:55:44 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 21:55:44 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 21:55:45 - root - INFO - 应用程序初始化完成
2025-07-07 21:55:45 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 21:55:45 - root - INFO - 主窗口创建完成
2025-07-07 21:55:45 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 21:55:45 - root - INFO - 数据库路径: data/database.db
2025-07-07 21:55:45 - root - INFO - 应用程序正在运行...
2025-07-07 21:56:06 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 21:56:06 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 21:56:06 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 21:56:08 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 21:56:17 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 21:56:22 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 21:56:22 - scrapers.data_mapper - INFO - 数据映射完成: 未知产品
2025-07-07 21:56:51 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 21:56:51 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 21:56:51 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 21:56:53 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 21:57:01 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 21:57:01 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 21:57:01 - scrapers.data_mapper - INFO - 数据映射完成: 未知产品
2025-07-07 21:57:08 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 21:57:08 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 21:57:08 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 21:57:09 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 21:57:19 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 21:57:19 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 21:57:19 - scrapers.data_mapper - INFO - 数据映射完成: 未知产品
2025-07-07 21:57:29 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 22:03:57 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 22:03:57 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 22:03:57 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 22:03:59 - root - INFO - 应用程序初始化完成
2025-07-07 22:03:59 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 22:04:00 - root - INFO - 主窗口创建完成
2025-07-07 22:04:01 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 22:04:01 - root - INFO - 数据库路径: data/database.db
2025-07-07 22:04:01 - root - INFO - 应用程序正在运行...
2025-07-07 22:04:08 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 22:04:08 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 22:04:08 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 22:04:10 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:04:19 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:04:20 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 22:04:20 - scrapers.data_mapper - INFO - 数据映射完成: 未知产品
2025-07-07 22:04:39 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 22:07:36 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 22:07:36 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 22:07:36 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 22:07:37 - root - INFO - 应用程序初始化完成
2025-07-07 22:07:37 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 22:07:37 - root - INFO - 主窗口创建完成
2025-07-07 22:07:38 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 22:07:38 - root - INFO - 数据库路径: data/database.db
2025-07-07 22:07:38 - root - INFO - 应用程序正在运行...
2025-07-07 22:07:50 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 22:07:50 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 22:07:50 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 22:07:52 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:08:00 - root - INFO - 成功提取内容: 标题=跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳...
2025-07-07 22:08:01 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:08:07 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:08:07 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:08:07 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:08:07 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 22:08:07 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球
2025-07-07 22:08:09 - root - WARNING - 加载图片失败: https://img.alicdn.com/imgextra/i2/O1CN015BJjTf1IneJFWRMoL_!!6000000000938-2-tps-46-40.png - HTTPSConnectionPool(host='img.alicdn.com', port=443): Max retries exceeded with url: /imgextra/i2/O1CN015BJjTf1IneJFWRMoL_!!6000000000938-2-tps-46-40.png (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1020)')))
2025-07-07 22:09:12 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球
2025-07-07 22:09:12 - root - INFO - 智能抓取数据已应用到货源表单: 跨境耐咬自动逗猫玩具球
2025-07-07 22:09:35 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 22:19:02 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 22:19:02 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 22:19:02 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 22:19:03 - root - INFO - 应用程序初始化完成
2025-07-07 22:19:03 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 22:19:04 - root - INFO - 主窗口创建完成
2025-07-07 22:19:04 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 22:19:04 - root - INFO - 数据库路径: data/database.db
2025-07-07 22:19:04 - root - INFO - 应用程序正在运行...
2025-07-07 22:19:35 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 22:19:35 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 22:19:35 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 22:19:36 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:19:45 - root - INFO - 成功提取内容: 标题=验证码拦截...
2025-07-07 22:19:46 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:19:54 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:19:54 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:19:54 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 22:19:54 - scrapers.data_mapper - INFO - 数据映射完成: 验证码拦截
2025-07-07 22:20:28 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 22:20:28 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 22:20:28 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 22:20:30 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:20:38 - root - INFO - 成功提取内容: 标题=验证码拦截...
2025-07-07 22:20:38 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:20:41 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:20:41 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:20:41 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 22:20:41 - scrapers.data_mapper - INFO - 数据映射完成: 验证码拦截
2025-07-07 22:20:42 - root - WARNING - 加载图片失败: https://img.alicdn.com/tfs/TB17G2dJGmWBuNjy1XaXXXCbXXa-241-41.png - HTTPSConnectionPool(host='img.alicdn.com', port=443): Max retries exceeded with url: /tfs/TB17G2dJGmWBuNjy1XaXXXCbXXa-241-41.png (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1020)')))
2025-07-07 22:20:43 - root - WARNING - 加载图片失败: https://img.alicdn.com/imgextra/i1/O1CN01rAc7zy1I9ns9kkTth_!!6000000000851-2-tps-134-134.png - HTTPSConnectionPool(host='img.alicdn.com', port=443): Max retries exceeded with url: /imgextra/i1/O1CN01rAc7zy1I9ns9kkTth_!!6000000000851-2-tps-134-134.png (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1020)')))
2025-07-07 22:20:52 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 22:23:35 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 22:23:35 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 22:23:35 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 22:23:35 - root - INFO - 应用程序初始化完成
2025-07-07 22:23:35 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 22:23:36 - root - INFO - 主窗口创建完成
2025-07-07 22:23:36 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 22:23:36 - root - INFO - 数据库路径: data/database.db
2025-07-07 22:23:36 - root - INFO - 应用程序正在运行...
2025-07-07 22:23:50 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 22:23:50 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 22:23:50 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 22:23:53 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:23:53 - root - INFO - 尝试访问策略 1: {'wait_until': 'domcontentloaded', 'timeout': 30000}
2025-07-07 22:23:54 - root - INFO - 访问策略 1 成功
2025-07-07 22:24:04 - root - INFO - 成功提取内容: 标题=验证码拦截...
2025-07-07 22:24:04 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 22:24:07 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:24:07 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-07 22:24:07 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-07 22:24:07 - scrapers.data_mapper - INFO - 数据映射完成: 验证码拦截
2025-07-07 22:24:08 - root - WARNING - 加载图片失败: https://img.alicdn.com/imgextra/i2/O1CN010VLpQY1VWKHBQuBUQ_!!6000000002660-2-tps-222-222.png - HTTPSConnectionPool(host='img.alicdn.com', port=443): Max retries exceeded with url: /imgextra/i2/O1CN010VLpQY1VWKHBQuBUQ_!!6000000002660-2-tps-222-222.png (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1020)')))
2025-07-07 22:24:19 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 22:27:53 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 22:27:53 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 22:27:53 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 22:27:54 - root - INFO - 应用程序初始化完成
2025-07-07 22:27:54 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 22:27:55 - root - INFO - 主窗口创建完成
2025-07-07 22:27:55 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 22:27:55 - root - INFO - 数据库路径: data/database.db
2025-07-07 22:27:55 - root - INFO - 应用程序正在运行...
2025-07-07 23:05:45 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 23:05:45 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 23:05:45 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 23:05:47 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 23:05:47 - root - INFO - 尝试访问策略 1: {'wait_until': 'domcontentloaded', 'timeout': 30000}
2025-07-07 23:05:49 - root - INFO - 访问策略 1 成功
2025-07-07 23:05:51 - root - WARNING - 检测到验证码或反爬页面，尝试处理...
2025-07-07 23:06:24 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 23:16:55 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 23:16:55 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 23:16:55 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 23:16:57 - root - INFO - 应用程序初始化完成
2025-07-07 23:16:57 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 23:16:58 - root - INFO - 主窗口创建完成
2025-07-07 23:16:59 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 23:16:59 - root - INFO - 数据库路径: data/database.db
2025-07-07 23:16:59 - root - INFO - 应用程序正在运行...
2025-07-07 23:42:16 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 23:42:16 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 23:42:16 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 23:42:18 - root - INFO - 应用程序初始化完成
2025-07-07 23:42:18 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 23:42:19 - root - INFO - 主窗口创建完成
2025-07-07 23:42:19 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 23:42:19 - root - INFO - 数据库路径: data/database.db
2025-07-07 23:42:19 - root - INFO - 应用程序正在运行...
2025-07-07 23:42:20 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 23:42:39 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-07 23:42:39 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-07 23:42:39 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-07 23:42:41 - root - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-07 23:42:41 - root - INFO - 尝试访问策略 1: {'wait_until': 'domcontentloaded', 'timeout': 30000}
2025-07-07 23:42:44 - root - WARNING - 访问策略 1 失败: Page.goto: net::ERR_CONNECTION_CLOSED at https://detail.1688.com/offer/840138940155.html?spm=a261y.7
2025-07-07 23:42:44 - root - INFO - 尝试访问策略 2: {'wait_until': 'load', 'timeout': 20000}
2025-07-07 23:42:44 - root - WARNING - 访问策略 2 失败: Page.goto: net::ERR_CONNECTION_CLOSED at https://detail.1688.com/offer/840138940155.html?spm=a261y.7
2025-07-07 23:42:44 - root - INFO - 尝试访问策略 3: {'wait_until': 'networkidle', 'timeout': 15000}
2025-07-07 23:42:48 - root - INFO - 访问策略 3 成功
2025-07-07 23:42:50 - root - WARNING - 检测到验证码或反爬页面，尝试处理...
2025-07-07 23:43:28 - root - INFO - 应用程序退出，返回值: 0
2025-07-07 23:56:41 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-07 23:56:41 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-07 23:56:41 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-07 23:56:42 - root - INFO - 应用程序初始化完成
2025-07-07 23:56:42 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-07 23:56:46 - root - INFO - 主窗口创建完成
2025-07-07 23:56:46 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-07 23:56:46 - root - INFO - 数据库路径: data/database.db
2025-07-07 23:56:46 - root - INFO - 应用程序正在运行...
2025-07-08 00:10:25 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 00:10:25 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 00:10:25 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-08 00:10:26 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:10:26 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:10:29 - web_scraper - INFO - 尝试访问策略 1: {'wait_until': 'domcontentloaded', 'timeout': 30000}
2025-07-08 00:10:32 - web_scraper - INFO - 访问策略 1 成功
2025-07-08 00:11:04 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 00:11:04 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 00:11:04 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-08 00:11:05 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:11:05 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:11:08 - web_scraper - INFO - 尝试访问策略 1: {'wait_until': 'domcontentloaded', 'timeout': 30000}
2025-07-08 00:11:08 - web_scraper - INFO - 访问策略 1 成功
2025-07-08 00:11:37 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 00:20:59 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 00:20:59 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 00:20:59 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 00:21:00 - root - INFO - 应用程序初始化完成
2025-07-08 00:21:00 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 00:21:02 - root - INFO - 主窗口创建完成
2025-07-08 00:21:03 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 00:21:03 - root - INFO - 数据库路径: data/database.db
2025-07-08 00:21:03 - root - INFO - 应用程序正在运行...
2025-07-08 00:21:14 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 00:21:14 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 00:21:14 - scrapers.ai_analyzer - INFO - 视觉模型 qwen2.5vl:latest 可用: True
2025-07-08 00:21:15 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:21:15 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:21:18 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 00:21:18 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 00:21:23 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 00:21:23 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 00:21:25 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 00:21:29 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 00:21:29 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 00:22:02 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 00:22:03 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:22:14 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 00:22:14 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 00:22:14 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 00:22:14 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-08 00:22:15 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 00:22:17 - root - WARNING - 加载图片失败: https://img.alicdn.com/imgextra/i3/O1CN01tOiNgL1rd7ymUJ9N1_!!6000000005653-2-tps-40-23.png - HTTPSConnectionPool(host='img.alicdn.com', port=443): Max retries exceeded with url: /imgextra/i3/O1CN01tOiNgL1rd7ymUJ9N1_!!6000000005653-2-tps-40-23.png (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1020)')))
2025-07-08 00:25:01 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 00:51:36 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 00:51:36 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 00:51:36 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 00:51:38 - root - INFO - 应用程序初始化完成
2025-07-08 00:51:38 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 00:51:39 - root - INFO - 主窗口创建完成
2025-07-08 00:51:39 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 00:51:39 - root - INFO - 数据库路径: data/database.db
2025-07-08 00:51:39 - root - INFO - 应用程序正在运行...
2025-07-08 00:51:56 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 00:51:56 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 00:51:56 - scrapers.ai_analyzer - INFO - 视觉模型 llava:latest 可用: True
2025-07-08 00:51:58 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:51:58 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:52:01 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 00:52:01 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 00:52:04 - auth_bypass - ERROR - 自动绕过验证失败: Page.goto: net::ERR_CONNECTION_CLOSED at https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
Call log:
  - navigating to "https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155", waiting until "domcontentloaded"

2025-07-08 00:52:04 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 00:52:13 - root - ERROR - 全内容提取失败: Page.evaluate: Target page, context or browser has been closed
2025-07-08 00:52:13 - root - ERROR - 内容提取失败: Page.content: Target page, context or browser has been closed
2025-07-08 00:52:14 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:52:17 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 00:52:17 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 00:52:17 - scrapers.ai_analyzer - INFO - 视觉模型 llava:latest 可用: True
2025-07-08 00:52:19 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:52:19 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:52:21 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 00:52:21 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 00:52:25 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 00:52:25 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 00:52:27 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 00:52:31 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 00:52:31 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 00:53:02 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 00:53:03 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 00:53:06 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 00:53:06 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 00:53:06 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 00:53:06 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-08 00:53:06 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 00:53:20 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 00:53:20 - root - INFO - 智能抓取数据已应用到货源表单: 中国大陆
2025-07-08 00:53:58 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 01:09:41 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 01:09:41 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 01:09:41 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 01:09:42 - root - INFO - 应用程序初始化完成
2025-07-08 01:09:42 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 01:09:42 - root - INFO - 主窗口创建完成
2025-07-08 01:09:43 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 01:09:43 - root - INFO - 数据库路径: data/database.db
2025-07-08 01:09:43 - root - INFO - 应用程序正在运行...
2025-07-08 01:10:01 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 01:10:01 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 01:10:01 - scrapers.ai_analyzer - INFO - 视觉模型 llava:latest 可用: True
2025-07-08 01:10:03 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 01:10:03 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 01:10:06 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 01:10:06 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 01:10:09 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 01:10:09 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 01:10:11 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 01:10:16 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 01:10:16 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 01:10:48 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 01:10:49 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 01:10:58 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 01:10:58 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 01:10:58 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 01:10:58 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-08 01:10:58 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 01:11:47 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 01:11:47 - root - INFO - 智能抓取数据已应用到货源表单: 中国大陆
2025-07-08 01:15:43 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 01:28:47 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 01:28:47 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 01:28:47 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 01:28:48 - root - INFO - 应用程序初始化完成
2025-07-08 01:28:48 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 01:28:48 - root - INFO - 主窗口创建完成
2025-07-08 01:28:48 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 01:28:48 - root - INFO - 数据库路径: data/database.db
2025-07-08 01:28:48 - root - INFO - 应用程序正在运行...
2025-07-08 01:29:23 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 01:29:23 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 01:29:23 - scrapers.ai_analyzer - INFO - 视觉模型 llava:latest 可用: True
2025-07-08 01:29:25 - web_scraper - INFO - 开始抓取URL: https://detail.tmall.com/item.htm?_u=61nrh0664ba&id=************&spm=a1z09.2.0.0.4c262e8dalHJEu&skuId=5575414220498
2025-07-08 01:29:25 - root - INFO - 尝试访问策略 1: {'wait_until': 'domcontentloaded', 'timeout': 30000}
2025-07-08 01:29:26 - root - INFO - 访问策略 1 成功
2025-07-08 01:29:28 - root - WARNING - 检测到验证码或反爬页面，尝试处理...
2025-07-08 01:30:12 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 10:14:19 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 10:14:19 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 10:14:19 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 10:14:20 - root - INFO - 应用程序初始化完成
2025-07-08 10:14:20 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 10:14:31 - root - INFO - 主窗口创建完成
2025-07-08 10:14:33 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 10:14:33 - root - INFO - 数据库路径: data/database.db
2025-07-08 10:14:33 - root - INFO - 应用程序正在运行...
2025-07-08 10:15:55 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 10:15:55 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 10:15:55 - scrapers.ai_analyzer - INFO - 视觉模型 llava:latest 可用: True
2025-07-08 10:16:02 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/859017716848.html?spm=a261y.7663282.sceneKey.1.2e8c2739riK2sQ&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367a17519409107806867e2e30&spm-url=a2639h.29135425.offerlist.i1&spm-auction=859017716848
2025-07-08 10:16:02 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/859017716848.html?spm=a261y.7663282.sceneKey.1.2e8c2739riK2sQ&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367a17519409107806867e2e30&spm-url=a2639h.29135425.offerlist.i1&spm-auction=859017716848
2025-07-08 10:16:06 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 10:16:06 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 10:16:09 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 10:16:09 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 10:16:12 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 10:16:16 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:16:19 - auth_bypass - INFO - 🔄 执行第2次自动刷新，绕过验证页面...
2025-07-08 10:16:25 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:16:29 - auth_bypass - INFO - 🔄 执行第3次自动刷新，绕过验证页面...
2025-07-08 10:16:34 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:16:37 - auth_bypass - INFO - 🔄 执行第4次自动刷新，绕过验证页面...
2025-07-08 10:16:44 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:16:47 - auth_bypass - INFO - 🔄 执行第5次自动刷新，绕过验证页面...
2025-07-08 10:16:54 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:16:57 - auth_bypass - INFO - 🔄 尝试重新访问页面...
2025-07-08 10:17:03 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:03 - auth_bypass - WARNING - ⚠️  自动绕过失败，等待手动处理验证...
2025-07-08 10:17:03 - auth_bypass - INFO - 检测到可能的验证码，等待人工处理...
2025-07-08 10:17:03 - auth_bypass - INFO - 请在浏览器中手动完成验证，最多等待300秒
2025-07-08 10:17:03 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:05 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:07 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:09 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:11 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:13 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:16 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:18 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:20 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:22 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:24 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:27 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:29 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:31 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:34 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:36 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:38 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:40 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:42 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:44 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:46 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:48 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:56 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:17:58 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:00 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:02 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:04 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:06 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:08 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:10 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:12 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:14 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:18 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:21 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:23 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:25 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:27 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:29 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:31 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:33 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:35 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:38 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:40 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:42 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:44 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:46 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:48 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:51 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:54 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:56 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:18:58 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:00 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:02 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:04 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:08 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:11 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:13 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:15 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:17 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:19 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:21 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:23 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:25 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:27 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:30 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:33 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:35 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:37 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:39 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:41 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:43 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:45 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:47 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:50 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:53 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:57 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:19:59 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:01 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:03 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:05 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:07 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:09 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:11 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:14 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:17 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:19 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:21 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:23 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:25 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:28 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:30 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:32 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:34 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:36 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:38 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:40 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:43 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:46 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:48 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:50 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:52 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:55 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:57 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:20:59 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:01 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:03 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:05 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:08 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:12 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:16 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:19 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:21 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:24 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:26 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:29 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:31 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:33 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:35 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:37 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:39 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:41 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:43 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:45 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:47 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:49 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:51 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:53 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:55 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:21:58 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:22:00 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:22:02 - auth_bypass - INFO - 检测到验证元素: [class*='slide']
2025-07-08 10:22:04 - auth_bypass - WARNING - 等待验证超时
2025-07-08 10:22:04 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 10:22:37 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 10:22:41 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/859017716848.html?spm=a261y.7663282.sceneKey.1.2e8c2739riK2sQ&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367a17519409107806867e2e30&spm-url=a2639h.29135425.offerlist.i1&spm-auction=859017716848
2025-07-08 10:23:13 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 10:23:13 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 10:23:13 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 10:23:13 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-08 10:23:13 - scrapers.data_mapper - INFO - 数据映射完成: 宠伴一生专供
2025-07-08 10:23:14 - root - ERROR - 更新AI分析结果显示失败: '>=' not supported between instances of 'str' and 'int'
2025-07-08 10:33:20 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 10:49:39 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 10:49:39 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 10:49:39 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 10:49:40 - root - INFO - 应用程序初始化完成
2025-07-08 10:49:40 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 10:49:41 - root - INFO - 主窗口创建完成
2025-07-08 10:49:41 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 10:49:41 - root - INFO - 数据库路径: data/database.db
2025-07-08 10:49:41 - root - INFO - 应用程序正在运行...
2025-07-08 10:52:30 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:52:30 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:52:30 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:52:30 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:52:34 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:52:34 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:52:34 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:52:34 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 10:52:39 - scrapers.ai_analyzer - INFO - 视觉模型 llava:latest 可用: True
2025-07-08 10:52:41 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 10:52:41 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 10:52:43 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 10:52:43 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 10:52:44 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:52:44 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:52:44 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:52:44 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:52:46 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 10:52:46 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 10:52:48 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 10:52:49 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:52:49 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:52:49 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:52:49 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:52:53 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 10:52:53 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 10:52:54 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:52:54 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:52:54 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:52:54 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:52:59 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:52:59 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:52:59 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:52:59 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:04 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:04 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:04 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:04 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:09 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:09 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:09 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:09 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:14 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:14 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:14 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:14 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:19 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:19 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:19 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:19 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:24 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:24 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:24 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:24 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:24 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 10:53:25 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 10:53:29 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:29 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:29 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:29 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:33 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 10:53:33 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 10:53:33 - scrapers.ai_analyzer - ERROR - Ollama视觉请求失败: 400
2025-07-08 10:53:33 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-08 10:53:33 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 10:53:34 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:34 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:34 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:34 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:39 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:39 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:39 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:39 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:44 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:44 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:44 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:44 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:49 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:58 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:58 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:58 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:53:59 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:53:59 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:53:59 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:53:59 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:04 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:04 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:04 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:04 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:09 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:09 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:09 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:09 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:14 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:14 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:14 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:14 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:19 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:19 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:19 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:19 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:24 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:24 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:24 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:24 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:29 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:29 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:29 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:29 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:34 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:34 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:34 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:34 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:39 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:39 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:39 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:39 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:44 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:44 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:44 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:44 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:49 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:49 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:49 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:49 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:54 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:54 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:54 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:54 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:54:59 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:54:59 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:54:59 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:54:59 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:55:04 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:55:04 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:55:04 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:55:04 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:55:09 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:55:09 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:55:09 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:55:09 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:55:14 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:55:14 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:55:14 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:55:14 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:55:19 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:55:19 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:55:19 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:55:19 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:55:24 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:55:24 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:55:24 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:55:24 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:55:29 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:55:29 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:55:29 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:55:29 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:55:34 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 10:55:34 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 10:55:34 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 10:55:34 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 10:55:37 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 11:38:59 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 11:38:59 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 11:38:59 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 11:39:00 - root - INFO - 应用程序初始化完成
2025-07-08 11:39:00 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 11:39:07 - root - INFO - 主窗口创建完成
2025-07-08 11:39:07 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 11:39:07 - root - INFO - 数据库路径: data/database.db
2025-07-08 11:39:07 - root - INFO - 应用程序正在运行...
2025-07-08 11:39:38 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:39:38 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:39:38 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:39:38 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:39:42 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:39:42 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:39:42 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:39:42 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:39:47 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:39:47 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:39:47 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:39:47 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:39:52 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:39:52 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:39:52 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:39:52 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:39:57 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:39:57 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:39:57 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:39:57 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:40:02 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:40:02 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:40:02 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:40:02 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:40:07 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:40:07 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:40:07 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:40:07 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:40:12 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:40:12 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:40:12 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:40:12 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:40:14 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:40:14 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:40:14 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:40:14 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:40:14 - scrapers.ai_analyzer - ERROR - Ollama连接测试失败: 'vision_model'
2025-07-08 11:40:17 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:40:17 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:40:17 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:40:17 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:40:22 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:40:22 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 11:40:22 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 11:40:22 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:40:26 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 11:52:55 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 11:52:56 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 11:52:56 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 11:52:56 - root - INFO - 应用程序初始化完成
2025-07-08 11:52:56 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 11:52:57 - root - INFO - 主窗口创建完成
2025-07-08 11:52:57 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 11:52:57 - root - INFO - 数据库路径: data/database.db
2025-07-08 11:52:57 - root - INFO - 应用程序正在运行...
2025-07-08 11:53:02 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:02 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:02 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:02 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:06 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:06 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:06 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:06 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:09 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:09 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:09 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:09 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:09 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 11:53:09 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 11:53:09 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 11:53:11 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:11 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:11 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:11 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:15 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 11:53:15 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 11:53:16 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:16 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:16 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:16 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:17 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 11:53:17 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 11:53:21 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 11:53:21 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 11:53:21 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:21 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:21 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:21 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:23 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 11:53:26 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:26 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:26 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:26 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:27 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 11:53:27 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 11:53:31 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:31 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:32 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:33 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:36 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:36 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:36 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:36 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:41 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:41 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:41 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:41 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:46 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:46 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:46 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:46 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:51 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:51 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:51 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:51 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:53:56 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:53:56 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:53:56 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:53:56 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:00 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 11:54:01 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 11:54:01 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:01 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:01 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:01 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:06 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:06 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:06 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:06 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:11 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:11 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:11 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:11 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:16 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:16 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:16 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:16 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:16 - scrapers.ai_analyzer - INFO - 跳过图片分析（配置禁用），发现 10 张图片
2025-07-08 11:54:16 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-08 11:54:16 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 11:54:21 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:21 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:21 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:21 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:26 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:26 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:26 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:26 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:31 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:31 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:31 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:31 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:36 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:36 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:36 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:36 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:41 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:41 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:41 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:41 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:46 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:46 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:46 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:46 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:51 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:51 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:51 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:51 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:54:56 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:54:56 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:54:56 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:54:56 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:55:01 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:55:01 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:55:01 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:55:01 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:55:06 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:55:06 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:55:06 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:55:06 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:55:11 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:55:11 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:55:11 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:55:11 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:55:16 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:55:16 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:55:16 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:55:16 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:55:21 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:55:21 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:55:21 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:55:21 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:55:26 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:55:26 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:55:26 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:55:26 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 11:55:31 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 11:55:31 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 11:55:31 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 11:55:31 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 12:39:00 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 12:39:00 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 12:39:00 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 12:39:02 - root - INFO - 应用程序初始化完成
2025-07-08 12:39:02 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 12:39:03 - root - INFO - 主窗口创建完成
2025-07-08 12:39:04 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 12:39:04 - root - INFO - 数据库路径: data/database.db
2025-07-08 12:39:04 - root - INFO - 应用程序正在运行...
2025-07-08 12:39:48 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 12:53:04 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 12:53:04 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 12:53:04 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 12:53:06 - root - INFO - 应用程序初始化完成
2025-07-08 12:53:06 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 12:53:06 - root - INFO - 主窗口创建完成
2025-07-08 12:53:06 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 12:53:06 - root - INFO - 数据库路径: data/database.db
2025-07-08 12:53:06 - root - INFO - 应用程序正在运行...
2025-07-08 12:53:20 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 12:53:20 - scrapers.ai_analyzer - INFO - 最大tokens: 120000
2025-07-08 12:53:20 - scrapers.ai_analyzer - INFO - 分块大小: 30000
2025-07-08 12:53:20 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 12:53:20 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 12:53:20 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 12:53:20 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 12:53:22 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 12:53:22 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 12:53:24 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 12:53:24 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 12:53:27 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 12:53:27 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 12:53:29 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 12:53:34 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 12:53:34 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 12:54:06 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 12:54:07 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 12:54:15 - scrapers.ai_analyzer - INFO - 跳过图片分析（配置禁用），发现 10 张图片
2025-07-08 12:54:15 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-08 12:54:15 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 12:54:15 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 12:55:16 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 13:03:29 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 13:03:29 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 13:03:29 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 13:03:31 - root - INFO - 应用程序初始化完成
2025-07-08 13:03:31 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 13:03:32 - root - INFO - 主窗口创建完成
2025-07-08 13:03:32 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 13:03:32 - root - INFO - 数据库路径: data/database.db
2025-07-08 13:03:32 - root - INFO - 应用程序正在运行...
2025-07-08 13:04:00 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 13:04:00 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 13:04:00 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 13:04:00 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 13:04:00 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 13:04:00 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 13:04:00 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 13:04:02 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:04:02 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:04:04 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 13:04:04 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 13:04:08 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 13:04:08 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 13:04:10 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 13:04:14 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 13:04:14 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 13:04:45 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 13:04:46 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:04:54 - scrapers.ai_analyzer - INFO - 启用图片分析
2025-07-08 13:04:54 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 13:04:54 - scrapers.ai_analyzer - INFO - AI分析完成
2025-07-08 13:04:54 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 13:04:55 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:10:46 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 13:30:42 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 13:30:42 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 13:30:42 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 13:30:43 - root - INFO - 应用程序初始化完成
2025-07-08 13:30:43 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 13:30:45 - root - INFO - 主窗口创建完成
2025-07-08 13:30:45 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 13:30:45 - root - INFO - 数据库路径: data/database.db
2025-07-08 13:30:45 - root - INFO - 应用程序正在运行...
2025-07-08 13:30:57 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 13:30:57 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 13:30:57 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 13:30:57 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 13:30:57 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用
2025-07-08 13:30:57 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 13:30:57 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 13:30:57 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 13:30:58 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:30:58 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:31:00 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 13:31:00 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 13:31:03 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 13:31:03 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 13:31:06 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 13:31:09 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 13:31:09 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 13:31:40 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 13:31:41 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:31:41 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 13:31:41 - scrapers.content_cleaner - INFO - 开始清洗内容，原始长度: 84
2025-07-08 13:31:41 - scrapers.content_cleaner - INFO - 内容清洗完成，清洗后长度: 84
2025-07-08 13:31:41 - scrapers.content_cleaner - INFO - 内容压缩率: 100.0%
2025-07-08 13:31:41 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 84
2025-07-08 13:31:41 - scrapers.ai_analyzer - INFO - 清洗后长度: 84
2025-07-08 13:31:41 - scrapers.ai_analyzer - INFO - 压缩率: 100.0%
2025-07-08 13:31:41 - scrapers.ai_analyzer - INFO - 中文字符: 34, 英文单词: 4, 数字: 6
2025-07-08 13:31:41 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 13:31:48 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 13:31:48 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 13:31:48 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 13:31:48 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 13:31:48 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 13:31:48 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 13:31:48 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:33:16 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 13:41:09 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 13:41:09 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 13:41:09 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 13:41:10 - root - INFO - 应用程序初始化完成
2025-07-08 13:41:10 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 13:41:11 - root - INFO - 主窗口创建完成
2025-07-08 13:41:12 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 13:41:12 - root - INFO - 数据库路径: data/database.db
2025-07-08 13:41:12 - root - INFO - 应用程序正在运行...
2025-07-08 13:41:21 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 13:41:21 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 13:41:21 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 13:41:21 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 13:41:21 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用
2025-07-08 13:41:21 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 13:41:21 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 13:41:21 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 13:41:23 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:41:23 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:41:25 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 13:41:25 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 13:41:29 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 13:41:29 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 13:41:31 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 13:41:35 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 13:41:35 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 13:42:05 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 13:42:07 - root - ERROR - 抓取过程异常: string indices must be integers, not 'str'
2025-07-08 13:51:24 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 13:51:24 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 13:51:24 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 13:51:25 - root - INFO - 应用程序初始化完成
2025-07-08 13:51:25 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 13:51:26 - root - INFO - 主窗口创建完成
2025-07-08 13:51:27 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 13:51:27 - root - INFO - 数据库路径: data/database.db
2025-07-08 13:51:27 - root - INFO - 应用程序正在运行...
2025-07-08 13:51:35 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 13:51:35 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 13:51:35 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 13:51:35 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 13:51:35 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用
2025-07-08 13:51:35 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 13:51:35 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 13:51:35 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 13:51:37 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:51:37 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:51:40 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 13:51:40 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 13:51:43 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 13:51:43 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 13:51:45 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 13:51:49 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 13:51:49 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 13:52:22 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 13:52:23 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 13:52:23 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 13:52:23 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 13:52:23 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 84
2025-07-08 13:52:23 - scrapers.ai_analyzer - INFO - 清洗后长度: 84
2025-07-08 13:52:23 - scrapers.ai_analyzer - INFO - 压缩率: 100.0%
2025-07-08 13:52:23 - scrapers.ai_analyzer - INFO - 中文字符: 34, 英文单词: 4, 数字: 6
2025-07-08 13:52:23 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 13:52:26 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 13:52:26 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 13:52:26 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 13:52:26 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 13:52:26 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 13:52:26 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 13:52:27 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 14:51:30 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 15:49:03 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 15:49:03 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 15:49:03 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 15:49:05 - root - INFO - 应用程序初始化完成
2025-07-08 15:49:05 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 15:49:06 - root - INFO - 主窗口创建完成
2025-07-08 15:49:07 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 15:49:07 - root - INFO - 数据库路径: data/database.db
2025-07-08 15:49:07 - root - INFO - 应用程序正在运行...
2025-07-08 15:49:15 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 15:49:15 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 15:49:15 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 15:49:15 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 15:49:15 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用增强清理器（90%+垃圾内容清理）
2025-07-08 15:49:16 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 15:49:16 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 15:49:16 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 15:49:17 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 15:49:17 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 15:49:20 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 15:49:20 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 15:49:23 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 15:49:23 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 15:49:25 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 15:49:29 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 15:49:29 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 15:49:52 - web_scraper - WARNING - 检测到遗漏的验证码，等待人工处理...
2025-07-08 15:49:52 - auth_bypass - INFO - 检测到可能的验证码，等待人工处理...
2025-07-08 15:49:52 - auth_bypass - INFO - 请在浏览器中手动完成验证，最多等待300秒
2025-07-08 15:49:52 - auth_bypass - INFO - 验证完成，页面可正常访问
2025-07-08 15:50:00 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 15:50:02 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 15:50:02 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 15:50:02 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 15:50:02 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 84
2025-07-08 15:50:02 - scrapers.ai_analyzer - INFO - 清洗后长度: 21
2025-07-08 15:50:02 - scrapers.ai_analyzer - INFO - 压缩率: 75.0%
2025-07-08 15:50:02 - scrapers.ai_analyzer - INFO - 中文字符: 20, 英文单词: 0, 数字: 0
2025-07-08 15:50:02 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 15:50:13 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 15:51:58 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 15:51:58 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 15:51:58 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 15:51:58 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 15:51:58 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆 温州辛宠宠物用品有限公司
2025-07-08 15:51:59 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:15:18 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 16:15:18 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 16:15:18 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 16:15:19 - root - INFO - 应用程序初始化完成
2025-07-08 16:15:20 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 16:15:21 - root - INFO - 主窗口创建完成
2025-07-08 16:15:21 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 16:15:21 - root - INFO - 数据库路径: data/database.db
2025-07-08 16:15:21 - root - INFO - 应用程序正在运行...
2025-07-08 16:15:36 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 16:15:36 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 16:15:36 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 16:15:36 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 16:15:36 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用增强清理器（90%+垃圾内容清理）
2025-07-08 16:15:36 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 16:15:36 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 16:15:36 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 16:15:38 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:15:38 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:15:41 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 16:15:41 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 16:15:44 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 16:15:44 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 16:15:46 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 16:15:50 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 16:15:50 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 16:16:13 - web_scraper - WARNING - 检测到遗漏的验证码，等待人工处理...
2025-07-08 16:16:13 - auth_bypass - INFO - 检测到可能的验证码，等待人工处理...
2025-07-08 16:16:13 - auth_bypass - INFO - 请在浏览器中手动完成验证，最多等待300秒
2025-07-08 16:16:13 - auth_bypass - INFO - 验证完成，页面可正常访问
2025-07-08 16:16:22 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 16:16:23 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:16:23 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 16:16:23 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 16:16:23 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 96942
2025-07-08 16:16:23 - scrapers.ai_analyzer - INFO - 清洗后长度: 401
2025-07-08 16:16:23 - scrapers.ai_analyzer - INFO - 压缩率: 99.6%
2025-07-08 16:16:23 - scrapers.ai_analyzer - INFO - 中文字符: 372, 英文单词: 0, 数字: 0
2025-07-08 16:16:23 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 16:16:31 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 16:16:31 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 16:16:31 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 16:16:31 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 16:16:31 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 16:16:31 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球猫咪玩具
2025-07-08 16:16:32 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:22:39 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 16:22:39 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 16:22:39 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 16:22:41 - root - INFO - 应用程序初始化完成
2025-07-08 16:22:41 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 16:22:42 - root - INFO - 主窗口创建完成
2025-07-08 16:22:43 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 16:22:43 - root - INFO - 数据库路径: data/database.db
2025-07-08 16:22:43 - root - INFO - 应用程序正在运行...
2025-07-08 16:22:53 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 16:22:53 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 16:22:53 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 16:22:53 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 16:22:53 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用增强清理器（90%+垃圾内容清理）
2025-07-08 16:22:53 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 16:22:53 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 16:22:53 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 16:22:55 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:22:55 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:22:58 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 16:22:58 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 16:23:01 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 16:23:01 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 16:23:03 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 16:23:07 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 16:23:07 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 16:23:42 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 16:24:00 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:24:00 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 16:24:00 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 16:24:00 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 100378
2025-07-08 16:24:00 - scrapers.ai_analyzer - INFO - 清洗后长度: 616
2025-07-08 16:24:00 - scrapers.ai_analyzer - INFO - 压缩率: 99.4%
2025-07-08 16:24:00 - scrapers.ai_analyzer - INFO - 中文字符: 419, 英文单词: 16, 数字: 19
2025-07-08 16:24:00 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 16:24:10 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 16:24:10 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 16:24:10 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 16:24:10 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 16:24:10 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 16:24:10 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球猫咪玩具
2025-07-08 16:24:11 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 16:25:12 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 17:25:35 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 17:25:36 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 17:25:36 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 17:25:37 - root - INFO - 应用程序初始化完成
2025-07-08 17:25:37 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 17:25:38 - root - INFO - 主窗口创建完成
2025-07-08 17:25:39 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 17:25:39 - root - INFO - 数据库路径: data/database.db
2025-07-08 17:25:39 - root - INFO - 应用程序正在运行...
2025-07-08 17:25:57 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 17:25:57 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 17:25:57 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 17:25:57 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 17:25:57 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用增强清理器（90%+垃圾内容清理）
2025-07-08 17:25:57 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 17:25:57 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 17:25:57 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 17:25:59 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 17:25:59 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 17:26:01 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 17:26:01 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 17:26:05 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 17:26:05 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 17:26:07 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 17:26:11 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 17:26:11 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 17:26:42 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 616字符
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 代发参谋 24小时揽收率 82.00% 24小时揽收率</span><span style="font-size: 14px; line-height: color: rgb(51, 51, 51);...
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 100340
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 清洗后长度: 616
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 压缩率: 99.4%
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 中文字符: 419, 英文单词: 16, 数字: 19
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 616字符
2025-07-08 17:26:43 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 代发参谋 24小时揽收率 82.00% 24小时揽收率</span><span style="font-size: 14px; line-height: color: rgb(51, 51, 51);">82.00% 48小时揽收率 98.00% 48小时揽收率</span><span 51);">98.00% 月代发订单数 100以 月代发订单数</span><span 14 下游铺货数 下游铺...
2025-07-08 17:26:53 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 17:26:53 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 17:26:53 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 17:26:53 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 17:26:53 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 17:26:53 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球猫咪玩具
2025-07-08 17:26:54 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 17:28:26 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 17:55:56 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 17:55:56 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 17:55:56 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 17:55:58 - root - INFO - 应用程序初始化完成
2025-07-08 17:55:58 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 17:55:59 - root - INFO - 主窗口创建完成
2025-07-08 17:55:59 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 17:55:59 - root - INFO - 数据库路径: data/database.db
2025-07-08 17:55:59 - root - INFO - 应用程序正在运行...
2025-07-08 17:56:10 - scrapers.enhanced_data_cleaner - INFO - 🛡️ 初始化完成：57个核心保护模式 + 18个补充模式 = 75个总模式
2025-07-08 17:56:10 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 17:56:10 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 17:56:10 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 17:56:10 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 17:56:10 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用增强清理器（90%+垃圾内容清理）
2025-07-08 17:56:10 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 17:56:10 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 17:56:10 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 17:56:12 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 17:56:12 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 17:56:14 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 17:56:14 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 17:56:18 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 17:56:18 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 17:56:20 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 17:56:24 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 17:56:24 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 17:56:55 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - 🛡️ 初始化完成：57个核心保护模式 + 18个补充模式 = 75个总模式
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - 🛡️ 第一阶段：提取并保护重要业务指标...
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - 🔒 第一阶段完成：已保护 131 个重要业务指标
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - 🧹 第二阶段：开始HTML结构清理...
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - 🔓 第三阶段：恢复保护的业务信息...
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - 🔓 已恢复 131 个保护的业务指标
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - ✅ '48小时发货'信息已成功恢复到内容中
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - 🧼 第四阶段：最终文本清理...
2025-07-08 17:56:56 - scrapers.enhanced_data_cleaner - INFO - ✅ 最终结果包含'48小时发货'相关信息
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 1000字符
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 代发参谋 24小时揽收率 82.00% 24小时揽收率/spanspan style="font-size: 14px; line-height: color: rgb(51, 51, 51);"82...
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 100340
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 清洗后长度: 1000
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 压缩率: 99.0%
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 中文字符: 0, 英文单词: 0, 数字: 0
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 1000字符
2025-07-08 17:56:56 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 代发参谋 24小时揽收率 82.00% 24小时揽收率/spanspan style="font-size: 14px; line-height: color: rgb(51, 51, 51);"82.00% 48小时揽收率 98.00% 48小时揽收率/spanspan 51);"98.00% 月代发订单数 100以 月代发订单数/spanspan 14 下游铺货数 下游铺货数/spanspan...
2025-07-08 17:57:05 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 17:57:05 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 17:57:05 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 17:57:05 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 17:57:05 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 17:57:05 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆代发参谋
2025-07-08 17:57:06 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 18:13:24 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 19:20:08 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 19:20:08 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 19:20:08 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 19:20:10 - root - INFO - 应用程序初始化完成
2025-07-08 19:20:10 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 19:20:13 - root - INFO - 主窗口创建完成
2025-07-08 19:20:14 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 19:20:14 - root - INFO - 数据库路径: data/database.db
2025-07-08 19:20:14 - root - INFO - 应用程序正在运行...
2025-07-08 19:20:25 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 19:20:25 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 19:20:25 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 19:20:25 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 19:20:25 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用超级增强清理器（200+保护规则）
2025-07-08 19:20:25 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 19:20:25 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 19:20:25 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 19:20:27 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 19:20:27 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 19:20:29 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 19:20:29 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 19:20:32 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 19:20:32 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 19:20:34 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 19:20:39 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 19:20:39 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 19:21:15 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - 🛡️ 初始化完成：57个核心保护模式 + 18个补充模式 = 75个总模式
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - 🛡️ 第一阶段：提取并保护重要业务指标...
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - 🔒 第一阶段完成：已保护 131 个重要业务指标
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - 🧹 第二阶段：开始HTML结构清理...
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - 🔓 第三阶段：恢复保护的业务信息...
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - 🔓 已恢复 131 个保护的业务指标
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - ✅ '48小时发货'信息已成功恢复到内容中
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - 🧼 第四阶段：最终文本清理...
2025-07-08 19:21:16 - scrapers.enhanced_data_cleaner - INFO - ✅ 最终结果包含'48小时发货'相关信息
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 1000字符
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 代发参谋 24小时揽收率 82.00% 24小时揽收率/spanspan style="font-size: 14px; line-height: color: rgb(51, 51, 51);"82...
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 100375
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 清洗后长度: 1000
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 压缩率: 99.0%
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 中文字符: 0, 英文单词: 0, 数字: 0
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 1000字符
2025-07-08 19:21:16 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 代发参谋 24小时揽收率 82.00% 24小时揽收率/spanspan style="font-size: 14px; line-height: color: rgb(51, 51, 51);"82.00% 48小时揽收率 98.00% 48小时揽收率/spanspan 51);"98.00% 月代发订单数 100以 月代发订单数/spanspan 14 下游铺货数 下游铺货数/spanspan...
2025-07-08 19:21:26 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 19:21:26 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 19:21:26 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 19:21:26 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 19:21:26 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 19:21:26 - scrapers.data_mapper - INFO - 数据映射完成: 中国大陆
2025-07-08 19:21:27 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 19:29:58 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 19:44:26 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 19:44:26 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 19:44:26 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 19:44:26 - root - INFO - 应用程序初始化完成
2025-07-08 19:44:26 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 19:44:27 - root - INFO - 主窗口创建完成
2025-07-08 19:44:27 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 19:44:27 - root - INFO - 数据库路径: data/database.db
2025-07-08 19:44:27 - root - INFO - 应用程序正在运行...
2025-07-08 19:44:37 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 19:44:37 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 19:44:37 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 19:44:37 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 19:44:37 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用超级增强清理器（200+保护规则）
2025-07-08 19:44:37 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 19:44:37 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 19:44:37 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 19:44:39 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 19:44:39 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 19:44:41 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 19:44:41 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 19:44:44 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 19:44:44 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 19:44:46 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 19:44:50 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 19:44:50 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 19:45:22 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 11843字符
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫...
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 98520
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 清洗后长度: 11843
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 压缩率: 88.0%
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 中文字符: 0, 英文单词: 0, 数字: 0
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 11843字符
2025-07-08 19:45:24 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫一扫进入手机旺铺 TOP1 销量 TOP2 销量 本店 找货源 找工厂 搜索 首页 全部商品 所有产品 （15） 宠物梳子 （3） 宠物玩具 （13） 宠物清洁用品 （7） 猫玩具 （15） 未分类 ...
2025-07-08 19:45:34 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 19:45:34 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 19:45:34 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 19:45:34 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 19:45:34 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 19:45:34 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球
2025-07-08 19:45:35 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 19:49:55 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 20:24:54 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 20:24:54 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4-\data\logs\app.log
2025-07-08 20:24:54 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 20:24:55 - root - INFO - 应用程序初始化完成
2025-07-08 20:24:55 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 20:24:55 - root - INFO - 主窗口创建完成
2025-07-08 20:24:56 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 20:24:56 - root - INFO - 数据库路径: data/database.db
2025-07-08 20:24:56 - root - INFO - 应用程序正在运行...
2025-07-08 20:25:10 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-08 20:25:10 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-08 20:25:10 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-08 20:25:10 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-08 20:25:10 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用超级增强清理器（200+保护规则）
2025-07-08 20:25:10 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-08 20:25:10 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-08 20:25:10 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-08 20:25:12 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 20:25:12 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 20:25:14 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-08 20:25:14 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-08 20:25:17 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-08 20:25:17 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-08 20:25:19 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-08 20:25:24 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-08 20:25:24 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-08 20:25:54 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 11953字符
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫...
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 100338
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 清洗后长度: 11953
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 压缩率: 88.1%
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 中文字符: 0, 英文单词: 0, 数字: 0
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 11953字符
2025-07-08 20:25:55 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫一扫进入手机旺铺 TOP1 销量 TOP2 销量 本店 找货源 找工厂 搜索 首页 全部商品 所有产品 （15） 宠物梳子 （3） 宠物玩具 （13） 宠物清洁用品 （7） 猫玩具 （15） 未分类 ...
2025-07-08 20:26:01 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-08 20:26:01 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-08 20:26:01 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-08 20:26:01 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-08 20:26:01 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-08 20:26:01 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球
2025-07-08 20:26:02 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-08 20:28:37 - root - INFO - 应用程序退出，返回值: 0
2025-07-08 20:39:07 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-08 20:39:07 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Source_Compare_电商商品货源选品对比_v2.2.4- - 副本\data\logs\app.log
2025-07-08 20:39:07 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-08 20:39:09 - root - INFO - 应用程序初始化完成
2025-07-08 20:39:09 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-08 20:39:11 - root - INFO - 主窗口创建完成
2025-07-08 20:39:11 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-08 20:39:11 - root - INFO - 数据库路径: data/database.db
2025-07-08 20:39:11 - root - INFO - 应用程序正在运行...
2025-07-08 20:40:56 - root - INFO - 应用程序退出，返回值: 0
2025-07-09 01:20:39 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-09 01:20:39 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Compare_电商商品货源选品对比_v2.4.2-\data\logs\app.log
2025-07-09 01:20:39 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-09 01:20:39 - root - INFO - 应用程序初始化完成
2025-07-09 01:20:39 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-09 01:20:42 - root - INFO - 主窗口创建完成
2025-07-09 01:20:42 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-09 01:20:42 - root - INFO - 数据库路径: data/database.db
2025-07-09 01:20:42 - root - INFO - 应用程序正在运行...
2025-07-09 01:21:21 - root - INFO - 应用程序退出，返回值: 0
2025-07-09 01:25:07 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-09 01:25:07 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Compare_电商商品货源选品对比_v2.4.2-\data\logs\app.log
2025-07-09 01:25:07 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-09 01:25:07 - root - INFO - 应用程序初始化完成
2025-07-09 01:25:07 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-09 01:25:08 - root - INFO - 主窗口创建完成
2025-07-09 01:25:08 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-09 01:25:08 - root - INFO - 数据库路径: data/database.db
2025-07-09 01:25:08 - root - INFO - 应用程序正在运行...
2025-07-09 01:25:46 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-09 01:25:46 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-09 01:25:46 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-09 01:25:46 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-09 01:25:46 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用超级增强清理器（200+保护规则）
2025-07-09 01:25:46 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-09 01:25:46 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-09 01:25:46 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-09 01:25:52 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 01:25:52 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 01:25:54 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-09 01:25:54 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-09 01:25:57 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-09 01:25:57 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-09 01:25:59 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-09 01:26:03 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-09 01:26:03 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-09 01:26:35 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 11763字符
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫...
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 95545
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 清洗后长度: 11763
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 压缩率: 87.7%
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 中文字符: 0, 英文单词: 0, 数字: 0
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 11763字符
2025-07-09 01:26:36 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫一扫进入手机旺铺 本店 找货源 找工厂 搜索 首页 全部商品 店铺动态 加工专区 工厂档案 联系方式 Load start Play 00:00 00:00 Fullscreen 咨询 客服 全网比价...
2025-07-09 01:27:13 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-09 01:27:13 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-09 01:27:13 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-09 01:27:13 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-09 01:27:13 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-09 01:27:13 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球
2025-07-09 01:27:14 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 01:30:40 - root - INFO - 应用程序退出，返回值: 0
2025-07-09 01:42:22 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-09 01:42:22 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Compare_电商商品货源选品对比_v2.4.2-\data\logs\app.log
2025-07-09 01:42:22 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-09 01:42:23 - root - INFO - 应用程序初始化完成
2025-07-09 01:42:23 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-09 01:42:23 - root - INFO - 主窗口创建完成
2025-07-09 01:42:23 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-09 01:42:23 - root - INFO - 数据库路径: data/database.db
2025-07-09 01:42:23 - root - INFO - 应用程序正在运行...
2025-07-09 01:42:32 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-09 01:42:32 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-09 01:42:32 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-09 01:42:32 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-09 01:42:32 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用超级增强清理器（200+保护规则）
2025-07-09 01:42:32 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-09 01:42:32 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-09 01:42:32 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-09 01:42:33 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 01:42:33 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 01:42:35 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-09 01:42:35 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-09 01:42:38 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-09 01:42:38 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-09 01:42:40 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-09 01:42:44 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-09 01:42:44 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-09 01:43:13 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 11763字符
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫...
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 95583
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 清洗后长度: 11763
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 压缩率: 87.7%
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 中文字符: 0, 英文单词: 0, 数字: 0
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 11763字符
2025-07-09 01:43:14 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫一扫进入手机旺铺 本店 找货源 找工厂 搜索 首页 全部商品 店铺动态 加工专区 工厂档案 联系方式 Load start Play 00:00 00:00 Fullscreen 咨询 客服 全网比价...
2025-07-09 01:43:23 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-09 01:43:23 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-09 01:43:23 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-09 01:43:23 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-09 01:43:23 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-09 01:43:23 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳
2025-07-09 01:43:23 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 01:45:49 - root - INFO - 应用程序退出，返回值: 0
2025-07-09 02:06:36 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-09 02:06:36 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Compare_电商商品货源选品对比_v2.4.2-\data\logs\app.log
2025-07-09 02:06:36 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-09 02:06:36 - root - INFO - 应用程序初始化完成
2025-07-09 02:06:36 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-09 02:06:36 - root - INFO - 主窗口创建完成
2025-07-09 02:06:36 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-09 02:06:36 - root - INFO - 数据库路径: data/database.db
2025-07-09 02:06:36 - root - INFO - 应用程序正在运行...
2025-07-09 02:07:18 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-09 02:07:18 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-09 02:07:18 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-09 02:07:18 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-09 02:07:18 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用超级增强清理器（200+保护规则）
2025-07-09 02:07:18 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-09 02:07:18 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-09 02:07:18 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-09 02:07:20 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 02:07:20 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 02:07:21 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-09 02:07:21 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-09 02:07:25 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-09 02:07:25 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-09 02:07:27 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-09 02:07:31 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-09 02:07:31 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-09 02:08:03 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 11946字符
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫...
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 100387
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 清洗后长度: 11946
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 压缩率: 88.1%
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 中文字符: 0, 英文单词: 0, 数字: 0
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 11946字符
2025-07-09 02:08:04 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫一扫进入手机旺铺 TOP1 销量 TOP2 销量 本店 找货源 找工厂 搜索 首页 全部商品 所有产品 （15） 宠物梳子 （3） 宠物玩具 （13） 宠物清洁用品 （7） 猫玩具 （15） 未分类 ...
2025-07-09 02:08:13 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-09 02:08:13 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-09 02:08:13 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-09 02:08:13 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-09 02:08:13 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-09 02:08:13 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳
2025-07-09 02:08:13 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 02:12:58 - root - INFO - 应用程序退出，返回值: 0
2025-07-09 03:13:07 - root - INFO - 日志系统已启动 - 级别: INFO
2025-07-09 03:13:07 - root - INFO - 日志文件路径: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Python_Ecom_Product_Compare_电商商品货源选品对比_v2.4.2-\data\logs\app.log
2025-07-09 03:13:07 - root - INFO - 程序启动 - Python_Ecom_Product_Source_Compare v2.3.6
2025-07-09 03:13:08 - root - INFO - 应用程序初始化完成
2025-07-09 03:13:08 - root - INFO - 数据库初始化成功 - 路径: data/database.db
2025-07-09 03:13:08 - root - INFO - 主窗口创建完成
2025-07-09 03:13:08 - root - INFO - === Python_Ecom_Product_Source_Compare v2.3.6 启动成功 ===
2025-07-09 03:13:08 - root - INFO - 数据库路径: data/database.db
2025-07-09 03:13:08 - root - INFO - 应用程序正在运行...
2025-07-09 03:13:16 - scrapers.ai_analyzer - INFO - AI分析器初始化 - 模式: large_context_mode
2025-07-09 03:13:16 - scrapers.ai_analyzer - INFO - 最大tokens: 900000
2025-07-09 03:13:16 - scrapers.ai_analyzer - INFO - 分块大小: 200000
2025-07-09 03:13:16 - scrapers.ai_analyzer - INFO - 智能总结: True
2025-07-09 03:13:16 - scrapers.ai_analyzer - INFO - 内容清洗: 已启用超级增强清理器（200+保护规则）
2025-07-09 03:13:16 - scrapers.ai_analyzer - INFO - Ollama连接成功
2025-07-09 03:13:16 - scrapers.ai_analyzer - INFO - 文本模型 qwen2.5:14b 可用: True
2025-07-09 03:13:16 - scrapers.ai_analyzer - INFO - 未配置视觉模型，仅使用文本模型
2025-07-09 03:13:18 - web_scraper - INFO - 开始抓取URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 03:13:18 - web_scraper - INFO - 使用验证绕过模式抓取: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 03:13:20 - auth_bypass - INFO - 🚀 开始自动绕过验证流程...
2025-07-09 03:13:20 - auth_bypass - INFO - 📖 第一次访问页面...
2025-07-09 03:13:23 - auth_bypass - INFO - 🔍 检测到验证页面，开始自动绕过...
2025-07-09 03:13:23 - auth_bypass - INFO - ⌨️  模拟按回车键触发页面刷新...
2025-07-09 03:13:25 - auth_bypass - INFO - 🔄 执行第1次自动刷新，绕过验证页面...
2025-07-09 03:13:29 - auth_bypass - INFO - ✅ 自动绕过验证成功！
2025-07-09 03:13:29 - web_scraper - INFO - ✅ 页面访问和验证绕过已完成
2025-07-09 03:13:59 - root - INFO - 成功提取内容: 标题=中国大陆...
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 开始AI分析: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 步骤1: 开始内容清洗预处理...
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 使用UI预处理的清洗内容
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 🔍 AI收到的清洗内容长度: 11953字符
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 🔍 AI收到的内容前100字符: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫...
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 内容清洗完成 - 原始长度: 100338
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 清洗后长度: 11953
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 压缩率: 88.1%
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 中文字符: 0, 英文单词: 0, 数字: 0
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 步骤2: 开始AI智能分析...
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容长度: 11953字符
2025-07-09 03:14:00 - scrapers.ai_analyzer - INFO - 🚀 传递给AI的内容预览: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳 温州辛宠宠物用品有限公司 1年 综合服务 评价 价格很便宜 6 客服很热情 5 性价比很高 3 入手推荐 3 关注领券 阿里巴巴客户端 扫一扫进入手机旺铺 TOP1 销量 TOP2 销量 本店 找货源 找工厂 搜索 首页 全部商品 所有产品 （15） 宠物梳子 （3） 宠物玩具 （13） 宠物清洁用品 （7） 猫玩具 （15） 未分类 ...
2025-07-09 03:14:11 - scrapers.ai_analyzer - INFO - 步骤3: 图片内容分析...
2025-07-09 03:14:11 - scrapers.ai_analyzer - INFO - 启用图片分析，发现 10 张图片
2025-07-09 03:14:11 - scrapers.ai_analyzer - INFO - 未配置视觉模型，跳过图片分析
2025-07-09 03:14:11 - scrapers.ai_analyzer - WARNING - 图片分析失败
2025-07-09 03:14:11 - scrapers.ai_analyzer - INFO - 🎉 AI分析完成！数据提取成功
2025-07-09 03:14:11 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳
2025-07-09 03:14:11 - root - INFO - 抓取和分析完成 - URL: https://detail.1688.com/offer/840138940155.html?spm=a261y.7663282.sceneKey.1.2c045e79UgMS8s&sk=consign&fromkv=cbuPcPlugin%3AimageSearchDrawerCard&traceId=213e367817518935889816630e3167&spm-url=a2639h.29135425.offerlist.i2&spm-auction=840138940155
2025-07-09 03:14:23 - scrapers.data_mapper - INFO - 数据映射完成: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳
2025-07-09 03:14:23 - root - INFO - 商品详情数据已应用到商品详情选项卡
2025-07-09 03:14:23 - root - INFO - 智能抓取数据已应用到货源表单: 跨境耐咬自动逗猫玩具球猫咪玩具球自嗨解闷神器带绳宠物用品跳跳
2025-07-09 03:15:09 - root - INFO - 应用程序退出，返回值: 0
