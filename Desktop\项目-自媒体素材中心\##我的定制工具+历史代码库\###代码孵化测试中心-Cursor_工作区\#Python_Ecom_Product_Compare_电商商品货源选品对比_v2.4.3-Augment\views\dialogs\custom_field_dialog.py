"""
自定义字段编辑对话框
支持自定义字段的添加和编辑
"""

from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QFormLayout,
    QLineEdit,
    QTextEdit,
    QComboBox,
    QDialogButtonBox,
    QMessageBox,
    QLabel,
)
from PyQt6.QtCore import Qt, pyqtSignal

from models.custom_field import CustomField
from models.database import ProductService


class CustomFieldDialog(QDialog):
    """自定义字段编辑对话框"""

    # 信号定义
    field_saved = pyqtSignal(object)  # 自定义字段保存完成

    def __init__(
        self,
        field: CustomField = None,
        product_id: int = None,
        product_service: ProductService = None,
        parent=None,
    ):
        super().__init__(parent)
        self.field = field
        self.product_id = product_id
        self.product_service = product_service
        self.is_edit_mode = field is not None

        self.setup_ui()
        if self.is_edit_mode:
            self.load_field_data()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("编辑自定义字段" if self.is_edit_mode else "添加自定义字段")
        self.setMinimumSize(400, 300)

        # 设置对话框字体
        from PyQt6.QtGui import QFont

        font = QFont("Microsoft YaHei UI", 12)
        self.setFont(font)

        layout = QVBoxLayout(self)

        # 表单布局
        form_layout = QFormLayout()

        # 字段名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入字段名称（英文）")
        self.name_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        form_layout.addRow("字段名称*:", self.name_edit)

        # 显示名称
        self.display_name_edit = QLineEdit()
        self.display_name_edit.setPlaceholderText("请输入显示名称（中文）")
        self.display_name_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        form_layout.addRow("显示名称*:", self.display_name_edit)

        # 字段类型
        self.type_combo = QComboBox()
        self.type_combo.addItems(["文本", "多行文本", "数字", "日期", "选择"])
        self.type_combo.setStyleSheet("font-size: 12px; padding: 8px;")
        form_layout.addRow("字段类型*:", self.type_combo)

        # 连接类型变化信号
        self.type_combo.currentTextChanged.connect(self.on_type_changed)

        layout.addLayout(form_layout)

        # 字段值输入区域
        self.value_label = QLabel("字段值:")
        self.value_label.setStyleSheet("font-size: 12px; font-weight: bold;")
        layout.addWidget(self.value_label)

        # 文本输入
        self.value_edit = QLineEdit()
        self.value_edit.setPlaceholderText("请输入字段值")
        self.value_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        layout.addWidget(self.value_edit)

        # 多行文本输入
        self.value_text_edit = QTextEdit()
        self.value_text_edit.setPlaceholderText("请输入字段值")
        self.value_text_edit.setMaximumHeight(100)
        self.value_text_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        self.value_text_edit.hide()
        layout.addWidget(self.value_text_edit)

        # 数字输入
        from PyQt6.QtWidgets import QDoubleSpinBox

        self.value_number_edit = QDoubleSpinBox()
        self.value_number_edit.setRange(-999999.99, 999999.99)
        self.value_number_edit.setDecimals(2)
        self.value_number_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        self.value_number_edit.hide()
        layout.addWidget(self.value_number_edit)

        # 日期输入
        from PyQt6.QtWidgets import QDateEdit
        from PyQt6.QtCore import QDate

        self.value_date_edit = QDateEdit()
        self.value_date_edit.setCalendarPopup(True)
        self.value_date_edit.setDate(QDate.currentDate())
        self.value_date_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        self.value_date_edit.hide()
        layout.addWidget(self.value_date_edit)

        # 选择输入
        self.value_combo_edit = QComboBox()
        self.value_combo_edit.setEditable(True)
        self.value_combo_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        self.value_combo_edit.hide()
        layout.addWidget(self.value_combo_edit)

        # 使用提示
        tip_label = QLabel("提示：字段名称用于程序内部标识，显示名称用于界面显示")
        tip_label.setStyleSheet("color: #666; font-size: 12px; margin-top: 10px;")
        layout.addWidget(tip_label)

        layout.addStretch()

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.setStyleSheet("QPushButton { font-size: 12px; padding: 8px 16px; }")
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)

        # 设置焦点
        self.name_edit.setFocus()

    def on_type_changed(self, type_text):
        """字段类型变化时的处理"""
        # 隐藏所有输入控件
        self.value_edit.hide()
        self.value_text_edit.hide()
        self.value_number_edit.hide()
        self.value_date_edit.hide()
        self.value_combo_edit.hide()

        # 根据类型显示对应的输入控件
        if type_text == "文本":
            self.value_edit.show()
        elif type_text == "多行文本":
            self.value_text_edit.show()
        elif type_text == "数字":
            self.value_number_edit.show()
        elif type_text == "日期":
            self.value_date_edit.show()
        elif type_text == "选择":
            self.value_combo_edit.show()

    def load_field_data(self):
        """加载字段数据"""
        if self.field:
            self.name_edit.setText(self.field.name)
            self.display_name_edit.setText(self.field.display_name)

            # 设置类型
            type_mapping = {
                "text": "文本",
                "multiline": "多行文本",
                "number": "数字",
                "date": "日期",
                "select": "选择",
            }
            display_type = type_mapping.get(self.field.field_type, "文本")
            self.type_combo.setCurrentText(display_type)

            # 设置值
            self.set_field_value(self.field.value)

    def set_field_value(self, value):
        """设置字段值"""
        type_text = self.type_combo.currentText()

        if type_text == "文本":
            self.value_edit.setText(str(value))
        elif type_text == "多行文本":
            self.value_text_edit.setPlainText(str(value))
        elif type_text == "数字":
            try:
                self.value_number_edit.setValue(float(value))
            except (ValueError, TypeError):
                self.value_number_edit.setValue(0.0)
        elif type_text == "日期":
            from PyQt6.QtCore import QDate

            try:
                date = QDate.fromString(str(value), "yyyy-MM-dd")
                if date.isValid():
                    self.value_date_edit.setDate(date)
            except:
                self.value_date_edit.setDate(QDate.currentDate())
        elif type_text == "选择":
            self.value_combo_edit.setCurrentText(str(value))

    def get_field_value(self):
        """获取字段值"""
        type_text = self.type_combo.currentText()

        if type_text == "文本":
            return self.value_edit.text().strip()
        elif type_text == "多行文本":
            return self.value_text_edit.toPlainText().strip()
        elif type_text == "数字":
            return str(self.value_number_edit.value())
        elif type_text == "日期":
            return self.value_date_edit.date().toString("yyyy-MM-dd")
        elif type_text == "选择":
            return self.value_combo_edit.currentText().strip()

        return ""

    def get_field_data(self):
        """获取字段数据"""
        # 类型映射
        type_mapping = {
            "文本": "text",
            "多行文本": "multiline",
            "数字": "number",
            "日期": "date",
            "选择": "select",
        }

        return {
            "name": self.name_edit.text().strip(),
            "display_name": self.display_name_edit.text().strip(),
            "field_type": type_mapping.get(self.type_combo.currentText(), "text"),
            "value": self.get_field_value(),
        }

    def validate_input(self):
        """验证输入"""
        data = self.get_field_data()

        # 验证必填字段
        if not data["name"]:
            QMessageBox.warning(self, "验证错误", "请输入字段名称")
            self.name_edit.setFocus()
            return False

        if not data["display_name"]:
            QMessageBox.warning(self, "验证错误", "请输入显示名称")
            self.display_name_edit.setFocus()
            return False

        # 验证字段名称格式（只允许英文、数字、下划线）
        import re

        if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", data["name"]):
            QMessageBox.warning(
                self,
                "验证错误",
                "字段名称只能包含英文字母、数字和下划线，且不能以数字开头",
            )
            self.name_edit.setFocus()
            return False

        return True

    def save_field(self):
        """保存字段"""
        if not self.validate_input():
            return False

        try:
            data = self.get_field_data()

            if self.is_edit_mode and self.field:
                # 更新现有字段
                self.field.name = data["name"]
                self.field.display_name = data["display_name"]
                self.field.field_type = data["field_type"]
                self.field.value = data["value"]

                success = self.product_service.update_custom_field(self.field)
                if success:
                    # QMessageBox.information(self, "成功", "自定义字段更新成功")  # 移除弹窗
                    self.field_saved.emit(self.field)
                    return True
                else:
                    QMessageBox.warning(self, "错误", "自定义字段更新失败")
                    return False
            else:
                # 创建新字段
                if not self.product_id:
                    QMessageBox.warning(self, "错误", "缺少产品ID信息")
                    return False

                field = self.product_service.add_custom_field(
                    product_id=self.product_id,
                    name=data["name"],
                    value=data["value"],
                    field_type=data["field_type"],
                    display_name=data["display_name"],
                )

                if field:
                    QMessageBox.information(
                        self, "成功", f"自定义字段 '{field.display_name}' 添加成功"
                    )
                    self.field_saved.emit(field)
                    return True
                else:
                    QMessageBox.warning(self, "错误", "自定义字段创建失败")
                    return False

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存自定义字段失败: {str(e)}")
            return False

    def accept(self):
        """确认对话框"""
        if self.save_field():
            super().accept()


# 修复导入问题
from PyQt6.QtCore import QDate
