"""
标签管理对话框
"""

from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QTableWidget,
    QTableWidgetItem,
    QPushButton,
    QLabel,
    QLineEdit,
    QColorDialog,
    QTextEdit,
    QMessageBox,
    QHeaderView,
    QGroupBox,
    QFormLayout,
    QDialogButtonBox,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QColor

from models.database import TagService
from models.tag import Tag


class TagManagerDialog(QDialog):
    """标签管理对话框"""

    tag_updated = pyqtSignal()  # 标签更新信号

    def __init__(self, tag_service: TagService, parent=None):
        super().__init__(parent)
        self.tag_service = tag_service
        self.current_tag = None
        self.setup_ui()
        self.load_tags()

    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("标签管理")
        self.setFixedSize(800, 600)

        # 主布局
        main_layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("标签管理")
        title_label.setStyleSheet(
            "font-size: 18px; font-weight: bold; margin-bottom: 10px;"
        )
        main_layout.addWidget(title_label)

        # 水平布局：左侧标签列表，右侧编辑区域
        content_layout = QHBoxLayout()

        # 左侧：标签列表
        left_group = QGroupBox("标签列表")
        left_layout = QVBoxLayout(left_group)

        # 标签表格
        self.tag_table = QTableWidget()
        self.tag_table.setColumnCount(4)
        self.tag_table.setHorizontalHeaderLabels(["名称", "颜色", "使用次数", "描述"])

        # 设置表格属性
        self.tag_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tag_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.tag_table.setAlternatingRowColors(True)

        # 设置列宽
        header = self.tag_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)

        self.tag_table.setColumnWidth(0, 120)
        self.tag_table.setColumnWidth(1, 80)
        self.tag_table.setColumnWidth(2, 80)

        left_layout.addWidget(self.tag_table)

        # 表格下方按钮
        table_buttons_layout = QHBoxLayout()

        self.add_btn = QPushButton("添加")
        self.edit_btn = QPushButton("编辑")
        self.delete_btn = QPushButton("删除")
        self.refresh_btn = QPushButton("刷新")

        table_buttons_layout.addWidget(self.add_btn)
        table_buttons_layout.addWidget(self.edit_btn)
        table_buttons_layout.addWidget(self.delete_btn)
        table_buttons_layout.addStretch()
        table_buttons_layout.addWidget(self.refresh_btn)

        left_layout.addLayout(table_buttons_layout)

        # 右侧：编辑区域
        right_group = QGroupBox("标签编辑")
        right_layout = QVBoxLayout(right_group)

        # 编辑表单
        form_layout = QFormLayout()

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入标签名称")
        form_layout.addRow("名称:", self.name_edit)

        # 颜色选择
        color_layout = QHBoxLayout()
        self.color_button = QPushButton()
        self.color_button.setFixedSize(80, 30)
        self.color_button.setStyleSheet(
            "background-color: #1976d2; border: 1px solid #ccc;"
        )
        self.color_button.setText("选择颜色")
        color_layout.addWidget(self.color_button)
        color_layout.addStretch()
        form_layout.addRow("颜色:", color_layout)

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("输入标签描述（可选）")
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("描述:", self.description_edit)

        right_layout.addLayout(form_layout)

        # 编辑按钮
        edit_buttons_layout = QHBoxLayout()
        self.save_btn = QPushButton("保存")
        self.cancel_btn = QPushButton("取消")

        edit_buttons_layout.addWidget(self.save_btn)
        edit_buttons_layout.addWidget(self.cancel_btn)
        edit_buttons_layout.addStretch()

        right_layout.addLayout(edit_buttons_layout)
        right_layout.addStretch()

        # 设置左右布局比例
        content_layout.addWidget(left_group, 2)
        content_layout.addWidget(right_group, 1)

        main_layout.addLayout(content_layout)

        # 底部按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

        # 设置默认状态
        self.set_edit_mode(False)

    def setup_connections(self):
        """设置信号连接"""
        self.tag_table.selectionModel().selectionChanged.connect(
            self.on_selection_changed
        )
        self.add_btn.clicked.connect(self.add_tag)
        self.edit_btn.clicked.connect(self.edit_tag)
        self.delete_btn.clicked.connect(self.delete_tag)
        self.refresh_btn.clicked.connect(self.load_tags)
        self.save_btn.clicked.connect(self.save_tag)
        self.cancel_btn.clicked.connect(self.cancel_edit)
        self.color_button.clicked.connect(self.choose_color)

    def load_tags(self):
        """加载标签数据"""
        tags = self.tag_service.get_all_tags()

        self.tag_table.setRowCount(len(tags))
        for i, tag in enumerate(tags):
            # 名称
            self.tag_table.setItem(i, 0, QTableWidgetItem(tag.name))

            # 颜色
            color_item = QTableWidgetItem()
            color_item.setBackground(QColor(tag.color))
            color_item.setText(tag.color)
            self.tag_table.setItem(i, 1, color_item)

            # 使用次数
            self.tag_table.setItem(i, 2, QTableWidgetItem(str(tag.usage_count)))

            # 描述
            self.tag_table.setItem(i, 3, QTableWidgetItem(tag.description))

            # 存储标签ID
            self.tag_table.item(i, 0).setData(Qt.ItemDataRole.UserRole, tag.id)

    def on_selection_changed(self):
        """表格选择变化"""
        selected_rows = self.tag_table.selectionModel().selectedRows()
        if selected_rows:
            self.edit_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
        else:
            self.edit_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)

    def add_tag(self):
        """添加标签"""
        self.current_tag = None
        self.clear_form()
        self.set_edit_mode(True)

    def edit_tag(self):
        """编辑标签"""
        selected_rows = self.tag_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        tag_id = self.tag_table.item(row, 0).data(Qt.ItemDataRole.UserRole)

        self.current_tag = self.tag_service.get_tag(tag_id)
        if self.current_tag:
            self.load_form(self.current_tag)
            self.set_edit_mode(True)

    def delete_tag(self):
        """删除标签"""
        selected_rows = self.tag_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        tag_id = self.tag_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        tag_name = self.tag_table.item(row, 0).text()

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除标签 '{tag_name}' 吗？\n\n删除后将从所有关联的产品中移除此标签。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.tag_service.delete_tag(tag_id):
                self.load_tags()
                self.tag_updated.emit()
                # QMessageBox.information(self, "成功", "标签删除成功！")  # 移除弹窗
            else:
                QMessageBox.warning(self, "错误", "删除标签失败！")

    def save_tag(self):
        """保存标签"""
        name = self.name_edit.text().strip()
        description = self.description_edit.toPlainText().strip()
        color = (
            self.color_button.styleSheet().split("background-color: ")[1].split(";")[0]
        )

        if not name:
            QMessageBox.warning(self, "错误", "标签名称不能为空！")
            return

        try:
            if self.current_tag:
                # 更新现有标签
                self.current_tag.name = name
                self.current_tag.description = description
                self.current_tag.color = color

                if self.tag_service.update_tag(self.current_tag):
                    # QMessageBox.information(self, "成功", "标签更新成功！")  # 移除弹窗
                    pass  # 成功时不做任何操作
                else:
                    QMessageBox.warning(self, "错误", "更新标签失败！")
            else:
                # 创建新标签
                tag = self.tag_service.create_tag(name, color, description)
                if tag:
                    # QMessageBox.information(self, "成功", "标签创建成功！")  # 移除弹窗
                    pass  # 成功时不做任何操作
                else:
                    QMessageBox.warning(self, "错误", "创建标签失败！")

            self.load_tags()
            self.tag_updated.emit()
            self.set_edit_mode(False)

        except Exception as e:
            if "UNIQUE constraint failed" in str(e):
                QMessageBox.warning(self, "错误", "标签名称已存在，请使用不同的名称！")
            else:
                QMessageBox.warning(self, "错误", f"保存标签时发生错误：{str(e)}")

    def cancel_edit(self):
        """取消编辑"""
        self.set_edit_mode(False)
        self.clear_form()

    def choose_color(self):
        """选择颜色"""
        current_color = (
            self.color_button.styleSheet().split("background-color: ")[1].split(";")[0]
        )
        color = QColorDialog.getColor(QColor(current_color), self)

        if color.isValid():
            self.color_button.setStyleSheet(
                f"background-color: {color.name()}; border: 1px solid #ccc;"
            )

    def clear_form(self):
        """清空表单"""
        self.name_edit.clear()
        self.description_edit.clear()
        self.color_button.setStyleSheet(
            "background-color: #1976d2; border: 1px solid #ccc;"
        )

    def load_form(self, tag: Tag):
        """加载表单数据"""
        self.name_edit.setText(tag.name)
        self.description_edit.setPlainText(tag.description)
        self.color_button.setStyleSheet(
            f"background-color: {tag.color}; border: 1px solid #ccc;"
        )

    def set_edit_mode(self, enabled: bool):
        """设置编辑模式"""
        self.name_edit.setEnabled(enabled)
        self.description_edit.setEnabled(enabled)
        self.color_button.setEnabled(enabled)
        self.save_btn.setEnabled(enabled)
        self.cancel_btn.setEnabled(enabled)

        # 如果不是编辑模式，清空表单
        if not enabled:
            self.clear_form()
            self.current_tag = None

    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        self.setup_connections()
        self.load_tags()

    def closeEvent(self, event):
        """关闭事件"""
        self.tag_updated.emit()
        super().closeEvent(event)
