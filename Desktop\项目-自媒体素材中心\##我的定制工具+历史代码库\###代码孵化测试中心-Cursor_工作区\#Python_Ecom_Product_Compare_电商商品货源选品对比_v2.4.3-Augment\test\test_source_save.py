#!/usr/bin/env python3
"""
专门测试货源保存功能的程序
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("=" * 50)
print("货源保存测试程序")
print("=" * 50)

try:
    print("1. 导入模块...")
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from models.database import ProductService, DatabaseManager
    from models.source import Source, SourceMode

    print("   模块导入成功")

    print("2. 创建QApplication...")
    app = QApplication(sys.argv)
    print("   QApplication创建成功")

    print("3. 创建数据库服务...")
    db_manager = DatabaseManager()
    product_service = ProductService(db_manager)
    print("   数据库服务创建成功")

    print("4. 创建测试产品...")
    test_product = product_service.create_product(
        name="测试产品",
        sku="TEST001",
        selling_price=100.0,
        description="这是一个测试产品",
    )
    print(f"   测试产品创建成功: {test_product}")

    print("5. 测试货源保存...")
    try:
        # 创建测试货源数据
        test_source_data = {
            "name": "测试供应商",
            "price": 50.0,
            "url": "https://test.com",
            "shop_info": "测试店铺",
            "quantity": 100,
            "min_order_quantity": 10,
            "shipping_cost": 10.0,
            "shipping_cost_suffix": "",
            "shipping_location": "广州",
            "product_name": "测试货源产品",
            "sales_count": 1000,
            "status": "active",
            "return_policy": "7天无理由退货",
            "modes": ["wholesale", "dropship"],
            "contact_info": "联系人：张三，电话：13800138000",
            "notes": "这是测试备注",
            "is_active": True,
            # 批发模式字段
            "pickup_rate_24h": 95.0,
            "pickup_rate_48h": 98.0,
            "monthly_dropship_orders": 500,
            "downstream_stores": 50,
            "distributor_count": 20,
            "product_publish_time": None,
            "available_products": 1000,
            "monthly_new_products": 50,
            "image_urls": [],
            # 批发模式专属字段
            "wholesale_min_order_quantity": 50,
            "wholesale_payment_terms": 30,
            "wholesale_rebate_rate": 0.1,
            "wholesale_price_tiers": "100件以上8折",
            # 代发模式专属字段
            "dropship_service_fee": 5.0,
            "dropship_processing_time": 24,
            "dropship_packaging": "无标签包装",
            "dropship_inventory_sync": "实时同步",
            "dropship_min_quantity": 1,
            "dropship_shipping_location": "深圳",
            "dropship_support_regions": "全国",
        }

        print(f"   货源数据: {test_source_data}")

        # 从字典中移除已经作为位置参数传递的字段
        kwargs_dict = test_source_data.copy()
        kwargs_dict.pop("name", None)
        kwargs_dict.pop("price", None)

        print("   开始调用add_source...")
        saved_source = product_service.add_source(
            test_product.id,
            test_source_data["name"],
            test_source_data["price"],
            **kwargs_dict,
        )

        if saved_source:
            print(f"   货源保存成功: {saved_source}")
            print(f"   货源ID: {saved_source.id}")
            print(f"   货源名称: {saved_source.name}")
            print(f"   货源价格: {saved_source.price}")
            QMessageBox.information(
                None, "成功", f"货源保存成功！\n货源ID: {saved_source.id}"
            )
        else:
            print("   货源保存失败")
            QMessageBox.warning(None, "失败", "货源保存失败")

    except Exception as e:
        print(f"   货源保存异常: {str(e)}")
        import traceback

        traceback.print_exc()
        QMessageBox.critical(None, "错误", f"货源保存异常: {str(e)}")

    print("6. 清理测试数据...")
    try:
        product_service.delete_product(test_product.id)
        print("   测试数据清理完成")
    except Exception as e:
        print(f"   测试数据清理失败: {e}")

    print("=" * 50)
    print("测试完成")
    print("=" * 50)

except Exception as e:
    print(f"程序启动失败: {str(e)}")
    import traceback

    traceback.print_exc()
    input("按Enter键退出...")
