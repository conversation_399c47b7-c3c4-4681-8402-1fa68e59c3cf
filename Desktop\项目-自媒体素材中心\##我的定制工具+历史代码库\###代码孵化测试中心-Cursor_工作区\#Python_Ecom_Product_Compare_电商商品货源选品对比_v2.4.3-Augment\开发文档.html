<!doctype html>
<html>
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="ie=edge" />
<title>Markmap</title>
<style>
* {
  margin: 0;
  padding: 0;
}
html {
  font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}
#mindmap {
  display: block;
  width: 100vw;
  height: 100vh;
}
.markmap-dark {
  background: #27272a;
  color: white;
}
</style>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/markmap-toolbar@0.18.12/dist/style.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@highlightjs/cdn-assets@11.11.1/styles/default.min.css">
</head>
<body>
<svg id="mindmap"></svg>
<script src="https://cdn.jsdelivr.net/npm/d3@7.9.0/dist/d3.min.js"></script><script src="https://cdn.jsdelivr.net/npm/markmap-view@0.18.12/dist/browser/index.js"></script><script src="https://cdn.jsdelivr.net/npm/markmap-toolbar@0.18.12/dist/index.js"></script><script>(r => {
              setTimeout(r);
            })(function renderToolbar() {
  const {
    markmap,
    mm
  } = window;
  const {
    el
  } = markmap.Toolbar.create(mm);
  el.setAttribute('style', 'position:absolute;bottom:20px;right:20px');
  document.body.append(el);
})</script><script>((getMarkmap, getOptions, root2, jsonOptions) => {
              const markmap = getMarkmap();
              window.mm = markmap.Markmap.create(
                "svg#mindmap",
                (getOptions || markmap.deriveOptions)(jsonOptions),
                root2
              );
              if (window.matchMedia("(prefers-color-scheme: dark)").matches) {
                document.documentElement.classList.add("markmap-dark");
              }
            })(() => window.markmap,null,{"content":"&#x7535;&#x5546;&#x4ea7;&#x54c1;&#x5bf9;&#x6bd4;&#x9009;&#x54c1;&#x5de5;&#x5177; - &#x5f00;&#x53d1;&#x6587;&#x6863; v2.1.7","children":[{"content":"&#x7248;&#x672c;&#x66f4;&#x65b0; (v2.1.7)","children":[{"content":"&#x1f41b; &#x5173;&#x952e;&#x95ee;&#x9898;&#x4fee;&#x590d;","children":[{"content":"&#x5bfc;&#x5165;&#x529f;&#x80fd;&#x9519;&#x8bef;&#x4fee;&#x590d;","children":[{"content":"<strong>&#x95ee;&#x9898;</strong>&#xff1a;<code>&apos;ProductService&apos; object has no attribute &apos;update_product_images&apos;</code>","children":[],"payload":{"tag":"li","lines":"7,8"}},{"content":"<strong>&#x539f;&#x56e0;</strong>&#xff1a;&#x5bfc;&#x5165;&#x529f;&#x80fd;&#x8c03;&#x7528;&#x4e86;&#x4e0d;&#x5b58;&#x5728;&#x7684;&#x516c;&#x6709;&#x65b9;&#x6cd5;","children":[],"payload":{"tag":"li","lines":"8,9"}},{"content":"<strong>&#x89e3;&#x51b3;&#x65b9;&#x6848;</strong>&#xff1a;<pre data-lines=\"10,28\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x5728;ProductService&#x4e2d;&#x6dfb;&#x52a0;&#x516c;&#x6709;&#x65b9;&#x6cd5;</span>\n<span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">update_product_images</span>(<span class=\"hljs-params\">self, product_id: <span class=\"hljs-built_in\">int</span>, image_paths: <span class=\"hljs-type\">List</span>[<span class=\"hljs-built_in\">str</span>]</span>) -&gt; <span class=\"hljs-type\">List</span>[<span class=\"hljs-built_in\">str</span>]:\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x66f4;&#x65b0;&#x4ea7;&#x54c1;&#x56fe;&#x7247;&#xff08;&#x516c;&#x6709;&#x65b9;&#x6cd5;&#xff09;&quot;&quot;&quot;</span>\n    product = <span class=\"hljs-variable language_\">self</span>.get_product(product_id)\n    <span class=\"hljs-keyword\">if</span> <span class=\"hljs-keyword\">not</span> product:\n        <span class=\"hljs-keyword\">return</span> []\n    \n    final_images = <span class=\"hljs-variable language_\">self</span>._update_product_images(product_id, product.name, image_paths)\n    \n    <span class=\"hljs-comment\"># &#x66f4;&#x65b0;&#x6570;&#x636e;&#x5e93;</span>\n    cursor = <span class=\"hljs-variable language_\">self</span>.db.connection.cursor()\n    cursor.execute(<span class=\"hljs-string\">&quot;UPDATE products SET images = ? WHERE id = ?&quot;</span>, \n                  (json.dumps(final_images), product_id))\n    <span class=\"hljs-variable language_\">self</span>.db.connection.commit()\n    \n    <span class=\"hljs-keyword\">return</span> final_images\n</code></pre>","children":[],"payload":{"tag":"li","lines":"9,29"}}],"payload":{"tag":"h4","lines":"6,7"}},{"content":"&#x6807;&#x7b7e;&#x552f;&#x4e00;&#x6027;&#x7ea6;&#x675f;&#x9519;&#x8bef;&#x4fee;&#x590d;","children":[{"content":"<strong>&#x95ee;&#x9898;</strong>&#xff1a;<code>UNIQUE constraint failed: tags.name</code>","children":[],"payload":{"tag":"li","lines":"30,31"}},{"content":"<strong>&#x539f;&#x56e0;</strong>&#xff1a;&#x5bfc;&#x5165;&#x65f6;&#x91cd;&#x590d;&#x521b;&#x5efa;&#x540c;&#x540d;&#x6807;&#x7b7e;","children":[],"payload":{"tag":"li","lines":"31,32"}},{"content":"<strong>&#x89e3;&#x51b3;&#x65b9;&#x6848;</strong>&#xff1a;<pre data-lines=\"33,40\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x5bfc;&#x5165;&#x524d;&#x68c0;&#x67e5;&#x6807;&#x7b7e;&#x662f;&#x5426;&#x5b58;&#x5728;</span>\nexisting_tag = <span class=\"hljs-variable language_\">self</span>.tag_service.get_tag_by_name(tag_data[<span class=\"hljs-string\">&quot;name&quot;</span>])\n<span class=\"hljs-keyword\">if</span> <span class=\"hljs-keyword\">not</span> existing_tag:\n    <span class=\"hljs-comment\"># &#x53ea;&#x6709;&#x5f53;&#x6807;&#x7b7e;&#x4e0d;&#x5b58;&#x5728;&#x65f6;&#x624d;&#x521b;&#x5efa;</span>\n    <span class=\"hljs-variable language_\">self</span>.tag_service.create_tag(tag_data[<span class=\"hljs-string\">&quot;name&quot;</span>], tag_data[<span class=\"hljs-string\">&quot;color&quot;</span>], ...)\n</code></pre>","children":[],"payload":{"tag":"li","lines":"32,41"}}],"payload":{"tag":"h4","lines":"29,30"}},{"content":"&#x56fe;&#x7247;&#x5bfc;&#x51fa;&#x8def;&#x5f84;&#x4fee;&#x590d;","children":[{"content":"<strong>&#x95ee;&#x9898;</strong>&#xff1a;&#x5bfc;&#x51fa;&#x529f;&#x80fd;&#x4e2d;&#x56fe;&#x7247;&#x672a;&#x80fd;&#x6b63;&#x786e;&#x590d;&#x5236;&#x5230;&#x5bfc;&#x51fa;&#x6587;&#x4ef6;&#x5939;&#xff0c;&#x56e0;&#x4e3a;&#x6570;&#x636e;&#x5e93;&#x4e2d;&#x5b58;&#x50a8;&#x7684;&#x662f;&#x5b8c;&#x6574;&#x8def;&#x5f84;&#xff0c;&#x4f46;&#x5bfc;&#x51fa;&#x903b;&#x8f91;&#x5c06;&#x5176;&#x4f5c;&#x4e3a;&#x6587;&#x4ef6;&#x540d;&#x5904;&#x7406;&#x3002;","children":[],"payload":{"tag":"li","lines":"42,43"}},{"content":"<strong>&#x539f;&#x56e0;</strong>&#xff1a;&#x5bfc;&#x51fa;&#x65f6;&#xff0c;<code>product.images</code> &#x5217;&#x8868;&#x4e2d;&#x7684;&#x6bcf;&#x4e2a;&#x5143;&#x7d20;&#x5df2;&#x7ecf;&#x662f; <code>data/images/products/product_ID/image_name.ext</code> &#x8fd9;&#x6837;&#x7684;&#x5b8c;&#x6574;&#x76f8;&#x5bf9;&#x8def;&#x5f84;&#xff0c;&#x4f46;&#x4ee3;&#x7801;&#x5728;&#x6784;&#x9020;&#x6e90;&#x8def;&#x5f84;&#x65f6;&#x53c8;&#x62fc;&#x63a5;&#x4e86;&#x4e00;&#x6b21;&#x3002;","children":[],"payload":{"tag":"li","lines":"43,44"}},{"content":"<strong>&#x89e3;&#x51b3;&#x65b9;&#x6848;</strong>&#xff1a;&#x4fee;&#x6539;&#x5bfc;&#x51fa;&#x903b;&#x8f91;&#xff0c;&#x5224;&#x65ad;&#x56fe;&#x7247;&#x8def;&#x5f84;&#x662f;&#x5b8c;&#x6574;&#x8def;&#x5f84;&#x8fd8;&#x662f;&#x6587;&#x4ef6;&#x540d;&#xff0c;&#x7136;&#x540e;&#x6b63;&#x786e;&#x6784;&#x9020;&#x6e90;&#x8def;&#x5f84;&#x3002;<pre data-lines=\"45,58\"><code class=\"language-python\"><span class=\"hljs-comment\"># views/main_window.py &#x4e2d;&#x7684;&#x5bfc;&#x51fa;&#x56fe;&#x7247;&#x903b;&#x8f91;</span>\n<span class=\"hljs-keyword\">for</span> image_path <span class=\"hljs-keyword\">in</span> product.images:\n    <span class=\"hljs-keyword\">if</span> os.path.isabs(image_path) <span class=\"hljs-keyword\">or</span> image_path.startswith(<span class=\"hljs-string\">&apos;data/&apos;</span>):\n        src_path = Path(image_path) <span class=\"hljs-comment\"># &#x76f4;&#x63a5;&#x4f7f;&#x7528;&#x5b8c;&#x6574;&#x8def;&#x5f84;</span>\n    <span class=\"hljs-keyword\">else</span>:\n        <span class=\"hljs-comment\"># &#x65e7;&#x7684;&#x6587;&#x4ef6;&#x540d;&#x683c;&#x5f0f;&#xff0c;&#x9700;&#x8981;&#x62fc;&#x63a5;</span>\n        src_path = Path(IMAGES_DIR) / <span class=\"hljs-string\">&quot;products&quot;</span> / <span class=\"hljs-string\">f&quot;product_<span class=\"hljs-subst\">{product.<span class=\"hljs-built_in\">id</span>}</span>&quot;</span> / image_path\n    \n    image_filename = os.path.basename(<span class=\"hljs-built_in\">str</span>(src_path))\n    dst_path = os.path.join(product_images_dir, image_filename)\n    <span class=\"hljs-comment\"># ...&#x590d;&#x5236;&#x6587;&#x4ef6;&#x548c;&#x66f4;&#x65b0; product_data[&quot;images&quot;] ...</span>\n</code></pre>","children":[],"payload":{"tag":"li","lines":"44,59"}}],"payload":{"tag":"h4","lines":"41,42"}},{"content":"&#x56fe;&#x7247;&#x5bfc;&#x5165;&#x8def;&#x5f84;&#x5b58;&#x50a8;&#x4e0e;&#x663e;&#x793a;&#x4fee;&#x590d;","children":[{"content":"<strong>&#x95ee;&#x9898;</strong>&#xff1a;&#x5bfc;&#x5165;&#x540e;&#x56fe;&#x7247;&#x6587;&#x4ef6;&#x5df2;&#x590d;&#x5236;&#x5230; <code>data/images/products/product_ID/</code> &#x76ee;&#x5f55;&#xff0c;&#x4f46;&#x6570;&#x636e;&#x5e93;&#x4e2d;&#x5b58;&#x50a8;&#x7684;&#x56fe;&#x7247;&#x8def;&#x5f84;&#x4e0d;&#x5b8c;&#x6574;&#xff0c;&#x5bfc;&#x81f4;&#x56fe;&#x7247;&#x65e0;&#x6cd5;&#x663e;&#x793a;&#x3002;","children":[],"payload":{"tag":"li","lines":"60,61"}},{"content":"<strong>&#x539f;&#x56e0;</strong>&#xff1a;&#x5bfc;&#x5165;&#x903b;&#x8f91;&#x5728;&#x66f4;&#x65b0;&#x6570;&#x636e;&#x5e93;&#x65f6;&#xff0c;&#x53ea;&#x5b58;&#x50a8;&#x4e86;&#x6587;&#x4ef6;&#x540d;&#xff08;&#x5982; <code>&#x9ad8;&#x8ddf;&#x978b;-1_001.png</code>&#xff09;&#xff0c;&#x800c;&#x4e0d;&#x662f;&#x80fd;&#x591f;&#x76f4;&#x63a5;&#x52a0;&#x8f7d;&#x7684;&#x5b8c;&#x6574;&#x76f8;&#x5bf9;&#x8def;&#x5f84;&#x3002;","children":[],"payload":{"tag":"li","lines":"61,62"}},{"content":"<strong>&#x89e3;&#x51b3;&#x65b9;&#x6848;</strong>&#xff1a;&#x5728;&#x56fe;&#x7247;&#x590d;&#x5236;&#x5b8c;&#x6210;&#x540e;&#xff0c;&#x5c06;&#x56fe;&#x7247;&#x7684;&#x5b8c;&#x6574;&#x76f8;&#x5bf9;&#x8def;&#x5f84;&#x5b58;&#x50a8;&#x5230;&#x6570;&#x636e;&#x5e93;&#x4e2d;&#x3002;<pre data-lines=\"63,73\"><code class=\"language-python\"><span class=\"hljs-comment\"># views/main_window.py &#x4e2d;&#x7684;&#x5bfc;&#x5165;&#x56fe;&#x7247;&#x903b;&#x8f91;</span>\n<span class=\"hljs-keyword\">for</span> image_name <span class=\"hljs-keyword\">in</span> product_data[<span class=\"hljs-string\">&quot;images&quot;</span>]:\n    <span class=\"hljs-comment\"># ...&#x590d;&#x5236;&#x56fe;&#x7247;&#x5230; dst_path...</span>\n    <span class=\"hljs-keyword\">if</span> os.path.exists(src_path):\n        shutil.copy2(src_path, dst_path)\n        relative_path = <span class=\"hljs-built_in\">str</span>(dst_path.relative_to(Path.cwd())).replace(<span class=\"hljs-string\">&quot;\\\\&quot;</span>, <span class=\"hljs-string\">&quot;/&quot;</span>)\n        copied_images.append(relative_path)\n        <span class=\"hljs-comment\"># ...&#x66f4;&#x65b0;&#x6570;&#x636e;&#x5e93;... (json.dumps(copied_images))</span>\n</code></pre>","children":[],"payload":{"tag":"li","lines":"62,74"}}],"payload":{"tag":"h4","lines":"59,60"}},{"content":"&#x56fe;&#x7247;&#x7f16;&#x53f7;&#x667a;&#x80fd;&#x8865;&#x4f4d;","children":[{"content":"<strong>&#x95ee;&#x9898;</strong>&#xff1a;&#x5220;&#x9664;&#x4e2d;&#x95f4;&#x7f16;&#x53f7;&#x7684;&#x56fe;&#x7247;&#x540e;&#xff0c;&#x65b0;&#x5efa;&#x56fe;&#x7247;&#x7f16;&#x53f7;&#x4e0d;&#x4f1a;&#x81ea;&#x52a8;&#x586b;&#x5145;&#x7a7a;&#x7f3a;&#xff0c;&#x800c;&#x662f;&#x4ece;&#x5f53;&#x524d;&#x6700;&#x5927;&#x7f16;&#x53f7;&#x7ee7;&#x7eed;&#x9012;&#x589e;&#x3002;","children":[],"payload":{"tag":"li","lines":"75,76"}},{"content":"<strong>&#x539f;&#x56e0;</strong>&#xff1a;<code>_copy_images_to_product_dir</code> &#x65b9;&#x6cd5;&#x4e2d;&#x7684; <code>start_index</code> &#x903b;&#x8f91;&#x53ea;&#x662f;&#x7b80;&#x5355;&#x5730; <code>len(existing_files) + 1</code>&#x3002;","children":[],"payload":{"tag":"li","lines":"76,77"}},{"content":"<strong>&#x89e3;&#x51b3;&#x65b9;&#x6848;</strong>&#xff1a;&#x65b0;&#x589e; <code>_find_next_available_index</code> &#x65b9;&#x6cd5;&#xff0c;&#x626b;&#x63cf;&#x73b0;&#x6709;&#x56fe;&#x7247;&#x6587;&#x4ef6;&#x540d;&#xff0c;&#x627e;&#x5230;&#x6700;&#x5c0f;&#x53ef;&#x7528;&#x7684;&#x7f16;&#x53f7;&#x3002;<pre data-lines=\"78,90\"><code class=\"language-python\"><span class=\"hljs-comment\"># models/database.py &#x65b0;&#x589e;&#x65b9;&#x6cd5;</span>\n<span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">_find_next_available_index</span>(<span class=\"hljs-params\">self, product_dir: Path, product_name: <span class=\"hljs-built_in\">str</span></span>) -&gt; <span class=\"hljs-built_in\">int</span>:\n    <span class=\"hljs-comment\"># ... &#x626b;&#x63cf; product_dir &#x4e0b;&#x7684;&#x6587;&#x4ef6;&#xff0c;&#x63d0;&#x53d6;&#x5df2;&#x4f7f;&#x7528;&#x7684;&#x7f16;&#x53f7; ...</span>\n    <span class=\"hljs-comment\"># ... &#x5bfb;&#x627e;&#x6700;&#x5c0f;&#x7684;&#x672a;&#x88ab;&#x4f7f;&#x7528;&#x7684;&#x7f16;&#x53f7;&#xff08;&#x4ece;1&#x5f00;&#x59cb;&#xff09;...</span>\n    <span class=\"hljs-keyword\">return</span> index\n\n<span class=\"hljs-comment\"># models/database.py _copy_images_to_product_dir &#x65b9;&#x6cd5;&#x4e2d;&#x66f4;&#x65b0;</span>\n<span class=\"hljs-comment\"># &#x5c06; `start_index = len(existing_files) + 1` &#x66ff;&#x6362;&#x4e3a;&#xff1a;</span>\navailable_index = <span class=\"hljs-variable language_\">self</span>._find_next_available_index(product_dir, product_name)\n<span class=\"hljs-comment\"># &#x7136;&#x540e;&#x5728;&#x5faa;&#x73af;&#x4e2d;&#x4f7f;&#x7528; available_index &#x9012;&#x589e;&#x751f;&#x6210;&#x6587;&#x4ef6;&#x540d;</span>\n</code></pre>","children":[],"payload":{"tag":"li","lines":"77,91"}}],"payload":{"tag":"h4","lines":"74,75"}}],"payload":{"tag":"h3","lines":"4,5"}},{"content":"&#x1f3f7;&#xfe0f; &#x6807;&#x7b7e;&#x7ba1;&#x7406;&#x529f;&#x80fd;&#x589e;&#x5f3a;","children":[{"content":"&#x53f3;&#x952e;&#x5220;&#x9664;&#x6807;&#x7b7e;&#x529f;&#x80fd;","children":[{"content":"<strong>&#x65b0;&#x589e;&#x529f;&#x80fd;</strong>&#xff1a;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x53f3;&#x952e;&#x83dc;&#x5355;&#x6dfb;&#x52a0;&quot;&#x5220;&#x9664;&#x6807;&#x7b7e;&quot;&#x5b50;&#x83dc;&#x5355;","children":[],"payload":{"tag":"li","lines":"94,95"}},{"content":"<strong>&#x6280;&#x672f;&#x5b9e;&#x73b0;</strong>&#xff1a;<pre data-lines=\"96,106\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x5728;show_context_menu&#x4e2d;&#x6dfb;&#x52a0;&#x5220;&#x9664;&#x6807;&#x7b7e;&#x83dc;&#x5355;</span>\n<span class=\"hljs-keyword\">if</span> product.tags:\n    remove_tag_menu = menu.addMenu(<span class=\"hljs-string\">&quot;&#x5220;&#x9664;&#x6807;&#x7b7e;&quot;</span>)\n    <span class=\"hljs-keyword\">for</span> tag <span class=\"hljs-keyword\">in</span> product.tags:\n        remove_action = remove_tag_menu.addAction(<span class=\"hljs-string\">f&quot;&#x79fb;&#x9664; <span class=\"hljs-subst\">{tag.name}</span>&quot;</span>)\n        remove_action.triggered.connect(\n            <span class=\"hljs-keyword\">lambda</span> checked, t_id=tag.<span class=\"hljs-built_in\">id</span>: <span class=\"hljs-variable language_\">self</span>.remove_tag_from_product(product_id, t_id)\n        )\n</code></pre>","children":[],"payload":{"tag":"li","lines":"95,107"}}],"payload":{"tag":"h4","lines":"93,94"}},{"content":"&#x6807;&#x7b7e;&#x64cd;&#x4f5c;&#x4f18;&#x5316;","children":[{"content":"<strong>&#x667a;&#x80fd;&#x663e;&#x793a;</strong>&#xff1a;&#x5220;&#x9664;&#x83dc;&#x5355;&#x53ea;&#x663e;&#x793a;&#x5f53;&#x524d;&#x4ea7;&#x54c1;&#x5df2;&#x5173;&#x8054;&#x7684;&#x6807;&#x7b7e;","children":[],"payload":{"tag":"li","lines":"108,109"}},{"content":"<strong>&#x7528;&#x6237;&#x4f53;&#x9a8c;</strong>&#xff1a;&#x5220;&#x9664;&#x64cd;&#x4f5c;&#x663e;&#x793a;&#x6807;&#x7b7e;&#x540d;&#x79f0;&#x800c;&#x975e;ID","children":[],"payload":{"tag":"li","lines":"109,110"}},{"content":"<strong>&#x5b9e;&#x65f6;&#x66f4;&#x65b0;</strong>&#xff1a;&#x5220;&#x9664;&#x540e;&#x81ea;&#x52a8;&#x5237;&#x65b0;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x548c;&#x6807;&#x7b7e;&#x7ec4;&#x4ef6;","children":[],"payload":{"tag":"li","lines":"110,112"}}],"payload":{"tag":"h4","lines":"107,108"}}],"payload":{"tag":"h3","lines":"91,92"}},{"content":"&#x1f4c1; &#x667a;&#x80fd;&#x5bfc;&#x5165;&#x5bfc;&#x51fa;&#x7cfb;&#x7edf;","children":[{"content":"&#x5bfc;&#x5165;&#x9009;&#x62e9;&#x754c;&#x9762;&#x91cd;&#x6784;","children":[{"content":"<strong>&#x65b0;&#x589e;&#x7ec4;&#x4ef6;</strong>&#xff1a;<code>ImportSelectionDialog</code>","children":[],"payload":{"tag":"li","lines":"115,116"}},{"content":"<strong>&#x6838;&#x5fc3;&#x529f;&#x80fd;</strong>&#xff1a;<pre data-lines=\"117,131\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">ImportSelectionDialog</span>(<span class=\"hljs-title class_ inherited__\">QDialog</span>):\n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">load_export_folders</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x626b;&#x63cf;&#x5e76;&#x52a0;&#x8f7d;&#x5bfc;&#x51fa;&#x7248;&#x672c;&#x5217;&#x8868;&quot;&quot;&quot;</span>\n        export_dir = Path(<span class=\"hljs-string\">&quot;export&quot;</span>)\n        <span class=\"hljs-keyword\">for</span> folder <span class=\"hljs-keyword\">in</span> export_dir.iterdir():\n            <span class=\"hljs-keyword\">if</span> folder.is_dir() <span class=\"hljs-keyword\">and</span> folder.name.startswith(<span class=\"hljs-string\">&quot;&#x4ea7;&#x54c1;&#x6570;&#x636e;&#x5bfc;&#x51fa;_&quot;</span>):\n                <span class=\"hljs-comment\"># &#x8bfb;&#x53d6;&#x7248;&#x672c;&#x4fe1;&#x606f;</span>\n                data_file = folder / <span class=\"hljs-string\">&quot;data.json&quot;</span>\n                <span class=\"hljs-keyword\">if</span> data_file.exists():\n                    <span class=\"hljs-keyword\">with</span> <span class=\"hljs-built_in\">open</span>(data_file, <span class=\"hljs-string\">&apos;r&apos;</span>, encoding=<span class=\"hljs-string\">&apos;utf-8&apos;</span>) <span class=\"hljs-keyword\">as</span> f:\n                        data = json.load(f)\n                    <span class=\"hljs-comment\"># &#x89e3;&#x6790;&#x5e76;&#x663e;&#x793a;&#x7248;&#x672c;&#x4fe1;&#x606f;</span>\n</code></pre>","children":[],"payload":{"tag":"li","lines":"116,132"}}],"payload":{"tag":"h4","lines":"114,115"}},{"content":"&#x7248;&#x672c;&#x4fe1;&#x606f;&#x5c55;&#x793a;","children":[{"content":"<strong>&#x663e;&#x793a;&#x5185;&#x5bb9;</strong>&#xff1a;","children":[{"content":"&#x5bfc;&#x51fa;&#x65f6;&#x95f4;&#xff08;&#x683c;&#x5f0f;&#x5316;&#x663e;&#x793a;&#xff09;","children":[],"payload":{"tag":"li","lines":"134,135"}},{"content":"&#x4ea7;&#x54c1;&#x6570;&#x91cf;","children":[],"payload":{"tag":"li","lines":"135,136"}},{"content":"&#x6807;&#x7b7e;&#x6570;&#x91cf;","children":[],"payload":{"tag":"li","lines":"136,137"}},{"content":"&#x6587;&#x4ef6;&#x5939;&#x540d;&#x79f0;","children":[],"payload":{"tag":"li","lines":"137,138"}}],"payload":{"tag":"li","lines":"133,138"}},{"content":"<strong>&#x6392;&#x5e8f;&#x673a;&#x5236;</strong>&#xff1a;&#x6309;&#x65f6;&#x95f4;&#x6233;&#x964d;&#x5e8f;&#x6392;&#x5217;&#xff0c;&#x6700;&#x65b0;&#x7248;&#x672c;&#x5728;&#x524d;","children":[],"payload":{"tag":"li","lines":"138,140"}}],"payload":{"tag":"h4","lines":"132,133"}},{"content":"&#x5220;&#x9664;&#x5bfc;&#x51fa;&#x7248;&#x672c;&#x529f;&#x80fd;","children":[{"content":"<strong>&#x5b89;&#x5168;&#x5220;&#x9664;</strong>&#xff1a;&#x9700;&#x8981;&#x7528;&#x6237;&#x786e;&#x8ba4;","children":[],"payload":{"tag":"li","lines":"141,142"}},{"content":"<strong>&#x6280;&#x672f;&#x5b9e;&#x73b0;</strong>&#xff1a;<pre data-lines=\"143,151\"><code class=\"language-python\"><span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">delete_selected_export</span>(<span class=\"hljs-params\">self</span>):\n    reply = QMessageBox.question(<span class=\"hljs-variable language_\">self</span>, <span class=\"hljs-string\">&quot;&#x786e;&#x8ba4;&#x5220;&#x9664;&quot;</span>, \n                                 <span class=\"hljs-string\">f&quot;&#x786e;&#x5b9a;&#x8981;&#x5220;&#x9664;&#x5bfc;&#x51fa;&#x7248;&#x672c; &apos;<span class=\"hljs-subst\">{folder_name}</span>&apos; &#x5417;&#xff1f;&quot;</span>)\n    <span class=\"hljs-keyword\">if</span> reply == QMessageBox.StandardButton.Yes:\n        shutil.rmtree(folder_path)\n        <span class=\"hljs-variable language_\">self</span>.load_export_folders()  <span class=\"hljs-comment\"># &#x5237;&#x65b0;&#x5217;&#x8868;</span>\n</code></pre>","children":[],"payload":{"tag":"li","lines":"142,152"}}],"payload":{"tag":"h4","lines":"140,141"}}],"payload":{"tag":"h3","lines":"112,113"}},{"content":"&#x1f4ca; &#x5b8c;&#x6574;&#x65e5;&#x5fd7;&#x7cfb;&#x7edf;","children":[{"content":"&#x65e5;&#x5fd7;&#x914d;&#x7f6e;&#x67b6;&#x6784;","children":[{"content":"<strong>&#x914d;&#x7f6e;&#x6587;&#x4ef6;</strong>&#xff1a;<code>config.py</code>&#x4e2d;&#x65b0;&#x589e;<code>LOG_CONFIG</code>","children":[],"payload":{"tag":"li","lines":"155,156"}},{"content":"<strong>&#x914d;&#x7f6e;&#x53c2;&#x6570;</strong>&#xff1a;<pre data-lines=\"157,168\"><code class=\"language-python\">LOG_CONFIG = {\n    <span class=\"hljs-string\">&quot;level&quot;</span>: <span class=\"hljs-string\">&quot;INFO&quot;</span>,\n    <span class=\"hljs-string\">&quot;file_path&quot;</span>: DATA_DIR / <span class=\"hljs-string\">&quot;logs&quot;</span> / <span class=\"hljs-string\">&quot;app.log&quot;</span>,\n    <span class=\"hljs-string\">&quot;max_file_size&quot;</span>: <span class=\"hljs-number\">10</span> * <span class=\"hljs-number\">1024</span> * <span class=\"hljs-number\">1024</span>,  <span class=\"hljs-comment\"># 10MB</span>\n    <span class=\"hljs-string\">&quot;backup_count&quot;</span>: <span class=\"hljs-number\">5</span>,\n    <span class=\"hljs-string\">&quot;format&quot;</span>: <span class=\"hljs-string\">&quot;%(asctime)s - %(name)s - %(levelname)s - %(message)s&quot;</span>,\n    <span class=\"hljs-string\">&quot;console_output&quot;</span>: <span class=\"hljs-literal\">True</span>,\n    <span class=\"hljs-string\">&quot;file_output&quot;</span>: <span class=\"hljs-literal\">True</span>,\n}\n</code></pre>","children":[],"payload":{"tag":"li","lines":"156,169"}}],"payload":{"tag":"h4","lines":"154,155"}},{"content":"&#x65e5;&#x5fd7;&#x7cfb;&#x7edf;&#x521d;&#x59cb;&#x5316;","children":[{"content":"<strong>&#x542f;&#x52a8;&#x65f6;&#x521d;&#x59cb;&#x5316;</strong>&#xff1a;&#x5728;<code>main.py</code>&#x4e2d;&#x6dfb;&#x52a0;<code>setup_logging()</code>&#x51fd;&#x6570;","children":[],"payload":{"tag":"li","lines":"170,171"}},{"content":"<strong>&#x53cc;&#x91cd;&#x8f93;&#x51fa;</strong>&#xff1a;&#x540c;&#x65f6;&#x8f93;&#x51fa;&#x5230;&#x63a7;&#x5236;&#x53f0;&#x548c;&#x6587;&#x4ef6;","children":[],"payload":{"tag":"li","lines":"171,172"}},{"content":"<strong>&#x8f6e;&#x8f6c;&#x7ba1;&#x7406;</strong>&#xff1a;&#x4f7f;&#x7528;<code>RotatingFileHandler</code>&#x81ea;&#x52a8;&#x7ba1;&#x7406;&#x65e5;&#x5fd7;&#x6587;&#x4ef6;&#x5927;&#x5c0f;","children":[],"payload":{"tag":"li","lines":"172,174"}}],"payload":{"tag":"h4","lines":"169,170"}},{"content":"&#x5bfc;&#x5165;&#x8fc7;&#x7a0b;&#x65e5;&#x5fd7;&#x8bb0;&#x5f55;","children":[{"content":"<strong>&#x8be6;&#x7ec6;&#x8bb0;&#x5f55;</strong>&#xff1a;<pre data-lines=\"176,182\"><code class=\"language-python\">logger.info(<span class=\"hljs-string\">f&quot;&#x5f00;&#x59cb;&#x5bfc;&#x5165;&#x6570;&#x636e; - &#x6587;&#x4ef6;&#x5939;: <span class=\"hljs-subst\">{folder_path}</span>&quot;</span>)\nlogger.info(<span class=\"hljs-string\">f&quot;&#x6570;&#x636e;&#x6587;&#x4ef6;&#x8bfb;&#x53d6;&#x6210;&#x529f; - &#x4ea7;&#x54c1;&#x6570;&#x91cf;: <span class=\"hljs-subst\">{<span class=\"hljs-built_in\">len</span>(products)}</span>, &#x6807;&#x7b7e;&#x6570;&#x91cf;: <span class=\"hljs-subst\">{<span class=\"hljs-built_in\">len</span>(tags)}</span>&quot;</span>)\nlogger.info(<span class=\"hljs-string\">f&quot;&#x5bfc;&#x5165;&#x4ea7;&#x54c1;: <span class=\"hljs-subst\">{product_data[<span class=\"hljs-string\">&apos;name&apos;</span>]}</span>&quot;</span>)\nlogger.info(<span class=\"hljs-string\">f&quot;&#x590d;&#x5236;&#x56fe;&#x7247;: <span class=\"hljs-subst\">{image_name}</span>&quot;</span>)\n</code></pre>","children":[],"payload":{"tag":"li","lines":"175,183"}}],"payload":{"tag":"h4","lines":"174,175"}}],"payload":{"tag":"h3","lines":"152,153"}},{"content":"&#x1f3a8; &#x7528;&#x6237;&#x4f53;&#x9a8c;&#x4f18;&#x5316;","children":[{"content":"&#x5bfc;&#x5165;&#x754c;&#x9762;&#x6539;&#x8fdb;","children":[{"content":"<strong>&#x8fdb;&#x5ea6;&#x663e;&#x793a;</strong>&#xff1a;&#x8be6;&#x7ec6;&#x7684;&#x5bfc;&#x5165;&#x8fdb;&#x5ea6;&#x548c;&#x72b6;&#x6001;&#x4fe1;&#x606f;","children":[],"payload":{"tag":"li","lines":"186,187"}},{"content":"<strong>&#x9519;&#x8bef;&#x5904;&#x7406;</strong>&#xff1a;&#x53cb;&#x597d;&#x7684;&#x9519;&#x8bef;&#x63d0;&#x793a;&#x548c;&#x786e;&#x8ba4;&#x673a;&#x5236;","children":[],"payload":{"tag":"li","lines":"187,188"}},{"content":"<strong>&#x64cd;&#x4f5c;&#x5b89;&#x5168;</strong>&#xff1a;&#x5220;&#x9664;&#x64cd;&#x4f5c;&#x9700;&#x8981;&#x7528;&#x6237;&#x786e;&#x8ba4;","children":[],"payload":{"tag":"li","lines":"188,190"}}],"payload":{"tag":"h4","lines":"185,186"}},{"content":"&#x754c;&#x9762;&#x54cd;&#x5e94;&#x4f18;&#x5316;","children":[{"content":"<strong>&#x5f02;&#x6b65;&#x64cd;&#x4f5c;</strong>&#xff1a;&#x5bfc;&#x5165;&#x5bfc;&#x51fa;&#x8fc7;&#x7a0b;&#x4e0d;&#x963b;&#x585e;&#x754c;&#x9762;","children":[],"payload":{"tag":"li","lines":"191,192"}},{"content":"<strong>&#x5b9e;&#x65f6;&#x53cd;&#x9988;</strong>&#xff1a;&#x64cd;&#x4f5c;&#x72b6;&#x6001;&#x5b9e;&#x65f6;&#x663e;&#x793a;","children":[],"payload":{"tag":"li","lines":"192,193"}},{"content":"<strong>&#x667a;&#x80fd;&#x63d0;&#x793a;</strong>&#xff1a;&#x6839;&#x636e;&#x64cd;&#x4f5c;&#x7ed3;&#x679c;&#x7ed9;&#x51fa;&#x76f8;&#x5e94;&#x63d0;&#x793a;","children":[],"payload":{"tag":"li","lines":"193,195"}}],"payload":{"tag":"h4","lines":"190,191"}}],"payload":{"tag":"h3","lines":"183,184"}}],"payload":{"tag":"h2","lines":"2,3"}},{"content":"&#x7248;&#x672c;&#x66f4;&#x65b0; (v2.1.6)","children":[{"content":"&#x1f3f7;&#xfe0f; &#x6807;&#x7b7e;&#x7cfb;&#x7edf;&#x67b6;&#x6784;&#x8bbe;&#x8ba1;","children":[{"content":"&#x6570;&#x636e;&#x6a21;&#x578b;&#x8bbe;&#x8ba1;","children":[{"content":"\n<p data-lines=\"200,201\"><strong>Tag&#x6a21;&#x578b;</strong>&#xff1a;<code>models/tag.py</code></p>","children":[{"content":"&#x6807;&#x7b7e;&#x57fa;&#x672c;&#x4fe1;&#x606f;&#xff1a;id&#x3001;name&#x3001;color&#x3001;description","children":[],"payload":{"tag":"li","lines":"201,202"}},{"content":"&#x7edf;&#x8ba1;&#x4fe1;&#x606f;&#xff1a;usage_count&#x3001;created_at&#x3001;updated_at","children":[],"payload":{"tag":"li","lines":"202,203"}},{"content":"&#x652f;&#x6301;&#x81ea;&#x5b9a;&#x4e49;&#x989c;&#x8272;&#x9009;&#x62e9;&#x548c;&#x4f7f;&#x7528;&#x7edf;&#x8ba1;","children":[],"payload":{"tag":"li","lines":"203,205"}}],"payload":{"tag":"li","lines":"200,205"}},{"content":"\n<p data-lines=\"205,206\"><strong>ProductTag&#x5173;&#x8054;&#x6a21;&#x578b;</strong>&#xff1a;<code>models/product_tag.py</code></p>","children":[{"content":"&#x591a;&#x5bf9;&#x591a;&#x5173;&#x7cfb;&#xff1a;product_id&#x3001;tag_id","children":[],"payload":{"tag":"li","lines":"206,207"}},{"content":"&#x5173;&#x8054;&#x65f6;&#x95f4;&#xff1a;created_at","children":[],"payload":{"tag":"li","lines":"207,209"}}],"payload":{"tag":"li","lines":"205,209"}}],"payload":{"tag":"h4","lines":"199,200"}},{"content":"&#x670d;&#x52a1;&#x5c42;&#x67b6;&#x6784;","children":[{"content":"<strong>TagService</strong>&#xff1a;<code>models/database.py</code>","children":[{"content":"&#x6807;&#x7b7e;CRUD&#x64cd;&#x4f5c;&#xff1a;&#x521b;&#x5efa;&#x3001;&#x8bfb;&#x53d6;&#x3001;&#x66f4;&#x65b0;&#x3001;&#x5220;&#x9664;","children":[],"payload":{"tag":"li","lines":"211,212"}},{"content":"&#x7edf;&#x8ba1;&#x8ba1;&#x7b97;&#xff1a;&#x66f4;&#x65b0;&#x6807;&#x7b7e;&#x4f7f;&#x7528;&#x6b21;&#x6570;","children":[],"payload":{"tag":"li","lines":"212,213"}},{"content":"&#x4ea7;&#x54c1;&#x5173;&#x8054;&#xff1a;&#x7ba1;&#x7406;&#x4ea7;&#x54c1;-&#x6807;&#x7b7e;&#x5173;&#x7cfb;","children":[],"payload":{"tag":"li","lines":"213,215"}}],"payload":{"tag":"li","lines":"210,215"}}],"payload":{"tag":"h4","lines":"209,210"}},{"content":"&#x754c;&#x9762;&#x7ec4;&#x4ef6;&#x8bbe;&#x8ba1;","children":[{"content":"\n<p data-lines=\"216,217\"><strong>TagWidget</strong>&#xff1a;<code>views/tag_widget.py</code></p>","children":[{"content":"&#x6807;&#x7b7e;&#x6d41;&#x5f0f;&#x5e03;&#x5c40;&#x663e;&#x793a;","children":[],"payload":{"tag":"li","lines":"217,218"}},{"content":"&#x652f;&#x6301;&#x6697;&#x9ed1;/&#x660e;&#x4eae;&#x4e3b;&#x9898;&#x5207;&#x6362;","children":[],"payload":{"tag":"li","lines":"218,219"}},{"content":"&#x6807;&#x7b7e;&#x70b9;&#x51fb;&#x7b5b;&#x9009;&#x529f;&#x80fd;","children":[],"payload":{"tag":"li","lines":"219,220"}},{"content":"&#x7ba1;&#x7406;&#x548c;&#x6e05;&#x9664;&#x6309;&#x94ae;","children":[],"payload":{"tag":"li","lines":"220,222"}}],"payload":{"tag":"li","lines":"216,222"}},{"content":"\n<p data-lines=\"222,223\"><strong>TagButton</strong>&#xff1a;&#x81ea;&#x5b9a;&#x4e49;&#x6807;&#x7b7e;&#x6309;&#x94ae;&#x7ec4;&#x4ef6;</p>","children":[{"content":"&#x6e10;&#x53d8;&#x80cc;&#x666f;&#x6548;&#x679c;","children":[],"payload":{"tag":"li","lines":"223,224"}},{"content":"&#x60ac;&#x505c;&#x548c;&#x9009;&#x4e2d;&#x72b6;&#x6001;","children":[],"payload":{"tag":"li","lines":"224,225"}},{"content":"&#x53f3;&#x952e;&#x83dc;&#x5355;&#x64cd;&#x4f5c;","children":[],"payload":{"tag":"li","lines":"225,227"}}],"payload":{"tag":"li","lines":"222,227"}},{"content":"\n<p data-lines=\"227,228\"><strong>TagManagerDialog</strong>&#xff1a;<code>views/dialogs/tag_manager_dialog.py</code></p>","children":[{"content":"&#x6807;&#x7b7e;&#x7ba1;&#x7406;&#x754c;&#x9762;","children":[],"payload":{"tag":"li","lines":"228,229"}},{"content":"&#x989c;&#x8272;&#x9009;&#x62e9;&#x5668;","children":[],"payload":{"tag":"li","lines":"229,230"}},{"content":"&#x4f7f;&#x7528;&#x7edf;&#x8ba1;&#x663e;&#x793a;","children":[],"payload":{"tag":"li","lines":"230,232"}}],"payload":{"tag":"li","lines":"227,232"}}],"payload":{"tag":"h4","lines":"215,216"}}],"payload":{"tag":"h3","lines":"197,198"}},{"content":"&#x1f4c1; &#x5bfc;&#x51fa;&#x5bfc;&#x5165;&#x7cfb;&#x7edf;&#x8bbe;&#x8ba1;","children":[{"content":"&#x6570;&#x636e;&#x7ed3;&#x6784;&#x8bbe;&#x8ba1;","children":[{"content":"<pre data-lines=\"235,251\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x5bfc;&#x51fa;&#x6570;&#x636e;&#x7ed3;&#x6784;</span>\nexport_data = {\n    <span class=\"hljs-string\">&quot;metadata&quot;</span>: {\n        <span class=\"hljs-string\">&quot;export_date&quot;</span>: <span class=\"hljs-string\">&quot;2025-01-13T10:30:00Z&quot;</span>,\n        <span class=\"hljs-string\">&quot;app_version&quot;</span>: <span class=\"hljs-string\">&quot;2.1.6&quot;</span>,\n        <span class=\"hljs-string\">&quot;total_products&quot;</span>: <span class=\"hljs-number\">10</span>,\n        <span class=\"hljs-string\">&quot;total_sources&quot;</span>: <span class=\"hljs-number\">25</span>,\n        <span class=\"hljs-string\">&quot;total_tags&quot;</span>: <span class=\"hljs-number\">8</span>\n    },\n    <span class=\"hljs-string\">&quot;products&quot;</span>: [...],\n    <span class=\"hljs-string\">&quot;sources&quot;</span>: [...], \n    <span class=\"hljs-string\">&quot;tags&quot;</span>: [...],\n    <span class=\"hljs-string\">&quot;product_tags&quot;</span>: [...]\n}\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"235,251"}}],"payload":{"tag":"h4","lines":"234,235"}},{"content":"&#x6587;&#x4ef6;&#x5939;&#x7ed3;&#x6784;","children":[{"content":"<pre data-lines=\"253,261\"><code data-lines=\"253,261\">&#x5bfc;&#x51fa;&#x6587;&#x4ef6;&#x5939;<span class=\"hljs-symbol\">/</span>\n&#x251c;&#x2500;&#x2500; data.json          <span class=\"hljs-comment\"># &#x4e3b;&#x6570;&#x636e;&#x6587;&#x4ef6;</span>\n&#x251c;&#x2500;&#x2500; images<span class=\"hljs-symbol\">/</span>            <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x6587;&#x4ef6;&#x5939;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; products<span class=\"hljs-symbol\">/</span>      <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x56fe;&#x7247;</span>\n&#x2502;   &#x2514;&#x2500;&#x2500; ...\n&#x2514;&#x2500;&#x2500; README.txt         <span class=\"hljs-comment\"># &#x8bf4;&#x660e;&#x6587;&#x4ef6;</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"253,261"}}],"payload":{"tag":"h4","lines":"252,253"}},{"content":"&#x5bfc;&#x5165;&#x5bfc;&#x51fa;&#x6d41;&#x7a0b;","children":[{"content":"1. \n<p data-lines=\"263,264\"><strong>&#x5bfc;&#x51fa;&#x6d41;&#x7a0b;</strong>&#xff1a;</p>","children":[{"content":"&#x5e8f;&#x5217;&#x5316;&#x6240;&#x6709;&#x6570;&#x636e;&#x5230;JSON","children":[],"payload":{"tag":"li","lines":"264,265"}},{"content":"&#x590d;&#x5236;&#x56fe;&#x7247;&#x6587;&#x4ef6;&#x5230;&#x5bfc;&#x51fa;&#x76ee;&#x5f55;","children":[],"payload":{"tag":"li","lines":"265,266"}},{"content":"&#x751f;&#x6210;README&#x8bf4;&#x660e;&#x6587;&#x4ef6;","children":[],"payload":{"tag":"li","lines":"266,267"}},{"content":"&#x663e;&#x793a;&#x8fdb;&#x5ea6;&#x6761;","children":[],"payload":{"tag":"li","lines":"267,269"}}],"payload":{"tag":"li","lines":"263,269","listIndex":1}},{"content":"2. \n<p data-lines=\"269,270\"><strong>&#x5bfc;&#x5165;&#x6d41;&#x7a0b;</strong>&#xff1a;</p>","children":[{"content":"&#x9a8c;&#x8bc1;&#x6587;&#x4ef6;&#x5939;&#x7ed3;&#x6784;","children":[],"payload":{"tag":"li","lines":"270,271"}},{"content":"&#x89e3;&#x6790;JSON&#x6570;&#x636e;","children":[],"payload":{"tag":"li","lines":"271,272"}},{"content":"&#x6062;&#x590d;&#x56fe;&#x7247;&#x6587;&#x4ef6;","children":[],"payload":{"tag":"li","lines":"272,273"}},{"content":"&#x91cd;&#x5efa;&#x6570;&#x636e;&#x5e93;&#x5173;&#x7cfb;","children":[],"payload":{"tag":"li","lines":"273,275"}}],"payload":{"tag":"li","lines":"269,275","listIndex":2}}],"payload":{"tag":"h4","lines":"262,263"}}],"payload":{"tag":"h3","lines":"232,233"}},{"content":"&#x1f3a8; &#x754c;&#x9762;&#x7f8e;&#x5316;&#x67b6;&#x6784;","children":[{"content":"&#x4e3b;&#x9898;&#x7cfb;&#x7edf;&#x5347;&#x7ea7;","children":[{"content":"\n<p data-lines=\"278,279\"><strong>&#x7edf;&#x4e00;&#x6837;&#x5f0f;&#x7ba1;&#x7406;</strong>&#xff1a;</p>","children":[{"content":"&#x6807;&#x7b7e;&#x7ec4;&#x4ef6;&#x652f;&#x6301;&#x4e3b;&#x9898;&#x5207;&#x6362;","children":[],"payload":{"tag":"li","lines":"279,280"}},{"content":"&#x6309;&#x94ae;&#x6837;&#x5f0f;&#x52a8;&#x6001;&#x66f4;&#x65b0;","children":[],"payload":{"tag":"li","lines":"280,281"}},{"content":"&#x6eda;&#x52a8;&#x533a;&#x57df;&#x4e3b;&#x9898;&#x9002;&#x914d;","children":[],"payload":{"tag":"li","lines":"281,283"}}],"payload":{"tag":"li","lines":"278,283"}},{"content":"\n<p data-lines=\"283,284\"><strong>&#x6e10;&#x53d8;&#x6548;&#x679c;&#x5b9e;&#x73b0;</strong>&#xff1a;</p>\n<pre data-lines=\"284,290\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x6807;&#x7b7e;&#x6e10;&#x53d8;&#x80cc;&#x666f;</span>\nbackground: qlineargradient(x1: <span class=\"hljs-number\">0</span>, y1: <span class=\"hljs-number\">0</span>, x2: <span class=\"hljs-number\">0</span>, y2: <span class=\"hljs-number\">1</span>,\n    stop: <span class=\"hljs-number\">0</span> {tag.color},\n    stop: <span class=\"hljs-number\">1</span> {darken_color(tag.color, <span class=\"hljs-number\">0.15</span>)})\n</code></pre>","children":[],"payload":{"tag":"li","lines":"283,291"}}],"payload":{"tag":"h4","lines":"277,278"}},{"content":"&#x5e03;&#x5c40;&#x4f18;&#x5316;","children":[{"content":"<strong>&#x6807;&#x7b7e;&#x4f4d;&#x7f6e;&#x8c03;&#x6574;</strong>&#xff1a;&#x4ece;&#x4ea7;&#x54c1;&#x540d;&#x79f0;&#x4e0b;&#x65b9;&#x79fb;&#x5230;&#x4e0e;&#x8d27;&#x6e90;&#x4fe1;&#x606f;&#x5bf9;&#x9f50;","children":[],"payload":{"tag":"li","lines":"292,293"}},{"content":"<strong>&#x4ea7;&#x54c1;&#x5361;&#x7247;&#x5e03;&#x5c40;</strong>&#xff1a;&#x4f18;&#x5316;&#x4fe1;&#x606f;&#x663e;&#x793a;&#x5c42;&#x6b21;","children":[],"payload":{"tag":"li","lines":"293,294"}},{"content":"<strong>&#x72b6;&#x6001;&#x680f;&#x56fe;&#x6807;&#x5316;</strong>&#xff1a;&#x6dfb;&#x52a0;&#x89c6;&#x89c9;&#x56fe;&#x6807;&#x63d0;&#x5347;&#x7528;&#x6237;&#x4f53;&#x9a8c;","children":[],"payload":{"tag":"li","lines":"294,296"}}],"payload":{"tag":"h4","lines":"291,292"}}],"payload":{"tag":"h3","lines":"275,276"}},{"content":"&#x1f527; &#x6570;&#x636e;&#x5904;&#x7406;&#x4f18;&#x5316;","children":[{"content":"&#x6027;&#x80fd;&#x4f18;&#x5316;","children":[{"content":"<strong>&#x5173;&#x8054;&#x6570;&#x636e;&#x52a0;&#x8f7d;</strong>&#xff1a;&#x4fee;&#x590d;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x6b63;&#x786e;&#x52a0;&#x8f7d;&#x8d27;&#x6e90;&#x548c;&#x6807;&#x7b7e;","children":[],"payload":{"tag":"li","lines":"299,300"}},{"content":"<strong>&#x7edf;&#x8ba1;&#x8ba1;&#x7b97;&#x4f18;&#x5316;</strong>&#xff1a;&#x8d27;&#x6e90;&#x6570;&#x91cf;&#x7edf;&#x8ba1;&#x4fee;&#x590d;","children":[],"payload":{"tag":"li","lines":"300,301"}},{"content":"<strong>&#x7b5b;&#x9009;&#x529f;&#x80fd;&#x589e;&#x5f3a;</strong>&#xff1a;&#x652f;&#x6301;&#x591a;&#x6807;&#x7b7e;&#x7ec4;&#x5408;&#x7b5b;&#x9009;","children":[],"payload":{"tag":"li","lines":"301,303"}}],"payload":{"tag":"h4","lines":"298,299"}},{"content":"&#x6570;&#x636e;&#x5e93;&#x7ed3;&#x6784;&#x5347;&#x7ea7;","children":[{"content":"<pre data-lines=\"304,326\"><code class=\"language-sql\"><span class=\"hljs-comment\">-- &#x65b0;&#x589e;&#x6807;&#x7b7e;&#x8868;</span>\n<span class=\"hljs-keyword\">CREATE TABLE</span> tags (\n    id <span class=\"hljs-type\">INTEGER</span> <span class=\"hljs-keyword\">PRIMARY KEY</span> AUTOINCREMENT,\n    name TEXT <span class=\"hljs-keyword\">NOT NULL</span> <span class=\"hljs-keyword\">UNIQUE</span>,\n    color TEXT <span class=\"hljs-keyword\">NOT NULL</span> <span class=\"hljs-keyword\">DEFAULT</span> <span class=\"hljs-string\">&apos;#007bff&apos;</span>,\n    description TEXT,\n    usage_count <span class=\"hljs-type\">INTEGER</span> <span class=\"hljs-keyword\">DEFAULT</span> <span class=\"hljs-number\">0</span>,\n    created_at <span class=\"hljs-type\">TIMESTAMP</span> <span class=\"hljs-keyword\">DEFAULT</span> <span class=\"hljs-built_in\">CURRENT_TIMESTAMP</span>,\n    updated_at <span class=\"hljs-type\">TIMESTAMP</span> <span class=\"hljs-keyword\">DEFAULT</span> <span class=\"hljs-built_in\">CURRENT_TIMESTAMP</span>\n);\n\n<span class=\"hljs-comment\">-- &#x65b0;&#x589e;&#x4ea7;&#x54c1;&#x6807;&#x7b7e;&#x5173;&#x8054;&#x8868;</span>\n<span class=\"hljs-keyword\">CREATE TABLE</span> product_tags (\n    product_id <span class=\"hljs-type\">INTEGER</span>,\n    tag_id <span class=\"hljs-type\">INTEGER</span>,\n    created_at <span class=\"hljs-type\">TIMESTAMP</span> <span class=\"hljs-keyword\">DEFAULT</span> <span class=\"hljs-built_in\">CURRENT_TIMESTAMP</span>,\n    <span class=\"hljs-keyword\">PRIMARY KEY</span> (product_id, tag_id),\n    <span class=\"hljs-keyword\">FOREIGN KEY</span> (product_id) <span class=\"hljs-keyword\">REFERENCES</span> products(id) <span class=\"hljs-keyword\">ON</span> <span class=\"hljs-keyword\">DELETE</span> CASCADE,\n    <span class=\"hljs-keyword\">FOREIGN KEY</span> (tag_id) <span class=\"hljs-keyword\">REFERENCES</span> tags(id) <span class=\"hljs-keyword\">ON</span> <span class=\"hljs-keyword\">DELETE</span> CASCADE\n);\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"304,326"}}],"payload":{"tag":"h4","lines":"303,304"}}],"payload":{"tag":"h3","lines":"296,297"}}],"payload":{"tag":"h2","lines":"195,196"}},{"content":"&#x7248;&#x672c;&#x66f4;&#x65b0; (v2.0.8)","children":[{"content":"&#x1f5bc;&#xfe0f; &#x56fe;&#x7247;&#x7ba1;&#x7406;&#x529f;&#x80fd;&#x5b8c;&#x5584;","children":[{"content":"&#x6838;&#x5fc3;&#x56fe;&#x7247;&#x590d;&#x5236;&#x903b;&#x8f91;","children":[{"content":"<strong>&#x76ee;&#x7684;</strong>&#xff1a;&#x786e;&#x4fdd;&#x56fe;&#x7247;&#x6587;&#x4ef6;&#x88ab;&#x590d;&#x5236;&#x5230;&#x9879;&#x76ee;&#x5185;&#x90e8;&#xff0c;&#x89e3;&#x51b3;&#x539f;&#x56fe;&#x8def;&#x5f84;&#x53d8;&#x5316;&#x5bfc;&#x81f4;&#x56fe;&#x7247;&#x4e22;&#x5931;&#x7684;&#x95ee;&#x9898;&#x3002;","children":[],"payload":{"tag":"li","lines":"332,333"}},{"content":"<strong>&#x65b9;&#x6848;</strong>&#xff1a;&#x5728; <code>ProductService</code> &#x4e2d;&#x5b9e;&#x73b0;&#x56fe;&#x7247;&#x6587;&#x4ef6;&#x590d;&#x5236;&#xff0c;&#x7edf;&#x4e00;&#x5b58;&#x50a8;&#x5230; <code>data/images/products/product_{id}/</code> &#x76ee;&#x5f55;&#x4e0b;&#x3002;","children":[],"payload":{"tag":"li","lines":"333,334"}},{"content":"<strong>&#x547d;&#x540d;&#x89c4;&#x5219;</strong>&#xff1a;<code>{&#x5546;&#x54c1;&#x540d;}_{&#x5e8f;&#x53f7;}.{&#x539f;&#x6269;&#x5c55;&#x540d;}</code>&#xff0c;&#x786e;&#x4fdd;&#x6587;&#x4ef6;&#x552f;&#x4e00;&#x6027;&#x3002;","children":[],"payload":{"tag":"li","lines":"334,336"}}],"payload":{"tag":"h4","lines":"331,332"}},{"content":"&#x4f18;&#x5316;&#x56fe;&#x7247;&#x5217;&#x8868;&#x663e;&#x793a;","children":[{"content":"<strong>&#x95ee;&#x9898;&#x80cc;&#x666f;</strong>&#xff1a;&#x56fe;&#x7247;&#x7ba1;&#x7406;&#x5bf9;&#x8bdd;&#x6846;&#x4e2d;&#xff0c;&#x5de6;&#x4fa7;&#x56fe;&#x7247;&#x5217;&#x8868;&#x5728;&#x6dfb;&#x52a0;&#x56fe;&#x7247;&#x540e;&#x672a;&#x80fd;&#x6b63;&#x786e;&#x663e;&#x793a;&#x3002;","children":[],"payload":{"tag":"li","lines":"337,338"}},{"content":"<strong>&#x6280;&#x672f;&#x65b9;&#x6848;</strong>&#xff1a;&#x5728; <code>ImageViewer</code> &#x7684;&#x521d;&#x59cb;&#x5316;&#x65b9;&#x6cd5; <code>__init__</code> &#x4e2d;&#x65b0;&#x589e;&#x8c03;&#x7528; <code>self.update_image_list()</code>&#xff0c;&#x786e;&#x4fdd;&#x7ec4;&#x4ef6;&#x521d;&#x59cb;&#x5316;&#x65f6;&#x5373;&#x66f4;&#x65b0;&#x56fe;&#x7247;&#x5217;&#x8868;&#x3002;","children":[],"payload":{"tag":"li","lines":"338,339"}},{"content":"<strong>&#x8c03;&#x8bd5;&#x6539;&#x8fdb;</strong>&#xff1a;&#x5728; <code>update_image_list</code> &#x65b9;&#x6cd5;&#x4e2d;&#x6dfb;&#x52a0;&#x8be6;&#x7ec6;&#x65e5;&#x5fd7;&#x8f93;&#x51fa;&#xff0c;&#x65b9;&#x4fbf;&#x8c03;&#x8bd5;&#x56fe;&#x7247;&#x5217;&#x8868;&#x66f4;&#x65b0;&#x6d41;&#x7a0b;&#x3002;","children":[],"payload":{"tag":"li","lines":"339,341"}}],"payload":{"tag":"h4","lines":"336,337"}},{"content":"&#x7edf;&#x4e00;&#x5bf9;&#x8bdd;&#x6846;&#x5c3a;&#x5bf8;","children":[{"content":"<strong>&#x76ee;&#x7684;</strong>&#xff1a;&#x63d0;&#x4f9b;&#x4e00;&#x81f4;&#x7684;&#x7528;&#x6237;&#x4f53;&#x9a8c;&#xff0c;&#x4f18;&#x5316;&#x56fe;&#x7247;&#x7ba1;&#x7406;&#x5bf9;&#x8bdd;&#x6846;&#x7684;&#x5e03;&#x5c40;&#x3002;","children":[],"payload":{"tag":"li","lines":"342,343"}},{"content":"<strong>&#x65b9;&#x6848;</strong>&#xff1a;&#x5c06; <code>ProductDialog</code> &#x548c; <code>ImageManagementDialog</code> &#x7684;&#x56fa;&#x5b9a;&#x5927;&#x5c0f;&#x7edf;&#x4e00;&#x8bbe;&#x7f6e;&#x4e3a; <code>1300x600</code>&#x3002;","children":[],"payload":{"tag":"li","lines":"343,345"}}],"payload":{"tag":"h4","lines":"341,342"}},{"content":"&#x4fee;&#x590d;&#x56fe;&#x7247;&#x4fdd;&#x5b58;&#x8986;&#x76d6;&#x95ee;&#x9898;","children":[{"content":"<strong>&#x95ee;&#x9898;&#x80cc;&#x666f;</strong>&#xff1a;&#x5728;&#x66f4;&#x65b0;&#x4ea7;&#x54c1;&#x56fe;&#x7247;&#x65f6;&#xff0c;&#x65b0;&#x6dfb;&#x52a0;&#x7684;&#x56fe;&#x7247;&#x4f1a;&#x8986;&#x76d6;&#x65e7;&#x56fe;&#x7247;&#xff0c;&#x5bfc;&#x81f4;&#x53ea;&#x4fdd;&#x7559;&#x6700;&#x540e;&#x4e00;&#x5f20;&#x3002;","children":[],"payload":{"tag":"li","lines":"346,347"}},{"content":"<strong>&#x95ee;&#x9898;&#x539f;&#x56e0;</strong>&#xff1a;<code>_update_product_images</code> &#x65b9;&#x6cd5;&#x4f1a;&#x6e05;&#x7406;&#x6240;&#x6709;&#x65e7;&#x56fe;&#x7247;&#x7136;&#x540e;&#x91cd;&#x65b0;&#x590d;&#x5236;&#xff0c;&#x4e14;&#x6587;&#x4ef6;&#x547d;&#x540d;&#x6ca1;&#x6709;&#x8003;&#x8651;&#x5df2;&#x5b58;&#x5728;&#x56fe;&#x7247;&#x3002;","children":[],"payload":{"tag":"li","lines":"347,348"}},{"content":"<strong>&#x89e3;&#x51b3;&#x65b9;&#x6848;</strong>&#xff1a;&#x4fee;&#x6539; <code>_update_product_images</code> &#x65b9;&#x6cd5;&#xff0c;&#x4e0d;&#x518d;&#x6e05;&#x7406;&#x6240;&#x6709;&#x65e7;&#x56fe;&#x7247;&#xff0c;&#x800c;&#x662f;&#x901a;&#x8fc7;&#x68c0;&#x67e5;&#x73b0;&#x6709;&#x6587;&#x4ef6;&#x6570;&#x91cf;&#x6765;&#x751f;&#x6210;&#x552f;&#x4e00;&#x7684;&#x56fe;&#x7247;&#x5e8f;&#x53f7;&#xff0c;&#x786e;&#x4fdd;&#x65b0;&#x56fe;&#x7247;&#x4e0d;&#x4f1a;&#x8986;&#x76d6;&#x65e7;&#x56fe;&#x7247;&#x3002;","children":[],"payload":{"tag":"li","lines":"348,350"}}],"payload":{"tag":"h4","lines":"345,346"}}],"payload":{"tag":"h3","lines":"329,330"}},{"content":"&#x1f527; &#x6280;&#x672f;&#x6539;&#x8fdb;","children":[{"content":"&#x8def;&#x5f84;&#x5904;&#x7406;&#x8de8;&#x5e73;&#x53f0;&#x517c;&#x5bb9;&#x6027;","children":[{"content":"<strong>&#x95ee;&#x9898;&#x80cc;&#x666f;</strong>&#xff1a;Windows &#x7cfb;&#x7edf;&#x4e0b;&#x7684;&#x8def;&#x5f84;&#x4f7f;&#x7528;&#x53cd;&#x659c;&#x6760; <code>\\</code>&#xff0c;&#x53ef;&#x80fd;&#x5bfc;&#x81f4;&#x56fe;&#x7247;&#x52a0;&#x8f7d;&#x5931;&#x8d25;&#x3002;","children":[],"payload":{"tag":"li","lines":"353,354"}},{"content":"<strong>&#x89e3;&#x51b3;&#x65b9;&#x6848;</strong>&#xff1a;&#x7edf;&#x4e00;&#x5728;&#x56fe;&#x7247;&#x8def;&#x5f84;&#x5904;&#x7406;&#x4e2d;&#x4f7f;&#x7528;&#x6b63;&#x659c;&#x6760; <code>/</code>&#xff0c;&#x63d0;&#x9ad8;&#x8de8;&#x5e73;&#x53f0;&#x517c;&#x5bb9;&#x6027;&#x3002;","children":[],"payload":{"tag":"li","lines":"354,356"}}],"payload":{"tag":"h4","lines":"352,353"}},{"content":"&#x5065;&#x58ee;&#x6027;&#x589e;&#x5f3a;","children":[{"content":"<strong>&#x9519;&#x8bef;&#x5904;&#x7406;</strong>&#xff1a;&#x5728;&#x56fe;&#x7247;&#x52a0;&#x8f7d;&#x548c;&#x663e;&#x793a;&#x8fc7;&#x7a0b;&#x4e2d;&#xff0c;&#x589e;&#x52a0;&#x4e86;&#x5bf9; QLabel &#x5bf9;&#x8c61;&#x662f;&#x5426;&#x5df2;&#x5220;&#x9664;&#x7684;&#x68c0;&#x67e5;&#xff0c;&#x907f;&#x514d; <code>RuntimeError</code>&#x3002;","children":[],"payload":{"tag":"li","lines":"357,358"}},{"content":"<strong>&#x65e5;&#x5fd7;&#x8f93;&#x51fa;</strong>&#xff1a;&#x589e;&#x52a0;&#x5173;&#x952e;&#x64cd;&#x4f5c;&#x7684;&#x65e5;&#x5fd7;&#x8f93;&#x51fa;&#xff0c;&#x65b9;&#x4fbf;&#x95ee;&#x9898;&#x8ffd;&#x8e2a;&#x548c;&#x8c03;&#x8bd5;&#x3002;","children":[],"payload":{"tag":"li","lines":"358,360"}}],"payload":{"tag":"h4","lines":"356,357"}}],"payload":{"tag":"h3","lines":"350,351"}}],"payload":{"tag":"h2","lines":"327,328"}},{"content":"&#x7248;&#x672c;&#x66f4;&#x65b0; (v2.0.7)","children":[{"content":"&#x1f3a8; UI/UX &#x4f18;&#x5316;","children":[{"content":"&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x4e3b;&#x9898;&#x9002;&#x914d;&#x6539;&#x8fdb;","children":[{"content":"<strong>&#x95ee;&#x9898;&#x80cc;&#x666f;</strong>&#xff1a;&#x6697;&#x9ed1;&#x4e3b;&#x9898;&#x4e0b;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x9879;&#x6587;&#x5b57;&#x989c;&#x8272;&#x663e;&#x793a;&#x5f02;&#x5e38;&#xff0c;&#x5b58;&#x5728;&#x6837;&#x5f0f;&#x51b2;&#x7a81;","children":[],"payload":{"tag":"li","lines":"365,366"}},{"content":"<strong>&#x6280;&#x672f;&#x65b9;&#x6848;</strong>&#xff1a;","children":[{"content":"&#x4e3a; <code>ProductListItem</code> &#x7ec4;&#x4ef6;&#x6dfb;&#x52a0;&#x4e13;&#x95e8;&#x7684;&#x4e3b;&#x9898;&#x6837;&#x5f0f;&#x8bbe;&#x7f6e;","children":[],"payload":{"tag":"li","lines":"367,368"}},{"content":"&#x901a;&#x8fc7; <code>setStyleSheet()</code> &#x76f4;&#x63a5;&#x63a7;&#x5236;&#x7ec4;&#x4ef6;&#x6837;&#x5f0f;&#xff0c;&#x907f;&#x514d;&#x88ab;&#x5916;&#x90e8;&#x6837;&#x5f0f;&#x8986;&#x76d6;","children":[],"payload":{"tag":"li","lines":"368,369"}},{"content":"&#x5b9e;&#x73b0;&#x660e;&#x4eae;/&#x6697;&#x9ed1;&#x4e3b;&#x9898;&#x7684;&#x6761;&#x4ef6;&#x6837;&#x5f0f;&#x5e94;&#x7528;","children":[],"payload":{"tag":"li","lines":"369,371"}}],"payload":{"tag":"li","lines":"366,371"}}],"payload":{"tag":"h4","lines":"364,365"}},{"content":"&#x6837;&#x5f0f;&#x7ee7;&#x627f;&#x4f18;&#x5316;","children":[{"content":"\n<p data-lines=\"372,373\"><strong>&#x6838;&#x5fc3;&#x6539;&#x8fdb;</strong>&#xff1a;</p>\n<pre data-lines=\"373,380\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x4fee;&#x590d;&#x524d;&#xff1a;&#x4f9d;&#x8d56;&#x5916;&#x90e8;&#x6837;&#x5f0f;&#xff0c;&#x5bb9;&#x6613;&#x88ab;&#x8986;&#x76d6;</span>\nname_label.setStyleSheet(<span class=\"hljs-string\">f&quot;color: <span class=\"hljs-subst\">{colors[<span class=\"hljs-string\">&apos;text&apos;</span>]}</span>;&quot;</span>)\n\n<span class=\"hljs-comment\"># &#x4fee;&#x590d;&#x540e;&#xff1a;&#x660e;&#x786e;&#x80cc;&#x666f;&#x8272;&#xff0c;&#x907f;&#x514d;&#x6837;&#x5f0f;&#x51b2;&#x7a81;</span>\nname_label.setStyleSheet(<span class=\"hljs-string\">f&quot;color: <span class=\"hljs-subst\">{colors[<span class=\"hljs-string\">&apos;text&apos;</span>]}</span>; background-color: transparent;&quot;</span>)\n</code></pre>","children":[],"payload":{"tag":"li","lines":"372,381"}},{"content":"\n<p data-lines=\"381,382\"><strong>&#x67b6;&#x6784;&#x6539;&#x8fdb;</strong>&#xff1a;</p>\n<pre data-lines=\"382,402\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x7ec4;&#x4ef6;&#x7ea7;&#x522b;&#x7684;&#x4e3b;&#x9898;&#x6837;&#x5f0f;&#x63a7;&#x5236;</span>\n<span class=\"hljs-keyword\">if</span> current_theme == <span class=\"hljs-string\">&quot;dark&quot;</span>:\n    <span class=\"hljs-variable language_\">self</span>.setStyleSheet(<span class=\"hljs-string\">&quot;&quot;&quot;\n        ProductListItem {\n            background-color: #2d2d2d;\n            color: #ffffff;\n            border: 1px solid #404040;\n            border-radius: 4px;\n            margin: 2px;\n        }\n        ProductListItem:hover {\n            background-color: #404040;\n        }\n        ProductListItem QLabel {\n            color: #ffffff;\n            background-color: transparent;\n        }\n    &quot;&quot;&quot;</span>)\n</code></pre>","children":[],"payload":{"tag":"li","lines":"381,403"}}],"payload":{"tag":"h4","lines":"371,372"}}],"payload":{"tag":"h3","lines":"362,363"}},{"content":"&#x1f527; &#x6280;&#x672f;&#x6539;&#x8fdb;","children":[{"content":"&#x4e3b;&#x9898;&#x7cfb;&#x7edf;&#x4f18;&#x5316;","children":[{"content":"<strong>&#x6837;&#x5f0f;&#x51b2;&#x7a81;&#x89e3;&#x51b3;</strong>&#xff1a;&#x901a;&#x8fc7;&#x7ec4;&#x4ef6;&#x7ea7;&#x522b;&#x7684;&#x6837;&#x5f0f;&#x8bbe;&#x7f6e;&#xff0c;&#x907f;&#x514d;&#x5168;&#x5c40;&#x6837;&#x5f0f;&#x5e72;&#x6270;","children":[],"payload":{"tag":"li","lines":"406,407"}},{"content":"<strong>&#x54cd;&#x5e94;&#x673a;&#x5236;&#x6539;&#x8fdb;</strong>&#xff1a;&#x4f18;&#x5316;&#x4e3b;&#x9898;&#x5207;&#x6362;&#x7684;&#x54cd;&#x5e94;&#x901f;&#x5ea6;&#x548c;&#x89c6;&#x89c9;&#x6548;&#x679c;","children":[],"payload":{"tag":"li","lines":"407,408"}},{"content":"<strong>&#x517c;&#x5bb9;&#x6027;&#x63d0;&#x5347;</strong>&#xff1a;&#x786e;&#x4fdd;&#x5728;&#x4e0d;&#x540c;&#x7cfb;&#x7edf;&#x548c;&#x663e;&#x793a;&#x8bbe;&#x7f6e;&#x4e0b;&#x7684;&#x4e00;&#x81f4;&#x6027;","children":[],"payload":{"tag":"li","lines":"408,410"}}],"payload":{"tag":"h4","lines":"405,406"}},{"content":"&#x4ee3;&#x7801;&#x8d28;&#x91cf;&#x63d0;&#x5347;","children":[{"content":"<strong>&#x6837;&#x5f0f;&#x7ba1;&#x7406;</strong>&#xff1a;&#x96c6;&#x4e2d;&#x7ba1;&#x7406;&#x4e3b;&#x9898;&#x6837;&#x5f0f;&#xff0c;&#x63d0;&#x9ad8;&#x7ef4;&#x62a4;&#x6027;","children":[],"payload":{"tag":"li","lines":"411,412"}},{"content":"<strong>&#x6027;&#x80fd;&#x4f18;&#x5316;</strong>&#xff1a;&#x51cf;&#x5c11;&#x4e0d;&#x5fc5;&#x8981;&#x7684;&#x6837;&#x5f0f;&#x8ba1;&#x7b97;&#x548c;DOM&#x64cd;&#x4f5c;","children":[],"payload":{"tag":"li","lines":"412,413"}},{"content":"<strong>&#x9519;&#x8bef;&#x5904;&#x7406;</strong>&#xff1a;&#x589e;&#x5f3a;&#x4e3b;&#x9898;&#x5207;&#x6362;&#x7684;&#x5bb9;&#x9519;&#x80fd;&#x529b;","children":[],"payload":{"tag":"li","lines":"413,415"}}],"payload":{"tag":"h4","lines":"410,411"}}],"payload":{"tag":"h3","lines":"403,404"}},{"content":"&#x1f4da; &#x6587;&#x6863;&#x66f4;&#x65b0;","children":[{"content":"&#x66f4;&#x65b0;&#x6280;&#x672f;&#x67b6;&#x6784;&#x6587;&#x6863;&#xff0c;&#x8bf4;&#x660e;&#x4e3b;&#x9898;&#x7cfb;&#x7edf;&#x7684;&#x5b9e;&#x73b0;&#x7ec6;&#x8282;","children":[],"payload":{"tag":"li","lines":"416,417"}},{"content":"&#x5b8c;&#x5584;&#x6837;&#x5f0f;&#x7ba1;&#x7406;&#x7684;&#x6700;&#x4f73;&#x5b9e;&#x8df5;","children":[],"payload":{"tag":"li","lines":"417,418"}},{"content":"&#x6dfb;&#x52a0;UI&#x7ec4;&#x4ef6;&#x7684;&#x4e3b;&#x9898;&#x9002;&#x914d;&#x6307;&#x5357;","children":[],"payload":{"tag":"li","lines":"418,420"}}],"payload":{"tag":"h3","lines":"415,416"}}],"payload":{"tag":"h2","lines":"360,361"}},{"content":"&#x9879;&#x76ee;&#x6982;&#x8ff0;","children":[{"content":"&#x8bbe;&#x8ba1;&#x76ee;&#x6807;","children":[],"payload":{"tag":"h3","lines":"424,425"}},{"content":"&#x6838;&#x5fc3;&#x7406;&#x5ff5;","children":[{"content":"<strong>&#x7b80;&#x6d01;&#x81f3;&#x4e0a;</strong>&#xff1a;&#x907f;&#x514d;&#x8fc7;&#x5ea6;&#x8bbe;&#x8ba1;&#xff0c;&#x4e13;&#x6ce8;&#x6838;&#x5fc3;&#x529f;&#x80fd;","children":[],"payload":{"tag":"li","lines":"428,429"}},{"content":"<strong>&#x6027;&#x80fd;&#x4f18;&#x5148;</strong>&#xff1a;&#x8f7b;&#x91cf;&#x7ea7;&#x67b6;&#x6784;&#xff0c;&#x5feb;&#x901f;&#x54cd;&#x5e94;","children":[],"payload":{"tag":"li","lines":"429,430"}},{"content":"<strong>&#x6613;&#x4e8e;&#x7ef4;&#x62a4;</strong>&#xff1a;&#x6e05;&#x6670;&#x7684;&#x4ee3;&#x7801;&#x7ed3;&#x6784;&#xff0c;&#x6700;&#x5c0f;&#x5316;&#x4f9d;&#x8d56;","children":[],"payload":{"tag":"li","lines":"430,431"}},{"content":"<strong>&#x4e3b;&#x9898;&#x9002;&#x914d;</strong>&#xff1a;&#x5b8c;&#x5584;&#x7684;&#x6697;&#x9ed1;/&#x660e;&#x4eae;&#x4e3b;&#x9898;&#x652f;&#x6301;&#xff08;v2.0.7&#x4f18;&#x5316;&#xff09;","children":[],"payload":{"tag":"li","lines":"431,433"}}],"payload":{"tag":"h3","lines":"427,428"}}],"payload":{"tag":"h2","lines":"422,423"}},{"content":"&#x9879;&#x76ee;&#x5206;&#x6790;&#x5bf9;&#x6bd4;","children":[{"content":"&#x73b0;&#x6709;&#x9879;&#x76ee;&#x5206;&#x6790;","children":[{"content":"&#x2705; MacOS&#x7248;&#x672c;&#xff08;&#x6210;&#x529f;&#x6848;&#x4f8b;&#xff09;","children":[{"content":"<strong>&#x4f18;&#x70b9;</strong>&#xff1a;","children":[{"content":"&#x7b80;&#x6d01;&#x9ad8;&#x6548;&#xff1a;11&#x4e2a;&#x6587;&#x4ef6;&#xff0c;4774&#x884c;&#x4ee3;&#x7801;","children":[],"payload":{"tag":"li","lines":"439,440"}},{"content":"&#x6280;&#x672f;&#x6808;&#x7eaf;&#x51c0;&#xff1a;Swift + SwiftUI + SwiftData","children":[],"payload":{"tag":"li","lines":"440,441"}},{"content":"&#x529f;&#x80fd;&#x5b8c;&#x6574;&#xff1a;&#x4ea7;&#x54c1;&#x7ba1;&#x7406;&#x3001;&#x8d27;&#x6e90;&#x5bf9;&#x6bd4;&#x3001;&#x56fe;&#x7247;&#x8f6e;&#x64ad;","children":[],"payload":{"tag":"li","lines":"441,442"}},{"content":"&#x67b6;&#x6784;&#x6e05;&#x6670;&#xff1a;MVVM&#x6a21;&#x5f0f;&#xff0c;3&#x5c42;&#x7ed3;&#x6784;&#xff08;Models/Views/Services&#xff09;","children":[],"payload":{"tag":"li","lines":"442,443"}}],"payload":{"tag":"li","lines":"438,443"}},{"content":"<strong>&#x6838;&#x5fc3;&#x529f;&#x80fd;</strong>&#xff1a;","children":[{"content":"&#x4ea7;&#x54c1;&#x7ba1;&#x7406;&#xff08;&#x589e;&#x5220;&#x6539;&#x67e5;&#xff09;","children":[],"payload":{"tag":"li","lines":"444,445"}},{"content":"&#x591a;&#x8d27;&#x6e90;&#x5bf9;&#x6bd4;&#x5206;&#x6790;","children":[],"payload":{"tag":"li","lines":"445,446"}},{"content":"&#x56fe;&#x7247;&#x8f6e;&#x64ad;&#x67e5;&#x770b;","children":[],"payload":{"tag":"li","lines":"446,447"}},{"content":"&#x6570;&#x636e;&#x5bfc;&#x5165;&#x5bfc;&#x51fa;","children":[],"payload":{"tag":"li","lines":"447,449"}}],"payload":{"tag":"li","lines":"443,449"}}],"payload":{"tag":"h4","lines":"437,438"}},{"content":"&#x274c; Python&#x7248;&#x672c;&#xff08;&#x590d;&#x6742;&#x5931;&#x8d25;&#xff09;","children":[{"content":"<strong>&#x95ee;&#x9898;</strong>&#xff1a;","children":[{"content":"&#x6280;&#x672f;&#x6808;&#x8fc7;&#x591a;&#xff1a;FastAPI + SQLAlchemy + Alembic + CustomTkinter + PyQt6","children":[],"payload":{"tag":"li","lines":"451,452"}},{"content":"&#x8fc7;&#x5ea6;&#x8bbe;&#x8ba1;&#xff1a;&#x684c;&#x9762;&#x5e94;&#x7528;+Web&#x5e94;&#x7528;&#x53cc;&#x6a21;&#x5f0f;","children":[],"payload":{"tag":"li","lines":"452,453"}},{"content":"&#x590d;&#x6742;&#x67b6;&#x6784;&#xff1a;&#x591a;&#x5c42;&#x5d4c;&#x5957;&#xff0c;1241&#x884c;&#x6587;&#x6863;","children":[],"payload":{"tag":"li","lines":"453,454"}},{"content":"&#x6027;&#x80fd;&#x95ee;&#x9898;&#xff1a;&#x5185;&#x6838;&#x590d;&#x6742;&#x5bfc;&#x81f4;&#x54cd;&#x5e94;&#x7f13;&#x6162;","children":[],"payload":{"tag":"li","lines":"454,456"}}],"payload":{"tag":"li","lines":"450,456"}}],"payload":{"tag":"h4","lines":"449,450"}},{"content":"&#x2705; Inventory_Management&#xff08;&#x7b80;&#x6d01;&#x53c2;&#x8003;&#xff09;","children":[{"content":"<strong>&#x4f18;&#x70b9;</strong>&#xff1a;","children":[{"content":"&#x4f9d;&#x8d56;&#x7cbe;&#x7b80;&#xff1a;&#x53ea;&#x6709;7&#x4e2a;&#x6838;&#x5fc3;&#x5305;","children":[],"payload":{"tag":"li","lines":"458,459"}},{"content":"&#x67b6;&#x6784;&#x6e05;&#x6670;&#xff1a;PyQt6 + SQLite","children":[],"payload":{"tag":"li","lines":"459,460"}},{"content":"&#x6027;&#x80fd;&#x826f;&#x597d;&#xff1a;&#x4e13;&#x6ce8;&#x6838;&#x5fc3;&#x529f;&#x80fd;","children":[],"payload":{"tag":"li","lines":"460,461"}},{"content":"&#x6613;&#x4e8e;&#x7ef4;&#x62a4;&#xff1a;&#x6a21;&#x5757;&#x5316;&#x8bbe;&#x8ba1;","children":[],"payload":{"tag":"li","lines":"461,463"}}],"payload":{"tag":"li","lines":"457,463"}}],"payload":{"tag":"h4","lines":"456,457"}}],"payload":{"tag":"h3","lines":"435,436"}},{"content":"&#x65b0;&#x9879;&#x76ee;&#x8bbe;&#x8ba1;&#x539f;&#x5219;","children":[{"content":"1. <strong>&#x6280;&#x672f;&#x6808;&#x6700;&#x5c0f;&#x5316;</strong>&#xff1a;&#x53ea;&#x4f7f;&#x7528;PyQt6 + SQLite + &#x5fc5;&#x8981;&#x5de5;&#x5177;&#x5305;","children":[],"payload":{"tag":"li","lines":"465,466","listIndex":1}},{"content":"2. <strong>&#x529f;&#x80fd;&#x4e13;&#x6ce8;&#x5316;</strong>&#xff1a;&#x4e13;&#x6ce8;&#x4ea7;&#x54c1;&#x5bf9;&#x6bd4;&#x9009;&#x54c1;&#x6838;&#x5fc3;&#x529f;&#x80fd;","children":[],"payload":{"tag":"li","lines":"466,467","listIndex":2}},{"content":"3. <strong>&#x67b6;&#x6784;&#x6241;&#x5e73;&#x5316;</strong>&#xff1a;&#x907f;&#x514d;&#x8fc7;&#x5ea6;&#x5206;&#x5c42;&#xff0c;&#x4fdd;&#x6301;&#x7b80;&#x6d01;","children":[],"payload":{"tag":"li","lines":"467,468","listIndex":3}},{"content":"4. <strong>&#x6027;&#x80fd;&#x4f18;&#x5316;</strong>&#xff1a;&#x5185;&#x5b58;&#x9ad8;&#x6548;&#xff0c;&#x54cd;&#x5e94;&#x8fc5;&#x901f;","children":[],"payload":{"tag":"li","lines":"468,469","listIndex":4}},{"content":"5. <strong>&#x4e3b;&#x9898;&#x7cfb;&#x7edf;</strong>&#xff1a;&#x5b8c;&#x5584;&#x7684;&#x4e3b;&#x9898;&#x9002;&#x914d;&#x673a;&#x5236;&#xff08;v2.0.7&#x91cd;&#x70b9;&#x4f18;&#x5316;&#xff09;","children":[],"payload":{"tag":"li","lines":"469,471","listIndex":5}}],"payload":{"tag":"h3","lines":"463,464"}}],"payload":{"tag":"h2","lines":"433,434"}},{"content":"&#x6280;&#x672f;&#x67b6;&#x6784;&#x8bbe;&#x8ba1;","children":[{"content":"&#x6280;&#x672f;&#x6808;&#x9009;&#x62e9;","children":[{"content":"<pre data-lines=\"475,484\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x6838;&#x5fc3;&#x4f9d;&#x8d56;&#xff08;&#x4ec5;6&#x4e2a;&#x5305;&#xff09;</span>\nPyQt6&gt;=<span class=\"hljs-number\">6.4</span><span class=\"hljs-number\">.0</span>          <span class=\"hljs-comment\"># &#x73b0;&#x4ee3;GUI&#x6846;&#x67b6;</span>\npandas&gt;=<span class=\"hljs-number\">1.5</span><span class=\"hljs-number\">.0</span>         <span class=\"hljs-comment\"># &#x6570;&#x636e;&#x5904;&#x7406;</span>\npillow&gt;=<span class=\"hljs-number\">9.0</span><span class=\"hljs-number\">.0</span>         <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x5904;&#x7406;</span>\npython-dateutil&gt;=<span class=\"hljs-number\">2.8</span><span class=\"hljs-number\">.2</span> <span class=\"hljs-comment\"># &#x65e5;&#x671f;&#x5904;&#x7406;</span>\nrequests&gt;=<span class=\"hljs-number\">2.28</span><span class=\"hljs-number\">.0</span>      <span class=\"hljs-comment\"># &#x7f51;&#x7edc;&#x8bf7;&#x6c42;&#xff08;&#x53ef;&#x9009;&#xff09;</span>\nbeautifulsoup4&gt;=<span class=\"hljs-number\">4.11</span><span class=\"hljs-number\">.0</span> <span class=\"hljs-comment\"># HTML&#x89e3;&#x6790;&#xff08;&#x53ef;&#x9009;&#xff09;</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"475,484"}}],"payload":{"tag":"h3","lines":"473,474"}},{"content":"&#x9879;&#x76ee;&#x7ed3;&#x6784;","children":[{"content":"<pre data-lines=\"487,528\"><code data-lines=\"487,528\">product_comparison_tool<span class=\"hljs-symbol\">/</span>\n&#x251c;&#x2500;&#x2500; main.py                    <span class=\"hljs-comment\"># &#x5e94;&#x7528;&#x5165;&#x53e3;</span>\n&#x251c;&#x2500;&#x2500; config.py                  <span class=\"hljs-comment\"># &#x914d;&#x7f6e;&#x7ba1;&#x7406;</span>\n&#x251c;&#x2500;&#x2500; requirements.txt           <span class=\"hljs-comment\"># &#x4f9d;&#x8d56;&#x5217;&#x8868;</span>\n&#x251c;&#x2500;&#x2500; README.md                  <span class=\"hljs-comment\"># &#x9879;&#x76ee;&#x8bf4;&#x660e;</span>\n&#x2502;\n&#x251c;&#x2500;&#x2500; models<span class=\"hljs-symbol\">/</span>                    <span class=\"hljs-comment\"># &#x6570;&#x636e;&#x6a21;&#x578b;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; __init__.py\n&#x2502;   &#x251c;&#x2500;&#x2500; product.py            <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x6a21;&#x578b;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; source.py             <span class=\"hljs-comment\"># &#x8d27;&#x6e90;&#x6a21;&#x578b;</span>\n&#x2502;   &#x2514;&#x2500;&#x2500; database.py           <span class=\"hljs-comment\"># &#x6570;&#x636e;&#x5e93;&#x7ba1;&#x7406;</span>\n&#x2502;\n&#x251c;&#x2500;&#x2500; views<span class=\"hljs-symbol\">/</span>                     <span class=\"hljs-comment\"># &#x754c;&#x9762;&#x89c6;&#x56fe;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; __init__.py\n&#x2502;   &#x251c;&#x2500;&#x2500; main_window.py        <span class=\"hljs-comment\"># &#x4e3b;&#x7a97;&#x53e3;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; product_list.py       <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x5217;&#x8868;&#xff08;v2.0.7&#x4e3b;&#x9898;&#x4f18;&#x5316;&#xff09;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; product_detail.py     <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x8be6;&#x60c5;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; comparison_view.py    <span class=\"hljs-comment\"># &#x5bf9;&#x6bd4;&#x5206;&#x6790;</span>\n&#x2502;   &#x2514;&#x2500;&#x2500; image_viewer.py       <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x67e5;&#x770b;&#x5668;</span>\n&#x2502;\n&#x251c;&#x2500;&#x2500; widgets<span class=\"hljs-symbol\">/</span>                   <span class=\"hljs-comment\"># &#x81ea;&#x5b9a;&#x4e49;&#x7ec4;&#x4ef6;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; __init__.py\n&#x2502;   &#x251c;&#x2500;&#x2500; product_card.py       <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x5361;&#x7247;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; source_table.py       <span class=\"hljs-comment\"># &#x8d27;&#x6e90;&#x8868;&#x683c;</span>\n&#x2502;   &#x2514;&#x2500;&#x2500; image_carousel.py     <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x8f6e;&#x64ad;</span>\n&#x2502;\n&#x251c;&#x2500;&#x2500; utils<span class=\"hljs-symbol\">/</span>                     <span class=\"hljs-comment\"># &#x5de5;&#x5177;&#x51fd;&#x6570;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; __init__.py\n&#x2502;   &#x251c;&#x2500;&#x2500; image_utils.py        <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x5904;&#x7406;</span>\n&#x2502;   &#x2514;&#x2500;&#x2500; data_utils.py         <span class=\"hljs-comment\"># &#x6570;&#x636e;&#x5904;&#x7406;</span>\n&#x2502;\n&#x251c;&#x2500;&#x2500; resources<span class=\"hljs-symbol\">/</span>                 <span class=\"hljs-comment\"># &#x8d44;&#x6e90;&#x6587;&#x4ef6;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; icons<span class=\"hljs-symbol\">/</span>                <span class=\"hljs-comment\"># &#x56fe;&#x6807;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; styles<span class=\"hljs-symbol\">/</span>               <span class=\"hljs-comment\"># &#x6837;&#x5f0f;&#xff08;v2.0.7&#x4f18;&#x5316;&#xff09;</span>\n&#x2502;   &#x2514;&#x2500;&#x2500; images<span class=\"hljs-symbol\">/</span>               <span class=\"hljs-comment\"># &#x56fe;&#x7247;</span>\n&#x2502;\n&#x2514;&#x2500;&#x2500; data<span class=\"hljs-symbol\">/</span>                      <span class=\"hljs-comment\"># &#x6570;&#x636e;&#x5b58;&#x50a8;</span>\n    &#x251c;&#x2500;&#x2500; database.db           <span class=\"hljs-comment\"># SQLite&#x6570;&#x636e;&#x5e93;</span>\n    &#x2514;&#x2500;&#x2500; images<span class=\"hljs-symbol\">/</span>               <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x5b58;&#x50a8;</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"487,528"}}],"payload":{"tag":"h3","lines":"485,486"}}],"payload":{"tag":"h2","lines":"471,472"}},{"content":"&#x4e3b;&#x9898;&#x7cfb;&#x7edf;&#x8bbe;&#x8ba1; (v2.0.7&#x91cd;&#x70b9;&#x4f18;&#x5316;)","children":[{"content":"&#x4e3b;&#x9898;&#x67b6;&#x6784;","children":[{"content":"<pre data-lines=\"533,566\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x4e3b;&#x9898;&#x914d;&#x7f6e;&#x7cfb;&#x7edf;</span>\n<span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">ThemeConfig</span>:\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x4e3b;&#x9898;&#x914d;&#x7f6e;&#x7ba1;&#x7406;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-comment\"># &#x6697;&#x9ed1;&#x4e3b;&#x9898;&#x914d;&#x7f6e;</span>\n    DARK_THEME = {\n        <span class=\"hljs-string\">&quot;background_color&quot;</span>: <span class=\"hljs-string\">&quot;#1e1e1e&quot;</span>,\n        <span class=\"hljs-string\">&quot;surface_color&quot;</span>: <span class=\"hljs-string\">&quot;#2d2d2d&quot;</span>,\n        <span class=\"hljs-string\">&quot;text_color&quot;</span>: <span class=\"hljs-string\">&quot;#ffffff&quot;</span>,\n        <span class=\"hljs-string\">&quot;secondary_text_color&quot;</span>: <span class=\"hljs-string\">&quot;#b0b0b0&quot;</span>,\n        <span class=\"hljs-string\">&quot;primary_color&quot;</span>: <span class=\"hljs-string\">&quot;#2196f3&quot;</span>,\n        <span class=\"hljs-string\">&quot;success_color&quot;</span>: <span class=\"hljs-string\">&quot;#4caf50&quot;</span>,\n        <span class=\"hljs-string\">&quot;error_color&quot;</span>: <span class=\"hljs-string\">&quot;#f44336&quot;</span>,\n        <span class=\"hljs-string\">&quot;border_color&quot;</span>: <span class=\"hljs-string\">&quot;#404040&quot;</span>,\n        <span class=\"hljs-string\">&quot;hover_color&quot;</span>: <span class=\"hljs-string\">&quot;#404040&quot;</span>,\n        <span class=\"hljs-string\">&quot;pressed_color&quot;</span>: <span class=\"hljs-string\">&quot;#505050&quot;</span>,\n    }\n    \n    <span class=\"hljs-comment\"># &#x660e;&#x4eae;&#x4e3b;&#x9898;&#x914d;&#x7f6e;</span>\n    LIGHT_THEME = {\n        <span class=\"hljs-string\">&quot;background_color&quot;</span>: <span class=\"hljs-string\">&quot;#ffffff&quot;</span>,\n        <span class=\"hljs-string\">&quot;surface_color&quot;</span>: <span class=\"hljs-string\">&quot;#f5f5f5&quot;</span>,\n        <span class=\"hljs-string\">&quot;text_color&quot;</span>: <span class=\"hljs-string\">&quot;#333333&quot;</span>,\n        <span class=\"hljs-string\">&quot;secondary_text_color&quot;</span>: <span class=\"hljs-string\">&quot;#666666&quot;</span>,\n        <span class=\"hljs-string\">&quot;primary_color&quot;</span>: <span class=\"hljs-string\">&quot;#1976d2&quot;</span>,\n        <span class=\"hljs-string\">&quot;success_color&quot;</span>: <span class=\"hljs-string\">&quot;#4caf50&quot;</span>,\n        <span class=\"hljs-string\">&quot;error_color&quot;</span>: <span class=\"hljs-string\">&quot;#f44336&quot;</span>,\n        <span class=\"hljs-string\">&quot;border_color&quot;</span>: <span class=\"hljs-string\">&quot;#e0e0e0&quot;</span>,\n        <span class=\"hljs-string\">&quot;hover_color&quot;</span>: <span class=\"hljs-string\">&quot;#f5f5f5&quot;</span>,\n        <span class=\"hljs-string\">&quot;pressed_color&quot;</span>: <span class=\"hljs-string\">&quot;#e0e0e0&quot;</span>,\n    }\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"533,566"}}],"payload":{"tag":"h3","lines":"531,532"}},{"content":"&#x7ec4;&#x4ef6;&#x4e3b;&#x9898;&#x9002;&#x914d;","children":[{"content":"<pre data-lines=\"569,624\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">ThemedComponent</span>(<span class=\"hljs-title class_ inherited__\">QWidget</span>):\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x4e3b;&#x9898;&#x5316;&#x7ec4;&#x4ef6;&#x57fa;&#x7c7b;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">__init__</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-built_in\">super</span>().__init__()\n        <span class=\"hljs-variable language_\">self</span>.setup_theme()\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_theme</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x8bbe;&#x7f6e;&#x4e3b;&#x9898;&#x6837;&#x5f0f;&quot;&quot;&quot;</span>\n        current_theme = get_current_theme()\n        <span class=\"hljs-variable language_\">self</span>.apply_theme_styles(current_theme)\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">apply_theme_styles</span>(<span class=\"hljs-params\">self, theme</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x5e94;&#x7528;&#x4e3b;&#x9898;&#x6837;&#x5f0f;&quot;&quot;&quot;</span>\n        <span class=\"hljs-comment\"># &#x7ec4;&#x4ef6;&#x7ea7;&#x522b;&#x7684;&#x6837;&#x5f0f;&#x63a7;&#x5236;</span>\n        <span class=\"hljs-keyword\">if</span> theme == <span class=\"hljs-string\">&quot;dark&quot;</span>:\n            <span class=\"hljs-variable language_\">self</span>.setStyleSheet(<span class=\"hljs-variable language_\">self</span>.get_dark_stylesheet())\n        <span class=\"hljs-keyword\">else</span>:\n            <span class=\"hljs-variable language_\">self</span>.setStyleSheet(<span class=\"hljs-variable language_\">self</span>.get_light_stylesheet())\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">get_dark_stylesheet</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x83b7;&#x53d6;&#x6697;&#x9ed1;&#x4e3b;&#x9898;&#x6837;&#x5f0f;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-string\">&quot;&quot;&quot;\n            QWidget {\n                background-color: #2d2d2d;\n                color: #ffffff;\n                border: 1px solid #404040;\n            }\n            QWidget:hover {\n                background-color: #404040;\n            }\n            QLabel {\n                color: #ffffff;\n                background-color: transparent;\n            }\n        &quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">get_light_stylesheet</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x83b7;&#x53d6;&#x660e;&#x4eae;&#x4e3b;&#x9898;&#x6837;&#x5f0f;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-string\">&quot;&quot;&quot;\n            QWidget {\n                background-color: #ffffff;\n                color: #333333;\n                border: 1px solid #e0e0e0;\n            }\n            QWidget:hover {\n                background-color: #f5f5f5;\n            }\n            QLabel {\n                color: #333333;\n                background-color: transparent;\n            }\n        &quot;&quot;&quot;</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"569,624"}}],"payload":{"tag":"h3","lines":"567,568"}},{"content":"&#x6837;&#x5f0f;&#x51b2;&#x7a81;&#x89e3;&#x51b3;&#x65b9;&#x6848;","children":[{"content":"<pre data-lines=\"627,694\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">ProductListItem</span>(<span class=\"hljs-title class_ inherited__\">ThemedComponent</span>):\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x9879;&#x7ec4;&#x4ef6;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_ui</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x8bbe;&#x7f6e;&#x7528;&#x6237;&#x754c;&#x9762;&quot;&quot;&quot;</span>\n        <span class=\"hljs-comment\"># 1. &#x521b;&#x5efa;&#x5e03;&#x5c40;&#x548c;&#x7ec4;&#x4ef6;</span>\n        layout = QVBoxLayout(<span class=\"hljs-variable language_\">self</span>)\n        \n        <span class=\"hljs-comment\"># 2. &#x83b7;&#x53d6;&#x4e3b;&#x9898;&#x989c;&#x8272;</span>\n        colors = <span class=\"hljs-variable language_\">self</span>.get_theme_colors()\n        \n        <span class=\"hljs-comment\"># 3. &#x5e94;&#x7528;&#x7ec4;&#x4ef6;&#x7ea7;&#x522b;&#x6837;&#x5f0f;&#xff08;&#x907f;&#x514d;&#x88ab;&#x5916;&#x90e8;&#x6837;&#x5f0f;&#x8986;&#x76d6;&#xff09;</span>\n        <span class=\"hljs-variable language_\">self</span>.apply_component_styles()\n        \n        <span class=\"hljs-comment\"># 4. &#x4e3a;&#x6bcf;&#x4e2a;&#x5b50;&#x7ec4;&#x4ef6;&#x8bbe;&#x7f6e;&#x6837;&#x5f0f;</span>\n        <span class=\"hljs-variable language_\">self</span>.setup_child_styles(colors)\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">apply_component_styles</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x5e94;&#x7528;&#x7ec4;&#x4ef6;&#x7ea7;&#x522b;&#x6837;&#x5f0f;&quot;&quot;&quot;</span>\n        current_theme = get_current_theme()\n        \n        <span class=\"hljs-comment\"># &#x901a;&#x8fc7;&#x7ec4;&#x4ef6;&#x7ea7;&#x522b;&#x7684;&#x6837;&#x5f0f;&#x8bbe;&#x7f6e;&#xff0c;&#x786e;&#x4fdd;&#x6837;&#x5f0f;&#x4f18;&#x5148;&#x7ea7;</span>\n        <span class=\"hljs-keyword\">if</span> current_theme == <span class=\"hljs-string\">&quot;dark&quot;</span>:\n            <span class=\"hljs-variable language_\">self</span>.setStyleSheet(<span class=\"hljs-string\">f&quot;&quot;&quot;\n                ProductListItem {{\n                    background-color: #2d2d2d;\n                    color: #ffffff;\n                    border: 1px solid #404040;\n                    border-radius: 4px;\n                    margin: 2px;\n                }}\n                ProductListItem:hover {{\n                    background-color: #404040;\n                }}\n                ProductListItem QLabel {{\n                    color: #ffffff;\n                    background-color: transparent;\n                }}\n            &quot;&quot;&quot;</span>)\n        <span class=\"hljs-keyword\">else</span>:\n            <span class=\"hljs-variable language_\">self</span>.setStyleSheet(<span class=\"hljs-string\">f&quot;&quot;&quot;\n                ProductListItem {{\n                    background-color: #ffffff;\n                    color: #333333;\n                    border: 1px solid #e0e0e0;\n                    border-radius: 4px;\n                    margin: 2px;\n                }}\n                ProductListItem:hover {{\n                    background-color: #f5f5f5;\n                }}\n                ProductListItem QLabel {{\n                    color: #333333;\n                    background-color: transparent;\n                }}\n            &quot;&quot;&quot;</span>)\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_child_styles</span>(<span class=\"hljs-params\">self, colors</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x8bbe;&#x7f6e;&#x5b50;&#x7ec4;&#x4ef6;&#x6837;&#x5f0f;&quot;&quot;&quot;</span>\n        <span class=\"hljs-comment\"># &#x4e3a;&#x6bcf;&#x4e2a;&#x6807;&#x7b7e;&#x8bbe;&#x7f6e;&#x660e;&#x786e;&#x7684;&#x6837;&#x5f0f;&#xff0c;&#x5305;&#x62ec;&#x80cc;&#x666f;&#x8272;</span>\n        <span class=\"hljs-keyword\">for</span> label <span class=\"hljs-keyword\">in</span> <span class=\"hljs-variable language_\">self</span>.findChildren(QLabel):\n            label.setStyleSheet(<span class=\"hljs-string\">f&quot;&quot;&quot;\n                color: <span class=\"hljs-subst\">{colors[<span class=\"hljs-string\">&apos;text&apos;</span>]}</span>;\n                background-color: transparent;\n            &quot;&quot;&quot;</span>)\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"627,694"}}],"payload":{"tag":"h3","lines":"625,626"}}],"payload":{"tag":"h2","lines":"529,530"}},{"content":"&#x6570;&#x636e;&#x6a21;&#x578b;&#x8bbe;&#x8ba1;","children":[{"content":"&#x6838;&#x5fc3;&#x6a21;&#x578b;&#x5173;&#x7cfb;","children":[{"content":"<pre data-lines=\"699,705\"><code class=\"language-python\"><span class=\"hljs-comment\"># &#x7b80;&#x5316;&#x7684;&#x6570;&#x636e;&#x6a21;&#x578b;&#x5173;&#x7cfb;</span>\nProduct (<span class=\"hljs-number\">1</span>) &#x2190;&#x2192; (N) Source      <span class=\"hljs-comment\"># &#x4e00;&#x4e2a;&#x4ea7;&#x54c1;&#x5bf9;&#x5e94;&#x591a;&#x4e2a;&#x8d27;&#x6e90;</span>\nProduct (<span class=\"hljs-number\">1</span>) &#x2190;&#x2192; (N) CustomField <span class=\"hljs-comment\"># &#x4e00;&#x4e2a;&#x4ea7;&#x54c1;&#x5bf9;&#x5e94;&#x591a;&#x4e2a;&#x81ea;&#x5b9a;&#x4e49;&#x5b57;&#x6bb5;</span>\nProduct (<span class=\"hljs-number\">1</span>) &#x2190;&#x2192; (N) ProductImage <span class=\"hljs-comment\"># &#x4e00;&#x4e2a;&#x4ea7;&#x54c1;&#x5bf9;&#x5e94;&#x591a;&#x5f20;&#x56fe;&#x7247;</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"699,705"}}],"payload":{"tag":"h3","lines":"697,698"}},{"content":"Product &#x6a21;&#x578b;","children":[{"content":"<pre data-lines=\"708,756\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">Product</span>:\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x4ea7;&#x54c1;&#x6a21;&#x578b; - &#x6838;&#x5fc3;&#x5b9e;&#x4f53;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-comment\"># &#x57fa;&#x672c;&#x5c5e;&#x6027;</span>\n    <span class=\"hljs-built_in\">id</span>: <span class=\"hljs-built_in\">int</span>                    <span class=\"hljs-comment\"># &#x4e3b;&#x952e;</span>\n    name: <span class=\"hljs-built_in\">str</span>                  <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x540d;&#x79f0;</span>\n    sku: <span class=\"hljs-built_in\">str</span>                   <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x7f16;&#x7801;</span>\n    selling_price: <span class=\"hljs-built_in\">float</span>       <span class=\"hljs-comment\"># &#x552e;&#x4ef7;</span>\n    created_at: datetime       <span class=\"hljs-comment\"># &#x521b;&#x5efa;&#x65f6;&#x95f4;</span>\n    updated_at: datetime       <span class=\"hljs-comment\"># &#x66f4;&#x65b0;&#x65f6;&#x95f4;</span>\n    \n    <span class=\"hljs-comment\"># &#x5173;&#x8054;&#x5173;&#x7cfb;</span>\n    sources: <span class=\"hljs-type\">List</span>[Source]      <span class=\"hljs-comment\"># &#x8d27;&#x6e90;&#x5217;&#x8868;</span>\n    custom_fields: <span class=\"hljs-type\">List</span>[CustomField] <span class=\"hljs-comment\"># &#x81ea;&#x5b9a;&#x4e49;&#x5b57;&#x6bb5;</span>\n    images: <span class=\"hljs-type\">List</span>[<span class=\"hljs-built_in\">str</span>]          <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x8def;&#x5f84;&#x5217;&#x8868;</span>\n    \n    <span class=\"hljs-comment\"># &#x8ba1;&#x7b97;&#x5c5e;&#x6027;</span>\n<span class=\"hljs-meta\">    @property</span>\n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">lowest_cost</span>(<span class=\"hljs-params\">self</span>) -&gt; <span class=\"hljs-built_in\">float</span>:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x6700;&#x4f4e;&#x6210;&#x672c;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-built_in\">min</span>(source.price <span class=\"hljs-keyword\">for</span> source <span class=\"hljs-keyword\">in</span> <span class=\"hljs-variable language_\">self</span>.sources) <span class=\"hljs-keyword\">if</span> <span class=\"hljs-variable language_\">self</span>.sources <span class=\"hljs-keyword\">else</span> <span class=\"hljs-number\">0</span>\n    \n<span class=\"hljs-meta\">    @property</span>\n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">highest_cost</span>(<span class=\"hljs-params\">self</span>) -&gt; <span class=\"hljs-built_in\">float</span>:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x6700;&#x9ad8;&#x6210;&#x672c;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-built_in\">max</span>(source.price <span class=\"hljs-keyword\">for</span> source <span class=\"hljs-keyword\">in</span> <span class=\"hljs-variable language_\">self</span>.sources) <span class=\"hljs-keyword\">if</span> <span class=\"hljs-variable language_\">self</span>.sources <span class=\"hljs-keyword\">else</span> <span class=\"hljs-number\">0</span>\n    \n<span class=\"hljs-meta\">    @property</span>\n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">average_cost</span>(<span class=\"hljs-params\">self</span>) -&gt; <span class=\"hljs-built_in\">float</span>:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x5e73;&#x5747;&#x6210;&#x672c;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-built_in\">sum</span>(source.price <span class=\"hljs-keyword\">for</span> source <span class=\"hljs-keyword\">in</span> <span class=\"hljs-variable language_\">self</span>.sources) / <span class=\"hljs-built_in\">len</span>(<span class=\"hljs-variable language_\">self</span>.sources) <span class=\"hljs-keyword\">if</span> <span class=\"hljs-variable language_\">self</span>.sources <span class=\"hljs-keyword\">else</span> <span class=\"hljs-number\">0</span>\n    \n<span class=\"hljs-meta\">    @property</span>\n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">profit</span>(<span class=\"hljs-params\">self</span>) -&gt; <span class=\"hljs-built_in\">float</span>:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x5229;&#x6da6;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-variable language_\">self</span>.selling_price - <span class=\"hljs-variable language_\">self</span>.lowest_cost\n    \n<span class=\"hljs-meta\">    @property</span>\n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">profit_margin</span>(<span class=\"hljs-params\">self</span>) -&gt; <span class=\"hljs-built_in\">float</span>:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x5229;&#x6da6;&#x7387;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> (<span class=\"hljs-variable language_\">self</span>.profit / <span class=\"hljs-variable language_\">self</span>.selling_price * <span class=\"hljs-number\">100</span>) <span class=\"hljs-keyword\">if</span> <span class=\"hljs-variable language_\">self</span>.selling_price &gt; <span class=\"hljs-number\">0</span> <span class=\"hljs-keyword\">else</span> <span class=\"hljs-number\">0</span>\n    \n<span class=\"hljs-meta\">    @property</span>\n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">best_source</span>(<span class=\"hljs-params\">self</span>) -&gt; <span class=\"hljs-type\">Optional</span>[Source]:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x6700;&#x4f18;&#x8d27;&#x6e90;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-built_in\">min</span>(<span class=\"hljs-variable language_\">self</span>.sources, key=<span class=\"hljs-keyword\">lambda</span> s: s.price) <span class=\"hljs-keyword\">if</span> <span class=\"hljs-variable language_\">self</span>.sources <span class=\"hljs-keyword\">else</span> <span class=\"hljs-literal\">None</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"708,756"}}],"payload":{"tag":"h3","lines":"706,707"}},{"content":"Source &#x6a21;&#x578b;","children":[{"content":"<pre data-lines=\"759,778\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">Source</span>:\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x8d27;&#x6e90;&#x6a21;&#x578b;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-built_in\">id</span>: <span class=\"hljs-built_in\">int</span>                    <span class=\"hljs-comment\"># &#x4e3b;&#x952e;</span>\n    product_id: <span class=\"hljs-built_in\">int</span>           <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;ID</span>\n    name: <span class=\"hljs-built_in\">str</span>                 <span class=\"hljs-comment\"># &#x4f9b;&#x5e94;&#x5546;&#x540d;&#x79f0;</span>\n    price: <span class=\"hljs-built_in\">float</span>              <span class=\"hljs-comment\"># &#x5355;&#x4ef7;</span>\n    url: <span class=\"hljs-built_in\">str</span>                  <span class=\"hljs-comment\"># &#x7f51;&#x5740;&#x94fe;&#x63a5;</span>\n    shop_info: <span class=\"hljs-built_in\">str</span>            <span class=\"hljs-comment\"># &#x5e97;&#x94fa;&#x4fe1;&#x606f;</span>\n    quantity: <span class=\"hljs-built_in\">int</span>             <span class=\"hljs-comment\"># &#x5e93;&#x5b58;&#x6570;&#x91cf;</span>\n    created_at: datetime      <span class=\"hljs-comment\"># &#x521b;&#x5efa;&#x65f6;&#x95f4;</span>\n    updated_at: datetime      <span class=\"hljs-comment\"># &#x66f4;&#x65b0;&#x65f6;&#x95f4;</span>\n    \n<span class=\"hljs-meta\">    @property</span>\n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">total_cost</span>(<span class=\"hljs-params\">self</span>) -&gt; <span class=\"hljs-built_in\">float</span>:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x603b;&#x6210;&#x672c;&quot;&quot;&quot;</span>\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-variable language_\">self</span>.price * <span class=\"hljs-variable language_\">self</span>.quantity\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"759,778"}}],"payload":{"tag":"h3","lines":"757,758"}},{"content":"CustomField &#x6a21;&#x578b;","children":[{"content":"<pre data-lines=\"781,792\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">CustomField</span>:\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x81ea;&#x5b9a;&#x4e49;&#x5b57;&#x6bb5;&#x6a21;&#x578b;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-built_in\">id</span>: <span class=\"hljs-built_in\">int</span>                    <span class=\"hljs-comment\"># &#x4e3b;&#x952e;</span>\n    product_id: <span class=\"hljs-built_in\">int</span>           <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;ID</span>\n    name: <span class=\"hljs-built_in\">str</span>                 <span class=\"hljs-comment\"># &#x5b57;&#x6bb5;&#x540d;&#x79f0;</span>\n    value: <span class=\"hljs-built_in\">str</span>                <span class=\"hljs-comment\"># &#x5b57;&#x6bb5;&#x503c;</span>\n    field_type: <span class=\"hljs-built_in\">str</span>           <span class=\"hljs-comment\"># &#x5b57;&#x6bb5;&#x7c7b;&#x578b;</span>\n    created_at: datetime      <span class=\"hljs-comment\"># &#x521b;&#x5efa;&#x65f6;&#x95f4;</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"781,792"}}],"payload":{"tag":"h3","lines":"779,780"}}],"payload":{"tag":"h2","lines":"695,696"}},{"content":"&#x754c;&#x9762;&#x8bbe;&#x8ba1;","children":[{"content":"&#x4e3b;&#x7a97;&#x53e3;&#x5e03;&#x5c40;","children":[{"content":"<pre data-lines=\"797,822\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">MainWindow</span>(<span class=\"hljs-title class_ inherited__\">QMainWindow</span>):\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x4e3b;&#x7a97;&#x53e3; - &#x91c7;&#x7528;&#x5de6;&#x53f3;&#x5206;&#x680f;&#x5e03;&#x5c40;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">__init__</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-built_in\">super</span>().__init__()\n        <span class=\"hljs-variable language_\">self</span>.setup_ui()\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_ui</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-comment\"># &#x5de6;&#x4fa7;&#xff1a;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x533a;&#x57df;</span>\n        <span class=\"hljs-variable language_\">self</span>.product_list = ProductListWidget()\n        \n        <span class=\"hljs-comment\"># &#x53f3;&#x4fa7;&#xff1a;&#x8be6;&#x60c5;/&#x5bf9;&#x6bd4;&#x533a;&#x57df;</span>\n        <span class=\"hljs-variable language_\">self</span>.detail_stack = QStackedWidget()\n        <span class=\"hljs-variable language_\">self</span>.detail_stack.addWidget(ProductDetailWidget())\n        <span class=\"hljs-variable language_\">self</span>.detail_stack.addWidget(ComparisonWidget())\n        \n        <span class=\"hljs-comment\"># &#x4e3b;&#x5e03;&#x5c40;</span>\n        splitter = QSplitter(Qt.Horizontal)\n        splitter.addWidget(<span class=\"hljs-variable language_\">self</span>.product_list)\n        splitter.addWidget(<span class=\"hljs-variable language_\">self</span>.detail_stack)\n        splitter.setSizes([<span class=\"hljs-number\">300</span>, <span class=\"hljs-number\">700</span>])  <span class=\"hljs-comment\"># 3:7&#x6bd4;&#x4f8b;</span>\n        \n        <span class=\"hljs-variable language_\">self</span>.setCentralWidget(splitter)\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"797,822"}}],"payload":{"tag":"h3","lines":"795,796"}},{"content":"&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x7ec4;&#x4ef6;","children":[{"content":"<pre data-lines=\"825,857\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">ProductListWidget</span>(<span class=\"hljs-title class_ inherited__\">QWidget</span>):\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x7ec4;&#x4ef6;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">__init__</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-built_in\">super</span>().__init__()\n        <span class=\"hljs-variable language_\">self</span>.setup_ui()\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_ui</span>(<span class=\"hljs-params\">self</span>):\n        layout = QVBoxLayout()\n        \n        <span class=\"hljs-comment\"># &#x641c;&#x7d22;&#x680f;</span>\n        <span class=\"hljs-variable language_\">self</span>.search_bar = QLineEdit()\n        <span class=\"hljs-variable language_\">self</span>.search_bar.setPlaceholderText(<span class=\"hljs-string\">&quot;&#x641c;&#x7d22;&#x4ea7;&#x54c1;...&quot;</span>)\n        \n        <span class=\"hljs-comment\"># &#x6392;&#x5e8f;&#x9009;&#x62e9;</span>\n        <span class=\"hljs-variable language_\">self</span>.sort_combo = QComboBox()\n        <span class=\"hljs-variable language_\">self</span>.sort_combo.addItems([<span class=\"hljs-string\">&quot;&#x6309;&#x540d;&#x79f0;&quot;</span>, <span class=\"hljs-string\">&quot;&#x6309;&#x4ef7;&#x683c;&quot;</span>, <span class=\"hljs-string\">&quot;&#x6309;&#x5229;&#x6da6;&quot;</span>, <span class=\"hljs-string\">&quot;&#x6309;&#x65f6;&#x95f4;&quot;</span>])\n        \n        <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x5217;&#x8868;</span>\n        <span class=\"hljs-variable language_\">self</span>.product_list = QListWidget()\n        \n        <span class=\"hljs-comment\"># &#x5de5;&#x5177;&#x680f;</span>\n        <span class=\"hljs-variable language_\">self</span>.toolbar = <span class=\"hljs-variable language_\">self</span>.create_toolbar()\n        \n        layout.addWidget(<span class=\"hljs-variable language_\">self</span>.search_bar)\n        layout.addWidget(<span class=\"hljs-variable language_\">self</span>.sort_combo)\n        layout.addWidget(<span class=\"hljs-variable language_\">self</span>.product_list)\n        layout.addWidget(<span class=\"hljs-variable language_\">self</span>.toolbar)\n        \n        <span class=\"hljs-variable language_\">self</span>.setLayout(layout)\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"825,857"}}],"payload":{"tag":"h3","lines":"823,824"}},{"content":"&#x56fe;&#x7247;&#x8f6e;&#x64ad;&#x7ec4;&#x4ef6;","children":[{"content":"<pre data-lines=\"860,900\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">ImageCarousel</span>(<span class=\"hljs-title class_ inherited__\">QWidget</span>):\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x56fe;&#x7247;&#x8f6e;&#x64ad;&#x7ec4;&#x4ef6; - &#x6a21;&#x4eff;MacOS&#x7248;&#x672c;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">__init__</span>(<span class=\"hljs-params\">self, image_paths: <span class=\"hljs-type\">List</span>[<span class=\"hljs-built_in\">str</span>]</span>):\n        <span class=\"hljs-built_in\">super</span>().__init__()\n        <span class=\"hljs-variable language_\">self</span>.image_paths = image_paths\n        <span class=\"hljs-variable language_\">self</span>.current_index = <span class=\"hljs-number\">0</span>\n        <span class=\"hljs-variable language_\">self</span>.setup_ui()\n        <span class=\"hljs-variable language_\">self</span>.setup_timer()\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_ui</span>(<span class=\"hljs-params\">self</span>):\n        layout = QVBoxLayout()\n        \n        <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x663e;&#x793a;&#x533a;&#x57df;</span>\n        <span class=\"hljs-variable language_\">self</span>.image_label = QLabel()\n        <span class=\"hljs-variable language_\">self</span>.image_label.setScaledContents(<span class=\"hljs-literal\">True</span>)\n        <span class=\"hljs-variable language_\">self</span>.image_label.setMinimumSize(<span class=\"hljs-number\">400</span>, <span class=\"hljs-number\">300</span>)\n        \n        <span class=\"hljs-comment\"># &#x63a7;&#x5236;&#x6309;&#x94ae;</span>\n        controls = QHBoxLayout()\n        <span class=\"hljs-variable language_\">self</span>.prev_btn = QPushButton(<span class=\"hljs-string\">&quot;&#x25c0;&quot;</span>)\n        <span class=\"hljs-variable language_\">self</span>.next_btn = QPushButton(<span class=\"hljs-string\">&quot;&#x25b6;&quot;</span>)\n        <span class=\"hljs-variable language_\">self</span>.page_label = QLabel(<span class=\"hljs-string\">&quot;1/3&quot;</span>)\n        \n        controls.addWidget(<span class=\"hljs-variable language_\">self</span>.prev_btn)\n        controls.addWidget(<span class=\"hljs-variable language_\">self</span>.page_label)\n        controls.addWidget(<span class=\"hljs-variable language_\">self</span>.next_btn)\n        \n        layout.addWidget(<span class=\"hljs-variable language_\">self</span>.image_label)\n        layout.addLayout(controls)\n        \n        <span class=\"hljs-variable language_\">self</span>.setLayout(layout)\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_timer</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x81ea;&#x52a8;&#x64ad;&#x653e;&#x5b9a;&#x65f6;&#x5668;&quot;&quot;&quot;</span>\n        <span class=\"hljs-variable language_\">self</span>.timer = QTimer()\n        <span class=\"hljs-variable language_\">self</span>.timer.timeout.connect(<span class=\"hljs-variable language_\">self</span>.next_image)\n        <span class=\"hljs-variable language_\">self</span>.timer.start(<span class=\"hljs-number\">3000</span>)  <span class=\"hljs-comment\"># 3&#x79d2;&#x95f4;&#x9694;</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"860,900"}}],"payload":{"tag":"h3","lines":"858,859"}},{"content":"&#x5bf9;&#x6bd4;&#x5206;&#x6790;&#x89c6;&#x56fe;","children":[{"content":"<pre data-lines=\"903,939\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">ComparisonWidget</span>(<span class=\"hljs-title class_ inherited__\">QWidget</span>):\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x5bf9;&#x6bd4;&#x5206;&#x6790;&#x89c6;&#x56fe;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">__init__</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-built_in\">super</span>().__init__()\n        <span class=\"hljs-variable language_\">self</span>.setup_ui()\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_ui</span>(<span class=\"hljs-params\">self</span>):\n        layout = QVBoxLayout()\n        \n        <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x9009;&#x62e9;&#x533a;&#x57df;</span>\n        <span class=\"hljs-variable language_\">self</span>.product_selector = QComboBox()\n        <span class=\"hljs-variable language_\">self</span>.product_selector.setMultipleSelection(<span class=\"hljs-literal\">True</span>)\n        \n        <span class=\"hljs-comment\"># &#x5bf9;&#x6bd4;&#x8868;&#x683c;</span>\n        <span class=\"hljs-variable language_\">self</span>.comparison_table = QTableWidget()\n        <span class=\"hljs-variable language_\">self</span>.setup_table_headers()\n        \n        <span class=\"hljs-comment\"># &#x56fe;&#x8868;&#x533a;&#x57df;</span>\n        <span class=\"hljs-variable language_\">self</span>.chart_widget = <span class=\"hljs-variable language_\">self</span>.create_chart_widget()\n        \n        layout.addWidget(QLabel(<span class=\"hljs-string\">&quot;&#x9009;&#x62e9;&#x8981;&#x5bf9;&#x6bd4;&#x7684;&#x4ea7;&#x54c1;:&quot;</span>))\n        layout.addWidget(<span class=\"hljs-variable language_\">self</span>.product_selector)\n        layout.addWidget(<span class=\"hljs-variable language_\">self</span>.comparison_table)\n        layout.addWidget(<span class=\"hljs-variable language_\">self</span>.chart_widget)\n        \n        <span class=\"hljs-variable language_\">self</span>.setLayout(layout)\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">setup_table_headers</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x8bbe;&#x7f6e;&#x8868;&#x683c;&#x5934;&#x90e8;&quot;&quot;&quot;</span>\n        headers = [<span class=\"hljs-string\">&quot;&#x4ea7;&#x54c1;&#x540d;&#x79f0;&quot;</span>, <span class=\"hljs-string\">&quot;&#x6700;&#x4f4e;&#x6210;&#x672c;&quot;</span>, <span class=\"hljs-string\">&quot;&#x6700;&#x9ad8;&#x6210;&#x672c;&quot;</span>, <span class=\"hljs-string\">&quot;&#x5e73;&#x5747;&#x6210;&#x672c;&quot;</span>, \n                  <span class=\"hljs-string\">&quot;&#x552e;&#x4ef7;&quot;</span>, <span class=\"hljs-string\">&quot;&#x5229;&#x6da6;&quot;</span>, <span class=\"hljs-string\">&quot;&#x5229;&#x6da6;&#x7387;&quot;</span>, <span class=\"hljs-string\">&quot;&#x6700;&#x4f18;&#x8d27;&#x6e90;&quot;</span>]\n        <span class=\"hljs-variable language_\">self</span>.comparison_table.setColumnCount(<span class=\"hljs-built_in\">len</span>(headers))\n        <span class=\"hljs-variable language_\">self</span>.comparison_table.setHorizontalHeaderLabels(headers)\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"903,939"}}],"payload":{"tag":"h3","lines":"901,902"}}],"payload":{"tag":"h2","lines":"793,794"}},{"content":"&#x6838;&#x5fc3;&#x529f;&#x80fd;&#x5b9e;&#x73b0;","children":[{"content":"&#x6570;&#x636e;&#x5e93;&#x7ba1;&#x7406;","children":[{"content":"<pre data-lines=\"944,1017\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">DatabaseManager</span>:\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x6570;&#x636e;&#x5e93;&#x7ba1;&#x7406;&#x5668; - &#x7b80;&#x5316;&#x7248;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">__init__</span>(<span class=\"hljs-params\">self, db_path: <span class=\"hljs-built_in\">str</span> = <span class=\"hljs-string\">&quot;data/database.db&quot;</span></span>):\n        <span class=\"hljs-variable language_\">self</span>.db_path = db_path\n        <span class=\"hljs-variable language_\">self</span>.connection = <span class=\"hljs-literal\">None</span>\n        <span class=\"hljs-variable language_\">self</span>.init_database()\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">init_database</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x521d;&#x59cb;&#x5316;&#x6570;&#x636e;&#x5e93;&quot;&quot;&quot;</span>\n        <span class=\"hljs-variable language_\">self</span>.connection = sqlite3.connect(<span class=\"hljs-variable language_\">self</span>.db_path)\n        <span class=\"hljs-variable language_\">self</span>.create_tables()\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">create_tables</span>(<span class=\"hljs-params\">self</span>):\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x521b;&#x5efa;&#x6570;&#x636e;&#x8868;&quot;&quot;&quot;</span>\n        cursor = <span class=\"hljs-variable language_\">self</span>.connection.cursor()\n        \n        <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x8868;</span>\n        cursor.execute(<span class=\"hljs-string\">&apos;&apos;&apos;\n            CREATE TABLE IF NOT EXISTS products (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                name TEXT NOT NULL,\n                sku TEXT,\n                selling_price REAL NOT NULL,\n                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n            )\n        &apos;&apos;&apos;</span>)\n        \n        <span class=\"hljs-comment\"># &#x8d27;&#x6e90;&#x8868;</span>\n        cursor.execute(<span class=\"hljs-string\">&apos;&apos;&apos;\n            CREATE TABLE IF NOT EXISTS sources (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                product_id INTEGER NOT NULL,\n                name TEXT NOT NULL,\n                price REAL NOT NULL,\n                url TEXT,\n                shop_info TEXT,\n                quantity INTEGER DEFAULT 1,\n                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                FOREIGN KEY (product_id) REFERENCES products (id)\n            )\n        &apos;&apos;&apos;</span>)\n        \n        <span class=\"hljs-comment\"># &#x81ea;&#x5b9a;&#x4e49;&#x5b57;&#x6bb5;&#x8868;</span>\n        cursor.execute(<span class=\"hljs-string\">&apos;&apos;&apos;\n            CREATE TABLE IF NOT EXISTS custom_fields (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                product_id INTEGER NOT NULL,\n                name TEXT NOT NULL,\n                value TEXT,\n                field_type TEXT DEFAULT &apos;text&apos;,\n                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                FOREIGN KEY (product_id) REFERENCES products (id)\n            )\n        &apos;&apos;&apos;</span>)\n        \n        <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x56fe;&#x7247;&#x8868;</span>\n        cursor.execute(<span class=\"hljs-string\">&apos;&apos;&apos;\n            CREATE TABLE IF NOT EXISTS product_images (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                product_id INTEGER NOT NULL,\n                image_path TEXT NOT NULL,\n                is_primary BOOLEAN DEFAULT 0,\n                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                FOREIGN KEY (product_id) REFERENCES products (id)\n            )\n        &apos;&apos;&apos;</span>)\n        \n        <span class=\"hljs-variable language_\">self</span>.connection.commit()\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"944,1017"}}],"payload":{"tag":"h3","lines":"942,943"}},{"content":"&#x4ea7;&#x54c1;&#x670d;&#x52a1;","children":[{"content":"<pre data-lines=\"1020,1099\"><code class=\"language-python\"><span class=\"hljs-keyword\">class</span> <span class=\"hljs-title class_\">ProductService</span>:\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x4ea7;&#x54c1;&#x670d;&#x52a1; - &#x4e1a;&#x52a1;&#x903b;&#x8f91;&#x5c42;&quot;&quot;&quot;</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">__init__</span>(<span class=\"hljs-params\">self, db_manager: DatabaseManager</span>):\n        <span class=\"hljs-variable language_\">self</span>.db = db_manager\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">create_product</span>(<span class=\"hljs-params\">self, name: <span class=\"hljs-built_in\">str</span>, sku: <span class=\"hljs-built_in\">str</span>, selling_price: <span class=\"hljs-built_in\">float</span></span>) -&gt; Product:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x521b;&#x5efa;&#x4ea7;&#x54c1;&quot;&quot;&quot;</span>\n        cursor = <span class=\"hljs-variable language_\">self</span>.db.connection.cursor()\n        cursor.execute(\n            <span class=\"hljs-string\">&quot;INSERT INTO products (name, sku, selling_price) VALUES (?, ?, ?)&quot;</span>,\n            (name, sku, selling_price)\n        )\n        <span class=\"hljs-variable language_\">self</span>.db.connection.commit()\n        \n        product_id = cursor.lastrowid\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-variable language_\">self</span>.get_product(product_id)\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">get_product</span>(<span class=\"hljs-params\">self, product_id: <span class=\"hljs-built_in\">int</span></span>) -&gt; Product:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x83b7;&#x53d6;&#x4ea7;&#x54c1;&quot;&quot;&quot;</span>\n        cursor = <span class=\"hljs-variable language_\">self</span>.db.connection.cursor()\n        cursor.execute(<span class=\"hljs-string\">&quot;SELECT * FROM products WHERE id = ?&quot;</span>, (product_id,))\n        row = cursor.fetchone()\n        \n        <span class=\"hljs-keyword\">if</span> row:\n            product = Product(*row)\n            product.sources = <span class=\"hljs-variable language_\">self</span>.get_product_sources(product_id)\n            product.custom_fields = <span class=\"hljs-variable language_\">self</span>.get_product_custom_fields(product_id)\n            product.images = <span class=\"hljs-variable language_\">self</span>.get_product_images(product_id)\n            <span class=\"hljs-keyword\">return</span> product\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-literal\">None</span>\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">get_all_products</span>(<span class=\"hljs-params\">self</span>) -&gt; <span class=\"hljs-type\">List</span>[Product]:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x83b7;&#x53d6;&#x6240;&#x6709;&#x4ea7;&#x54c1;&quot;&quot;&quot;</span>\n        cursor = <span class=\"hljs-variable language_\">self</span>.db.connection.cursor()\n        cursor.execute(<span class=\"hljs-string\">&quot;SELECT * FROM products ORDER BY created_at DESC&quot;</span>)\n        rows = cursor.fetchall()\n        \n        products = []\n        <span class=\"hljs-keyword\">for</span> row <span class=\"hljs-keyword\">in</span> rows:\n            product = Product(*row)\n            product.sources = <span class=\"hljs-variable language_\">self</span>.get_product_sources(product.<span class=\"hljs-built_in\">id</span>)\n            products.append(product)\n        \n        <span class=\"hljs-keyword\">return</span> products\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">add_source</span>(<span class=\"hljs-params\">self, product_id: <span class=\"hljs-built_in\">int</span>, name: <span class=\"hljs-built_in\">str</span>, price: <span class=\"hljs-built_in\">float</span>, **kwargs</span>) -&gt; Source:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x6dfb;&#x52a0;&#x8d27;&#x6e90;&quot;&quot;&quot;</span>\n        cursor = <span class=\"hljs-variable language_\">self</span>.db.connection.cursor()\n        cursor.execute(\n            <span class=\"hljs-string\">&quot;INSERT INTO sources (product_id, name, price, url, shop_info, quantity) VALUES (?, ?, ?, ?, ?, ?)&quot;</span>,\n            (product_id, name, price, kwargs.get(<span class=\"hljs-string\">&apos;url&apos;</span>, <span class=\"hljs-string\">&apos;&apos;</span>), kwargs.get(<span class=\"hljs-string\">&apos;shop_info&apos;</span>, <span class=\"hljs-string\">&apos;&apos;</span>), kwargs.get(<span class=\"hljs-string\">&apos;quantity&apos;</span>, <span class=\"hljs-number\">1</span>))\n        )\n        <span class=\"hljs-variable language_\">self</span>.db.connection.commit()\n        \n        source_id = cursor.lastrowid\n        <span class=\"hljs-keyword\">return</span> <span class=\"hljs-variable language_\">self</span>.get_source(source_id)\n    \n    <span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">compare_products</span>(<span class=\"hljs-params\">self, product_ids: <span class=\"hljs-type\">List</span>[<span class=\"hljs-built_in\">int</span>]</span>) -&gt; <span class=\"hljs-type\">List</span>[<span class=\"hljs-type\">Dict</span>]:\n        <span class=\"hljs-string\">&quot;&quot;&quot;&#x4ea7;&#x54c1;&#x5bf9;&#x6bd4;&#x5206;&#x6790;&quot;&quot;&quot;</span>\n        products = [<span class=\"hljs-variable language_\">self</span>.get_product(pid) <span class=\"hljs-keyword\">for</span> pid <span class=\"hljs-keyword\">in</span> product_ids]\n        \n        comparison_data = []\n        <span class=\"hljs-keyword\">for</span> product <span class=\"hljs-keyword\">in</span> products:\n            <span class=\"hljs-keyword\">if</span> product:\n                comparison_data.append({\n                    <span class=\"hljs-string\">&apos;name&apos;</span>: product.name,\n                    <span class=\"hljs-string\">&apos;lowest_cost&apos;</span>: product.lowest_cost,\n                    <span class=\"hljs-string\">&apos;highest_cost&apos;</span>: product.highest_cost,\n                    <span class=\"hljs-string\">&apos;average_cost&apos;</span>: product.average_cost,\n                    <span class=\"hljs-string\">&apos;selling_price&apos;</span>: product.selling_price,\n                    <span class=\"hljs-string\">&apos;profit&apos;</span>: product.profit,\n                    <span class=\"hljs-string\">&apos;profit_margin&apos;</span>: product.profit_margin,\n                    <span class=\"hljs-string\">&apos;best_source&apos;</span>: product.best_source.name <span class=\"hljs-keyword\">if</span> product.best_source <span class=\"hljs-keyword\">else</span> <span class=\"hljs-string\">&apos;N/A&apos;</span>\n                })\n        \n        <span class=\"hljs-keyword\">return</span> comparison_data\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"1020,1099"}}],"payload":{"tag":"h3","lines":"1018,1019"}}],"payload":{"tag":"h2","lines":"940,941"}},{"content":"&#x5e94;&#x7528;&#x5165;&#x53e3;","children":[{"content":"<pre data-lines=\"1102,1135\"><code class=\"language-python\"><span class=\"hljs-comment\"># main.py</span>\n<span class=\"hljs-keyword\">import</span> sys\n<span class=\"hljs-keyword\">from</span> PyQt6.QtWidgets <span class=\"hljs-keyword\">import</span> QApplication\n<span class=\"hljs-keyword\">from</span> PyQt6.QtCore <span class=\"hljs-keyword\">import</span> Qt\n\n<span class=\"hljs-keyword\">from</span> models.database <span class=\"hljs-keyword\">import</span> DatabaseManager\n<span class=\"hljs-keyword\">from</span> views.main_window <span class=\"hljs-keyword\">import</span> MainWindow\n<span class=\"hljs-keyword\">from</span> config <span class=\"hljs-keyword\">import</span> Config\n\n<span class=\"hljs-keyword\">def</span> <span class=\"hljs-title function_\">main</span>():\n    <span class=\"hljs-string\">&quot;&quot;&quot;&#x5e94;&#x7528;&#x7a0b;&#x5e8f;&#x5165;&#x53e3;&quot;&quot;&quot;</span>\n    <span class=\"hljs-comment\"># &#x8bbe;&#x7f6e;&#x9ad8;DPI&#x652f;&#x6301;</span>\n    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)\n    \n    <span class=\"hljs-comment\"># &#x521b;&#x5efa;&#x5e94;&#x7528;</span>\n    app = QApplication(sys.argv)\n    app.setApplicationName(<span class=\"hljs-string\">&quot;&#x7535;&#x5546;&#x4ea7;&#x54c1;&#x5bf9;&#x6bd4;&#x9009;&#x54c1;&#x5de5;&#x5177;&quot;</span>)\n    app.setApplicationVersion(<span class=\"hljs-string\">&quot;1.0.0&quot;</span>)\n    \n    <span class=\"hljs-comment\"># &#x521d;&#x59cb;&#x5316;&#x6570;&#x636e;&#x5e93;</span>\n    db_manager = DatabaseManager()\n    \n    <span class=\"hljs-comment\"># &#x521b;&#x5efa;&#x4e3b;&#x7a97;&#x53e3;</span>\n    main_window = MainWindow(db_manager)\n    main_window.show()\n    \n    <span class=\"hljs-comment\"># &#x8fd0;&#x884c;&#x5e94;&#x7528;</span>\n    sys.exit(app.<span class=\"hljs-built_in\">exec</span>())\n\n<span class=\"hljs-keyword\">if</span> __name__ == <span class=\"hljs-string\">&quot;__main__&quot;</span>:\n    main()\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"1102,1135"}}],"payload":{"tag":"h2","lines":"1100,1101"}},{"content":"&#x5f00;&#x53d1;&#x8ba1;&#x5212;","children":[{"content":"&#x7b2c;&#x4e00;&#x9636;&#x6bb5;&#xff1a;&#x57fa;&#x7840;&#x6846;&#x67b6;&#xff08;1-2&#x5929;&#xff09;","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x9879;&#x76ee;&#x7ed3;&#x6784;&#x642d;&#x5efa;","children":[],"payload":{"tag":"li","lines":"1139,1140"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x6570;&#x636e;&#x6a21;&#x578b;&#x8bbe;&#x8ba1;","children":[],"payload":{"tag":"li","lines":"1140,1141"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x6570;&#x636e;&#x5e93;&#x7ba1;&#x7406;","children":[],"payload":{"tag":"li","lines":"1141,1142"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x4e3b;&#x7a97;&#x53e3;&#x6846;&#x67b6;","children":[],"payload":{"tag":"li","lines":"1142,1144"}}],"payload":{"tag":"h3","lines":"1138,1139"}},{"content":"&#x7b2c;&#x4e8c;&#x9636;&#x6bb5;&#xff1a;&#x6838;&#x5fc3;&#x529f;&#x80fd;&#xff08;2-3&#x5929;&#xff09;","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x4ea7;&#x54c1;&#x7ba1;&#x7406;&#xff08;&#x589e;&#x5220;&#x6539;&#x67e5;&#xff09;","children":[],"payload":{"tag":"li","lines":"1145,1146"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x8d27;&#x6e90;&#x7ba1;&#x7406;","children":[],"payload":{"tag":"li","lines":"1146,1147"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x56fe;&#x7247;&#x7ba1;&#x7406;","children":[],"payload":{"tag":"li","lines":"1147,1148"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x57fa;&#x7840;&#x754c;&#x9762;","children":[],"payload":{"tag":"li","lines":"1148,1150"}}],"payload":{"tag":"h3","lines":"1144,1145"}},{"content":"&#x7b2c;&#x4e09;&#x9636;&#x6bb5;&#xff1a;&#x9ad8;&#x7ea7;&#x529f;&#x80fd;&#xff08;1-2&#x5929;&#xff09;","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x4ea7;&#x54c1;&#x5bf9;&#x6bd4;&#x5206;&#x6790;","children":[],"payload":{"tag":"li","lines":"1151,1152"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x56fe;&#x7247;&#x8f6e;&#x64ad;&#x7ec4;&#x4ef6;","children":[],"payload":{"tag":"li","lines":"1152,1153"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x6570;&#x636e;&#x5bfc;&#x5165;&#x5bfc;&#x51fa;","children":[],"payload":{"tag":"li","lines":"1153,1154"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x641c;&#x7d22;&#x8fc7;&#x6ee4;","children":[],"payload":{"tag":"li","lines":"1154,1156"}}],"payload":{"tag":"h3","lines":"1150,1151"}},{"content":"&#x7b2c;&#x56db;&#x9636;&#x6bb5;&#xff1a;&#x4f18;&#x5316;&#x5b8c;&#x5584;&#xff08;1&#x5929;&#xff09;","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x6027;&#x80fd;&#x4f18;&#x5316;","children":[],"payload":{"tag":"li","lines":"1157,1158"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x754c;&#x9762;&#x7f8e;&#x5316;","children":[],"payload":{"tag":"li","lines":"1158,1159"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x9519;&#x8bef;&#x5904;&#x7406;","children":[],"payload":{"tag":"li","lines":"1159,1160"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path fill-rule=\"evenodd\" d=\"M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z\" clip-rule=\"evenodd\"/></svg> &#x6d4b;&#x8bd5;&#x5b8c;&#x5584;","children":[],"payload":{"tag":"li","lines":"1160,1162"}}],"payload":{"tag":"h3","lines":"1156,1157"}}],"payload":{"tag":"h2","lines":"1136,1137"}},{"content":"&#x603b;&#x7ed3;","children":[{"content":"&#x4f18;&#x52bf;","children":[{"content":"1. <strong>&#x8f7b;&#x91cf;&#x7ea7;</strong>&#xff1a;&#x4ec5;6&#x4e2a;&#x6838;&#x5fc3;&#x4f9d;&#x8d56;&#x5305;","children":[],"payload":{"tag":"li","lines":"1167,1168","listIndex":1}},{"content":"2. <strong>&#x9ad8;&#x6027;&#x80fd;</strong>&#xff1a;&#x76f4;&#x63a5;&#x7684;&#x6570;&#x636e;&#x5e93;&#x64cd;&#x4f5c;&#xff0c;&#x65e0;ORM&#x5f00;&#x9500;","children":[],"payload":{"tag":"li","lines":"1168,1169","listIndex":2}},{"content":"3. <strong>&#x7b80;&#x6d01;&#x6027;</strong>&#xff1a;&#x6241;&#x5e73;&#x5316;&#x9879;&#x76ee;&#x7ed3;&#x6784;&#xff0c;&#x6613;&#x4e8e;&#x7ef4;&#x62a4;","children":[],"payload":{"tag":"li","lines":"1169,1170","listIndex":3}},{"content":"4. <strong>&#x4e13;&#x6ce8;&#x6027;</strong>&#xff1a;&#x4e13;&#x6ce8;&#x6838;&#x5fc3;&#x529f;&#x80fd;&#xff0c;&#x907f;&#x514d;&#x529f;&#x80fd;&#x81a8;&#x80c0;","children":[],"payload":{"tag":"li","lines":"1170,1171","listIndex":4}},{"content":"5. <strong>&#x53ef;&#x6269;&#x5c55;</strong>&#xff1a;&#x6e05;&#x6670;&#x7684;&#x67b6;&#x6784;&#xff0c;&#x4fbf;&#x4e8e;&#x540e;&#x7eed;&#x6269;&#x5c55;","children":[],"payload":{"tag":"li","lines":"1171,1173","listIndex":5}}],"payload":{"tag":"h3","lines":"1166,1167"}},{"content":"&#x6280;&#x672f;&#x7279;&#x70b9;","children":[{"content":"<strong>PyQt6</strong>&#xff1a;&#x73b0;&#x4ee3;&#x5316;GUI&#x6846;&#x67b6;&#xff0c;&#x6027;&#x80fd;&#x4f18;&#x5f02;","children":[],"payload":{"tag":"li","lines":"1174,1175"}},{"content":"<strong>SQLite</strong>&#xff1a;&#x8f7b;&#x91cf;&#x7ea7;&#x6570;&#x636e;&#x5e93;&#xff0c;&#x65e0;&#x9700;&#x914d;&#x7f6e;","children":[],"payload":{"tag":"li","lines":"1175,1176"}},{"content":"<strong>&#x76f4;&#x63a5;&#x64cd;&#x4f5c;</strong>&#xff1a;&#x907f;&#x514d;&#x590d;&#x6742;&#x7684;ORM&#xff0c;&#x63d0;&#x9ad8;&#x6027;&#x80fd;","children":[],"payload":{"tag":"li","lines":"1176,1177"}},{"content":"<strong>&#x6a21;&#x5757;&#x5316;</strong>&#xff1a;&#x6e05;&#x6670;&#x7684;&#x4ee3;&#x7801;&#x7ec4;&#x7ec7;&#xff0c;&#x4fbf;&#x4e8e;&#x7ef4;&#x62a4;","children":[],"payload":{"tag":"li","lines":"1177,1179"}}],"payload":{"tag":"h3","lines":"1173,1174"}}],"payload":{"tag":"h2","lines":"1162,1163"}}],"payload":{"tag":"h1","lines":"0,1"}},{})</script>
</body>
</html>
