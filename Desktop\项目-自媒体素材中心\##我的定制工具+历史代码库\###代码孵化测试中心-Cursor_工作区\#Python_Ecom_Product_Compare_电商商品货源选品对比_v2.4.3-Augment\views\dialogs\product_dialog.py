"""
产品编辑对话框
支持产品的添加和编辑
"""

from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QFormLayout,
    QLineEdit,
    QTextEdit,
    QDoubleSpinBox,
    QSpinBox,
    QDialogButtonBox,
    QMessageBox,
    QTabWidget,
    QWidget,
    QListWidget,
    QPushButton,
    QLabel,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QAbstractItemView,
    QGroupBox,
    QScrollArea,
    QCheckBox,
    QGridLayout,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from models.product import Product
from models.source import Source
from models.database import ProductService, TagService
from models.tag import Tag
from views.dialogs.source_dialog import SourceDialog


class ProductDialog(QDialog):
    """产品编辑对话框"""

    # 信号定义
    product_saved = pyqtSignal(object)  # 产品保存完成

    def __init__(
        self,
        product: Product = None,
        product_service: ProductService = None,
        tag_service: TagService = None,
        parent=None,
    ):
        try:
            print("ProductDialog.__init__() 开始")
            super().__init__(parent)
            print("super().__init__() 完成")

            self.product = product
            self.product_service = product_service
            self.tag_service = tag_service
            self.is_edit_mode = product is not None
            print(f"基本属性设置完成: is_edit_mode={self.is_edit_mode}")

            # 初始化必要的属性
            self.image_paths = []
            self.sources_data = []
            self.selected_tags = []
            print("属性初始化完成")

            print("开始设置UI...")
            self.setup_ui()
            print("UI设置完成")

            if self.is_edit_mode:
                print("编辑模式，开始加载产品数据...")
                self.load_product_data()
                print("产品数据加载完成")

            print("ProductDialog.__init__() 完成")

        except Exception as e:
            print(f"ProductDialog.__init__() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            raise

    def setup_ui(self):
        """设置用户界面"""
        try:
            print("setup_ui() 开始")
            self.setWindowTitle("编辑产品" if self.is_edit_mode else "添加产品")
            print("窗口标题设置完成")

            self.setMinimumSize(1300, 600)
            self.resize(1300, 600)  # 设置初始大小
            print("窗口大小设置完成")

            # 设置对话框字体
            font = QFont("Microsoft YaHei UI", 12)
            self.setFont(font)
            print("字体设置完成")

        except Exception as e:
            print(f"setup_ui() 开始部分发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            raise

        # 设置输入框样式（支持暗黑模式）
        self.setStyleSheet(
            """
            QLineEdit {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                color: white;
                padding: 6px;
                border-radius: 3px;
            }
            QLineEdit:focus {
                border: 2px solid #1976d2;
            }
            QTextEdit {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                color: white;
                padding: 6px;
                border-radius: 3px;
            }
            QTextEdit:focus {
                border: 2px solid #1976d2;
            }
            QSpinBox {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                color: white;
                padding: 6px;
                border-radius: 3px;
            }
            QSpinBox:focus {
                border: 2px solid #1976d2;
            }
            QDoubleSpinBox {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                color: white;
                padding: 6px;
                border-radius: 3px;
            }
            QDoubleSpinBox:focus {
                border: 2px solid #1976d2;
            }
            QLabel {
                color: white;
            }
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: white;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3d3d3d;
            }
            QTabBar::tab {
                background-color: #2b2b2b;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #1976d2;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #444444;
            }
        """
        )

        layout = QVBoxLayout(self)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(
            "QTabWidget::tab-bar { font-size: 12px; } QTabBar::tab { font-size: 12px; padding: 8px 16px; }"
        )

        # 基本信息选项卡
        basic_tab = self.create_basic_tab()
        self.tab_widget.addTab(basic_tab, "基本信息")

        # 详细描述选项卡

        # 图片管理选项卡
        images_tab = self.create_images_tab()
        self.tab_widget.addTab(images_tab, "产品图片")

        # 货源管理选项卡
        sources_tab = self.create_sources_tab()
        self.tab_widget.addTab(sources_tab, "货源管理")

        layout.addWidget(self.tab_widget)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.setStyleSheet("QPushButton { font-size: 12px; padding: 8px 16px; }")
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)

        # 设置焦点
        self.name_edit.setFocus()

    def create_basic_tab(self):
        """创建基本信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 表单布局
        form_layout = QFormLayout()

        # 产品名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入产品名称")
        self.name_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        form_layout.addRow("产品名称*:", self.name_edit)

        # 产品编码
        self.sku_edit = QLineEdit()
        self.sku_edit.setPlaceholderText("请输入产品编码（可选）")
        self.sku_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        form_layout.addRow("产品编码:", self.sku_edit)

        # 售价
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0, 999999.99)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" 元")
        self.price_spinbox.setStyleSheet("font-size: 12px; padding: 8px;")
        self.price_spinbox.valueChanged.connect(self.on_price_changed)
        form_layout.addRow("售价*:", self.price_spinbox)

        # 包邮选项
        self.free_shipping_checkbox = QCheckBox("包邮")
        self.free_shipping_checkbox.setChecked(True)  # 默认包邮
        self.free_shipping_checkbox.setStyleSheet(
            "font-size: 12px; font-weight: bold; color: #4caf50;"
        )
        self.free_shipping_checkbox.toggled.connect(self.on_free_shipping_changed)
        form_layout.addRow("运费选项:", self.free_shipping_checkbox)

        # 运费
        self.shipping_fee_spinbox = QDoubleSpinBox()
        self.shipping_fee_spinbox.setRange(0, 999999.99)
        self.shipping_fee_spinbox.setDecimals(2)
        self.shipping_fee_spinbox.setSuffix(" 元")
        self.shipping_fee_spinbox.setStyleSheet("font-size: 12px; padding: 8px;")
        self.shipping_fee_spinbox.setEnabled(False)  # 默认包邮时禁用
        self.shipping_fee_spinbox.valueChanged.connect(self.on_shipping_fee_changed)
        form_layout.addRow("运费:", self.shipping_fee_spinbox)

        # 商品描述
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("请输入商品详细描述（可选）")
        self.description_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        self.description_edit.setMaximumHeight(100)  # 限制高度
        form_layout.addRow("商品描述:", self.description_edit)

        # 销量参考
        self.sales_reference_spinbox = QSpinBox()
        self.sales_reference_spinbox.setRange(0, 999999)
        self.sales_reference_spinbox.setValue(0)
        self.sales_reference_spinbox.setSuffix(" 件")
        self.sales_reference_spinbox.setStyleSheet("font-size: 12px; padding: 8px;")
        form_layout.addRow("销量参考:", self.sales_reference_spinbox)

        # 参考链接
        self.reference_url_edit = QLineEdit()
        self.reference_url_edit.setPlaceholderText("https://... （可选）")
        self.reference_url_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        form_layout.addRow("参考链接:", self.reference_url_edit)

        layout.addLayout(form_layout)

        # 标签选择区域
        if self.tag_service:
            tags_group = self.create_tags_group()
            layout.addWidget(tags_group)

        layout.addStretch()

        return widget

    def create_tags_group(self):
        """创建标签选择区域"""
        group = QGroupBox("产品标签")
        group.setStyleSheet("font-size: 12px; font-weight: bold;")
        layout = QVBoxLayout(group)

        # 标签选择区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(120)
        scroll_area.setStyleSheet(
            """
            QScrollArea {
                border: 1px solid #ccc;
                background-color: #fafafa;
                border-radius: 4px;
            }
        """
        )

        # 标签容器
        tags_container = QWidget()
        self.tags_layout = QGridLayout(tags_container)
        self.tags_layout.setContentsMargins(8, 8, 8, 8)
        self.tags_layout.setSpacing(5)

        # 加载标签
        self.load_tags()

        scroll_area.setWidget(tags_container)
        layout.addWidget(scroll_area)

        # 标签操作按钮
        button_layout = QHBoxLayout()

        add_tag_btn = QPushButton("新建标签")
        add_tag_btn.setStyleSheet("font-size: 11px; padding: 4px 8px;")
        add_tag_btn.clicked.connect(self.add_new_tag)
        button_layout.addWidget(add_tag_btn)

        button_layout.addStretch()

        refresh_tags_btn = QPushButton("刷新标签")
        refresh_tags_btn.setStyleSheet("font-size: 11px; padding: 4px 8px;")
        refresh_tags_btn.clicked.connect(self.load_tags)
        button_layout.addWidget(refresh_tags_btn)

        layout.addLayout(button_layout)

        return group

    def load_tags(self):
        """加载标签"""
        if not self.tag_service:
            return

        # 清除现有标签
        for i in reversed(range(self.tags_layout.count())):
            child = self.tags_layout.itemAt(i).widget()
            if child:
                child.deleteLater()

        # 获取所有标签
        tags = self.tag_service.get_all_tags()

        # 创建标签复选框
        row = 0
        col = 0
        for tag in tags:
            checkbox = QCheckBox(tag.name)
            checkbox.setStyleSheet(
                f"""
                QCheckBox {{
                    font-size: 11px;
                    color: {tag.color};
                    font-weight: bold;
                }}
                QCheckBox::indicator {{
                    width: 16px;
                    height: 16px;
                }}
                QCheckBox::indicator:checked {{
                    background-color: {tag.color};
                    border: 1px solid {tag.color};
                }}
            """
            )
            checkbox.setProperty("tag_id", tag.id)
            checkbox.toggled.connect(self.on_tag_toggled)

            # 如果是编辑模式，设置已选中的标签
            if self.is_edit_mode and self.product:
                for product_tag in self.product.tags:
                    if product_tag.id == tag.id:
                        checkbox.setChecked(True)
                        break

            self.tags_layout.addWidget(checkbox, row, col)

            col += 1
            if col >= 3:  # 每行3个标签
                col = 0
                row += 1

    def on_tag_toggled(self, checked):
        """标签复选框状态变化"""
        checkbox = self.sender()
        tag_id = checkbox.property("tag_id")

        if checked:
            if tag_id not in self.selected_tags:
                self.selected_tags.append(tag_id)
        else:
            if tag_id in self.selected_tags:
                self.selected_tags.remove(tag_id)

    def add_new_tag(self):
        """添加新标签"""
        if not self.tag_service:
            return

        from views.dialogs.tag_manager_dialog import TagManagerDialog

        dialog = TagManagerDialog(self.tag_service, self)
        dialog.tag_updated.connect(self.load_tags)
        dialog.exec()

    def create_images_tab(self):
        """创建图片管理选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 图片列表
        images_label = QLabel("产品图片:")
        images_label.setStyleSheet("font-size: 12px; font-weight: bold;")
        layout.addWidget(images_label)

        self.images_list = QListWidget()
        self.images_list.setMaximumHeight(200)  # 增加高度以适应更大的对话框
        self.images_list.setStyleSheet("font-size: 12px;")
        layout.addWidget(self.images_list)

        # 图片操作按钮
        buttons_layout = QHBoxLayout()

        add_image_btn = QPushButton("添加图片")
        add_image_btn.setStyleSheet("font-size: 12px; padding: 8px 16px;")
        add_image_btn.clicked.connect(self.add_image)
        buttons_layout.addWidget(add_image_btn)

        remove_image_btn = QPushButton("删除图片")
        remove_image_btn.setStyleSheet("font-size: 12px; padding: 8px 16px;")
        remove_image_btn.clicked.connect(self.remove_image)
        buttons_layout.addWidget(remove_image_btn)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # 提示信息
        tip_label = QLabel(
            "提示: 支持 JPG、PNG、BMP、GIF、WebP 格式图片，图片将自动复制到项目目录"
        )
        tip_label.setStyleSheet("color: #666; font-size: 12px;")
        tip_label.setWordWrap(True)  # 允许文字换行
        layout.addWidget(tip_label)

        layout.addStretch()

        return widget

    def add_source(self):
        """添加货源"""
        # 传递必要的参数给SourceDialog
        product_id = self.product.id if self.product else None
        dialog = SourceDialog(
            product_id=product_id, product_service=self.product_service, parent=self
        )
        if dialog.exec() == QDialog.DialogCode.Accepted:
            source_data = dialog.get_source_data()
            print(f"从SourceDialog获取的数据: {source_data}")
            try:
                # 创建临时Source对象用于显示，使用负数ID表示临时对象
                temp_source = Source(
                    id=-(len(self.sources_data) + 1),  # 使用负数临时ID
                    product_id=product_id or 0,  # 使用实际product_id或0
                    **source_data,
                )
                print(f"创建的临时Source对象: {temp_source}")
                self.sources_data.append(temp_source)
                self.refresh_sources_table()
                print("货源添加到sources_data成功")
            except Exception as e:
                print(f"创建Source对象失败: {str(e)}")
                import traceback

                traceback.print_exc()
                QMessageBox.critical(self, "错误", f"添加货源失败: {str(e)}")

    def edit_source(self):
        """编辑货源"""
        current_row = self.sources_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请选择要编辑的货源")
            return

        if current_row >= len(self.sources_data):
            return

        source = self.sources_data[current_row]
        dialog = SourceDialog(
            source=source, product_service=self.product_service, parent=self
        )
        if dialog.exec() == QDialog.DialogCode.Accepted:
            source_data = dialog.get_source_data()
            # 更新货源数据
            for key, value in source_data.items():
                setattr(source, key, value)
            self.refresh_sources_table()

    def remove_source(self):
        """删除货源"""
        current_row = self.sources_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请选择要删除的货源")
            return

        if current_row >= len(self.sources_data):
            return

        source = self.sources_data[current_row]
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除货源 '{source.name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.sources_data.pop(current_row)
            self.refresh_sources_table()

    def refresh_sources_table(self):
        """刷新货源表格"""
        self.sources_table.setRowCount(len(self.sources_data))

        for row, source in enumerate(self.sources_data):
            # 供应商名称
            self.sources_table.setItem(row, 0, QTableWidgetItem(source.name))

            # 单价
            self.sources_table.setItem(row, 1, QTableWidgetItem(f"¥{source.price:.2f}"))

            # 数量
            self.sources_table.setItem(row, 2, QTableWidgetItem(str(source.quantity)))

            # 运费
            self.sources_table.setItem(
                row, 3, QTableWidgetItem(f"¥{source.shipping_cost:.2f}")
            )

            # 模式
            self.sources_table.setItem(row, 4, QTableWidgetItem(source.modes_display))

            # 总成本(含运费)
            self.sources_table.setItem(
                row, 5, QTableWidgetItem(f"¥{source.total_cost_with_shipping:.2f}")
            )

            # 利润计算 - 根据包邮情况计算实际收款价格
            base_selling_price = (
                self.price_spinbox.value() if hasattr(self, "price_spinbox") else 0
            )
            shipping_fee = (
                self.shipping_fee_spinbox.value()
                if hasattr(self, "shipping_fee_spinbox")
                else 0
            )
            is_free_shipping = (
                self.free_shipping_checkbox.isChecked()
                if hasattr(self, "free_shipping_checkbox")
                else True
            )

            # 实际收款价格 = 基础售价 + 运费（如果不包邮）
            actual_selling_price = (
                base_selling_price
                if is_free_shipping
                else base_selling_price + shipping_fee
            )

            unit_cost_with_shipping = source.price + source.shipping_cost
            profit = actual_selling_price - unit_cost_with_shipping

            profit_item = QTableWidgetItem(f"¥{profit:.2f}")
            from PyQt6.QtGui import QColor

            if profit > 0:
                profit_item.setForeground(QColor("#FF69B4"))  # 粉红色
            elif profit == 0:
                profit_item.setForeground(QColor("#FFA500"))  # 橙色
            else:
                profit_item.setForeground(QColor("#f44336"))  # 红色
            self.sources_table.setItem(row, 6, profit_item)

            # 操作列暂时留空
            self.sources_table.setItem(row, 7, QTableWidgetItem(""))

        # 更新汇总信息
        self.update_sources_summary()

    def update_sources_summary(self):
        """更新货源汇总信息"""
        if not self.sources_data:
            self.total_sources_label.setText("0")
            self.lowest_cost_label.setText("¥0.00")
            self.highest_cost_label.setText("¥0.00")
            self.average_cost_label.setText("¥0.00")
            if hasattr(self, "profit_summary_label"):
                self.profit_summary_label.setText("¥0.00")
            return

        costs = [source.total_cost_with_shipping for source in self.sources_data]

        self.total_sources_label.setText(str(len(self.sources_data)))
        self.lowest_cost_label.setText(f"¥{min(costs):.2f}")
        self.highest_cost_label.setText(f"¥{max(costs):.2f}")
        self.average_cost_label.setText(f"¥{sum(costs)/len(costs):.2f}")

        # 计算利润汇总 - 根据包邮情况计算实际收款价格
        base_selling_price = (
            self.price_spinbox.value() if hasattr(self, "price_spinbox") else 0
        )
        shipping_fee = (
            self.shipping_fee_spinbox.value()
            if hasattr(self, "shipping_fee_spinbox")
            else 0
        )
        is_free_shipping = (
            self.free_shipping_checkbox.isChecked()
            if hasattr(self, "free_shipping_checkbox")
            else True
        )

        # 实际收款价格 = 基础售价 + 运费（如果不包邮）
        actual_selling_price = (
            base_selling_price
            if is_free_shipping
            else base_selling_price + shipping_fee
        )

        if actual_selling_price > 0 and hasattr(self, "profit_summary_label"):
            profits = [
                actual_selling_price - (source.price + source.shipping_cost)
                for source in self.sources_data
            ]
            avg_profit = sum(profits) / len(profits)
            self.profit_summary_label.setText(f"¥{avg_profit:.2f}")
        elif hasattr(self, "profit_summary_label"):
            self.profit_summary_label.setText("需要售价")

    def create_sources_tab(self):
        """创建货源管理选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 货源列表标题
        sources_label = QLabel("货源列表:")
        sources_label.setStyleSheet("font-size: 12px; font-weight: bold;")
        layout.addWidget(sources_label)

        # 货源表格
        self.sources_table = QTableWidget()
        self.sources_table.setColumnCount(8)
        self.sources_table.setHorizontalHeaderLabels(
            ["供应商", "单价", "数量", "运费", "模式", "总成本(含运费)", "利润", "操作"]
        )

        # 设置表格样式
        self.sources_table.setStyleSheet("font-size: 12px;")
        self.sources_table.setSelectionBehavior(
            QAbstractItemView.SelectionBehavior.SelectRows
        )
        self.sources_table.setAlternatingRowColors(True)

        # 调整列宽
        header = self.sources_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 供应商列自适应
        for i in range(1, 7):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        layout.addWidget(self.sources_table)

        # 货源操作按钮
        sources_buttons_layout = QHBoxLayout()

        add_source_btn = QPushButton("添加货源")
        add_source_btn.setStyleSheet("font-size: 12px; padding: 8px 16px;")
        add_source_btn.clicked.connect(self.add_source)
        sources_buttons_layout.addWidget(add_source_btn)

        edit_source_btn = QPushButton("编辑货源")
        edit_source_btn.setStyleSheet("font-size: 12px; padding: 8px 16px;")
        edit_source_btn.clicked.connect(self.edit_source)
        sources_buttons_layout.addWidget(edit_source_btn)

        remove_source_btn = QPushButton("删除货源")
        remove_source_btn.setStyleSheet("font-size: 12px; padding: 8px 16px;")
        remove_source_btn.clicked.connect(self.remove_source)
        sources_buttons_layout.addWidget(remove_source_btn)

        sources_buttons_layout.addStretch()

        layout.addLayout(sources_buttons_layout)

        # 成本汇总信息
        summary_layout = QFormLayout()

        self.total_sources_label = QLabel("0")
        self.total_sources_label.setStyleSheet("font-weight: bold; color: #1976d2;")
        summary_layout.addRow("货源总数:", self.total_sources_label)

        self.lowest_cost_label = QLabel("¥0.00")
        self.lowest_cost_label.setStyleSheet("font-weight: bold; color: #4caf50;")
        summary_layout.addRow("最低成本:", self.lowest_cost_label)

        self.highest_cost_label = QLabel("¥0.00")
        self.highest_cost_label.setStyleSheet("font-weight: bold; color: #f44336;")
        summary_layout.addRow("最高成本:", self.highest_cost_label)

        self.average_cost_label = QLabel("¥0.00")
        self.average_cost_label.setStyleSheet("font-weight: bold; color: #FFD700;")
        summary_layout.addRow("平均成本:", self.average_cost_label)

        self.profit_summary_label = QLabel("¥0.00")
        self.profit_summary_label.setStyleSheet("font-weight: bold; color: #FF69B4;")
        summary_layout.addRow("平均利润:", self.profit_summary_label)

        layout.addLayout(summary_layout)

        return widget

    def load_product_data(self):
        """加载产品数据"""
        if not self.product:
            return

        self.name_edit.setText(self.product.name)
        self.sku_edit.setText(self.product.sku)
        self.price_spinbox.setValue(self.product.selling_price)

        # 加载运费相关字段
        self.shipping_fee_spinbox.setValue(getattr(self.product, "shipping_fee", 0.0))
        free_shipping = getattr(self.product, "free_shipping", True)
        self.free_shipping_checkbox.setChecked(free_shipping)
        self.on_free_shipping_changed(free_shipping)  # 触发UI状态更新

        self.description_edit.setPlainText(self.product.description)
        self.sales_reference_spinbox.setValue(self.product.sales_reference)
        self.reference_url_edit.setText(self.product.reference_url)

        # 加载图片
        self.image_paths = self.product.images.copy()
        self._refresh_images_list()

        # 加载货源
        if self.product_service:
            self.sources_data = self.product_service.get_product_sources(
                self.product.id
            )
            self.refresh_sources_table()

        # 加载已选择的标签
        if self.tag_service and self.product:
            self.selected_tags = [tag.id for tag in self.product.tags]

    def get_product_data(self):
        """获取产品数据"""
        try:
            print("get_product_data() 开始")

            # 检查UI控件是否存在
            if not hasattr(self, "name_edit") or self.name_edit is None:
                raise AttributeError("name_edit控件不存在")
            if not hasattr(self, "sku_edit") or self.sku_edit is None:
                raise AttributeError("sku_edit控件不存在")
            if not hasattr(self, "price_spinbox") or self.price_spinbox is None:
                raise AttributeError("price_spinbox控件不存在")
            if not hasattr(self, "description_edit") or self.description_edit is None:
                raise AttributeError("description_edit控件不存在")
            if (
                not hasattr(self, "sales_reference_spinbox")
                or self.sales_reference_spinbox is None
            ):
                raise AttributeError("sales_reference_spinbox控件不存在")
            if (
                not hasattr(self, "reference_url_edit")
                or self.reference_url_edit is None
            ):
                raise AttributeError("reference_url_edit控件不存在")
            print("UI控件检查完成")

            data = {
                "name": self.name_edit.text().strip(),
                "sku": self.sku_edit.text().strip(),
                "selling_price": self.price_spinbox.value(),
                "shipping_fee": self.shipping_fee_spinbox.value(),
                "free_shipping": self.free_shipping_checkbox.isChecked(),
                "description": self.description_edit.toPlainText().strip(),
                "sales_reference": self.sales_reference_spinbox.value(),
                "reference_url": self.reference_url_edit.text().strip(),
                "images": self.image_paths if hasattr(self, "image_paths") else [],
            }
            print("产品数据获取完成")
            return data

        except Exception as e:
            print(f"get_product_data() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            raise

    def add_image(self):
        """添加图片"""
        from PyQt6.QtWidgets import QFileDialog
        from config import IMAGE_CONFIG

        # 构建文件过滤器
        filters = []
        for fmt in IMAGE_CONFIG["supported_formats"]:
            filters.append(f"*{fmt}")
        filter_string = f"图片文件 ({' '.join(filters)});;所有文件 (*.*)"

        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择图片文件", "", filter_string
        )

        for file_path in file_paths:
            # 检查是否已存在
            if file_path not in self.image_paths:
                self.image_paths.append(file_path)
                self.images_list.addItem(file_path)

    def remove_image(self):
        """删除图片"""
        current_row = self.images_list.currentRow()
        if current_row >= 0 and current_row < len(self.image_paths):
            # 从image_paths中移除
            removed_path = self.image_paths.pop(current_row)
            # 从列表显示中移除
            self.images_list.takeItem(current_row)

    def validate_input(self):
        """验证输入"""
        try:
            print("开始获取产品数据...")
            data = self.get_product_data()
            print(f"获取到的产品数据: {data}")

            # 验证必填字段
            print("验证产品名称...")
            if not data["name"]:
                print("产品名称为空")
                QMessageBox.warning(self, "验证错误", "请输入产品名称")
                self.name_edit.setFocus()
                return False

            print("验证产品价格...")
            if data["selling_price"] <= 0:
                print(f"产品价格无效: {data['selling_price']}")
                QMessageBox.warning(self, "验证错误", "售价必须大于0")
                self.price_spinbox.setFocus()
                return False

            print("输入验证通过")
            return True
        except Exception as e:
            print(f"validate_input() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"验证输入时发生错误: {str(e)}")
            return False

    def save_product(self):
        """保存产品"""
        try:
            print("save_product() 开始执行")
            product_data = self.get_product_data()
            print(f"产品数据: {product_data}")
            print(f"编辑模式: {self.is_edit_mode}")

            if self.is_edit_mode:
                # 编辑模式
                self.product.name = product_data["name"]
                self.product.sku = product_data["sku"]
                self.product.selling_price = product_data["selling_price"]
                self.product.shipping_fee = product_data["shipping_fee"]
                self.product.free_shipping = product_data["free_shipping"]
                self.product.description = product_data["description"]
                self.product.sales_reference = product_data["sales_reference"]
                self.product.reference_url = product_data["reference_url"]
                self.product.images = product_data["images"]

                success = self.product_service.update_product(self.product)
                if success:
                    # 保存标签关联
                    self.save_product_tags(self.product.id)

                    # QMessageBox.information(self, "成功", "产品更新成功")  # 移除弹窗

                    # 保存货源
                    saved_sources = self.save_sources(self.product.id)
                    if saved_sources > 0:
                        # QMessageBox.information(self, "成功", f"成功保存 {saved_sources} 个货源")  # 移除弹窗
                        pass

                    self.product_saved.emit(self.product)
                    return True
                else:
                    QMessageBox.critical(self, "错误", "产品更新失败")
                    return False
            else:
                # 添加模式
                print("创建新产品...")
                print(f"产品服务: {self.product_service}")
                new_product = self.product_service.create_product(
                    name=product_data["name"],
                    sku=product_data["sku"],
                    selling_price=product_data["selling_price"],
                    shipping_fee=product_data["shipping_fee"],
                    free_shipping=product_data["free_shipping"],
                    description=product_data["description"],
                    sales_reference=product_data["sales_reference"],
                    reference_url=product_data["reference_url"],
                    images=product_data["images"],
                )
                print(f"创建的产品: {new_product}")

                if new_product:
                    print("产品创建成功，开始保存标签...")
                    # 保存标签关联
                    self.save_product_tags(new_product.id)
                    print("标签保存完成")

                    print("显示成功消息...")
                    # QMessageBox.information(self, "成功", "产品添加成功")  # 移除弹窗
                    print("成功消息显示完成")

                    print("开始保存货源...")
                    # 保存货源
                    saved_sources = self.save_sources(new_product.id)
                    print(f"保存了 {saved_sources} 个货源")
                    if saved_sources > 0:
                        # QMessageBox.information(self, "成功", f"成功保存 {saved_sources} 个货源")  # 移除弹窗
                        pass

                    print("发出产品保存信号...")
                    self.product_saved.emit(new_product)
                    print("save_product() 返回 True")
                    return True
                else:
                    QMessageBox.critical(self, "错误", "产品添加失败")
                    return False

        except Exception as e:
            print(f"save_product() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"保存产品时发生错误: {str(e)}")
            return False

    def _refresh_images_list(self):
        """刷新图片列表显示"""
        self.images_list.clear()
        for image_path in self.image_paths:
            self.images_list.addItem(image_path)

    def save_sources(self, product_id: int) -> int:
        """保存货源信息，返回保存的货源数量"""
        try:
            print("save_sources() 开始")
            if not self.product_service:
                print("product_service为空，跳过货源保存")
                return 0

            print(f"保存货源，产品ID: {product_id}")
            print(f"货源数据: {len(self.sources_data)} 个")

            saved_count = 0

            # 如果是编辑模式，先删除现有的货源
            if self.is_edit_mode and hasattr(self, "product") and self.product:
                print("编辑模式，删除现有货源...")
                existing_sources = self.product_service.get_product_sources(product_id)
                for source in existing_sources:
                    self.product_service.delete_source(source.id)

            # 添加新的货源
            print("添加新的货源...")
            for i, source_data in enumerate(self.sources_data):
                try:
                    print(f"添加货源 {i+1}: {source_data.name}")
                    # 将Source对象转换为字典以传递给add_source
                    if hasattr(source_data, "to_dict"):
                        source_dict = source_data.to_dict()
                        print(f"货源字典数据: {source_dict}")
                    else:
                        # 如果不是Source对象，直接使用属性
                        source_dict = {
                            "name": getattr(source_data, "name", ""),
                            "price": getattr(source_data, "price", 0.0),
                            "url": getattr(source_data, "url", ""),
                            "shop_info": getattr(source_data, "shop_info", ""),
                            "quantity": getattr(source_data, "quantity", 1),
                            "min_order_quantity": getattr(
                                source_data, "min_order_quantity", 1
                            ),
                            "shipping_cost": getattr(source_data, "shipping_cost", 0.0),
                            "shipping_cost_suffix": getattr(
                                source_data, "shipping_cost_suffix", ""
                            ),
                            "shipping_location": getattr(
                                source_data, "shipping_location", ""
                            ),
                            "product_name": getattr(source_data, "product_name", ""),
                            "sales_count": getattr(source_data, "sales_count", 0),
                            "status": getattr(source_data, "status", "active"),
                            "return_policy": getattr(source_data, "return_policy", ""),
                            "modes": getattr(source_data, "modes", ["wholesale"]),
                            "contact_info": getattr(source_data, "contact_info", ""),
                            "notes": getattr(source_data, "notes", ""),
                            "is_active": getattr(source_data, "is_active", True),
                            # 批发模式字段
                            "pickup_rate_24h": getattr(
                                source_data, "pickup_rate_24h", 0.0
                            ),
                            "pickup_rate_48h": getattr(
                                source_data, "pickup_rate_48h", 0.0
                            ),
                            "monthly_dropship_orders": getattr(
                                source_data, "monthly_dropship_orders", 0
                            ),
                            "downstream_stores": getattr(
                                source_data, "downstream_stores", 0
                            ),
                            "distributor_count": getattr(
                                source_data, "distributor_count", 0
                            ),
                            "product_publish_time": getattr(
                                source_data, "product_publish_time", None
                            ),
                            "available_products": getattr(
                                source_data, "available_products", 0
                            ),
                            "monthly_new_products": getattr(
                                source_data, "monthly_new_products", 0
                            ),
                            "image_urls": getattr(source_data, "image_urls", []),
                            # 批发模式专属字段
                            "wholesale_min_order_quantity": getattr(
                                source_data, "wholesale_min_order_quantity", 1
                            ),
                            "wholesale_payment_terms": getattr(
                                source_data, "wholesale_payment_terms", 0
                            ),
                            "wholesale_rebate_rate": getattr(
                                source_data, "wholesale_rebate_rate", 0.0
                            ),
                            "wholesale_price_tiers": getattr(
                                source_data, "wholesale_price_tiers", ""
                            ),
                            # 代发模式专属字段
                            "dropship_service_fee": getattr(
                                source_data, "dropship_service_fee", 0.0
                            ),
                            "dropship_processing_time": getattr(
                                source_data, "dropship_processing_time", 0
                            ),
                            "dropship_packaging": getattr(
                                source_data, "dropship_packaging", ""
                            ),
                            "dropship_inventory_sync": getattr(
                                source_data, "dropship_inventory_sync", "实时同步"
                            ),
                            "dropship_min_quantity": getattr(
                                source_data, "dropship_min_quantity", 1
                            ),
                            "dropship_shipping_location": getattr(
                                source_data, "dropship_shipping_location", ""
                            ),
                            "dropship_support_regions": getattr(
                                source_data, "dropship_support_regions", ""
                            ),
                        }
                        print(f"转换后的货源字典数据: {source_dict}")

                    # 从字典中移除已经作为位置参数传递的字段
                    kwargs_dict = source_dict.copy()
                    kwargs_dict.pop("name", None)
                    kwargs_dict.pop("price", None)
                    kwargs_dict.pop("product_id", None)  # 移除product_id以避免参数冲突

                    saved_source = self.product_service.add_source(
                        product_id,
                        source_dict["name"],
                        source_dict["price"],
                        **kwargs_dict,  # 使用**kwargs传递其余参数
                    )
                    if saved_source:
                        saved_count += 1
                        print(f"货源 {i+1} 保存成功")
                    else:
                        print(f"货源 {i+1} 保存失败")
                except Exception as e:
                    print(f"保存货源 {i+1} 失败: {e}")

            print(f"货源保存完成，共保存 {saved_count} 个")
            return saved_count

        except Exception as e:
            print(f"save_sources() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            return 0

    def save_product_tags(self, product_id: int):
        """保存产品标签关联"""
        try:
            print("save_product_tags() 开始")
            if not self.tag_service:
                print("tag_service为空，跳过标签保存")
                return

            print(f"保存标签关联，产品ID: {product_id}")
            print(f"选中的标签: {self.selected_tags}")

            # 如果是编辑模式，先清除现有的标签关联
            if self.is_edit_mode:
                print("编辑模式，清除现有标签关联...")
                # 获取现有标签
                existing_tags = self.product_service.get_product_tags(product_id)
                print(f"现有标签: {existing_tags}")
                for tag in existing_tags:
                    self.product_service.remove_tag_from_product(product_id, tag.id)

            # 添加新的标签关联
            print("添加新的标签关联...")
            for tag_id in self.selected_tags:
                print(f"添加标签: {tag_id}")
                self.product_service.add_tag_to_product(product_id, tag_id)

            print("标签关联保存完成")

        except Exception as e:
            print(f"save_product_tags() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            print(f"保存标签关联失败: {e}")

    def on_price_changed(self):
        """价格变化时更新利润显示"""
        if hasattr(self, "sources_table"):
            self.refresh_sources_table()

    def on_free_shipping_changed(self, checked):
        """包邮选项变化处理"""
        self.shipping_fee_spinbox.setEnabled(not checked)
        if checked:
            # 包邮时将运费设为0
            self.shipping_fee_spinbox.setValue(0.0)
            self.free_shipping_checkbox.setStyleSheet(
                "font-size: 12px; font-weight: bold; color: #4caf50;"
            )
            self.free_shipping_checkbox.setText("包邮")
        else:
            # 不包邮时启用运费输入
            self.free_shipping_checkbox.setStyleSheet(
                "font-size: 12px; font-weight: bold; color: #ff9800;"
            )
            self.free_shipping_checkbox.setText("不包邮")

        # 更新货源汇总
        if hasattr(self, "sources_table"):
            self.refresh_sources_table()

    def on_shipping_fee_changed(self):
        """运费变化时更新货源汇总"""
        if hasattr(self, "sources_table"):
            self.refresh_sources_table()

    def accept(self):
        """确认对话框"""
        try:
            print("ProductDialog.accept() 开始执行")

            # 先验证输入
            print("开始验证输入...")
            if not self.validate_input():
                print("输入验证失败")
                return
            print("输入验证成功")

            print("开始保存产品...")
            if self.save_product():
                print("产品保存成功，关闭对话框")
                super().accept()
            else:
                print("产品保存失败")

        except Exception as e:
            print(f"ProductDialog.accept() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")
