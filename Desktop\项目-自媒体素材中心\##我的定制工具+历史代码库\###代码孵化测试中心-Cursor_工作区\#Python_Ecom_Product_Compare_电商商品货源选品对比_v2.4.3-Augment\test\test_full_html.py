#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from enhanced_data_cleaner import EnhancedDataCleaner


def test_full_html():
    """测试用户提供的完整HTML"""

    # 用户提供的完整HTML
    html_sample = """
    class="offer-title-content">商品属性</div></div><div class="offer-attr"><div class="offer-attr-switch"><i class="next-icon next-icon-arrow-down next-xxs"></i></div><div class="offer-attr-wrapper" style="height: 162px;"><div class="offer-attr-list"><div class="offer-attr-item"><span class="offer-attr-item-name">材质</span><span class="offer-attr-item-value" title="PC+硅胶">PC+硅胶</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">是否进口</span><span class="offer-attr-item-value" title="否">否</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">产品类别</span><span class="offer-attr-item-value" title="啃咬玩具">啃咬玩具</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">货号</span><span class="offer-attr-item-value" title="猫咪滚滚球">猫咪滚滚球</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">箱装数量</span><span class="offer-attr-item-value" title="200">200</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">重量</span><span class="offer-attr-item-value" title="75克">75克</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">品牌</span><span class="offer-attr-item-value" title="其他">其他</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">是否专利货源</span><span class="offer-attr-item-value" title="否">否</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">颜色</span><span class="offer-attr-item-value" title="蜻蜓带绳款带唤醒,带绳款猫球红色【非唤醒模式】,带绳款绿色猫球【非唤醒模式】,带绳款猫球蓝色【非唤醒模式】,带绳款猫球黄色【非唤醒模式】,带绳款猫球粉色【非唤醒模式】,带绳款灰色灰绳子【非唤醒模式】,带绳款灰色红绳子【非唤醒模式】,带绳款猫球紫色【非唤醒模式】,（鸟叫声）带绳款猫球红色智能,鸟叫声）毛绒款猫球红色智能（可备注颜色）,带绳款猫球红色,带绳款猫球绿色,带绳款猫球蓝色,带绳款猫球粉色,带绳款猫球黄色,带绳款猫球灰色红绳子,带绳款猫球灰色灰绳子,带绳款猫球紫色,毛绒款红色,毛绒款绿色,毛绒款灰色,魔尾款红色,魔尾款绿色,魔尾款灰色,新款横纹凸凸球蓝色,新款横纹凸凸球橙色,毛绒款猫球红色（智能）+捕猎罩,（鸟叫声）带绳款猫球绿色智能,（鸟叫声）带绳款猫球灰色智能,带绳款猫球红色+绿色,替换绳,替换毛绒,捕猎罩70cm,蜻蜓带绳款红色（带唤醒）,蜻蜓带绳款绿色（带唤醒）,蜻蜓带绳款灰色（带唤醒）,蜻蜓带绳款蓝色（带唤醒）,蜻蜓带绳款黄色（带唤醒）,蜻蜓带绳款粉色（带唤醒）,齿轮款凸凸球蓝色,齿轮款凸凸球橙色,鸟鸣猫爪款绳子球红色,鸟鸣猫爪款绳子球灰色,鸟鸣猫爪款绳子球绿色,长续航一小时绳子红带唤醒300毫安+保护板">蜻蜓带绳款带唤醒,带绳款猫球红色【非唤醒模式】,带绳款绿色猫球【非唤醒模式】,带绳款猫球蓝色【非唤醒模式】,带绳款猫球黄色【非唤醒模式】,带绳款猫球粉色【非唤醒模式】,带绳款灰色灰绳子【非唤醒模式】,带绳款灰色红绳子【非唤醒模式】,带绳款猫球紫色【非唤醒模式】,（鸟叫声）带绳款猫球红色智能,鸟叫声）毛绒款猫球红色智能（可备注颜色）,带绳款猫球红色,带绳款猫球绿色,带绳款猫球蓝色,带绳款猫球粉色,带绳款猫球黄色,带绳款猫球灰色红绳子,带绳款猫球灰色灰绳子,带绳款猫球紫色,毛绒款红色,毛绒款绿色,毛绒款灰色,魔尾款红色,魔尾款绿色,魔尾款灰色,新款横纹凸凸球蓝色,新款横纹凸凸球橙色,毛绒款猫球红色（智能）+捕猎罩,（鸟叫声）带绳款猫球绿色智能,（鸟叫声）带绳款猫球灰色智能,带绳款猫球红色+绿色,替换绳,替换毛绒,捕猎罩70cm,蜻蜓带绳款红色（带唤醒）,蜻蜓带绳款绿色（带唤醒）,蜻蜓带绳款灰色（带唤醒）,蜻蜓带绳款蓝色（带唤醒）,蜻蜓带绳款黄色（带唤醒）,蜻蜓带绳款粉色（带唤醒）,齿轮款凸凸球蓝色,齿轮款凸凸球橙色,鸟鸣猫爪款绳子球红色,鸟鸣猫爪款绳子球灰色,鸟鸣猫爪款绳子球绿色,长续航一小时绳子红带唤醒300毫安+保护板</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">是否跨境出口专供货源</span><span class="offer-attr-item-value" title="否">否</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">是否属于礼品</span><span class="offer-attr-item-value" title="否">否</span></div><div class="offer-attr-item"><span class="offer-attr-item-name">是否IP授权</span><span class="offer-attr-item-value" title="否">否</span></div></div></div></div></div><div class="od-pc-detail-description"><div data-click="超链供应商" id="detail-shadow-vender-top"></div><div class="detail-desc-module"><div id="offer-title-300252630336286-1" class="offer-title-wrapper"
    """

    print("🧪 测试完整HTML商品属性提取...")
    print(f"输入HTML长度: {len(html_sample)} 字符")
    print("=" * 60)

    # 创建清理器
    cleaner = EnhancedDataCleaner()

    # 提取属性
    result = cleaner._extract_product_attributes(html_sample)

    print("🎯 提取结果:")
    print(result)
    print("=" * 60)
    print(f"输出长度: {len(result)} 字符")


if __name__ == "__main__":
    test_full_html()
