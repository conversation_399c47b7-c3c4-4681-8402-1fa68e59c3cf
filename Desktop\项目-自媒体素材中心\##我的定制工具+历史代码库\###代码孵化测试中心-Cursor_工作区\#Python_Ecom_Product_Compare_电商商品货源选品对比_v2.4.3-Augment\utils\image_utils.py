"""
图片处理工具类
包含图片查重、相似度检测等功能
"""

import os
import hashlib
from typing import List, Tuple, Dict, Optional
from pathlib import Path
from PIL import Image
import imagehash
from PyQt6.QtWidgets import QMessageBox, QProgressDialog, QApplication
from PyQt6.QtCore import Qt, QCoreApplication
from PyQt6.QtGui import QPixmap
import logging


class ImageDuplicateChecker:
    """图片查重工具类"""

    def __init__(self):
        self.hash_size = 8  # 哈希大小，越大越精确但计算量越大
        self.similarity_threshold = 5  # 相似度阈值，越小越严格

    def calculate_image_hash(self, image_path: str) -> Optional[str]:
        """
        计算图片的感知哈希值

        Args:
            image_path: 图片路径

        Returns:
            图片哈希值，如果计算失败返回None
        """
        try:
            if not os.path.exists(image_path):
                return None

            with Image.open(image_path) as img:
                # 转换为RGB模式以确保一致性
                if img.mode != "RGB":
                    img = img.convert("RGB")

                # 计算感知哈希
                phash = imagehash.phash(img, hash_size=self.hash_size)
                return str(phash)

        except Exception as e:
            print(f"计算图片哈希失败 {image_path}: {e}")
            return None

    def calculate_hamming_distance(self, hash1: str, hash2: str) -> int:
        """
        计算两个哈希值之间的汉明距离

        Args:
            hash1: 第一个哈希值
            hash2: 第二个哈希值

        Returns:
            汉明距离（0表示完全相同）
        """
        try:
            h1 = imagehash.hex_to_hash(hash1)
            h2 = imagehash.hex_to_hash(hash2)
            return h1 - h2
        except Exception as e:
            print(f"计算汉明距离失败: {e}")
            return 999  # 返回一个很大的数表示不相似

    def are_images_similar(self, image_path1: str, image_path2: str) -> bool:
        """
        判断两张图片是否相似

        Args:
            image_path1: 第一张图片路径
            image_path2: 第二张图片路径

        Returns:
            如果相似返回True，否则返回False
        """
        hash1 = self.calculate_image_hash(image_path1)
        hash2 = self.calculate_image_hash(image_path2)

        if hash1 is None or hash2 is None:
            return False

        distance = self.calculate_hamming_distance(hash1, hash2)
        return distance <= self.similarity_threshold

    def find_duplicates(
        self, image_paths: List[str], parent_widget=None
    ) -> List[List[str]]:
        """
        查找重复或相似的图片

        Args:
            image_paths: 图片路径列表
            parent_widget: 父窗口部件，用于显示进度条

        Returns:
            重复图片组的列表，每个组包含相似的图片路径
        """
        if not image_paths:
            return []

        # 创建进度条
        progress = None
        if parent_widget:
            progress = QProgressDialog(
                "正在分析图片...", "取消", 0, len(image_paths), parent_widget
            )
            progress.setWindowTitle("图片查重")
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

        # 计算所有图片的哈希值
        image_hashes = {}
        for i, image_path in enumerate(image_paths):
            if progress:
                progress.setValue(i)
                progress.setLabelText(
                    f"正在分析图片 {i+1}/{len(image_paths)}\n{Path(image_path).name}"
                )
                QApplication.processEvents()

                if progress.wasCanceled():
                    return []

            hash_value = self.calculate_image_hash(image_path)
            if hash_value:
                image_hashes[image_path] = hash_value

        if progress:
            progress.setValue(len(image_paths))
            progress.setLabelText("正在查找重复图片...")
            QApplication.processEvents()

        # 查找重复组
        duplicate_groups = []
        processed_images = set()

        for image_path, hash_value in image_hashes.items():
            if image_path in processed_images:
                continue

            # 查找与当前图片相似的图片
            similar_images = [image_path]
            processed_images.add(image_path)

            for other_path, other_hash in image_hashes.items():
                if other_path != image_path and other_path not in processed_images:
                    distance = self.calculate_hamming_distance(hash_value, other_hash)
                    if distance <= self.similarity_threshold:
                        similar_images.append(other_path)
                        processed_images.add(other_path)

            # 如果找到相似图片，添加到重复组
            if len(similar_images) > 1:
                duplicate_groups.append(similar_images)

        if progress:
            progress.close()

        return duplicate_groups

    def get_image_info(self, image_path: str) -> Dict:
        """
        获取图片信息

        Args:
            image_path: 图片路径

        Returns:
            包含图片信息的字典
        """
        try:
            if not os.path.exists(image_path):
                return {"error": "文件不存在"}

            file_stat = os.stat(image_path)
            file_size = file_stat.st_size

            with Image.open(image_path) as img:
                width, height = img.size
                format_name = img.format
                mode = img.mode

            return {
                "path": image_path,
                "filename": Path(image_path).name,
                "size": file_size,
                "width": width,
                "height": height,
                "format": format_name,
                "mode": mode,
                "size_mb": round(file_size / (1024 * 1024), 2),
            }

        except Exception as e:
            return {"error": str(e)}

    def set_similarity_threshold(self, threshold: int):
        """
        设置相似度阈值

        Args:
            threshold: 阈值（0-64，0表示完全相同，数值越大越宽松）
        """
        self.similarity_threshold = max(0, min(64, threshold))

    def get_similarity_percentage(self, image_path1: str, image_path2: str) -> float:
        """
        获取两张图片的相似度百分比

        Args:
            image_path1: 第一张图片路径
            image_path2: 第二张图片路径

        Returns:
            相似度百分比（0-100）
        """
        hash1 = self.calculate_image_hash(image_path1)
        hash2 = self.calculate_image_hash(image_path2)

        if hash1 is None or hash2 is None:
            return 0.0

        distance = self.calculate_hamming_distance(hash1, hash2)
        max_distance = self.hash_size * self.hash_size
        similarity = (1.0 - distance / max_distance) * 100
        return max(0.0, min(100.0, similarity))


def load_image_safely(
    image_path: str, default_text: str = "无法加载图片", max_retries: int = 1
) -> QPixmap:
    """
    安全地加载图片，避免重复的错误输出

    Args:
        image_path: 图片路径
        default_text: 默认显示文本
        max_retries: 最大重试次数

    Returns:
        QPixmap对象，如果加载失败返回空的QPixmap
    """
    try:
        # 检查文件是否存在
        if not Path(image_path).exists():
            return QPixmap()

        # 尝试加载图片
        for attempt in range(max_retries + 1):
            try:
                pixmap = QPixmap(str(image_path))
                if not pixmap.isNull():
                    return pixmap

                # 如果加载失败且是第一次尝试，等待一小段时间再试
                if attempt < max_retries:
                    QCoreApplication.processEvents()
                    continue

            except Exception as e:
                if attempt == max_retries:
                    # 只在最后一次尝试失败时记录错误
                    logging.debug(
                        f"图片加载失败 ({attempt + 1}/{max_retries + 1}): {image_path} - {str(e)}"
                    )
                continue

        return QPixmap()

    except Exception as e:
        logging.debug(f"图片加载异常: {image_path} - {str(e)}")
        return QPixmap()
