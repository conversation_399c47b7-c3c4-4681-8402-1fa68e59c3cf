"""
数据映射转换器
将网页抓取和AI分析结果映射到货源数据结构
"""

import re
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal, InvalidOperation
import logging
from datetime import datetime
import json

from models.source import Source


class SourceDataMapper:
    """货源数据映射器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 价格提取正则表达式
        self.price_patterns = [
            r"[¥￥]\s*(\d+(?:\.\d+)?)",  # ¥123.45
            r"(\d+(?:\.\d+)?)\s*[元]",  # 123.45元
            r"价格[：:]\s*[¥￥]?(\d+(?:\.\d+)?)",  # 价格：123.45
            r"(\d+(?:\.\d+)?)\s*[-~～]\s*(\d+(?:\.\d+)?)",  # 价格区间
        ]

        # 数量单位映射
        self.quantity_units = {
            "个": "pcs",
            "件": "pcs",
            "只": "pcs",
            "套": "set",
            "箱": "box",
            "包": "package",
            "袋": "bag",
            "kg": "kg",
            "公斤": "kg",
            "g": "g",
            "克": "g",
            "m": "m",
            "米": "m",
        }

    def map_to_source_data(self, scraped_content, analysis_result) -> Dict[str, Any]:
        """
        将抓取和分析结果映射为货源数据

        Args:
            scraped_content: ScrapedContent对象
            analysis_result: AnalysisResult对象

        Returns:
            映射后的货源数据字典
        """

        # 添加调试信息
        print(f"🔍 Debug: 开始数据映射")
        print(
            f"🔍 Debug: analysis_result.success = {analysis_result.success if analysis_result else None}"
        )
        if analysis_result and analysis_result.success:
            print(
                f"🔍 Debug: analysis_result.price_info = {analysis_result.price_info}"
            )
            print(
                f"🔍 Debug: analysis_result.product_attributes = {analysis_result.product_attributes}"
            )
            print(
                f"🔍 Debug: analysis_result.sales_data = {analysis_result.sales_data}"
            )
            print(
                f"🔍 Debug: analysis_result.dropship_data = {analysis_result.dropship_data}"
            )

        source_data = {
            # 基本信息
            "name": self._get_product_name(scraped_content, analysis_result),
            "supplier_name": self._get_supplier_name(scraped_content, analysis_result),
            "url": scraped_content.url,
            "description": self._get_description(scraped_content, analysis_result),
            "notes": self._generate_notes(scraped_content, analysis_result),
            # 价格信息
            "wholesale_price": self._extract_wholesale_price(
                scraped_content, analysis_result
            ),
            "wholesale_min_quantity": self._extract_min_quantity(analysis_result),
            "dropship_price": self._extract_dropship_price(
                scraped_content, analysis_result
            ),
            # 成本和运费
            "shipping_cost": self._estimate_shipping_cost(analysis_result),
            "other_cost": 0.0,
            # 规格信息
            "specifications": self._map_specifications(analysis_result),
            # 图片信息
            "image_urls": scraped_content.images[:10] if scraped_content.images else [],
            # 分类和标签
            "category": self._determine_category(analysis_result),
            "tags": self._extract_tags(analysis_result),
            # 元数据
            "scraped_at": scraped_content.scraped_at,
            "source_type": scraped_content.source_type,
            "ai_confidence": self._calculate_confidence(analysis_result),
            # 1688专用字段
            "shop_info": self._get_shop_info(scraped_content, analysis_result),
            "quantity": self._extract_quantity(analysis_result),
            "min_order_quantity": self._extract_min_quantity(analysis_result),
            "shipping_location": self._extract_shipping_location(analysis_result),
            "sales_count": self._extract_sales_count(analysis_result),
            "modes": ["wholesale", "dropship"],  # 1688默认支持两种模式
        }

        # 映射现有的代发服务字段
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.dropship_data
        ):
            dropship_data = analysis_result.dropship_data
            source_data.update(
                {
                    "pickup_rate_24h": self._safe_float(
                        dropship_data.get("pickup_rate_24h", 0)
                    ),
                    "pickup_rate_48h": self._safe_float(
                        dropship_data.get("pickup_rate_48h", 0)
                    ),
                    "monthly_dropship_orders": self._safe_int(
                        dropship_data.get("monthly_orders", 0)
                    ),
                    "downstream_stores": self._safe_int(
                        dropship_data.get("downstream_stores", 0)
                    ),
                    "distributor_count": self._safe_int(
                        dropship_data.get("distributor_count", 0)
                    ),
                    "product_publish_time": self._parse_date(
                        dropship_data.get("listing_date")
                    ),
                }
            )

        # 映射供应商信息
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.supplier_info
        ):
            supplier_info = analysis_result.supplier_info
            if supplier_info.get("business_years"):
                source_data["shop_info"] = (
                    f"{source_data.get('shop_info', '')} | 经营{supplier_info['business_years']}年".strip(
                        " |"
                    )
                )

        # 生成自定义字段数据
        custom_fields = self._generate_custom_fields(scraped_content, analysis_result)
        source_data["custom_fields"] = custom_fields

        self.logger.info(f"数据映射完成: {source_data['name']}")

        # 添加调试信息
        print(f"🔍 Debug: 数据映射完成")
        print(f"🔍 Debug: 映射后的wholesale_price = {source_data['wholesale_price']}")
        print(f"🔍 Debug: 映射后的dropship_price = {source_data['dropship_price']}")
        print(f"🔍 Debug: 映射后的自定义字段数量 = {len(custom_fields)}")
        print(f"🔍 Debug: 自定义字段前5个:")
        for i, field in enumerate(custom_fields[:5]):
            print(
                f"  {i+1}. {field.get('name', 'unknown')}: {field.get('value', 'empty')}"
            )

        return source_data

    def _get_product_name(self, scraped_content, analysis_result) -> str:
        """获取产品名称"""
        # 优先使用AI清洗后的标题
        if analysis_result and analysis_result.success and analysis_result.title:
            return analysis_result.title

        # 清洗原始标题
        if scraped_content.title:
            return self._clean_title(scraped_content.title)

        return "未知产品"

    def _clean_title(self, title: str) -> str:
        """清洗产品标题"""
        # 移除HTML标签
        title = re.sub(r"<[^>]+>", "", title)

        # 移除多余的空格和换行
        title = re.sub(r"\s+", " ", title).strip()

        # 移除常见的干扰词
        noise_words = ["包邮", "热销", "爆款", "限时", "特价", "促销"]
        for word in noise_words:
            title = title.replace(word, "")

        return title.strip()

    def _get_supplier_name(self, scraped_content, analysis_result) -> str:
        """获取供应商名称"""
        # 优先使用AI提取的供应商信息
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.supplier_info
            and analysis_result.supplier_info.get("company")
        ):
            return analysis_result.supplier_info["company"]

        # 使用抓取的供应商信息
        if scraped_content.supplier:
            return scraped_content.supplier

        # 从URL推断
        if scraped_content.url:
            if "1688.com" in scraped_content.url:
                return "1688供应商"
            elif "taobao.com" in scraped_content.url:
                return "淘宝供应商"
            elif "alibaba.com" in scraped_content.url:
                return "阿里巴巴供应商"

        return "未知供应商"

    def _get_description(self, scraped_content, analysis_result) -> str:
        """获取产品描述"""
        descriptions = []

        # AI整理的内容
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.cleaned_content
        ):
            descriptions.append("AI整理内容:")
            descriptions.append(analysis_result.cleaned_content)

        # 产品特性
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.product_features
        ):
            descriptions.append("\n产品特性:")
            for feature in analysis_result.product_features:
                descriptions.append(f"• {feature}")

        # 图片描述
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.image_descriptions
        ):
            descriptions.append("\n图片分析:")
            for desc in analysis_result.image_descriptions:
                descriptions.append(f"• {desc}")

        # 原始内容作为备选
        if not descriptions and scraped_content.content:
            descriptions.append(scraped_content.content[:500])

        return "\n".join(descriptions)

    def _extract_wholesale_price(self, scraped_content, analysis_result) -> float:
        """提取批发价格"""
        price = 0.0

        # 优先使用AI提取的价格
        if analysis_result and analysis_result.success and analysis_result.price_info:
            price_info = analysis_result.price_info

            # 首先尝试wholesale_price
            if price_info.get("wholesale_price"):
                price = self._parse_price(price_info["wholesale_price"])

            # 如果wholesale_price解析失败，尝试unit_price
            if price == 0.0 and price_info.get("unit_price"):
                price = self._parse_price(price_info["unit_price"])

            # 如果还是失败，尝试price_range
            if price == 0.0 and price_info.get("price_range"):
                price = self._parse_price(price_info["price_range"])

        # 使用抓取的价格作为最后备选
        if price == 0.0 and scraped_content.price:
            price = self._parse_price(scraped_content.price)

        return price

    def _extract_dropship_price(self, scraped_content, analysis_result) -> float:
        """提取代发价格"""
        # 代发价格通常比批发价高10-30%
        wholesale_price = self._extract_wholesale_price(
            scraped_content, analysis_result
        )

        if wholesale_price > 0:
            return round(wholesale_price * 1.2, 2)  # 默认增加20%

        return 0.0

    def _extract_min_quantity(self, analysis_result) -> int:
        """提取最小起订量"""
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.price_info
            and analysis_result.price_info.get("min_order")
        ):

            min_order_str = analysis_result.price_info["min_order"]
            numbers = re.findall(r"\d+", min_order_str)
            if numbers:
                return int(numbers[0])

        return 1  # 默认最小起订量

    def _parse_price(self, price_str: str) -> float:
        """解析价格字符串"""
        if not price_str:
            return 0.0

        # 移除空格和换行
        price_str = re.sub(r"\s+", "", str(price_str))

        # 尝试各种价格模式
        for pattern in self.price_patterns:
            matches = re.findall(pattern, price_str)
            if matches:
                try:
                    # 如果是价格区间，取最低价
                    if isinstance(matches[0], tuple):
                        return float(matches[0][0])
                    else:
                        return float(matches[0])
                except (ValueError, TypeError):
                    continue

        return 0.0

    def _estimate_shipping_cost(self, analysis_result) -> float:
        """估算运费"""
        # 根据产品类型和重量估算运费
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.specifications
            and analysis_result.specifications.get("weight")
        ):

            weight_str = analysis_result.specifications["weight"]
            # 提取重量数值
            weight_match = re.search(r"(\d+(?:\.\d+)?)", weight_str)
            if weight_match:
                weight = float(weight_match.group(1))
                # 简单的运费计算：每公斤5元
                return round(weight * 5, 2)

        return 10.0  # 默认运费

    def _map_specifications(self, analysis_result) -> str:
        """映射产品规格"""
        if (
            not analysis_result
            or not analysis_result.success
            or not analysis_result.specifications
        ):
            return ""

        specs = []
        for key, value in analysis_result.specifications.items():
            if value and str(value).strip():
                specs.append(f"{key}: {value}")

        return "\n".join(specs)

    def _determine_category(self, analysis_result) -> str:
        """确定产品分类"""
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.categories
            and len(analysis_result.categories) > 0
        ):
            return analysis_result.categories[0]

        return "其他"

    def _extract_tags(self, analysis_result) -> List[str]:
        """提取标签"""
        tags = []

        if analysis_result and analysis_result.success:
            # 添加分类作为标签
            if analysis_result.categories:
                tags.extend(analysis_result.categories)

            # 从特性中提取关键词作为标签
            if analysis_result.product_features:
                for feature in analysis_result.product_features[:3]:  # 限制3个
                    # 提取关键词
                    keywords = re.findall(r"[\u4e00-\u9fff]+", feature)
                    tags.extend([kw for kw in keywords if len(kw) >= 2][:2])

        # 去重并限制数量
        unique_tags = list(dict.fromkeys(tags))[:5]
        return unique_tags

    def _generate_notes(self, scraped_content, analysis_result) -> str:
        """生成备注信息"""
        notes = []

        # 添加抓取信息
        notes.append(f"抓取时间: {scraped_content.scraped_at}")
        notes.append(f"来源网站: {scraped_content.source_type}")

        # AI分析信息
        if analysis_result and analysis_result.success:
            notes.append(f"AI分析耗时: {analysis_result.processing_time:.2f}秒")
            if analysis_result.supplier_info:
                for key, value in analysis_result.supplier_info.items():
                    if value and key != "company":
                        notes.append(f"{key}: {value}")

        return "\n".join(notes)

    def _calculate_confidence(self, analysis_result) -> float:
        """计算数据可信度"""
        if not analysis_result or not analysis_result.success:
            return 0.0

        confidence = 0.5  # 基础分

        # 有标题加分
        if analysis_result.title:
            confidence += 0.1

        # 有价格信息加分
        if analysis_result.price_info:
            confidence += 0.2

        # 有规格信息加分
        if analysis_result.specifications:
            confidence += 0.1

        # 有供应商信息加分
        if analysis_result.supplier_info:
            confidence += 0.1

        return min(confidence, 1.0)

    def _generate_custom_fields(
        self, scraped_content, analysis_result
    ) -> List[Dict[str, Any]]:
        """生成自定义字段数据"""
        custom_fields = []

        if not analysis_result or not analysis_result.success:
            return custom_fields

        # 1. 商品属性字段
        if analysis_result.product_attributes:
            attrs = analysis_result.product_attributes

            # 材质
            if attrs.get("material"):
                custom_fields.append(
                    {
                        "name": "material",
                        "value": str(attrs["material"]),
                        "field_type": "text",
                        "display_name": "材质",
                        "description": "商品材质信息",
                    }
                )

            # 产品类别
            if attrs.get("product_category"):
                custom_fields.append(
                    {
                        "name": "product_category",
                        "value": str(attrs["product_category"]),
                        "field_type": "text",
                        "display_name": "产品类别",
                        "description": "商品分类",
                    }
                )

            # 货号
            if attrs.get("product_code"):
                custom_fields.append(
                    {
                        "name": "product_code",
                        "value": str(attrs["product_code"]),
                        "field_type": "text",
                        "display_name": "货号",
                        "description": "商品货号",
                    }
                )

            # 净重
            if attrs.get("net_weight"):
                custom_fields.append(
                    {
                        "name": "net_weight",
                        "value": str(self._safe_float(attrs["net_weight"])),
                        "field_type": "number",
                        "display_name": "净重(克)",
                        "description": "商品净重",
                    }
                )

            # 毛重
            if attrs.get("gross_weight"):
                custom_fields.append(
                    {
                        "name": "gross_weight",
                        "value": str(self._safe_float(attrs["gross_weight"])),
                        "field_type": "number",
                        "display_name": "毛重(克)",
                        "description": "商品毛重",
                    }
                )

            # 品牌
            if attrs.get("brand"):
                custom_fields.append(
                    {
                        "name": "brand",
                        "value": str(attrs["brand"]),
                        "field_type": "text",
                        "display_name": "品牌",
                        "description": "商品品牌",
                    }
                )

            # 产地
            if attrs.get("origin"):
                custom_fields.append(
                    {
                        "name": "origin",
                        "value": str(attrs["origin"]),
                        "field_type": "text",
                        "display_name": "产地",
                        "description": "商品产地",
                    }
                )

        # 2. 物流包装信息
        if analysis_result.packaging_info:
            pkg = analysis_result.packaging_info

            # 外箱尺寸
            if pkg.get("box_dimensions"):
                custom_fields.append(
                    {
                        "name": "box_dimensions",
                        "value": str(pkg["box_dimensions"]),
                        "field_type": "text",
                        "display_name": "外箱尺寸",
                        "description": "包装外箱尺寸",
                    }
                )

            # 装箱数
            if pkg.get("box_quantity"):
                custom_fields.append(
                    {
                        "name": "box_quantity",
                        "value": str(self._safe_int(pkg["box_quantity"])),
                        "field_type": "number",
                        "display_name": "装箱数",
                        "description": "单箱装载数量",
                    }
                )

            # 箱子重量
            if pkg.get("box_weight"):
                custom_fields.append(
                    {
                        "name": "box_weight",
                        "value": str(self._safe_float(pkg["box_weight"])),
                        "field_type": "number",
                        "display_name": "箱子重量(kg)",
                        "description": "整箱重量",
                    }
                )

        # 3. 销售数据
        if analysis_result.sales_data:
            sales = analysis_result.sales_data

            # 年销量
            if sales.get("annual_sales"):
                custom_fields.append(
                    {
                        "name": "annual_sales",
                        "value": str(self._safe_int(sales["annual_sales"])),
                        "field_type": "number",
                        "display_name": "年销量",
                        "description": "商品年销售量",
                    }
                )

            # 评价数量
            if sales.get("review_count"):
                custom_fields.append(
                    {
                        "name": "review_count",
                        "value": str(self._safe_int(sales["review_count"])),
                        "field_type": "number",
                        "display_name": "评价数量",
                        "description": "买家评价总数",
                    }
                )

            # 已售数量
            if sales.get("sold_quantity"):
                custom_fields.append(
                    {
                        "name": "sold_quantity",
                        "value": str(self._safe_int(sales["sold_quantity"])),
                        "field_type": "number",
                        "display_name": "已售数量",
                        "description": "累计销售数量",
                    }
                )

            # 评价标签
            if sales.get("review_tags"):
                tags_str = json.dumps(sales["review_tags"], ensure_ascii=False)
                custom_fields.append(
                    {
                        "name": "review_tags",
                        "value": tags_str,
                        "field_type": "text",
                        "display_name": "评价标签",
                        "description": "买家评价标签",
                    }
                )

        # 4. 代发服务扩展数据
        if analysis_result.dropship_data:
            dropship = analysis_result.dropship_data

            # 退货率
            if dropship.get("return_rate"):
                custom_fields.append(
                    {
                        "name": "return_rate",
                        "value": str(self._safe_float(dropship["return_rate"])),
                        "field_type": "number",
                        "display_name": "退货率(%)",
                        "description": "商品退货率",
                    }
                )

        # 5. 服务保障信息
        if analysis_result.service_info:
            service = analysis_result.service_info

            # 发货承诺
            if service.get("shipping_promise"):
                custom_fields.append(
                    {
                        "name": "shipping_promise",
                        "value": str(service["shipping_promise"]),
                        "field_type": "text",
                        "display_name": "发货承诺",
                        "description": "发货时间承诺",
                    }
                )

            # 服务保障
            if service.get("service_guarantees"):
                guarantees_str = json.dumps(
                    service["service_guarantees"], ensure_ascii=False
                )
                custom_fields.append(
                    {
                        "name": "service_guarantees",
                        "value": guarantees_str,
                        "field_type": "text",
                        "display_name": "服务保障",
                        "description": "提供的服务保障",
                    }
                )

            # 支持快递
            if service.get("supported_couriers"):
                custom_fields.append(
                    {
                        "name": "supported_couriers",
                        "value": str(service["supported_couriers"]),
                        "field_type": "text",
                        "display_name": "支持快递",
                        "description": "支持的快递服务",
                    }
                )

        # 6. 规格变体信息
        if analysis_result.variant_info:
            variant = analysis_result.variant_info

            # 总规格数量
            if variant.get("total_variants"):
                custom_fields.append(
                    {
                        "name": "total_variants",
                        "value": str(self._safe_int(variant["total_variants"])),
                        "field_type": "number",
                        "display_name": "规格数量",
                        "description": "商品规格变体总数",
                    }
                )

            # 颜色规格
            if variant.get("color_variants"):
                colors_str = json.dumps(variant["color_variants"], ensure_ascii=False)
                custom_fields.append(
                    {
                        "name": "color_variants",
                        "value": colors_str,
                        "field_type": "text",
                        "display_name": "颜色规格",
                        "description": "可选颜色规格",
                    }
                )

            # 款式类型
            if variant.get("variant_types"):
                types_str = json.dumps(variant["variant_types"], ensure_ascii=False)
                custom_fields.append(
                    {
                        "name": "variant_types",
                        "value": types_str,
                        "field_type": "text",
                        "display_name": "款式类型",
                        "description": "商品款式分类",
                    }
                )

            # 特殊模式（新增）
            if variant.get("special_modes"):
                modes_str = json.dumps(variant["special_modes"], ensure_ascii=False)
                custom_fields.append(
                    {
                        "name": "special_modes",
                        "value": modes_str,
                        "field_type": "text",
                        "display_name": "特殊模式",
                        "description": "产品特殊功能模式",
                    }
                )

        # 7. 图片视频资源信息（新增）
        if analysis_result.media_resources:
            media = analysis_result.media_resources

            # 主图链接
            if media.get("main_images"):
                main_images_str = json.dumps(media["main_images"], ensure_ascii=False)
                custom_fields.append(
                    {
                        "name": "main_images",
                        "value": main_images_str,
                        "field_type": "text",
                        "display_name": "主图链接",
                        "description": "商品主要图片链接列表",
                    }
                )

            # 详情图链接
            if media.get("detail_images"):
                detail_images_str = json.dumps(
                    media["detail_images"], ensure_ascii=False
                )
                custom_fields.append(
                    {
                        "name": "detail_images",
                        "value": detail_images_str,
                        "field_type": "text",
                        "display_name": "详情图链接",
                        "description": "商品详情图片链接列表",
                    }
                )

            # 产品视频链接
            if media.get("video_url"):
                custom_fields.append(
                    {
                        "name": "video_url",
                        "value": str(media["video_url"]),
                        "field_type": "text",
                        "display_name": "产品视频",
                        "description": "商品展示视频链接",
                    }
                )

            # 视频封面
            if media.get("video_cover"):
                custom_fields.append(
                    {
                        "name": "video_cover",
                        "value": str(media["video_cover"]),
                        "field_type": "text",
                        "display_name": "视频封面",
                        "description": "产品视频封面图片链接",
                    }
                )

        return custom_fields

    def _safe_int(self, value) -> int:
        """安全的整数转换"""
        if value is None:
            return 0
        try:
            # 处理如"100000"、"400+"这样的值
            str_value = str(value).replace("+", "").replace(",", "").strip()
            # 提取数字
            numbers = re.findall(r"\d+", str_value)
            if numbers:
                return int(numbers[0])
            return 0
        except (ValueError, TypeError):
            return 0

    def _safe_float(self, value) -> float:
        """安全的浮点数转换"""
        if value is None:
            return 0.0
        try:
            # 处理百分比
            str_value = str(value).replace("%", "").replace(",", "").strip()
            # 提取数字（包括小数）
            numbers = re.findall(r"\d+\.?\d*", str_value)
            if numbers:
                return float(numbers[0])
            return 0.0
        except (ValueError, TypeError):
            return 0.0

    def _parse_date(self, date_str) -> Optional[datetime]:
        """解析日期字符串"""
        if not date_str:
            return None
        try:
            # 尝试解析常见的日期格式
            date_patterns = [
                "%Y年%m月",
                "%Y-%m",
                "%Y/%m",
                "%Y.%m",
                "%Y-%m-%d",
                "%Y/%m/%d",
            ]

            str_value = str(date_str).strip()
            for pattern in date_patterns:
                try:
                    return datetime.strptime(str_value, pattern)
                except ValueError:
                    continue
            return None
        except Exception:
            return None

    def _get_shop_info(self, scraped_content, analysis_result) -> str:
        """获取店铺信息"""
        info_parts = []

        # 从AI分析结果中获取
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.supplier_info
        ):
            supplier = analysis_result.supplier_info
            if supplier.get("location"):
                info_parts.append(supplier["location"])
            if supplier.get("business_years"):
                info_parts.append(f"经营{supplier['business_years']}年")

        # 从抓取内容获取
        if scraped_content.supplier and scraped_content.supplier not in info_parts:
            info_parts.append(scraped_content.supplier)

        return " | ".join(info_parts)

    def _extract_quantity(self, analysis_result) -> int:
        """提取库存数量"""
        # 从代发数据中提取月订单数作为参考库存
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.dropship_data
        ):
            monthly_orders = analysis_result.dropship_data.get("monthly_orders", 0)
            monthly_orders_int = self._safe_int(monthly_orders)
            if monthly_orders_int > 0:
                return monthly_orders_int * 30  # 假设月订单数*30天作为库存参考

        return 1000  # 默认库存

    def _extract_shipping_location(self, analysis_result) -> str:
        """提取发货地"""
        if (
            analysis_result
            and analysis_result.success
            and analysis_result.supplier_info
        ):
            return analysis_result.supplier_info.get("shipping_location", "")
        return ""

    def _extract_sales_count(self, analysis_result) -> int:
        """提取销售数量"""
        if analysis_result and analysis_result.success and analysis_result.sales_data:
            # 优先使用已售数量
            sold_qty = analysis_result.sales_data.get("sold_quantity", 0)
            if sold_qty:
                return self._safe_int(sold_qty)

            # 次选年销量除以365
            annual_sales = analysis_result.sales_data.get("annual_sales", 0)
            if annual_sales:
                return self._safe_int(annual_sales) // 365

        return 0
