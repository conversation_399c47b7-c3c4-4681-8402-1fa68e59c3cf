"""
产品列表组件
包含产品的列表显示、搜索、排序、管理功能
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QListWidget,
    QListWidgetItem,
    QLineEdit,
    QComboBox,
    QPushButton,
    QLabel,
    QMessageBox,
    QMenu,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

from models.database import ProductService
from models.product import Product
from views.dialogs import ProductDialog
from config import get_current_theme


class ProductListItem(QWidget):
    """产品列表项"""

    def __init__(self, product: Product):
        super().__init__()
        self.product = product
        self.is_hovered = False
        self.setup_ui()

    def enterEvent(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self.update_style()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        self.update_style()
        super().leaveEvent(event)

    def update_style(self):
        """更新样式"""
        selected = self.property("selected")
        if selected:
            # 选中状态，始终显示选中样式
            self.setStyleSheet(self._get_selected_style())
        elif self.is_hovered:
            # 未选中但鼠标悬停，显示悬停样式
            self.setStyleSheet(self._get_hover_style())
        else:
            # 未选中且鼠标不在上面，显示正常样式
            self.setStyleSheet(self._get_normal_style())

    def _get_selected_style(self):
        """获取选中状态的样式"""
        colors = self.get_theme_colors()
        current_theme = get_current_theme()

        if current_theme == "dark":
            return f"""
                ProductListItem {{
                    background-color: {colors['selected_bg']};
                    color: {colors['selected_text']};
                    border: 1px solid {colors['selected_bg']};
                    border-radius: 4px;
                    margin: 2px;
                }}
                ProductListItem QLabel.name {{
                    color: {colors['selected_text']};
                    font-weight: bold;
                    font-size: 14px;
                }}
                ProductListItem QLabel.price {{
                    color: {colors['selected_text']};
                    font-weight: bold;
                    font-size: 16px;
                }}
                ProductListItem QLabel.secondary {{
                    color: {colors['selected_text']};
                    font-weight: bold;
                    font-size: 12px;
                }}
                ProductListItem QLabel.success {{
                    color: {colors['selected_text']};
                    font-size: 12px;
                }}
                ProductListItem QLabel.error {{
                    color: {colors['selected_text']};
                    font-size: 12px;
                }}
            """
        else:
            return f"""
                ProductListItem {{
                    background-color: {colors['selected_bg']};
                    color: {colors['selected_text']};
                    border: 1px solid {colors['selected_bg']};
                    border-radius: 4px;
                    margin: 2px;
                }}
                ProductListItem QLabel.name {{
                    color: {colors['selected_text']};
                    font-weight: bold;
                    font-size: 14px;
                }}
                ProductListItem QLabel.price {{
                    color: {colors['selected_text']};
                    font-weight: bold;
                    font-size: 16px;
                }}
                ProductListItem QLabel.secondary {{
                    color: {colors['selected_text']};
                    font-weight: bold;
                    font-size: 12px;
                }}
                ProductListItem QLabel.success {{
                    color: {colors['selected_text']};
                    font-size: 12px;
                }}
                ProductListItem QLabel.error {{
                    color: {colors['selected_text']};
                    font-size: 12px;
                }}
            """

    def _get_normal_style(self):
        """获取正常状态的样式"""
        colors = self.get_theme_colors()
        current_theme = get_current_theme()

        if current_theme == "dark":
            return f"""
                ProductListItem {{
                    background-color: #2d2d2d;
                    color: #ffffff;
                    border: 1px solid #404040;
                    border-radius: 4px;
                    margin: 2px;
                }}
                ProductListItem:hover {{
                    background-color: #404040;
                }}
                ProductListItem QLabel.name {{
                    color: {colors['text']};
                    font-weight: bold;
                    font-size: 14px;
                }}
                ProductListItem QLabel.price {{
                    color: {colors['primary']};
                    font-weight: bold;
                    font-size: 16px;
                }}
                ProductListItem QLabel.secondary {{
                    color: {colors['secondary_text']};
                    font-weight: bold;
                    font-size: 12px;
                }}
                ProductListItem QLabel.success {{
                    color: {colors['success']};
                    font-size: 12px;
                }}
                ProductListItem QLabel.error {{
                    color: {colors['error']};
                    font-size: 12px;
                }}
            """
        else:
            return f"""
                ProductListItem {{
                    background-color: #ffffff;
                    color: #333333;
                    border: 1px solid #e0e0e0;
                    border-radius: 4px;
                    margin: 2px;
                }}
                ProductListItem:hover {{
                    background-color: #f5f5f5;
                }}
                ProductListItem QLabel.name {{
                    color: {colors['text']};
                    font-weight: bold;
                    font-size: 14px;
                }}
                ProductListItem QLabel.price {{
                    color: {colors['primary']};
                    font-weight: bold;
                    font-size: 16px;
                }}
                ProductListItem QLabel.secondary {{
                    color: {colors['secondary_text']};
                    font-weight: bold;
                    font-size: 12px;
                }}
                ProductListItem QLabel.success {{
                    color: {colors['success']};
                    font-size: 12px;
                }}
                ProductListItem QLabel.error {{
                    color: {colors['error']};
                    font-size: 12px;
                }}
            """

    def _get_hover_style(self):
        """获取悬停状态的样式"""
        colors = self.get_theme_colors()
        current_theme = get_current_theme()

        if current_theme == "dark":
            return f"""
                ProductListItem {{
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #505050;
                    border-radius: 4px;
                    margin: 2px;
                }}
                ProductListItem QLabel.name {{
                    color: {colors['text']};
                    font-weight: bold;
                    font-size: 14px;
                }}
                ProductListItem QLabel.price {{
                    color: {colors['primary']};
                    font-weight: bold;
                    font-size: 16px;
                }}
                ProductListItem QLabel.secondary {{
                    color: {colors['secondary_text']};
                    font-weight: bold;
                    font-size: 12px;
                }}
                ProductListItem QLabel.success {{
                    color: {colors['success']};
                    font-size: 12px;
                }}
                ProductListItem QLabel.error {{
                    color: {colors['error']};
                    font-size: 12px;
                }}
            """
        else:
            return f"""
                ProductListItem {{
                    background-color: #f5f5f5;
                    color: #333333;
                    border: 1px solid #cccccc;
                    border-radius: 4px;
                    margin: 2px;
                }}
                ProductListItem QLabel.name {{
                    color: {colors['text']};
                    font-weight: bold;
                    font-size: 14px;
                }}
                ProductListItem QLabel.price {{
                    color: {colors['primary']};
                    font-weight: bold;
                    font-size: 16px;
                }}
                ProductListItem QLabel.secondary {{
                    color: {colors['secondary_text']};
                    font-weight: bold;
                    font-size: 12px;
                }}
                ProductListItem QLabel.success {{
                    color: {colors['success']};
                    font-size: 12px;
                }}
                ProductListItem QLabel.error {{
                    color: {colors['error']};
                    font-size: 12px;
                }}
            """

    def get_theme_colors(self):
        """获取当前主题的颜色配置"""
        current_theme = get_current_theme()

        if current_theme == "dark":
            return {
                "primary": "#FFD700",  # 蛋黄色 - 用于价格
                "text": "#00BFFF",  # 天蓝色 - 用于文字
                "secondary_text": "#00BFFF",  # 天蓝色 - 用于次要文字
                "success": "#4caf50",
                "error": "#f44336",
                "selected_bg": "#2196f3",  # 选中背景色
                "selected_text": "#ffffff",  # 选中文字颜色
            }
        else:
            return {
                "primary": "#FFD700",  # 蛋黄色 - 用于价格
                "text": "#1E90FF",  # 道奇蓝 - 用于文字
                "secondary_text": "#1E90FF",  # 道奇蓝 - 用于次要文字
                "success": "#4caf50",
                "error": "#f44336",
                "selected_bg": "#2196f3",  # 选中背景色
                "selected_text": "#ffffff",  # 选中文字颜色
            }

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 应用初始样式
        self.setStyleSheet(self._get_normal_style())

        # 标题行
        title_layout = QHBoxLayout()

        # 产品名称
        name_label = QLabel(self.product.name)
        name_label.setProperty("class", "name")
        title_layout.addWidget(name_label)

        title_layout.addStretch()

        # 售价和运费信息
        price_layout = QVBoxLayout()
        price_layout.setSpacing(2)
        
        # 基础售价
        price_label = QLabel(f"¥{self.product.selling_price:.2f}")
        price_label.setProperty("class", "price")
        price_layout.addWidget(price_label)
        
        # 运费信息
        shipping_info = getattr(self.product, "shipping_display", "")
        if shipping_info:
            shipping_label = QLabel(shipping_info)
            shipping_label.setProperty("class", "secondary")
            shipping_label.setStyleSheet("font-size: 10px; color: #666;")
            price_layout.addWidget(shipping_label)
        
        # 实际收款价格（如果与基础售价不同）
        actual_price = getattr(self.product, "actual_selling_price", self.product.selling_price)
        if abs(actual_price - self.product.selling_price) > 0.01:  # 有差异时显示
            actual_price_label = QLabel(f"实收: ¥{actual_price:.2f}")
            actual_price_label.setProperty("class", "success")
            actual_price_label.setStyleSheet("font-size: 10px; font-weight: bold; color: #4caf50;")
            price_layout.addWidget(actual_price_label)
        
        price_widget = QWidget()
        price_widget.setLayout(price_layout)
        title_layout.addWidget(price_widget)

        layout.addLayout(title_layout)

        # 详情行
        details_layout = QHBoxLayout()

        # SKU
        if self.product.sku:
            sku_label = QLabel(f"编码: {self.product.sku}")
            sku_label.setProperty("class", "secondary")
            details_layout.addWidget(sku_label)

        # 标签信息 - 和SKU在同一行左侧
        if self.product.tags:
            # 添加标签图标
            tags_icon = QLabel("🏷️")
            tags_icon.setProperty("class", "secondary")
            tags_icon.setContentsMargins(5, 0, 0, 0)  # 左侧间距
            details_layout.addWidget(tags_icon)

            tags_to_show = self.product.tags[:3]  # 最多显示3个标签
            for tag in tags_to_show:
                tag_label = QLabel(tag.name)
                tag_label.setFixedHeight(16)

                # 计算文字宽度并设置合适的标签宽度
                font_metrics = tag_label.fontMetrics()
                text_width = font_metrics.horizontalAdvance(tag.name)
                tag_label.setMinimumWidth(text_width + 8)
                tag_label.setMaximumWidth(60)  # 最大宽度限制

                tag_label.setStyleSheet(
                    f"""
                    QLabel {{
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                            stop: 0 {tag.color},
                            stop: 1 {self.darken_color(tag.color, 0.15)});
                        color: white;
                        border-radius: 8px;
                        padding: 1px 5px;
                        font-size: 8px;
                        font-weight: bold;
                        margin-right: 3px;
                        border: 1px solid {self.darken_color(tag.color, 0.2)};
                    }}
                """
                )
                tag_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                details_layout.addWidget(tag_label)

            # 如果有更多标签，显示一个 +N 标签
            if len(self.product.tags) > 3:
                more_label = QLabel(f"+{len(self.product.tags) - 3}")
                more_label.setFixedHeight(16)
                more_label.setFixedWidth(20)
                more_label.setStyleSheet(
                    """
                    QLabel {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                            stop: 0 #888,
                            stop: 1 #666);
                        color: white;
                        border-radius: 8px;
                        padding: 1px 3px;
                        font-size: 7px;
                        font-weight: bold;
                        margin-right: 3px;
                        border: 1px solid #555;
                    }
                """
                )
                more_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                details_layout.addWidget(more_label)

        details_layout.addStretch()

        # 货源数量
        source_count = len(self.product.sources)
        source_label = QLabel(f"货源: {source_count}")
        source_label.setProperty("class", "secondary")
        details_layout.addWidget(source_label)

        # 利润信息
        if source_count > 0:
            profit_label = QLabel(f"利润: ¥{self.product.profit:.2f}")
            if self.product.profit > 0:
                profit_label.setProperty("class", "success")
            else:
                profit_label.setProperty("class", "error")
            details_layout.addWidget(profit_label)

        layout.addLayout(details_layout)

        # 设置最小高度确保内容完整显示
        min_height = 80
        self.setMinimumHeight(min_height)

    def darken_color(self, color_hex: str, factor: float = 0.1) -> str:
        """使颜色变暗"""
        from PyQt6.QtGui import QColor

        color = QColor(color_hex)
        h, s, l, a = color.getHsl()
        l = max(0, int(l * (1 - factor)))
        color.setHsl(h, s, l, a)
        return color.name()


class ProductListWidget(QWidget):
    """产品列表组件"""

    # 信号定义
    product_selected = pyqtSignal(int)  # 产品被选中
    comparison_requested = pyqtSignal(list)  # 请求对比

    def __init__(self, product_service: ProductService, tag_service=None):
        super().__init__()
        self.product_service = product_service
        self.tag_service = tag_service
        self.products = []
        self.selected_products = []

        self.setup_ui()
        self.setup_connections()

        # 搜索定时器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

    def get_theme_colors(self):
        """获取当前主题的颜色配置"""
        current_theme = get_current_theme()

        if current_theme == "dark":
            return {"text": "#ffffff", "secondary_text": "#b0b0b0"}
        else:
            return {"text": "#333333", "secondary_text": "#666666"}

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 获取主题颜色
        colors = self.get_theme_colors()

        # 头部工具栏
        header_layout = QVBoxLayout()

        # 搜索栏
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        search_label.setStyleSheet(f"font-size: 12px; font-weight: bold;")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入产品名称或编码...")
        self.search_edit.setStyleSheet("font-size: 12px; padding: 8px;")
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)

        header_layout.addLayout(search_layout)

        # 排序和筛选
        filter_layout = QHBoxLayout()

        # 排序选择
        sort_label = QLabel("排序:")
        sort_label.setStyleSheet(f"font-size: 12px; font-weight: bold;")
        self.sort_combo = QComboBox()

        # 根据当前主题设置样式
        if get_current_theme() == "dark":
            self.sort_combo.setStyleSheet(
                """
                QComboBox {
                    font-size: 12px;
                    padding: 8px;
                    border: 1px solid #404040;
                    border-radius: 4px;
                    background-color: #2d2d2d;
                    color: #ffffff;
                }
                QComboBox::drop-down {
                    border: none;
                    background: transparent;
                }
                QComboBox::down-arrow {
                    border: none;
                    background: none;
                    image: none;
                    width: 0;
                }
                QComboBox QAbstractItemView {
                    background-color: #2d2d2d;
                    color: #ffffff;
                    border: 1px solid #404040;
                    selection-background-color: #404040;
                }
            """
            )
        else:
            self.sort_combo.setStyleSheet(
                """
                QComboBox {
                    font-size: 12px;
                    padding: 8px;
                    border: 1px solid #cccccc;
                    border-radius: 4px;
                    background-color: #ffffff;
                    color: #333333;
                }
                QComboBox::drop-down {
                    border: none;
                    background: transparent;
                }
                QComboBox::down-arrow {
                    border: none;
                    background: none;
                    image: none;
                    width: 0;
                }
                QComboBox QAbstractItemView {
                    background-color: #ffffff;
                    color: #333333;
                    border: 1px solid #cccccc;
                    selection-background-color: #e0e0e0;
                }
            """
            )

        self.sort_combo.addItems(
            [
                "按创建时间（新→旧）",
                "按创建时间（旧→新）",
                "按名称（A→Z）",
                "按名称（Z→A）",
                "按价格（高→低）",
                "按价格（低→高）",
                "按利润（高→低）",
                "按利润（低→高）",
            ]
        )

        filter_layout.addWidget(sort_label)
        filter_layout.addWidget(self.sort_combo)
        filter_layout.addStretch()

        header_layout.addLayout(filter_layout)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("添加产品")
        self.add_button.setStyleSheet(
            "font-weight: bold; font-size: 12px; padding: 8px 16px;"
        )

        self.compare_button = QPushButton("对比选中")
        self.compare_button.setStyleSheet("font-size: 12px; padding: 8px 16px;")
        self.compare_button.setEnabled(False)

        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setStyleSheet("font-size: 12px; padding: 8px 16px;")

        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.compare_button)
        button_layout.addWidget(self.refresh_button)
        button_layout.addStretch()

        header_layout.addLayout(button_layout)

        layout.addLayout(header_layout)

        # 产品列表
        self.product_list = QListWidget()
        self.product_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        self.product_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.product_list.customContextMenuRequested.connect(self.show_context_menu)
        layout.addWidget(self.product_list)

        # 底部状态栏
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet(
            f"""
            QLabel {{
                font-size: 11px;
                padding: 6px 12px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa,
                    stop: 1 #e9ecef);
                border: 1px solid #dee2e6;
                border-radius: 6px;
                color: #495057;
                font-weight: 500;
                margin: 2px 0px;
            }}
        """
        )
        layout.addWidget(self.status_label)

    def setup_connections(self):
        """设置信号连接"""
        self.add_button.clicked.connect(self.add_product)
        self.compare_button.clicked.connect(self.request_comparison)
        self.refresh_button.clicked.connect(self.refresh)

        self.search_edit.textChanged.connect(self.on_search_changed)
        self.sort_combo.currentTextChanged.connect(self.apply_sort)

        self.product_list.itemClicked.connect(self.on_item_clicked)
        self.product_list.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.product_list.itemSelectionChanged.connect(self.on_selection_changed)

    def refresh(self):
        """刷新产品列表"""
        try:
            self.status_label.setText("正在加载...")

            # 获取所有产品
            self.products = self.product_service.get_all_products()

            # 应用搜索和排序
            self.update_display()

            self.status_label.setText(f"📦 共 {len(self.products)} 个产品")

            # 强制刷新产品详情视图，解决UI重复显示问题
            self._force_refresh_detail_view()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新产品列表失败: {str(e)}")
            self.status_label.setText("刷新失败")

    def _force_refresh_detail_view(self):
        """强制刷新产品详情视图"""
        try:
            # 通过父窗口查找ProductDetailWidget
            parent = self.parent()
            while parent:
                if hasattr(parent, "product_detail_widget"):
                    print("=== 从产品列表刷新按钮触发强制刷新ProductDetailWidget ===")
                    parent.product_detail_widget.force_refresh()
                    break
                parent = parent.parent()
        except Exception as e:
            print(f"强制刷新详情视图失败: {e}")

    def update_display(self):
        """更新显示"""
        # 保存当前选中的产品ID
        current_selected = self.get_selected_products()

        # 清空列表
        self.product_list.clear()

        # 过滤产品
        filtered_products = self.filter_products()

        # 排序产品
        sorted_products = self.sort_products(filtered_products)

        # 添加到列表
        for product in sorted_products:
            item = QListWidgetItem()
            item_widget = ProductListItem(product)

            # 确保列表项有足够的高度显示完整内容
            item_size = item_widget.sizeHint()
            min_height = 80
            if product.tags:
                min_height = 100  # 有标签时增加高度
            item_size.setHeight(max(item_size.height(), min_height))
            item.setSizeHint(item_size)

            self.product_list.addItem(item)
            self.product_list.setItemWidget(item, item_widget)

            # 存储产品ID
            item.setData(Qt.ItemDataRole.UserRole, product.id)

            # 如果这个产品之前是选中的，重新选中它
            if product.id in current_selected:
                item.setSelected(True)
                item_widget.setProperty("selected", True)
                item_widget.style().unpolish(item_widget)
                item_widget.style().polish(item_widget)

    def filter_products(self):
        """过滤产品"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            return self.products

        filtered = []
        for product in self.products:
            if (
                search_text in product.name.lower()
                or search_text in (product.sku or "").lower()
            ):
                filtered.append(product)

        return filtered

    def sort_products(self, products):
        """排序产品"""
        sort_text = self.sort_combo.currentText()

        if "按创建时间（新→旧）" in sort_text:
            return sorted(products, key=lambda p: p.created_at, reverse=True)
        elif "按创建时间（旧→新）" in sort_text:
            return sorted(products, key=lambda p: p.created_at)
        elif "按名称（A→Z）" in sort_text:
            return sorted(products, key=lambda p: p.name.lower())
        elif "按名称（Z→A）" in sort_text:
            return sorted(products, key=lambda p: p.name.lower(), reverse=True)
        elif "按价格（高→低）" in sort_text:
            return sorted(products, key=lambda p: p.selling_price, reverse=True)
        elif "按价格（低→高）" in sort_text:
            return sorted(products, key=lambda p: p.selling_price)
        elif "按利润（高→低）" in sort_text:
            return sorted(products, key=lambda p: p.profit, reverse=True)
        elif "按利润（低→高）" in sort_text:
            return sorted(products, key=lambda p: p.profit)

        return products

    def on_search_changed(self):
        """搜索文本改变"""
        # 延迟搜索，避免频繁刷新
        self.search_timer.stop()
        self.search_timer.start(500)  # 500ms延迟

    def perform_search(self):
        """执行搜索"""
        self.update_display()

        search_text = self.search_edit.text().strip()
        if search_text:
            filtered_count = self.product_list.count()
            self.status_label.setText(f"🔍 搜索到 {filtered_count} 个产品")
        else:
            self.status_label.setText(f"📦 共 {len(self.products)} 个产品")

    def apply_sort(self):
        """应用排序"""
        self.update_display()

    def on_item_clicked(self, item):
        """单击列表项"""
        product_id = item.data(Qt.ItemDataRole.UserRole)
        if product_id:
            self.product_selected.emit(product_id)

    def on_item_double_clicked(self, item):
        """双击列表项"""
        product_id = item.data(Qt.ItemDataRole.UserRole)
        if product_id:
            self.edit_product(product_id)

    def on_selection_changed(self):
        """选择改变"""
        selected_items = self.product_list.selectedItems()
        self.selected_products = [
            item.data(Qt.ItemDataRole.UserRole) for item in selected_items
        ]

        # 更新所有项的选中状态样式
        for i in range(self.product_list.count()):
            item = self.product_list.item(i)
            item_widget = self.product_list.itemWidget(item)
            is_selected = item.isSelected()

            if item_widget:
                item_widget.setProperty("selected", is_selected)
                # 强制更新样式
                item_widget.update_style()

        # 更新对比按钮状态
        self.compare_button.setEnabled(len(self.selected_products) >= 2)

        # 更新状态
        if len(self.selected_products) > 1:
            self.status_label.setText(f"✅ 已选择 {len(self.selected_products)} 个产品")
        elif len(self.selected_products) == 1:
            self.status_label.setText("✅ 已选择 1 个产品")
        else:
            self.status_label.setText(f"📦 共 {len(self.products)} 个产品")

    def add_product(self):
        """添加产品"""
        try:
            print("开始添加产品...")
            print(f"product_service: {self.product_service}")
            print(f"tag_service: {self.tag_service}")

            dialog = ProductDialog(
                product_service=self.product_service,
                tag_service=self.tag_service,
                parent=self,
            )
            print("ProductDialog 创建成功")

            dialog.product_saved.connect(self.on_product_saved)
            print("产品保存信号连接成功")

            print("准备显示对话框...")
            result = dialog.exec()
            print(f"对话框返回结果: {result}")

        except Exception as e:
            print(f"add_product() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"添加产品失败: {str(e)}")

    def edit_product(self, product_id: int):
        """编辑产品"""
        try:
            product = self.product_service.get_product(product_id)
            if not product:
                QMessageBox.warning(self, "警告", "产品不存在")
                return

            dialog = ProductDialog(
                product=product,
                product_service=self.product_service,
                tag_service=self.tag_service,
                parent=self,
            )
            dialog.product_saved.connect(self.on_product_saved)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑产品失败: {str(e)}")

    def on_product_saved(self, product):
        """产品保存后的回调"""
        try:
            print(f"产品保存回调: {product}")
            print("开始刷新产品列表...")
            self.refresh()
            print("产品列表刷新完成")

            # 选中保存的产品
            print(f"选中产品: {product.id}")
            self.select_product(product.id)
            print("产品选中完成")

        except Exception as e:
            print(f"on_product_saved() 发生异常: {str(e)}")
            import traceback

            traceback.print_exc()

    def delete_product(self, product_id: int):
        """删除产品"""
        try:
            product = self.product_service.get_product(product_id)
            if not product:
                QMessageBox.warning(self, "警告", "产品不存在")
                return

            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除产品 '{product.name}' 吗？\n\n此操作将同时删除相关的货源和自定义字段数据。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                success = self.product_service.delete_product(product_id)
                if success:
                    # QMessageBox.information(self, "成功", "产品删除成功")  # 移除弹窗
                    self.refresh()
                else:
                    QMessageBox.warning(self, "警告", "产品删除失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除产品失败: {str(e)}")

    def request_comparison(self):
        """请求对比"""
        if len(self.selected_products) < 2:
            QMessageBox.warning(self, "警告", "请至少选择两个产品进行对比")
            return

        self.comparison_requested.emit(self.selected_products)

    def select_product(self, product_id: int):
        """选中指定产品"""
        for i in range(self.product_list.count()):
            item = self.product_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == product_id:
                self.product_list.setCurrentItem(item)
                break

    def get_selected_products(self):
        """获取所有已选择的产品ID"""
        return self.selected_products

    def focus_search(self):
        """聚焦到搜索框"""
        self.search_edit.setFocus()

    def set_filtered_products(self, products):
        """设置筛选的产品列表"""
        self.products = products
        self.update_display()

    def clear_filter(self):
        """清除筛选，显示所有产品"""
        self.refresh()

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.product_list.itemAt(position)
        if not item:
            return

        product_id = item.data(Qt.ItemDataRole.UserRole)
        if not product_id:
            return

        # 获取产品信息
        try:
            product = self.product_service.get_product(product_id)
            if not product:
                return
        except Exception:
            return

        # 创建右键菜单
        menu = QMenu(self)

        # 编辑产品
        edit_action = menu.addAction("编辑产品")
        edit_action.triggered.connect(lambda: self.edit_product(product_id))

        # 添加分隔线
        menu.addSeparator()

        # 删除产品
        delete_action = menu.addAction("删除产品")
        delete_action.triggered.connect(lambda: self.delete_product(product_id))

        # 添加分隔线
        if self.tag_service:
            menu.addSeparator()

            # 快速添加标签子菜单
            tag_menu = menu.addMenu("快速添加标签")

            # 获取所有标签
            try:
                all_tags = self.tag_service.get_all_tags()
                product_tag_ids = [tag.id for tag in product.tags]

                for tag in all_tags:
                    if tag.id not in product_tag_ids:  # 只显示未添加的标签
                        tag_action = tag_menu.addAction(f"{tag.name}")
                        tag_action.triggered.connect(
                            lambda checked, t_id=tag.id: self.add_tag_to_product(
                                product_id, t_id
                            )
                        )

                if not any(tag.id not in product_tag_ids for tag in all_tags):
                    no_tags_action = tag_menu.addAction("所有标签已添加")
                    no_tags_action.setEnabled(False)

                # 删除标签子菜单
                if product.tags:  # 只有当产品有标签时才显示删除菜单
                    remove_tag_menu = menu.addMenu("删除标签")

                    for tag in product.tags:
                        remove_action = remove_tag_menu.addAction(f"移除 {tag.name}")
                        remove_action.triggered.connect(
                            lambda checked, t_id=tag.id: self.remove_tag_from_product(
                                product_id, t_id
                            )
                        )

            except Exception:
                pass

        # 显示菜单
        menu.exec(self.product_list.mapToGlobal(position))

    def add_tag_to_product(self, product_id: int, tag_id: int):
        """为产品添加标签"""
        try:
            if self.product_service.add_tag_to_product(product_id, tag_id):
                tag = self.tag_service.get_tag(tag_id)
                if tag:
                    QMessageBox.information(
                        self, "成功", f"已为产品添加标签: {tag.name}"
                    )
                    self.refresh()  # 刷新产品列表

                    # 通知主窗口刷新标签组件
                    parent = self.parent()
                    while parent:
                        if hasattr(parent, "tag_widget"):
                            parent.tag_widget.refresh()
                            break
                        parent = parent.parent()
            else:
                QMessageBox.warning(self, "失败", "添加标签失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加标签时发生错误: {str(e)}")

    def remove_tag_from_product(self, product_id: int, tag_id: int):
        """从产品中移除标签"""
        try:
            tag = self.tag_service.get_tag(tag_id)
            if self.product_service.remove_tag_from_product(product_id, tag_id):
                tag_name = tag.name if tag else "未知标签"
                # QMessageBox.information(self, "成功", f"已从产品中移除标签: {tag_name}")  # 移除弹窗
                self.refresh()  # 刷新产品列表

                # 通知主窗口刷新标签组件
                parent = self.parent()
                while parent:
                    if hasattr(parent, "tag_widget"):
                        parent.tag_widget.refresh()
                        break
                    parent = parent.parent()
            else:
                QMessageBox.warning(self, "失败", "移除标签失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"移除标签时发生错误: {str(e)}")
