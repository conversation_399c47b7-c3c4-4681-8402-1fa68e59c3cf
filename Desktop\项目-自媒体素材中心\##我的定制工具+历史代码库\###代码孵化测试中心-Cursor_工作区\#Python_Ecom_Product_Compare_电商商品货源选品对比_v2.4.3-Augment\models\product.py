"""
产品模型 - 核心实体
模仿MacOS版本的Product类设计
"""

from datetime import datetime
from typing import List, Optional
from dataclasses import dataclass, field


@dataclass
class Product:
    """产品模型 - 核心实体"""

    # 基本属性
    id: int = 0
    name: str = ""
    sku: str = ""
    selling_price: float = 0.0
    shipping_fee: float = 0.0  # 运费
    free_shipping: bool = True  # 包邮选项，默认包邮
    description: str = ""  # 商品描述
    sales_reference: int = 0  # 销量参考
    reference_url: str = ""  # 参考链接
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    # 关联关系 - 延迟加载
    sources: List["Source"] = field(default_factory=list)
    custom_fields: List["CustomField"] = field(default_factory=list)
    images: List[str] = field(default_factory=list)
    featured_images: List[str] = field(default_factory=list)  # 主要图片（最多3个）
    tags: List["Tag"] = field(default_factory=list)

    @property
    def actual_selling_price(self) -> float:
        """实际收款价格（包邮时=售价，不包邮时=售价+运费）"""
        if self.free_shipping:
            return self.selling_price
        else:
            return self.selling_price + self.shipping_fee

    @property
    def profit_calculation_base(self) -> float:
        """利润计算基础价格（用于与成本对比）"""
        if self.free_shipping:
            return self.selling_price
        else:
            return self.selling_price + self.shipping_fee

    @property
    def lowest_cost(self) -> float:
        """最低成本（含运费）"""
        if not self.sources:
            return 0.0
        return min(source.price + source.shipping_cost for source in self.sources)

    @property
    def highest_cost(self) -> float:
        """最高成本（含运费）"""
        if not self.sources:
            return 0.0
        return max(source.price + source.shipping_cost for source in self.sources)

    @property
    def average_cost(self) -> float:
        """平均成本（含运费）"""
        if not self.sources:
            return 0.0
        return sum(
            source.price + source.shipping_cost for source in self.sources
        ) / len(self.sources)

    @property
    def profit(self) -> float:
        """利润（使用实际收款价格 - 最低成本含运费）"""
        return self.profit_calculation_base - self.lowest_cost

    @property
    def profit_margin(self) -> float:
        """利润率"""
        if self.profit_calculation_base <= 0:
            return 0.0
        return (self.profit / self.profit_calculation_base) * 100

    @property
    def shipping_display(self) -> str:
        """运费显示文本"""
        if self.free_shipping:
            return "包邮"
        else:
            return f"运费: ¥{self.shipping_fee:.2f}"

    @property
    def total_quantity(self) -> int:
        """总库存数量"""
        return sum(source.quantity for source in self.sources)

    @property
    def best_source(self) -> Optional["Source"]:
        """最优货源（含运费成本最低）"""
        if not self.sources:
            return None
        return min(self.sources, key=lambda s: s.price + s.shipping_cost)

    @property
    def worst_source(self) -> Optional["Source"]:
        """最差货源（含运费成本最高）"""
        if not self.sources:
            return None
        return max(self.sources, key=lambda s: s.price + s.shipping_cost)

    @property
    def is_valid_reference_url(self) -> bool:
        """参考链接有效性检查"""
        if not self.reference_url:
            return False
        return self.reference_url.startswith(("http://", "https://"))

    def add_source(self, source: "Source"):
        """添加货源"""
        self.sources.append(source)
        source.product_id = self.id
        self.update_timestamp()

    def remove_source(self, source: "Source"):
        """移除货源"""
        self.sources = [s for s in self.sources if s.id != source.id]
        self.update_timestamp()

    def add_custom_field(self, field: "CustomField"):
        """添加自定义字段"""
        self.custom_fields.append(field)
        field.product_id = self.id
        self.update_timestamp()

    def remove_custom_field(self, field: "CustomField"):
        """移除自定义字段"""
        self.custom_fields = [f for f in self.custom_fields if f.id != field.id]
        self.update_timestamp()

    def add_image(self, image_path: str):
        """添加图片"""
        if image_path not in self.images:
            self.images.append(image_path)
            self.update_timestamp()

    def remove_image(self, image_path: str):
        """移除图片"""
        if image_path in self.images:
            self.images.remove(image_path)
            self.update_timestamp()
            # 如果图片是主要图片，也要从主要图片列表中移除
            if image_path in self.featured_images:
                self.featured_images.remove(image_path)

    def add_featured_image(self, image_path: str):
        """添加主要图片"""
        if image_path not in self.featured_images and len(self.featured_images) < 3:
            self.featured_images.append(image_path)
            self.update_timestamp()

    def remove_featured_image(self, image_path: str):
        """移除主要图片"""
        if image_path in self.featured_images:
            self.featured_images.remove(image_path)
            self.update_timestamp()

    def set_featured_images(self, image_paths: List[str]):
        """设置主要图片（最多3个）"""
        # 确保所有图片都在图片列表中
        valid_images = [img for img in image_paths if img in self.images]
        # 最多3个
        self.featured_images = valid_images[:3]
        self.update_timestamp()

    def get_display_images(self) -> List[str]:
        """获取用于显示的图片（优先显示主要图片）"""
        # 如果有主要图片，优先显示主要图片
        if self.featured_images:
            # 先显示主要图片，再显示其他图片
            remaining_images = [
                img for img in self.images if img not in self.featured_images
            ]
            return self.featured_images + remaining_images
        else:
            # 如果没有主要图片，返回所有图片
            return self.images

    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()

    def add_tag(self, tag: "Tag"):
        """添加标签"""
        if tag not in self.tags:
            self.tags.append(tag)
            self.update_timestamp()

    def remove_tag(self, tag: "Tag"):
        """移除标签"""
        if tag in self.tags:
            self.tags.remove(tag)
            self.update_timestamp()

    def has_tag(self, tag: "Tag") -> bool:
        """检查是否有某个标签"""
        return tag in self.tags

    def get_tag_names(self) -> List[str]:
        """获取所有标签名称"""
        return [tag.name for tag in self.tags]

    def get_custom_field_value(self, field_name: str) -> Optional[str]:
        """获取自定义字段值"""
        for field in self.custom_fields:
            if field.name == field_name:
                return field.value
        return None

    def set_custom_field_value(
        self, field_name: str, value: str, field_type: str = "text"
    ):
        """设置自定义字段值"""
        for field in self.custom_fields:
            if field.name == field_name:
                field.value = value
                field.field_type = field_type
                self.update_timestamp()
                return

        # 如果字段不存在，创建新字段
        from .custom_field import CustomField

        new_field = CustomField(
            name=field_name, value=value, field_type=field_type, product_id=self.id
        )
        self.add_custom_field(new_field)

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "sku": self.sku,
            "selling_price": self.selling_price,
            "shipping_fee": self.shipping_fee,
            "free_shipping": self.free_shipping,
            "description": self.description,
            "sales_reference": self.sales_reference,
            "reference_url": self.reference_url,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "sources": [source.to_dict() for source in self.sources],
            "custom_fields": [field.to_dict() for field in self.custom_fields],
            "images": self.images.copy(),
            "featured_images": self.featured_images.copy(),
            "tags": [tag.to_dict() for tag in self.tags],
            "lowest_cost": self.lowest_cost,
            "highest_cost": self.highest_cost,
            "average_cost": self.average_cost,
            "profit": self.profit,
            "profit_margin": self.profit_margin,
            "total_quantity": self.total_quantity,
            "is_valid_reference_url": self.is_valid_reference_url,
            "shipping_display": self.shipping_display,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "Product":
        """从字典创建产品"""
        # 创建基本产品对象
        product = cls(
            id=data.get("id", 0),
            name=data.get("name", ""),
            sku=data.get("sku", ""),
            selling_price=data.get("selling_price", 0.0),
            shipping_fee=data.get("shipping_fee", 0.0),
            free_shipping=data.get("free_shipping", True),
            description=data.get("description", ""),
            sales_reference=data.get("sales_reference", 0),
            reference_url=data.get("reference_url", ""),
            created_at=datetime.fromisoformat(
                data.get("created_at", datetime.now().isoformat())
            ),
            updated_at=datetime.fromisoformat(
                data.get("updated_at", datetime.now().isoformat())
            ),
        )

        # 设置图片
        product.images = data.get("images", [])
        product.featured_images = data.get("featured_images", [])

        # 设置货源（需要在数据库层面处理关联）
        # 设置自定义字段（需要在数据库层面处理关联）
        # 设置标签（需要在数据库层面处理关联）

        return product

    def __str__(self) -> str:
        return f"Product(id={self.id}, name='{self.name}', sku='{self.sku}', price={self.selling_price})"

    def __repr__(self) -> str:
        return self.__str__()


# 示例产品数据
SAMPLE_PRODUCTS = [
    {
        "name": "iPhone 15 Pro",
        "sku": "IP15P-256",
        "selling_price": 8999.0,
        "description": "苹果iPhone 15 Pro 256GB 深空黑色 A17 Pro芯片 全新钛金属设计 支持USB-C接口",
        "sales_reference": 1250,
    },
    {
        "name": "华为Mate 60 Pro",
        "sku": "HWM60P-512",
        "selling_price": 6999.0,
        "description": "华为Mate 60 Pro 512GB 雅川青 麒麟9000S芯片 支持5G网络 徕卡影像系统",
        "sales_reference": 890,
    },
    {
        "name": "小米14 Ultra",
        "sku": "MI14U-512",
        "selling_price": 5999.0,
        "description": "小米14 Ultra 512GB 白色 骁龙8 Gen3芯片 徕卡专业光学镜头 支持无线充电",
        "sales_reference": 2100,
    },
]
