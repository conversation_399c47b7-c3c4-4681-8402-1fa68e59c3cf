# API文档

## 概述

本文档详细描述了电商货源选品对比工具v2.3.6的API接口，包括核心模块、智能抓取系统和扩展接口。

## 目录

- [核心API](#核心api)
  - [产品管理API](#产品管理api)
  - [货源管理API](#货源管理api)
  - [数据库API](#数据库api)
- [智能抓取API](#智能抓取api)
  - [网页抓取器API](#网页抓取器api)
  - [AI分析引擎API](#ai分析引擎api)
  - [数据映射器API](#数据映射器api)
- [工具类API](#工具类api)
  - [图片处理API](#图片处理api)
  - [导入导出API](#导入导出api)
- [UI组件API](#ui组件api)

---

## 核心API

### 产品管理API

#### Product类

```python
class Product:
    """产品模型类"""
    
    def __init__(self, id=None, name="", code="", price=0.0, 
                 description="", reference_url="", sales_reference="",
                 shipping_fee=0.0, free_shipping=True, created_at=None):
        """初始化产品对象
        
        Args:
            id (int, optional): 产品ID
            name (str): 产品名称
            code (str): 产品编码
            price (float): 产品价格
            description (str): 产品描述
            reference_url (str): 参考链接
            sales_reference (str): 销量参考
            shipping_fee (float): 运费
            free_shipping (bool): 是否包邮
            created_at (datetime, optional): 创建时间
        """
```

##### 属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| id | int | 产品唯一标识符 |
| name | str | 产品名称 |
| code | str | 产品编码 |
| price | float | 产品价格 |
| description | str | 产品描述 |
| reference_url | str | 参考链接 |
| sales_reference | str | 销量参考 |
| shipping_fee | float | 运费金额 |
| free_shipping | bool | 是否包邮 |
| created_at | datetime | 创建时间 |

##### 方法

**save()**
```python
def save(self) -> bool:
    """保存产品到数据库
    
    Returns:
        bool: 保存成功返回True，失败返回False
        
    Example:
        >>> product = Product(name="测试产品", price=100.0)
        >>> result = product.save()
        >>> print(result)  # True
    """
```

**delete()**
```python
def delete(self) -> bool:
    """从数据库删除产品
    
    Returns:
        bool: 删除成功返回True，失败返回False
        
    Example:
        >>> product = Product.find_by_id(1)
        >>> result = product.delete()
        >>> print(result)  # True
    """
```

**to_dict()**
```python
def to_dict(self) -> dict:
    """将产品对象转换为字典
    
    Returns:
        dict: 包含产品所有属性的字典
        
    Example:
        >>> product = Product(name="测试产品", price=100.0)
        >>> data = product.to_dict()
        >>> print(data['name'])  # "测试产品"
    """
```

##### 类方法

**find_by_id()**
```python
@classmethod
def find_by_id(cls, product_id: int) -> Product | None:
    """根据ID查找产品
    
    Args:
        product_id (int): 产品ID
        
    Returns:
        Product | None: 找到返回Product实例，否则返回None
        
    Example:
        >>> product = Product.find_by_id(1)
        >>> if product:
        ...     print(product.name)
    """
```

**find_all()**
```python
@classmethod
def find_all(cls, conditions: dict = None) -> list[Product]:
    """查找所有产品
    
    Args:
        conditions (dict, optional): 查询条件
        
    Returns:
        list[Product]: 产品列表
        
    Example:
        >>> products = Product.find_all()
        >>> print(len(products))
        
        >>> products = Product.find_all({'price__gte': 100})
        >>> print(f"价格>=100的产品数量: {len(products)}")
    """
```

**search()**
```python
@classmethod
def search(cls, keyword: str) -> list[Product]:
    """搜索产品
    
    Args:
        keyword (str): 搜索关键词
        
    Returns:
        list[Product]: 匹配的产品列表
        
    Example:
        >>> products = Product.search("手机")
        >>> for product in products:
        ...     print(product.name)
    """
```

---

### 货源管理API

#### Source类

```python
class Source:
    """货源模型类"""
    
    def __init__(self, id=None, product_id=None, supplier_name="",
                 wholesale_price=0.0, dropship_price=0.0, contact_info="",
                 website_url="", minimum_order=1, stock_quantity=0,
                 is_wholesale=False, is_dropship=False, 
                 shipping_fee=0.0, notes="", created_at=None):
        """初始化货源对象
        
        Args:
            id (int, optional): 货源ID
            product_id (int): 关联的产品ID
            supplier_name (str): 供应商名称
            wholesale_price (float): 批发价格
            dropship_price (float): 代发价格
            contact_info (str): 联系信息
            website_url (str): 网站链接
            minimum_order (int): 最小订量
            stock_quantity (int): 库存数量
            is_wholesale (bool): 是否支持批发
            is_dropship (bool): 是否支持代发
            shipping_fee (float): 运费
            notes (str): 备注信息
            created_at (datetime, optional): 创建时间
        """
```

##### 主要方法

**calculate_profit()**
```python
def calculate_profit(self, selling_price: float, quantity: int = 1) -> dict:
    """计算利润信息
    
    Args:
        selling_price (float): 销售价格
        quantity (int): 数量，默认为1
        
    Returns:
        dict: 包含利润计算结果的字典
        
    Example:
        >>> source = Source(wholesale_price=50.0, shipping_fee=10.0)
        >>> profit_info = source.calculate_profit(100.0, 10)
        >>> print(profit_info)
        {
            'unit_cost': 50.0,
            'total_cost': 500.0,
            'shipping_cost': 10.0,
            'total_cost_with_shipping': 510.0,
            'unit_profit': 40.0,
            'total_profit': 390.0,
            'profit_margin': 0.39
        }
    """
```

**get_images()**
```python
def get_images(self) -> list[str]:
    """获取货源图片列表
    
    Returns:
        list[str]: 图片文件路径列表
        
    Example:
        >>> source = Source.find_by_id(1)
        >>> images = source.get_images()
        >>> print(f"图片数量: {len(images)}")
    """
```

---

### 数据库API

#### DatabaseManager类

```python
class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/database.db"):
        """初始化数据库管理器
        
        Args:
            db_path (str): 数据库文件路径
        """
```

##### 主要方法

**execute_query()**
```python
def execute_query(self, query: str, params: tuple = ()) -> list[dict]:
    """执行查询语句
    
    Args:
        query (str): SQL查询语句
        params (tuple): 查询参数
        
    Returns:
        list[dict]: 查询结果列表
        
    Example:
        >>> db = DatabaseManager()
        >>> results = db.execute_query(
        ...     "SELECT * FROM products WHERE price > ?", 
        ...     (100,)
        ... )
        >>> print(len(results))
    """
```

**execute_non_query()**
```python
def execute_non_query(self, query: str, params: tuple = ()) -> bool:
    """执行非查询语句（INSERT, UPDATE, DELETE）
    
    Args:
        query (str): SQL语句
        params (tuple): 参数
        
    Returns:
        bool: 执行成功返回True
        
    Example:
        >>> db = DatabaseManager()
        >>> success = db.execute_non_query(
        ...     "UPDATE products SET price = ? WHERE id = ?",
        ...     (120.0, 1)
        ... )
        >>> print(success)  # True
    """
```

---

## 智能抓取API

### 网页抓取器API

#### WebScraper类

```python
class WebScraper:
    """网页抓取器"""
    
    def __init__(self, headless: bool = True, timeout: int = 30000):
        """初始化网页抓取器
        
        Args:
            headless (bool): 是否无头模式运行浏览器
            timeout (int): 页面加载超时时间（毫秒）
        """
```

##### 主要方法

**scrape_product_info()**
```python
async def scrape_product_info(self, url: str) -> dict:
    """抓取商品信息
    
    Args:
        url (str): 商品页面URL
        
    Returns:
        dict: 抓取到的商品信息
        
    Raises:
        ScrapingError: 抓取失败时抛出
        
    Example:
        >>> scraper = WebScraper()
        >>> result = await scraper.scrape_product_info(
        ...     "https://detail.1688.com/offer/123456.html"
        ... )
        >>> print(result)
        {
            'title': '商品标题',
            'price': '12.50-15.80',
            'images': ['img1.jpg', 'img2.jpg'],
            'description': '商品描述',
            'platform': '1688'
        }
    """
```

**detect_platform()**
```python
def detect_platform(self, url: str) -> str:
    """检测电商平台
    
    Args:
        url (str): 商品页面URL
        
    Returns:
        str: 平台名称 ('1688', 'taobao', 'tmall', 'unknown')
        
    Example:
        >>> scraper = WebScraper()
        >>> platform = scraper.detect_platform(
        ...     "https://detail.1688.com/offer/123456.html"
        ... )
        >>> print(platform)  # '1688'
    """
```

**add_platform_selectors()**
```python
def add_platform_selectors(self, platform: str, selectors: dict):
    """添加平台选择器配置
    
    Args:
        platform (str): 平台名称
        selectors (dict): 选择器配置
        
    Example:
        >>> scraper = WebScraper()
        >>> scraper.add_platform_selectors('custom', {
        ...     'title': '.product-title',
        ...     'price': '.price-value',
        ...     'images': '.gallery img'
        ... })
    """
```

---

### AI分析引擎API

#### AIAnalyzer类

```python
class AIAnalyzer:
    """AI分析引擎"""
    
    def __init__(self, text_model: str = "qwen2.5:14b", 
                 vision_model: str = "qwen2.5vl:latest"):
        """初始化AI分析引擎
        
        Args:
            text_model (str): 文本分析模型名称
            vision_model (str): 视觉分析模型名称
        """
```

##### 主要方法

**analyze_product_data()**
```python
async def analyze_product_data(self, scraped_data: dict) -> dict:
    """分析商品数据
    
    Args:
        scraped_data (dict): 抓取的原始数据
        
    Returns:
        dict: AI分析结果
        
    Example:
        >>> analyzer = AIAnalyzer()
        >>> result = await analyzer.analyze_product_data({
        ...     'title': '原始商品标题',
        ...     'description': '商品描述文本',
        ...     'images': ['img1.jpg', 'img2.jpg']
        ... })
        >>> print(result)
        {
            'clean_title': '清理后的标题',
            'supplier_name': '推荐供应商名称',
            'wholesale_price': 12.5,
            'product_specs': '规格参数',
            'description': '优化后的描述'
        }
    """
```

**analyze_text()**
```python
async def analyze_text(self, text_content: dict) -> dict:
    """分析文本内容
    
    Args:
        text_content (dict): 包含文本的字典
        
    Returns:
        dict: 文本分析结果
        
    Example:
        >>> analyzer = AIAnalyzer()
        >>> result = await analyzer.analyze_text({
        ...     'title': '商品标题',
        ...     'description': '商品描述'
        ... })
    """
```

**analyze_images()**
```python
async def analyze_images(self, image_urls: list[str]) -> dict:
    """分析商品图片
    
    Args:
        image_urls (list[str]): 图片URL列表
        
    Returns:
        dict: 图片分析结果
        
    Example:
        >>> analyzer = AIAnalyzer()
        >>> result = await analyzer.analyze_images([
        ...     'https://example.com/img1.jpg',
        ...     'https://example.com/img2.jpg'
        ... ])
    """
```

**set_custom_prompt()**
```python
def set_custom_prompt(self, prompt_type: str, prompt_template: str):
    """设置自定义提示词模板
    
    Args:
        prompt_type (str): 提示词类型 ('text_analysis', 'image_analysis')
        prompt_template (str): 提示词模板
        
    Example:
        >>> analyzer = AIAnalyzer()
        >>> analyzer.set_custom_prompt('text_analysis', '''
        ... 请分析以下商品信息：
        ... 标题：{title}
        ... 描述：{description}
        ... 返回JSON格式结果...
        ... ''')
    """
```

---

### 数据映射器API

#### DataMapper类

```python
class DataMapper:
    """数据映射器"""
    
    def __init__(self):
        """初始化数据映射器"""
```

##### 主要方法

**map_scraped_data()**
```python
def map_scraped_data(self, scraped_data: dict, ai_analysis: dict) -> dict:
    """映射抓取数据到货源格式
    
    Args:
        scraped_data (dict): 原始抓取数据
        ai_analysis (dict): AI分析结果
        
    Returns:
        dict: 映射后的货源数据
        
    Example:
        >>> mapper = DataMapper()
        >>> result = mapper.map_scraped_data(
        ...     {'title': '原始标题', 'price': '12.50'},
        ...     {'clean_title': '清理标题', 'wholesale_price': 12.5}
        ... )
        >>> print(result)
        {
            'supplier_name': '清理标题',
            'wholesale_price': 12.5,
            'dropship_price': 15.0,
            'description': '商品描述',
            'images': ['img1.jpg', 'img2.jpg']
        }
    """
```

**add_custom_mapping()**
```python
def add_custom_mapping(self, field_name: str, mapping_config: dict):
    """添加自定义字段映射
    
    Args:
        field_name (str): 字段名称
        mapping_config (dict): 映射配置
        
    Example:
        >>> mapper = DataMapper()
        >>> mapper.add_custom_mapping('custom_field', {
        ...     'source': 'ai_analysis.custom_data',
        ...     'fallback': 'scraped_data.fallback_value',
        ...     'processor': 'custom_processor'
        ... })
    """
```

**register_processor()**
```python
def register_processor(self, processor_name: str, processor_func):
    """注册自定义数据处理器
    
    Args:
        processor_name (str): 处理器名称
        processor_func (callable): 处理器函数
        
    Example:
        >>> def custom_price_processor(value):
        ...     return float(value) * 1.2
        >>> 
        >>> mapper = DataMapper()
        >>> mapper.register_processor('markup_20', custom_price_processor)
    """
```

---

## 工具类API

### 图片处理API

#### ImageUtils类

```python
class ImageUtils:
    """图片处理工具类"""
    
    @staticmethod
    def resize_image(image_path: str, target_size: tuple, 
                    output_path: str = None) -> str:
        """调整图片大小
        
        Args:
            image_path (str): 原图片路径
            target_size (tuple): 目标尺寸 (width, height)
            output_path (str, optional): 输出路径，默认覆盖原文件
            
        Returns:
            str: 处理后的图片路径
            
        Example:
            >>> resized_path = ImageUtils.resize_image(
            ...     'original.jpg', 
            ...     (800, 600), 
            ...     'resized.jpg'
            ... )
        """
    
    @staticmethod
    def calculate_image_hash(image_path: str) -> str:
        """计算图片感知哈希
        
        Args:
            image_path (str): 图片路径
            
        Returns:
            str: 图片哈希值
            
        Example:
            >>> hash_value = ImageUtils.calculate_image_hash('image.jpg')
            >>> print(hash_value)  # 'a1b2c3d4e5f6...'
        """
    
    @staticmethod
    def find_duplicate_images(image_paths: list[str], 
                             threshold: int = 5) -> list[list[str]]:
        """查找重复图片
        
        Args:
            image_paths (list[str]): 图片路径列表
            threshold (int): 相似度阈值
            
        Returns:
            list[list[str]]: 重复图片组列表
            
        Example:
            >>> duplicates = ImageUtils.find_duplicate_images([
            ...     'img1.jpg', 'img2.jpg', 'img3.jpg'
            ... ])
            >>> print(duplicates)  # [['img1.jpg', 'img3.jpg']]
        """
```

---

### 导入导出API

#### ExportUtils类

```python
class ExportUtils:
    """导入导出工具类"""
    
    @staticmethod
    def export_products_to_excel(products: list[Product], 
                                filename: str) -> bool:
        """导出产品到Excel
        
        Args:
            products (list[Product]): 产品列表
            filename (str): 输出文件名
            
        Returns:
            bool: 导出成功返回True
            
        Example:
            >>> products = Product.find_all()
            >>> success = ExportUtils.export_products_to_excel(
            ...     products, 
            ...     'products_export.xlsx'
            ... )
        """
    
    @staticmethod
    def import_products_from_excel(filename: str) -> list[dict]:
        """从Excel导入产品
        
        Args:
            filename (str): Excel文件路径
            
        Returns:
            list[dict]: 产品数据列表
            
        Example:
            >>> product_data = ExportUtils.import_products_from_excel(
            ...     'products_import.xlsx'
            ... )
            >>> for data in product_data:
            ...     product = Product(**data)
            ...     product.save()
        """
    
    @staticmethod
    def export_to_json(data: list[dict], filename: str) -> bool:
        """导出数据到JSON
        
        Args:
            data (list[dict]): 要导出的数据
            filename (str): 输出文件名
            
        Returns:
            bool: 导出成功返回True
        """
```

---

## UI组件API

### 智能抓取选项卡API

#### SmartScraperTab类

```python
class SmartScraperTab(QWidget):
    """智能抓取选项卡"""
    
    # 信号定义
    scraping_started = pyqtSignal()
    progress_updated = pyqtSignal(str, int)
    scraping_completed = pyqtSignal(dict)
    scraping_failed = pyqtSignal(str)
    data_applied = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        """初始化智能抓取选项卡
        
        Args:
            parent (QWidget, optional): 父窗口
        """
```

##### 主要方法

**start_scraping()**
```python
def start_scraping(self, url: str):
    """开始抓取商品信息
    
    Args:
        url (str): 商品页面URL
        
    Emits:
        scraping_started: 抓取开始信号
        progress_updated: 进度更新信号
        scraping_completed: 抓取完成信号
        scraping_failed: 抓取失败信号
        
    Example:
        >>> tab = SmartScraperTab()
        >>> tab.scraping_completed.connect(self.handle_result)
        >>> tab.start_scraping('https://detail.1688.com/offer/123.html')
    """
```

**apply_scraped_data()**
```python
def apply_scraped_data(self) -> dict:
    """应用抓取的数据到表单
    
    Returns:
        dict: 应用的数据
        
    Emits:
        data_applied: 数据应用信号
        
    Example:
        >>> tab = SmartScraperTab()
        >>> data = tab.apply_scraped_data()
        >>> print(data['supplier_name'])
    """
```

**set_supported_platforms()**
```python
def set_supported_platforms(self, platforms: list[str]):
    """设置支持的平台列表
    
    Args:
        platforms (list[str]): 支持的平台名称列表
        
    Example:
        >>> tab = SmartScraperTab()
        >>> tab.set_supported_platforms(['1688', 'taobao', 'tmall'])
    """
```

---

### 抓取工作线程API

#### ScrapingWorker类

```python
class ScrapingWorker(QThread):
    """抓取工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(str, int)
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, url: str, scraper: WebScraper, 
                 analyzer: AIAnalyzer, mapper: DataMapper):
        """初始化抓取工作线程
        
        Args:
            url (str): 抓取URL
            scraper (WebScraper): 网页抓取器实例
            analyzer (AIAnalyzer): AI分析引擎实例
            mapper (DataMapper): 数据映射器实例
        """
```

##### 主要方法

**run()**
```python
def run(self):
    """执行抓取任务
    
    工作流程：
    1. 网页抓取 (20% 进度)
    2. AI分析 (60% 进度)
    3. 数据映射 (90% 进度)
    4. 完成 (100% 进度)
    
    Emits:
        progress_updated: 进度更新信号
        result_ready: 结果就绪信号
        error_occurred: 错误发生信号
    """
```

**cancel()**
```python
def cancel(self):
    """取消抓取任务
    
    Example:
        >>> worker = ScrapingWorker(url, scraper, analyzer, mapper)
        >>> worker.start()
        >>> # 如需取消
        >>> worker.cancel()
    """
```

---

## 异常处理

### 异常类层次结构

```python
class ApplicationError(Exception):
    """应用程序错误基类"""
    pass

class ScrapingError(ApplicationError):
    """抓取相关错误"""
    pass

class NetworkError(ScrapingError):
    """网络相关错误"""
    pass

class ParsingError(ScrapingError):
    """页面解析错误"""
    pass

class AIAnalysisError(ApplicationError):
    """AI分析错误"""
    pass

class DataMappingError(ApplicationError):
    """数据映射错误"""
    pass

class DatabaseError(ApplicationError):
    """数据库操作错误"""
    pass
```

### 错误处理示例

```python
try:
    # 执行抓取操作
    scraper = WebScraper()
    result = await scraper.scrape_product_info(url)
    
except NetworkError as e:
    # 处理网络错误
    logging.error(f"网络连接失败: {e}")
    show_error_message("网络连接失败，请检查网络设置")
    
except ParsingError as e:
    # 处理解析错误
    logging.error(f"页面解析失败: {e}")
    show_error_message("页面格式不支持，请尝试其他商品链接")
    
except AIAnalysisError as e:
    # 处理AI分析错误
    logging.error(f"AI分析失败: {e}")
    show_error_message("AI分析服务异常，请检查模型配置")
    
except Exception as e:
    # 处理其他未知错误
    logging.exception(f"未知错误: {e}")
    show_error_message("发生未知错误，请联系技术支持")
```

---

## 配置选项

### 应用配置

```python
# config.py
class Config:
    """应用配置类"""
    
    # 基本信息
    APP_NAME = "Python_Ecom_Product_Source_Compare"
    APP_VERSION = "2.3.6"
    
    # 数据库配置
    DATABASE_PATH = "data/database.db"
    DATABASE_TIMEOUT = 30
    
    # 抓取配置
    SCRAPING_TIMEOUT = 30000  # 毫秒
    SCRAPING_RETRY_COUNT = 3
    SCRAPING_USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        # 更多User-Agent字符串
    ]
    
    # AI配置
    AI_TEXT_MODEL = "qwen2.5:14b"
    AI_VISION_MODEL = "qwen2.5vl:latest"
    AI_REQUEST_TIMEOUT = 60
    
    # UI配置
    UI_THEME = "default"
    UI_LANGUAGE = "zh_CN"
    UI_WINDOW_SIZE = (1200, 800)
    
    # 图片配置
    IMAGE_MAX_SIZE = (1920, 1080)
    IMAGE_QUALITY = 85
    IMAGE_FORMATS = [".jpg", ".jpeg", ".png", ".gif"]
```

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `ECOM_DB_PATH` | 数据库文件路径 | `data/database.db` |
| `ECOM_LOG_LEVEL` | 日志级别 | `INFO` |
| `ECOM_AI_TIMEOUT` | AI请求超时时间 | `60` |
| `ECOM_SCRAPING_HEADLESS` | 无头浏览器模式 | `true` |

---

## 扩展开发

### 插件开发接口

```python
class PluginInterface:
    """插件接口"""
    
    def get_name(self) -> str:
        """获取插件名称"""
        pass
    
    def get_version(self) -> str:
        """获取插件版本"""
        pass
    
    def initialize(self, app_context: dict):
        """初始化插件"""
        pass
    
    def get_menu_items(self) -> list[dict]:
        """获取菜单项"""
        pass
    
    def handle_event(self, event_type: str, data: dict):
        """处理事件"""
        pass
```

### 自定义抓取器开发

```python
class CustomScraper(WebScraper):
    """自定义抓取器示例"""
    
    def __init__(self):
        super().__init__()
        self.add_platform_selectors('custom_platform', {
            'title': '.custom-title',
            'price': '.custom-price',
            'images': '.custom-gallery img'
        })
    
    async def extract_custom_data(self, page, platform):
        """提取自定义数据"""
        # 实现自定义提取逻辑
        pass
```

---

## 最佳实践

### 性能优化建议

1. **使用连接池**：数据库操作使用连接池避免频繁创建连接
2. **异步处理**：耗时操作使用异步线程，避免阻塞UI
3. **缓存机制**：合理使用缓存减少重复计算
4. **资源管理**：及时释放浏览器等资源

### 错误处理建议

1. **分层处理**：不同层次的错误分别处理
2. **用户友好**：错误信息要对用户友好
3. **日志记录**：详细记录错误信息用于调试
4. **优雅降级**：关键功能失败时提供备选方案

### 代码规范建议

1. **类型注解**：使用类型注解提高代码可读性
2. **文档字符串**：为公共API提供详细文档
3. **单元测试**：编写测试用例保证代码质量
4. **代码格式化**：使用black等工具保持代码风格一致

---

**文档版本**：v2.3.6  
**最后更新**：2025-01-07  
**维护团队**：AI Assistant & 开发者社区 