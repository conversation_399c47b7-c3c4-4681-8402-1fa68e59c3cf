#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析结果界面测试脚本
测试新增的"AI分析结果"标签页和增强的字段提取功能
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from scrapers.ai_analyzer import AIAnalyzer, AnalysisResult
from scrapers.data_mapper import SourceDataMapper
from scrapers.web_scraper import ScrapedContent


def create_mock_1688_content():
    """创建模拟的1688商品内容"""
    content = """
    中国大陆 宠物狗咬绳玩具 PC+硅胶材质 啃咬玩具类别
    货号: XC2024-001 净重57克 毛重75克 温州辛宠宠物用品有限公司
    价格¥2.80 年销量10万+ 评价30+条 已售3266件 好评如潮,质量棒,发货快
    24小时揽收率83% 48小时揽收率98% 月订单数100以内 分销商400+家 下游铺货数1200+家 退货率0%
    外箱尺寸59*34*34.5cm 装箱数200个 箱子重量16.2kg
    7天无理由退货 晚发必赔 支持圆通韵达申通中通顺丰邮政等6种快递面单
    40+种颜色款式 蜻蜓带绳款 毛绒款 经典款 迷你款 夜光款 彩虹款
    品牌: 辛宠 产地: 浙江温州 经营1年
    """

    scraped_content = ScrapedContent(
        title="宠物狗咬绳玩具 中国大陆",
        url="https://detail.1688.com/offer/781751893588981663.html",
        content=content.strip(),
        images=[
            "https://cbu01.alicdn.com/img/ibank/O1CN01XXX_1.jpg",
            "https://cbu01.alicdn.com/img/ibank/O1CN01XXX_2.jpg",
        ],
        price="¥2.80",
        price_range="¥2.80-¥3.36",
        supplier="温州辛宠宠物用品有限公司",
        specifications={
            "材质": "PC+硅胶",
            "产品类别": "啃咬玩具",
            "净重": "57克",
            "毛重": "75克",
        },
        description="宠物狗咬绳玩具，采用PC+硅胶材质，安全无毒",
        success=True,
        source_type="1688",
    )

    return scraped_content


def test_ai_analyzer():
    """测试AI分析器的增强功能"""
    print("🤖 测试AI分析器增强功能...")

    # 创建模拟内容
    scraped_content = create_mock_1688_content()

    # 初始化AI分析器
    analyzer = AIAnalyzer()

    # 测试AI连接
    print("- 检查AI连接状态...")
    if analyzer.test_connection():
        print("  ✅ AI连接正常")
    else:
        print("  ❌ AI连接失败，使用模拟数据")

    # 执行分析
    print("- 执行AI内容分析...")
    try:
        analysis_result = analyzer.analyze_content(scraped_content)

        if analysis_result.success:
            print("  ✅ AI分析成功")
            print(f"  ⏱️ 处理时间: {analysis_result.processing_time:.2f}秒")

            # 检查新增字段
            fields_to_check = [
                ("product_attributes", "商品属性"),
                ("packaging_info", "物流包装信息"),
                ("sales_data", "销售数据"),
                ("dropship_data", "代发服务数据"),
                ("service_info", "服务保障信息"),
                ("variant_info", "规格变体信息"),
            ]

            extracted_fields_count = 0
            for field_name, field_desc in fields_to_check:
                field_value = getattr(analysis_result, field_name, {})
                if isinstance(field_value, dict) and field_value:
                    valid_values = [
                        v for v in field_value.values() if v and str(v).strip()
                    ]
                    if valid_values:
                        print(f"  📦 {field_desc}: {len(valid_values)}个字段")
                        extracted_fields_count += len(valid_values)
                    else:
                        print(f"  📦 {field_desc}: 无有效数据")
                else:
                    print(f"  📦 {field_desc}: 字段缺失")

            print(f"  📊 总提取字段数: {extracted_fields_count}个")

        else:
            print(f"  ❌ AI分析失败: {analysis_result.error_message}")

    except Exception as e:
        print(f"  ❌ 分析过程异常: {e}")
        # 创建模拟分析结果用于测试
        analysis_result = create_mock_analysis_result()

    return scraped_content, analysis_result


def create_mock_analysis_result():
    """创建模拟的AI分析结果"""
    print("  📝 创建模拟AI分析结果用于测试...")

    analysis_result = AnalysisResult(
        success=True,
        title="宠物狗咬绳玩具",
        cleaned_content="宠物狗咬绳玩具测试内容",
        processing_time=1.25,
    )

    # 添加增强字段
    analysis_result.product_attributes = {
        "material": "PC+硅胶",
        "category": "啃咬玩具",
        "product_code": "XC2024-001",
        "net_weight": "57克",
        "gross_weight": "75克",
        "brand": "辛宠",
        "origin": "浙江温州",
    }

    analysis_result.sales_data = {
        "annual_sales": 100000,
        "sold_quantity": 3266,
        "review_count": 30,
        "review_tags": ["好评如潮", "质量棒", "发货快"],
    }

    analysis_result.dropship_data = {
        "pickup_rate_24h": 83.0,
        "pickup_rate_48h": 98.0,
        "monthly_orders": 100,
        "distributor_count": 400,
        "downstream_stores": 1200,
        "return_rate": 0.0,
    }

    analysis_result.packaging_info = {
        "box_dimensions": "59*34*34.5cm",
        "box_quantity": "200个",
        "box_weight": "16.2kg",
    }

    analysis_result.service_info = {
        "shipping_promise": "晚发必赔",
        "supported_couriers": "圆通韵达申通中通顺丰邮政等6种",
        "service_guarantees": ["7天无理由退货", "晚发必赔"],
    }

    analysis_result.variant_info = {
        "total_variants": 40,
        "color_variants": [
            "蜻蜓带绳款",
            "毛绒款",
            "经典款",
            "迷你款",
            "夜光款",
            "彩虹款",
        ],
        "variant_types": ["带绳款", "毛绒款", "经典款"],
    }

    return analysis_result


def test_data_mapper(scraped_content, analysis_result):
    """测试数据映射器"""
    print("\n🗂️ 测试数据映射器...")

    mapper = SourceDataMapper()

    try:
        # 执行数据映射
        mapped_data = mapper.map_to_source_data(scraped_content, analysis_result)

        print("  ✅ 数据映射成功")

        # 检查关键字段
        key_fields = [
            ("name", "产品名称"),
            ("supplier_name", "供应商"),
            ("wholesale_price", "批发价"),
            ("pickup_rate_24h", "24小时揽收率"),
            ("pickup_rate_48h", "48小时揽收率"),
            ("distributor_count", "分销商数"),
            ("downstream_stores", "下游铺货数"),
        ]

        print("  📋 关键字段检查:")
        for field_name, field_desc in key_fields:
            value = mapped_data.get(field_name, "未设置")
            print(f"    {field_desc}: {value}")

        # 检查自定义字段
        custom_fields = mapped_data.get("custom_fields", {})
        if custom_fields:
            print(f"  🔧 自定义字段数量: {len(custom_fields)}个")
            for i, (key, value) in enumerate(custom_fields.items()):
                if i < 5:  # 只显示前5个
                    print(f"    {key}: {value}")
            if len(custom_fields) > 5:
                print(f"    ... 还有 {len(custom_fields) - 5} 个字段")
        else:
            print("  🔧 自定义字段: 无")

        return mapped_data

    except Exception as e:
        print(f"  ❌ 数据映射失败: {e}")
        return None


def print_analysis_summary(analysis_result, mapped_data):
    """打印分析结果摘要"""
    print("\n📊 AI分析结果摘要:")
    print("=" * 50)

    if analysis_result and analysis_result.success:
        # 商品属性
        product_attrs = getattr(analysis_result, "product_attributes", {})
        if product_attrs:
            print("📦 商品属性:")
            for key, value in product_attrs.items():
                print(f"  {key}: {value}")

        # 销售数据
        sales_data = getattr(analysis_result, "sales_data", {})
        if sales_data:
            print("\n📈 销售数据:")
            for key, value in sales_data.items():
                if key == "review_tags" and isinstance(value, list):
                    print(f"  {key}: {', '.join(value)}")
                else:
                    print(f"  {key}: {value}")

        # 代发数据
        dropship_data = getattr(analysis_result, "dropship_data", {})
        if dropship_data:
            print("\n🚚 代发服务数据:")
            for key, value in dropship_data.items():
                print(f"  {key}: {value}")

        # 验证关键业务字段
        print("\n✅ 关键业务指标验证:")
        if mapped_data:
            annual_sales = sales_data.get("annual_sales", 0)
            pickup_24h = mapped_data.get("pickup_rate_24h", 0)
            distributor_count = mapped_data.get("distributor_count", 0)

            print(f"  年销量: {annual_sales:,} (目标: >10000)")
            print(f"  24h揽收率: {pickup_24h}% (目标: >80%)")
            print(f"  分销商数: {distributor_count} (目标: >100)")

            # 业务评估
            score = 0
            if annual_sales >= 10000:
                score += 1
            if pickup_24h >= 80:
                score += 1
            if distributor_count >= 100:
                score += 1

            print(
                f"\n🎯 供应商评分: {score}/3 ({'优秀' if score >= 2 else '一般' if score == 1 else '需改进'})"
            )
    else:
        print("❌ 无有效分析结果")


def main():
    """主测试函数"""
    print("🚀 AI分析结果界面测试")
    print("=" * 50)

    # 测试AI分析器
    scraped_content, analysis_result = test_ai_analyzer()

    # 测试数据映射器
    mapped_data = test_data_mapper(scraped_content, analysis_result)

    # 打印详细摘要
    print_analysis_summary(analysis_result, mapped_data)

    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    print("\n💡 使用建议:")
    print("1. 启动主程序: python main.py")
    print("2. 打开智能抓取功能")
    print("3. 输入1688商品链接进行测试")
    print("4. 查看'🎯 AI分析结果'标签页")
    print("5. 验证所有字段是否正确提取和显示")


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )
    main()
