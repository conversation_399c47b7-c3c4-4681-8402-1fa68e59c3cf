"""
验证绕过核心模块
基于参考项目的成功方案，专门处理1688等网站的验证码和反爬机制
增强版：包含关键的自动刷新绕过机制
"""

import asyncio
import random
from typing import Optional, Dict, Any
from playwright.async_api import async_playwright, Browser, BrowserContext, Page
import logging


class AuthBypass:
    """验证绕过类 - 基于参考项目的成功实现，增强自动绕过能力"""

    def __init__(self, headless: bool = False):  # 验证时需要显示浏览器
        self.headless = headless
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.playwright = None
        self.logger = logging.getLogger("auth_bypass")

    async def get_page_with_auto_bypass(self, url: str) -> Page:
        """
        获取页面并自动绕过验证 - 这是关键方法！
        基于参考项目的成功策略：通过多次刷新和等待来绕过验证码
        """
        if not self.browser:
            await self._initialize_browser()

        page = await self.context.new_page()

        # 设置额外的页面配置 - 完全复刻参考项目
        await page.set_extra_http_headers(
            {
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "DNT": "1",
            }
        )

        # 👉 关键步骤：自动绕过验证机制
        await self._auto_bypass_verification(page, url)

        return page

    async def get_page_with_auth(self, url: str) -> Page:
        """
        获取已验证的页面 - 保持向后兼容
        """
        return await self.get_page_with_auto_bypass(url)

    async def _auto_bypass_verification(self, page: Page, url: str) -> bool:
        """
        自动绕过验证机制 - 这是参考项目的核心秘密！
        通过连续的刷新和等待策略来绕过验证码页面
        """
        try:
            self.logger.info("🚀 开始自动绕过验证流程...")

            # 第一次访问页面
            self.logger.info("📖 第一次访问页面...")
            response = await page.goto(
                url, wait_until="domcontentloaded", timeout=30000
            )
            await page.wait_for_timeout(3000)  # 等待页面加载

            # 检查是否遇到验证页面
            is_verification_page = await self._detect_verification_page(page)

            if is_verification_page:
                self.logger.info("🔍 检测到验证页面，开始自动绕过...")

                # 👉 关键步骤1：模拟按回车键
                try:
                    self.logger.info("⌨️  模拟按回车键触发页面刷新...")
                    await page.keyboard.press("Enter")
                    await page.wait_for_load_state("domcontentloaded")
                    await page.wait_for_timeout(2000)
                except Exception as e:
                    self.logger.debug(f"回车键操作失败: {e}")

                # 👉 关键步骤2：自动刷新页面（参考项目的核心机制）
                max_refresh_attempts = 5  # 最多尝试5次刷新
                for attempt in range(max_refresh_attempts):
                    try:
                        self.logger.info(
                            f"🔄 执行第{attempt + 1}次自动刷新，绕过验证页面..."
                        )
                        await page.reload(wait_until="domcontentloaded", timeout=30000)
                        await page.wait_for_timeout(
                            3000 + attempt * 1000
                        )  # 递增等待时间

                        # 检查是否已经绕过验证
                        if not await self._detect_verification_page(page):
                            self.logger.info("✅ 自动绕过验证成功！")
                            return True

                        # 添加随机等待，模拟人类行为
                        await page.wait_for_timeout(random.randint(2000, 4000))

                    except Exception as e:
                        self.logger.debug(f"第{attempt + 1}次刷新失败: {e}")
                        continue

                # 👉 关键步骤3：如果刷新失败，尝试重新访问页面
                self.logger.info("🔄 尝试重新访问页面...")
                try:
                    await page.goto(url, wait_until="domcontentloaded", timeout=30000)
                    await page.wait_for_timeout(5000)

                    if not await self._detect_verification_page(page):
                        self.logger.info("✅ 重新访问成功绕过验证！")
                        return True

                except Exception as e:
                    self.logger.debug(f"重新访问失败: {e}")

                # 如果自动绕过失败，提示用户手动处理
                self.logger.warning("⚠️  自动绕过失败，等待手动处理验证...")
                verification_success = await self.wait_for_human_verification(page, url)
                return verification_success
            else:
                self.logger.info("✅ 未检测到验证页面，直接访问成功！")
                return True

        except Exception as e:
            self.logger.error(f"自动绕过验证失败: {e}")
            return False

    async def _detect_verification_page(self, page: Page) -> bool:
        """
        检测是否为验证页面 - 更准确的检测逻辑
        """
        try:
            # 检查页面标题
            title = await page.title()
            if any(
                keyword in title.lower()
                for keyword in ["验证", "验证码", "安全", "captcha", "verification"]
            ):
                return True

            # 检查页面文本内容
            body_text = await page.evaluate("document.body.innerText || ''")
            verification_keywords = [
                "拖动下方滑块完成验证",
                "请拖动滑块验证",
                "滑动验证",
                "安全验证",
                "人机验证",
                "点击验证",
                "验证码",
                "captcha",
                "verification",
                "please verify",
                "security check",
            ]

            for keyword in verification_keywords:
                if keyword in body_text.lower():
                    self.logger.info(f"检测到验证关键词: {keyword}")
                    return True

            # 检查常见的验证码元素
            captcha_selectors = [
                ".captcha",
                ".verify",
                ".verification",
                "#captcha",
                "[class*='captcha']",
                "[id*='captcha']",
                ".slide-verify",
                ".geetest",
                ".nc-lang-cnt",  # 阿里云验证码
                "[class*='slider']",
                "[class*='slide']",
                ".verify-slider",
                ".security-verify",
            ]

            for selector in captcha_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        self.logger.info(f"检测到验证元素: {selector}")
                        return True
                except:
                    continue

            # 检查页面URL是否包含验证相关路径
            current_url = page.url
            if any(
                keyword in current_url.lower()
                for keyword in ["verify", "captcha", "security", "check"]
            ):
                return True

            return False

        except Exception as e:
            self.logger.debug(f"验证页面检测异常: {e}")
            return False

    async def _initialize_browser(self):
        """初始化浏览器 - 使用最强的反检测配置"""
        self.playwright = await async_playwright().start()

        # 完全复刻参考项目的强力反检测启动参数
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,  # 验证时必须显示浏览器
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--no-first-run",
                "--no-zygote",
                "--disable-gpu",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor,TranslateUI",
                "--disable-background-networking",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-field-trial-config",
                "--disable-ipc-flooding-protection",
                "--enable-features=NetworkService,NetworkServiceLogging",
                "--disable-extensions",
                "--disable-plugins",
                # 注意：验证时不能禁用图片，因为验证码就是图片
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            ],
        )

        # 创建增强的上下文 - 完全复刻参考项目
        self.context = await self.browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            viewport={"width": 1920, "height": 1080},
            locale="zh-CN",
            timezone_id="Asia/Shanghai",
            geolocation={"longitude": 116.3974, "latitude": 39.9093},  # 北京坐标
            permissions=["geolocation"],
            extra_http_headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "DNT": "1",
            },
        )

        # 注入完整的反检测脚本 - 完全复刻参考项目
        await self.context.add_init_script(
            """
            // 隐藏webdriver特征
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪造plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 伪造languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            // 覆盖权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
            );
            
            // 伪造chrome属性
            window.chrome = {
                runtime: {},
                csi: function() {},
                loadTimes: function() { return {}; },
                app: {
                    isInstalled: false,
                    InstallState: {
                        DISABLED: 'disabled',
                        INSTALLED: 'installed',
                        NOT_INSTALLED: 'not_installed'
                    },
                    RunningState: {
                        CANNOT_RUN: 'cannot_run',
                        READY_TO_RUN: 'ready_to_run',
                        RUNNING: 'running'
                    }
                }
            };
            
            // 移除selenium标识
            delete navigator.__proto__.webdriver;
            
            // 伪造更多navigator属性
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 8,
            });
            
            // 伪造内存信息
            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => 8,
            });
            
            // 伪造连接信息
            Object.defineProperty(navigator, 'connection', {
                get: () => ({
                    effectiveType: '4g',
                    downlink: 2.0,
                    rtt: 150
                }),
            });
            
            // 覆盖Date.getTimezoneOffset
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {
                return -480; // 北京时间UTC+8
            };
        """
        )

    async def wait_for_human_verification(
        self, page: Page, url: str, max_wait_time: int = 300
    ) -> bool:
        """
        等待人工验证完成 - 这是参考项目的关键策略
        当遇到验证码时，等待用户手动完成验证
        """
        self.logger.info("检测到可能的验证码，等待人工处理...")
        self.logger.info(f"请在浏览器中手动完成验证，最多等待{max_wait_time}秒")

        start_time = asyncio.get_event_loop().time()

        while True:
            current_time = asyncio.get_event_loop().time()
            if current_time - start_time > max_wait_time:
                self.logger.warning("等待验证超时")
                return False

            try:
                # 检查页面是否已经加载完成，没有验证码
                if await self._check_page_ready(page):
                    self.logger.info("验证完成，页面可正常访问")
                    return True

                # 等待一段时间再检查
                await asyncio.sleep(2)

            except Exception as e:
                self.logger.debug(f"验证检查异常: {e}")
                await asyncio.sleep(2)

        return False

    async def _check_page_ready(self, page: Page) -> bool:
        """检查页面是否已准备就绪（没有验证码）"""
        try:
            # 使用更准确的检测方法
            return not await self._detect_verification_page(page)

        except Exception:
            return False

    async def close(self):
        """关闭浏览器资源"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._initialize_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.close()
