# Python_Ecom_Product_Source_Compare v2.3.6

电商商品货源选品对比工具 - 智能图片管理与货源分析系统

> **🤖 重大更新 v2.3.6**: 集成智能抓取功能！支持一键抓取1688、淘宝等电商平台货源信息，AI智能分析商品特性，自动填充货源表单。基于qwen2.5系列模型，本地化AI处理，安全高效。

一个基于PyQt6 + SQLite的轻量级电商产品对比选品工具，专注核心功能，简洁高效。

## 项目特点

- ✅ **简洁架构**：基于PyQt6 + SQLite，避免过度设计
- ✅ **高性能**：轻量级内核，快速响应
- ✅ **易于维护**：清晰的代码结构，最小化依赖
- ✅ **功能完整**：产品管理、货源对比、图片轮播
- ✅ **美观界面**：现代化UI设计，良好用户体验
- ✅ **快速访问**：一键打开货源网站链接
- ✅ **详细信息**：支持商品描述和销量参考
- ✅ **智能计算**：根据货源模式智能计算成本和利润（含运费）
- ✅ **智能图片管理**：支持多选删除、打开目录等便捷功能
- 🆕 **🤖 AI智能抓取**：集成本地AI模型，一键抓取电商平台货源信息
- 🆕 **🎯 智能数据填充**：AI分析商品特性，自动填充货源表单
- 🆕 **🛡️ 本地化AI处理**：基于qwen2.5系列模型，数据不出本地，安全可靠

## 主要功能

### 🔥 核心功能
- **产品管理**：增删改查产品信息
- **商品描述**：支持详细的产品描述信息
- **销量参考**：添加销量参考数据，便于选品决策
- **🚚 运费管理**：支持包邮/不包邮设置，可自定义运费金额
- **货源管理**：多货源价格对比分析
- **智能成本计算**：根据货源模式（批发/代发）智能计算成本（**含运费**）
- **💰 实际收款计算**：基于包邮情况计算真实收款价格（售价+运费）
- **网站链接**：一键打开货源网站链接
- **运费显示**：含运费信息以橘红色高亮显示
- **自定义字段**：支持产品规格、品牌等自定义属性
- **图片管理**：产品图片轮播查看，支持多选删除
- **对比分析**：智能对比多个产品的成本和利润

### 🤖 AI智能抓取功能 (v2.3.6新增)
- **一键智能抓取**：输入1688、淘宝、天猫等电商平台网址，一键抓取商品信息
- **AI商品分析**：使用qwen2.5:14b模型智能分析商品标题、描述、规格参数
- **AI图片识别**：使用qwen2.5vl:latest视觉模型分析商品图片，提取商品特性
- **智能数据映射**：自动将抓取的商品信息映射到货源表单字段
- **自动价格计算**：智能提取批发价格，自动计算代发价格（批发价+20%）
- **实时进度显示**：抓取过程可视化，支持异步处理不阻塞界面
- **一键应用**：抓取完成后一键应用到货源表单，秒级完成货源录入
- **本地AI处理**：所有AI分析都在本地进行，数据安全有保障

### 📊 数据分析
- 最低/最高/平均成本计算（**含运费**）
- 利润和利润率分析（**基于含运费成本**）
- 根据货源模式智能计算（批发模式显示总成本，代发模式只计算单个利润）
- 最优货源推荐（**按含运费成本排序**）
- 统计信息仪表板

### 🎨 用户界面
- 左右分栏布局：产品列表 + 详情/对比
- 实时搜索和多种排序方式
- 响应式设计，支持窗口缩放
- 现代化样式，美观易用

## 快速开始

### 环境要求
- Python 3.8+
- Windows 10/11 (主要测试环境)
- 🆕 **AI功能要求**：ollama + qwen2.5模型（智能抓取功能）

### 安装步骤

1. **克隆或下载项目**
   ```bash
   # 如果有git
   git clone <repository-url>
   
   # 或者直接下载压缩包解压
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **🤖 配置AI环境（可选，用于智能抓取功能）**
   ```bash
   # 运行自动配置脚本
   python setup_scraper.py
   
   # 脚本会自动完成：
   # - 检查Python版本
   # - 安装额外依赖（playwright, fake-useragent等）
   # - 配置playwright浏览器
   # - 下载AI模型（qwen2.5:14b和qwen2.5vl:latest）
   ```

4. **启动应用**
   
   **Windows用户**：
   ```bash
   # 双击运行
   start.bat
   
   # 或者命令行运行
   python main.py
   ```
   
   **其他系统**：
   ```bash
   python main.py
   ```

> **注意**：智能抓取功能需要先配置AI环境，如果不使用该功能可跳过第3步直接启动应用。

## 使用指南

### 1. 添加产品
- 点击左侧"添加产品"按钮
- 填写产品名称、编码、售价
- 添加商品描述和销量参考（可选）
- 添加参考链接，如竞品链接、市场调研网址等（可选）
- 保存后产品出现在列表中

### 2. 管理货源
- 选择产品后，在右侧详情区域点击"添加货源"
- 填写供应商信息、价格、联系方式等
- 选择货源模式（批发模式/代发模式）
- 添加网站链接，支持一键打开浏览器访问
- 含运费信息以橘红色高亮显示
- 系统根据货源模式智能计算成本和利润

### 3. 产品对比
- 在产品列表中选择多个产品（Ctrl+点击）
- 点击"对比选中"按钮
- 查看详细的对比分析表格

### 4. 货源模式说明
- **批发模式**：显示总成本和含运费总成本，适用于需要囤货的业务
- **代发模式**：只计算单个利润，不显示总成本，适用于无库存的代发业务
- **成本计算**：代发模式理论成本为0，除非售价低于成本+运费时显示亏损警示

### 5. 搜索和排序
- 使用搜索框快速查找产品
- 选择不同排序方式查看产品列表
- 支持按价格、利润、创建时间排序

### 6. 🤖 AI智能抓取使用 (v2.3.6新增)
- **准备工作**：首次使用需运行`python setup_scraper.py`配置AI环境
- **开始抓取**：点击"添加货源"，切换到"🤖 智能抓取"选项卡
- **输入网址**：复制1688、淘宝、天猫等商品详情页网址到输入框
- **启动抓取**：点击"开始抓取"按钮，系统自动执行以下步骤：
  1. 网页内容抓取（标题、价格、图片、描述等）
  2. AI智能分析（商品特性、规格参数提取）
  3. 数据智能映射（转换为货源表单格式）
- **预览结果**：在"抓取结果预览"区域查看AI分析的结果
- **一键应用**：确认无误后点击"应用到表单"，自动填充所有货源字段
- **支持平台**：1688、淘宝、天猫、阿里巴巴等主流电商平台

## 🤖 最新版本特性 (v2.3.6) - AI智能抓取重大更新

#### 🚀 核心亮点

- **AI智能抓取**：利用先进的AI模型（如Qwen2.5-14B、Qwen2.5-VL）深度分析商品详情页，自动提取标题、价格、图片、规格、供应商等关键信息。
- **数据清洗与结构化**：自动清理HTML垃圾内容，输出结构化JSON数据，便于分析和存储。
- **高成功率**：结合多重策略和智能优化，确保在复杂网站环境下的高抓取成功率。
- **兼容性广**：支持主流电商平台，并持续更新以适应新的网页结构和反爬机制。
- **模块化设计**：核心功能模块独立，易于维护和扩展。
- **🛡️ 验证码绕过机制**：独创的三重自动绕过策略，有效解决1688等平台的滑动验证码问题，大幅提升抓取自动化程度。

#### 🔍 系统优势
- **网页抓取引擎**：基于Playwright的高性能抓取器，支持动态内容加载
- **AI分析模型**：
  - `qwen2.5:14b`：用于文本分析，提取商品标题、描述、规格参数
  - `qwen2.5vl:latest`：用于视觉分析，识别商品图片特征
- **智能选择器**：针对1688、淘宝、天猫等平台优化的抓取选择器
- **异步处理架构**：UI非阻塞设计，抓取过程实时显示进度
- **容错机制**：完善的错误处理和重试机制，保证抓取稳定性

### 🎨 用户界面增强
- **🤖 智能抓取选项卡**：在货源对话框中新增专门的智能抓取界面
- **实时进度显示**：可视化抓取进度，包括网页抓取、AI分析、数据映射等阶段
- **结果预览面板**：抓取完成后可预览AI分析结果，确认后一键应用
- **平台支持提示**：清晰标注支持的电商平台列表

### 📊 数据智能处理
- **智能价格提取**：AI识别批发价、零售价，自动计算代发价格（批发价+20%）
- **商品特性分析**：智能提取商品规格、材质、尺寸等关键信息
- **图片智能筛选**：自动选择前5张主要商品图片，过滤广告和无关图片
- **描述优化**：AI整理商品描述，生成简洁清晰的产品介绍

### 🔄 数据流程
```
电商网址输入 → 网页内容抓取 → AI智能分析 → 数据格式映射 → 表单自动填充
```

### 📦 新增组件
- **scrapers/web_scraper.py**：核心网页抓取器
- **scrapers/ai_analyzer.py**：AI分析引擎
- **scrapers/data_mapper.py**：数据映射转换器
- **views/dialogs/smart_scraper_tab.py**：智能抓取UI组件
- **setup_scraper.py**：一键环境配置脚本

### 🛠️ 安装配置
```bash
# 一键配置AI环境
python setup_scraper.py

# 自动完成：
# 1. Python版本检查
# 2. 依赖包安装
# 3. Playwright浏览器配置
# 4. AI模型下载
# 5. 功能验证测试
```

## 历史版本特性 (v2.3.2)

### 🔗 商品参考链接功能
- **参考链接字段**：商品支持添加参考链接，可以存储竞品链接、市场调研网址等
- **快捷打开按钮**：在产品详情页面新增"打开链接"按钮，一键用浏览器打开参考链接
- **界面一致性**：参考链接功能与货源链接功能保持一致的操作体验
- **智能显示**：只有填写了有效链接时才显示"打开链接"按钮，保持界面简洁

### 📊 导入导出增强
- **完整数据支持**：导入导出功能完全支持新的参考链接字段
- **向后兼容**：自动升级数据库结构，已有产品参考链接字段默认为空
- **数据完整性**：保证导入导出过程中参考链接信息的完整性

## 历史版本特性 (v2.3.0)

### 🔧 价格管理优化
- **统一价格同步**：实现了基本单价、代发单价、批发单价的自动同步机制
- **智能价格更新**：修改任意一个价格字段，其他价格字段会自动同步更新
- **价格字段重命名**：将"基本单价"重命名为"统一单价"，避免概念混淆
- **修复代发价格归零**：解决了编辑货源时代发价格意外归零的问题

### 🖼️ 图片管理快捷入口
- **主界面快捷按钮**：在货源信息显示区域的价格旁边添加"管理图片"快捷按钮
- **简化操作流程**：无需进入编辑页面即可直接管理货源图片
- **基本信息页面图片管理**：在货源编辑对话框的基本信息页面也增加了图片管理快捷入口

### 📢 用户体验改进
- **减少弹窗干扰**：成功操作时不再弹窗提醒，只在出错时显示错误信息
- **操作反馈优化**：保持静默操作体验，减少不必要的用户打断

## 历史版本特性 (v2.2.5)

### 图片管理系统优化
- **✅ 图片累积添加修复**：修复了添加新图片时会覆盖现有图片的问题，现在支持真正的累积添加模式
- **🔍 智能图片查重**：基于感知哈希算法的图片相似度检测，支持自动删除重复图片
- **🏆 智能保留功能**：可以自动选择最佳图片保留（基于分辨率和文件大小），删除其他重复图片
- **🤖 批量智能处理**：支持一键为所有重复组选择最佳图片保留

### 上个版本特性 (v2.2.4)

### 🎨 图片管理界面全面升级
- **6张图片同时显示**：图片管理器从原来的3张图片改为6张图片同时显示（2行3列），大幅提升图片浏览效率
- **优化图片布局**：使用网格布局替代水平布局，提供更好的视觉效果和空间利用率
- **清晰图片显示**：优化图片缩放比例，保持清晰度的同时适配新布局

### 🖱️ 鼠标滚轮支持
- **流畅滚动浏览**：新增鼠标滚轮滑动支持，无需点击按钮即可翻页
- **自然操作体验**：向上滚动查看上一组图片，向下滚动查看下一组图片
- **直观操作提示**：界面标题增加🖱️图标，明确提示滚轮功能

### 🎯 用户体验优化
- **复选框位置改进**：复选框直接位于图片下方，与图片一一对应，操作更直观
- **清晰功能标识**：实时显示图片编号和主要图片标识（⭐主要）
- **统一操作逻辑**：产品和货源图片管理器保持完全一致的交互体验
- **减少翻页次数**：6张图片同时显示，浏览效率提升100%

### 🔧 技术改进
- **界面布局重构**：容器高度增加到720px，提供更大的显示空间
- **分页逻辑优化**：所有分页算法从每组3张调整为每组6张
- **向前兼容性**：保持数据完整性和所有原有功能

## 历史版本特性 (v2.2.2)

### 🚀 货源模式重构
- **简化模式设置**：移除复杂的混合模式配置，提供更直观的用户体验
- **基本信息集成**：在基本信息标签页中直接选择模式，默认选择代发模式
- **灵活模式选择**：支持批发模式、代发模式或两者都选
- **智能显示**：根据选择的模式动态显示相关字段和计算结果

### 🔧 计算显示优化
- **模式分离显示**：
  - 代发模式：显示单位成本、含运费成本、预计利润
  - 批发模式：显示单位成本、含运费成本、最小订量、预计利润
  - 基本信息：显示产品价格、运费、库存等通用信息
- **界面交互优化**：
  - 简化模式选择操作
  - 优化计算结果展示布局
  - 更直观的数据展示方式

### 🐛 修复关键问题
- **修复货源编辑闪退**：
  - 修复了货源编辑对话框中缺失基本控件导致的闪退问题
  - 添加了完整的异常处理和安全检查机制
  - 优化了信号连接和模式切换逻辑
  - 改进了初始化过程的错误处理

### 🔧 改进优化
- **货源编辑界面**：
  - 添加了缺失的基本控件（价格、数量、最小订量、运费等）
  - 优化了控件的初始化顺序
  - 改进了模式切换时的界面更新逻辑
  - 增强了用户输入验证机制

### 🎨 用户体验提升
- **错误处理优化**：
  - 添加了更友好的错误提示
  - 防止因控件缺失导致的程序崩溃
  - 确保即使部分功能失败，程序仍能继续运行
  - 优化了异常状态下的界面响应

### 🏷️ 标签管理增强
- 右键菜单新增"删除标签"功能，支持快速移除产品标签。
- 智能标签显示，只显示当前产品已关联的标签。
- 标签删除操作显示标签名称，用户体验更友好。

### 📁 智能导入导出系统
- 全新的导入选择界面，自动扫描并显示所有可用的导出版本。
- 显示详细的版本信息：导出时间、产品数量、标签数量。
- 导出版本按时间排序，最新版本显示在前。
- 支持在导入界面直接删除不需要的导出版本。
- 支持手动浏览其他位置的导出文件夹。
- **图片编号智能补位**：优化了新建产品图片时的编号逻辑，现在会智能寻找最小可用的编号进行补位，而不是简单地递增，有效利用编号空缺。

### 📊 完整日志系统
- 程序运行日志自动保存到`data/logs/app.log`文件。
- 日志同时输出到控制台和文件，方便调试。
- 导入过程详细日志记录，便于问题诊断。
- 完整的错误堆栈信息记录。
- 自动日志轮转，管理文件大小。

### 🎨 用户体验优化
- 导入过程显示详细的进度和状态信息。
- 更友好的错误提示和确认机制。
- 删除导出版本需要用户确认，避免误操作。
- 导入导出界面响应更流畅。

### 🔧 技术改进
- 日志配置系统：完整的日志配置管理，支持级别和格式自定义。
- 路径处理优化：改进文件路径处理，提升跨平台兼容性。
- 错误处理增强：更完善的异常处理机制。
- 数据验证：导入前验证数据完整性，避免数据损坏。

## 新版本特性 (v2.1.6)

### 🏷️ 标签系统全面升级
- 新增产品标签功能，支持快捷筛选和分类管理
- 标签快捷区域，点击标签即可筛选对应产品
- 标签管理界面，支持创建、编辑、删除标签
- 产品卡片直接显示标签，便于快速识别
- 右键菜单快速添加标签，提升使用效率

### 📁 完整导出导入功能
- 完整的数据导出功能，包含产品、货源、图片、标签等所有数据
- 智能文件夹结构，数据和图片分离，便于管理
- 自动生成说明文件，方便理解导出内容
- 完整的数据导入功能，支持跨设备数据迁移
- 进度显示，导入导出过程可视化

### 🎨 界面美化优化
- 标签样式全面升级，现代化渐变效果
- 标签位置优化，与货源信息对齐显示
- 暗黑模式下按钮文字显示修复
- 产品列表布局优化，信息显示更加清晰
- 状态栏图标化显示，提升视觉效果

### 🔧 数据处理优化
- 修复货源数量统计问题，确保正确显示
- 优化产品列表加载性能，正确加载关联数据
- 标签筛选功能，支持多标签组合筛选
- 数据结构完善，支持完整的标签关联关系

## 新版本特性 (v2.1.1)

### 🔧 成本计算优化
- 根据货源模式智能调整成本计算逻辑
- 批发模式：正常显示总成本和含运费总成本
- 代发模式：不显示总成本，只计算单个利润
- 代发利润逻辑：理论成本为0，售价过低时显示亏损警示

### 🎨 界面优化
- 代发模式标签使用橘色高亮显示，便于区分
- 利润显示根据模式调整（"代发利润" vs "利润"）
- 亏损提示更加明确

## 新版本特性 (v2.1.0)

### ✨ 增强产品信息
- 新增商品描述字段，支持详细的产品描述
- 新增销量参考字段，便于选品决策

### 🏪 改进货源管理
- 新增"打开网站"按钮，一键访问货源链接
- 含运费信息以橘红色高亮显示

### 🔧 数据库升级
- 自动检测并添加新字段
- 保持向后兼容性

### 🎨 界面优化
- 改进产品详情页面布局
- 优化货源信息显示效果

## 项目结构

```
New/
├── main.py                 # 应用程序入口
├── config.py              # 配置管理
├── requirements.txt       # 依赖包列表
├── start.bat             # Windows启动脚本
├── README.md             # 项目说明
├── CHANGELOG.md          # 版本更新记录
├── 使用指南.md           # 详细使用指南
├── models/               # 数据模型层
│   ├── __init__.py
│   ├── product.py        # 产品模型
│   ├── source.py         # 货源模型
│   ├── custom_field.py   # 自定义字段模型
│   └── database.py       # 数据库管理器
├── views/                # 视图层
│   ├── __init__.py
│   ├── main_window.py    # 主窗口
│   ├── product_list.py   # 产品列表
│   ├── product_detail.py # 产品详情
│   ├── comparison_view.py # 对比视图
│   ├── image_viewer.py   # 图片查看器
│   └── dialogs/          # 对话框
│       ├── __init__.py
│       ├── product_dialog.py
│       ├── source_dialog.py
│       └── custom_field_dialog.py
└── data/                 # 数据存储目录
    ├── database.db       # SQLite数据库
    └── images/           # 图片文件
```

## 技术特点

### 架构设计
- **分层架构**：模型层、视图层、服务层清晰分离
- **直接SQLite操作**：避免ORM复杂性，提升性能
- **信号槽机制**：组件间松耦合通信
- **配置化管理**：统一的配置文件管理

### 性能优化
- **延迟加载**：产品列表不加载关联数据
- **内存高效**：及时释放不用的资源
- **响应式UI**：异步操作，避免界面卡顿
- **搜索优化**：500ms延迟搜索，减少频繁查询

### 数据安全
- **外键约束**：保证数据完整性
- **事务处理**：确保数据一致性
- **错误处理**：完善的异常捕获机制
- **数据备份**：支持数据导入导出

## 开发计划

### ✅ 第一阶段：基础框架
- [x] 项目结构搭建
- [x] 数据模型设计
- [x] 数据库管理
- [x] 主窗口框架

### ✅ 第二阶段：核心功能
- [x] 产品管理（增删改查）
- [x] 基础界面组件
- [x] 搜索和排序功能

### ✅ 第三阶段：高级功能
- [x] 货源管理完善
- [x] 图片轮播组件
- [x] 自定义字段管理
- [x] 数据导入导出

### ✅ 第四阶段：优化完善
- [x] 性能优化
- [x] 界面美化
- [x] 错误处理完善
- [x] 功能测试

### ✅ 第五阶段：增强功能 (v2.1.0)
- [x] 商品描述字段
- [x] 销量参考字段
- [x] 货源网站链接
- [x] 运费显示优化
- [x] 数据库自动升级

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
```bash
# 克隆项目
git clone <repository-url>

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 运行应用
python main.py
```

## 问题反馈

如果遇到问题，请提供以下信息：
- 操作系统版本
- Python版本
- 错误信息截图
- 复现步骤

## 更新日志

### v2.1.1 (2025-01-XX)
- 🔧 根据货源模式优化成本计算逻辑
- 🎨 代发模式界面标识优化
- 💡 代发利润计算精准化
- ⚠️ 亏损风险提示增强

### v2.1.0 (2025-01-XX)
- ✨ 新增商品描述字段
- ✨ 新增销量参考字段
- ✨ 新增货源网站链接功能
- 🎨 优化含运费显示效果
- 🔧 数据库自动升级功能
- 📚 完善文档和使用指南

### v2.1.8 (2025-07-06)
- 优化暗黑主题下标题文字样式
- 移除产品详情页面标题的边框样式
- 统一所有标签的样式管理

### v2.0.7 (2024-12-28)
- 🐛 修复暗黑主题显示问题
- 🎨 优化产品列表样式
- 📚 更新文档

更多版本信息请查看 [CHANGELOG.md](CHANGELOG.md) 

## 最新版本特性 (v2.2.8)

### 🔧 图片管理功能修复
- **✅ 图片显示修复**：修复了ImageManagementDialog中缺少image_frames属性导致的显示问题
- **🎨 图片管理体验优化**：改进了图片管理对话框的初始化流程
- **🔍 图片显示稳定性**：增强了图片显示的稳定性和可靠性
- **🚀 性能优化**：优化了图片加载和显示的性能

### 🔧 图片管理功能全面优化
- **📁 新增打开目录按钮**：图片管理界面添加打开目录按钮，一键访问产品图片文件夹
- **📂 默认下载目录**：添加图片时自动打开用户下载文件夹，提升便捷性
- **🗑️ 多选删除功能**：
  - 左侧列表支持Ctrl/Shift多选进行批量删除
  - 右侧每张图片添加删除复选框，可直接勾选删除
  - "删除选中"按钮支持批量操作
  - 新增"全选"和"取消选择"快捷按钮

### 🎨 界面布局优化
- **复选框布局改进**：
  - 主要图片复选框(⭐主要)移至左侧
  - 删除复选框(🗑️删除)移至右侧  
  - 单行布局，界面更紧凑美观
  - 增加图片框架高度，避免遮挡问题
- **美化复选框样式**：
  - 16x16px大复选框，圆角边框设计
  - 悬停效果优化，交互更友好
  - 颜色主题统一：绿色(主要)、红色(删除)

### 🧮 利润计算修复（重要）
- **⚠️ 重要修复**：修复了产品列表中利润计算不包含运费的问题
- **成本计算优化**：
  - 所有成本计算现在均包含运费（商品价格 + 运费）
  - 利润 = 售价 - 最低成本(含运费)
  - 最优货源按含运费成本排序
  - 确保利润计算的准确性

### 🔄 状态显示增强
- **统一图标系统**：⭐(主要图片)、🗑️(待删除)
- **完整路径提示**：工具提示显示完整图片路径
- **状态同步**：左侧列表与右侧复选框状态完全同步 