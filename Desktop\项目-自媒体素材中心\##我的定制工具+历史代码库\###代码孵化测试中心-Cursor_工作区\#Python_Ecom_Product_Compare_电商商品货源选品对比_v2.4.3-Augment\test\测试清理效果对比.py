#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清理效果对比测试
比较外部数据清理脚本和项目内置清理脚本的效果
"""

import json
import sys
from pathlib import Path
import time

# 外部数据清理脚本
from 数据清理脚本 import DataCleaner

# 项目内置清理脚本
sys.path.append("scrapers")
from enhanced_data_cleaner import EnhancedDataCleaner
from enhanced_content_cleaner import EnhancedContentCleaner


def test_external_cleaner(input_file: str):
    """测试外部数据清理脚本"""
    print("=" * 60)
    print("🔧 外部数据清理脚本测试")
    print("=" * 60)

    cleaner = DataCleaner()

    start_time = time.time()
    cleaned_data = cleaner.clean_json_data(input_file)
    end_time = time.time()

    print(f"⏱️ 清理耗时: {end_time - start_time:.2f}秒")

    # 保存结果
    output_file = "外部脚本清理结果.json"
    cleaner.save_cleaned_data(cleaned_data, output_file)

    return cleaned_data, output_file


def test_builtin_cleaner(input_file: str):
    """测试项目内置清理脚本"""
    print("=" * 60)
    print("🏠 项目内置数据清理脚本测试")
    print("=" * 60)
    
    # 读取原始数据
    with open(input_file, "r", encoding="utf-8") as f:
        original_data = json.load(f)
    
    print(f"📖 读取文件: {input_file}")
    print(f"📊 原始数据大小: {len(json.dumps(original_data, ensure_ascii=False))} 字符")
    
    # 使用增强数据清理器
    cleaner = EnhancedDataCleaner()
    
    start_time = time.time()
    
    # 创建数据副本
    data = original_data.copy()
    
    # 清理主要描述内容
    if "description" in data and data["description"]:
        print("🧹 清理description字段...")
        try:
            cleaned_description = cleaner.clean_html_content(data["description"])
            data["description"] = cleaned_description
        except Exception as e:
            print(f"❌ 清理description字段失败: {e}")
            # 尝试使用其他清理方法
            from enhanced_content_cleaner import EnhancedContentCleaner
            alt_cleaner = EnhancedContentCleaner()
            data["description"] = alt_cleaner.clean_html_content(data["description"])
    
    # 清理content字段
    if "content" in data and data["content"]:
        print("🧹 清理content字段...")
        try:
            cleaned_content = cleaner._clean_text_content(data["content"])
            data["content"] = cleaned_content
        except Exception as e:
            print(f"❌ 清理content字段失败: {e}")
    
    # 清理其他字段
    cleaned_data = data

    end_time = time.time()

    cleaned_size = len(json.dumps(cleaned_data, ensure_ascii=False))
    original_size = len(json.dumps(data, ensure_ascii=False))
    compression_ratio = (1 - cleaned_size / original_size) * 100

    print(f"🧹 清理后数据大小: {cleaned_size} 字符")
    print(f"📈 压缩率: {compression_ratio:.1f}%")
    print(f"⏱️ 清理耗时: {end_time - start_time:.2f}秒")

    # 保存结果
    output_file = "内置脚本清理结果.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(cleaned_data, f, ensure_ascii=False, indent=2)

    print(f"💾 保存清理后的数据到: {output_file}")
    print("✅ 数据清理完成！")

    return cleaned_data, output_file


def compare_results(external_data, builtin_data, expected_content):
    """比较两种清理方法的结果"""
    print("\n" + "=" * 60)
    print("📊 清理效果对比分析")
    print("=" * 60)

    # 提取清理后的description字段进行对比
    external_desc = external_data.get("description", "")
    builtin_desc = builtin_data.get("description", "")

    print(f"📏 外部脚本清理结果长度: {len(external_desc)} 字符")
    print(f"📏 内置脚本清理结果长度: {len(builtin_desc)} 字符")

    # 检查重要信息保留情况
    important_keywords = [
        "代发参谋",
        "24小时揽收率",
        "48小时揽收率",
        "82.00%",
        "98.00%",
        "100以内",
        "400+",
        "0.00%",
        "2024年9月",
        "10以内",
        "48小时发货",
        "承诺48小时发货",
    ]

    print("\n🔍 重要业务信息保留情况:")
    print("-" * 40)

    external_preserved = 0
    builtin_preserved = 0

    for keyword in important_keywords:
        external_has = keyword in external_desc
        builtin_has = keyword in builtin_desc
        expected_has = keyword in expected_content

        status_external = "✅" if external_has else "❌"
        status_builtin = "✅" if builtin_has else "❌"
        status_expected = "🎯" if expected_has else "⭕"

        print(
            f"{keyword:20} | 外部:{status_external} | 内置:{status_builtin} | 期望:{status_expected}"
        )

        if external_has:
            external_preserved += 1
        if builtin_has:
            builtin_preserved += 1

    print("-" * 40)
    print(f"外部脚本保留重要信息: {external_preserved}/{len(important_keywords)}")
    print(f"内置脚本保留重要信息: {builtin_preserved}/{len(important_keywords)}")

    # 分析结果
    print("\n📈 综合评分:")
    print("-" * 30)

    external_score = (external_preserved / len(important_keywords)) * 100
    builtin_score = (builtin_preserved / len(important_keywords)) * 100

    print(f"外部脚本评分: {external_score:.1f}%")
    print(f"内置脚本评分: {builtin_score:.1f}%")

    if external_score > builtin_score:
        print("🏆 外部数据清理脚本效果更好！")
    elif builtin_score > external_score:
        print("🏆 项目内置清理脚本效果更好！")
    else:
        print("🤝 两种脚本效果相当")

    # 显示清理后的内容预览
    print("\n📝 清理结果预览:")
    print("-" * 40)
    print("外部脚本结果:")
    print(external_desc[:500] + "..." if len(external_desc) > 500 else external_desc)
    print("\n" + "-" * 40)
    print("内置脚本结果:")
    print(builtin_desc[:500] + "..." if len(builtin_desc) > 500 else builtin_desc)


def main():
    """主函数"""
    input_file = "原始数据.txt"

    if not Path(input_file).exists():
        print(f"❌ 错误: 文件 '{input_file}' 不存在")
        sys.exit(1)

    print("🚀 开始数据清理效果对比测试...")
    print(f"📁 测试文件: {input_file}")
    print()

    # 期望的清理结果（用户提供的样例）
    expected_content = """
    代发参谋 24小时揽收率 82.00% 24小时揽收率/spanspan style="font-size: 14px; line-height: color: rgb(51, 51, 51);"82.00% 48小时揽收率 98.00% 48小时揽收率/spanspan 51);"98.00% 月代发订单数 100以 月代发订单数/spanspan 14 下游铺货数 下游铺货数/spanspan 商品退货率 0.00% 商品退货率/spanspan 51);"0.00% 商品发布时间 2024年9月 商品发布时间/spanspan 51);"2024年9月 可分销商品数 10以 可分销商品数/spanspan 月上新商品数 月上新商品数/spanspan 分销商数 400+ 分销商数/spanspan 100以内 10以内 承诺48小时发货
    """

    try:
        # 测试外部清理脚本
        external_data, external_file = test_external_cleaner(input_file)

        # 测试项目内置清理脚本
        builtin_data, builtin_file = test_builtin_cleaner(input_file)

        # 比较结果
        compare_results(external_data, builtin_data, expected_content)

        print(f"\n📁 清理结果文件:")
        print(f"  - 外部脚本: {external_file}")
        print(f"  - 内置脚本: {builtin_file}")

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
