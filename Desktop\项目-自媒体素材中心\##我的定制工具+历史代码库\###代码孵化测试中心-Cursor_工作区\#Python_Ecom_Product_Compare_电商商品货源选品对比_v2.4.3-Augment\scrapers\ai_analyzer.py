"""
AI智能分析器
使用qwen2.5系列模型对抓取内容进行智能分析
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from dataclasses import dataclass
import aiohttp
import logging
import base64

# 导入超级增强数据清洗器
try:
    from .ultra_enhanced_cleaner import UltraEnhancedDataCleaner
except ImportError:
    try:
        from scrapers.ultra_enhanced_cleaner import UltraEnhancedDataCleaner
    except ImportError:
        # 如果找不到，使用原有的清理器
        try:
            from .enhanced_data_cleaner import (
                EnhancedDataCleaner as UltraEnhancedDataCleaner,
            )
        except ImportError:
            try:
                from scrapers.enhanced_data_cleaner import (
                    EnhancedDataCleaner as UltraEnhancedDataCleaner,
                )
            except ImportError:
                from enhanced_data_cleaner import (
                    EnhancedDataCleaner as UltraEnhancedDataCleaner,
                )

# 导入配置
try:
    from config import AI_CONFIG
except ImportError:
    # 如果无法导入配置，使用默认配置（纯文本模式）
    AI_CONFIG = {
        "large_context_mode": {
            "base_url": "http://localhost:11434",
            "text_model": "qwen2.5:14b",
            "timeout": 300,
            "temperature": 0.7,
            "max_tokens": 120000,
            "max_chunk_size": 30000,
            "enable_intelligent_summary": True,
            "enable_vision_analysis": False,
        },
        "current_mode": "large_context_mode",
    }


@dataclass
class AnalysisResult:
    """AI分析结果数据模型"""

    title: str = ""
    cleaned_content: str = ""
    price_info: Dict[str, Any] = None
    specifications: Dict[str, Any] = None
    supplier_info: Dict[str, str] = None
    product_features: List[str] = None
    categories: List[str] = None
    image_descriptions: List[str] = None

    # 新增字段：1688专用数据（基于200+保护规则）
    product_attributes: Dict[str, Any] = None  # 商品属性详情
    packaging_info: Dict[str, Any] = None  # 物流包装信息
    sales_data: Dict[str, Any] = None  # 销售数据
    dropship_data: Dict[str, Any] = None  # 代发服务数据
    service_info: Dict[str, Any] = None  # 服务保障信息
    variant_info: Dict[str, Any] = None  # 规格变体信息
    media_resources: Dict[str, Any] = None  # 图片视频资源

    success: bool = False
    error_message: str = ""
    processing_time: float = 0.0

    def __post_init__(self):
        if self.price_info is None:
            self.price_info = {}
        if self.specifications is None:
            self.specifications = {}
        if self.supplier_info is None:
            self.supplier_info = {}
        if self.product_features is None:
            self.product_features = []
        if self.categories is None:
            self.categories = []
        if self.image_descriptions is None:
            self.image_descriptions = []
        # 新增字段初始化（基于200+保护规则）
        if self.product_attributes is None:
            self.product_attributes = {}
        if self.packaging_info is None:
            self.packaging_info = {}
        if self.sales_data is None:
            self.sales_data = {}
        if self.dropship_data is None:
            self.dropship_data = {}
        if self.service_info is None:
            self.service_info = {}
        if self.variant_info is None:
            self.variant_info = {}
        if self.media_resources is None:
            self.media_resources = {}


class AIAnalyzer:
    """AI智能分析器"""

    def __init__(self, ollama_config: dict = None, analysis_mode: str = None):
        self.logger = logging.getLogger(__name__)

        # 初始化超级增强数据清洗器（支持90%+垃圾内容清理并保护200+业务指标）
        self.content_cleaner = UltraEnhancedDataCleaner()

        # 确定使用的分析模式
        if analysis_mode:
            self.current_mode = analysis_mode
        else:
            self.current_mode = AI_CONFIG.get("current_mode", "large_context_mode")

        # 从配置中获取对应模式的设置
        if ollama_config:
            self.ollama_config = ollama_config
        else:
            mode_config = AI_CONFIG.get(
                self.current_mode, AI_CONFIG["large_context_mode"]
            )
            self.ollama_config = mode_config.copy()

        # 记录当前使用的配置
        self.logger.info(f"AI分析器初始化 - 模式: {self.current_mode}")
        self.logger.info(
            f"最大tokens: {self.ollama_config.get('max_tokens', 'unknown')}"
        )
        self.logger.info(
            f"分块大小: {self.ollama_config.get('max_chunk_size', 'unknown')}"
        )
        self.logger.info(
            f"智能总结: {self.ollama_config.get('enable_intelligent_summary', False)}"
        )
        self.logger.info(f"内容清洗: 已启用超级增强清理器（200+保护规则）")

    def get_current_mode_info(self) -> dict:
        """获取当前分析模式信息"""
        return {
            "mode": self.current_mode,
            "config": self.ollama_config,
            "description": AI_CONFIG.get(self.current_mode, {}).get(
                "description", "未知模式"
            ),
        }

    def switch_mode(self, new_mode: str) -> bool:
        """切换分析模式"""
        if new_mode in AI_CONFIG:
            self.current_mode = new_mode
            self.ollama_config = AI_CONFIG[new_mode].copy()
            self.logger.info(f"已切换到分析模式: {new_mode}")
            return True
        else:
            self.logger.warning(f"未知的分析模式: {new_mode}")
            return False

    async def analyze_scraped_content(self, scraped_content) -> AnalysisResult:
        """分析抓取的内容"""
        start_time = datetime.now()
        result = AnalysisResult()

        try:
            self.logger.info(f"开始AI分析: {scraped_content.url}")

            # 1. 内容预处理和清洗
            self.logger.info("步骤1: 开始内容清洗预处理...")

            # 检查是否已有清洗后的内容（来自UI的预处理）
            if (
                hasattr(scraped_content, "cleaned_content")
                and scraped_content.cleaned_content
            ):
                cleaned_content = scraped_content.cleaned_content
                self.logger.info("使用UI预处理的清洗内容")
                self.logger.info(f"🔍 AI收到的清洗内容长度: {len(cleaned_content)}字符")
                self.logger.info(
                    f"🔍 AI收到的内容前100字符: {cleaned_content[:100]}..."
                )

                # 从已有的清洗统计中获取信息
                if hasattr(scraped_content, "cleaning_stats"):
                    cleaning_stats = scraped_content.cleaning_stats
                else:
                    # 如果没有统计信息，计算基本信息 - 修复：使用完整HTML内容
                    original_content = getattr(
                        scraped_content, "original_content", scraped_content.description
                    )
                    cleaning_stats = {
                        "original_length": len(original_content),
                        "cleaned_length": len(cleaned_content),
                        "compression_ratio": (
                            (1 - len(cleaned_content) / len(original_content))
                            if len(original_content) > 0
                            else 0
                        ),
                        "chinese_chars": len(
                            [c for c in cleaned_content if "\u4e00" <= c <= "\u9fff"]
                        ),
                        "english_words": len(cleaned_content.split()),
                        "numbers": len([c for c in cleaned_content if c.isdigit()]),
                    }
            else:
                # 进行内容清洗 - 修复：使用完整HTML内容
                original_content = getattr(scraped_content, "description", "")

                # 使用正确的方法清洗HTML内容
                cleaned_content = self.content_cleaner.clean_html_content(
                    original_content
                )

                # 获取清洗统计信息
                cleaning_stats = self.content_cleaner.get_cleaning_stats(
                    original_content, cleaned_content
                )

                # 保存清洗结果到分析结果中
                result.cleaned_content = cleaned_content

            self.logger.info(
                f"内容清洗完成 - 原始长度: {cleaning_stats.get('original_length', 0)}"
            )
            self.logger.info(f"清洗后长度: {cleaning_stats.get('cleaned_length', 0)}")

            # 压缩率可能是0-1的小数或0-100的百分比，需要兼容处理
            compression_ratio = cleaning_stats.get("compression_ratio", 0)
            if compression_ratio <= 1:
                compression_display = compression_ratio * 100
            else:
                compression_display = compression_ratio
            self.logger.info(f"压缩率: {compression_display:.1f}%")

            self.logger.info(
                f"中文字符: {cleaning_stats.get('chinese_chars', 0)}, 英文单词: {cleaning_stats.get('english_words', 0)}, 数字: {cleaning_stats.get('numbers', 0)}"
            )

            # 2. 检测是否为1688网站
            is_1688 = self._is_1688_content(scraped_content)

            # 3. 选择分析策略（使用清洗后的内容）
            self.logger.info("步骤2: 开始AI智能分析...")
            self.logger.info(f"🚀 传递给AI的内容长度: {len(cleaned_content)}字符")
            self.logger.info(f"🚀 传递给AI的内容预览: {cleaned_content[:200]}...")

            # 添加关键调试信息
            print(f"🔍 Debug: 即将调用AI分析")
            print(f"🔍 Debug: is_1688 = {is_1688}")
            print(f"🔍 Debug: 标题 = {scraped_content.title}")
            print(f"🔍 Debug: 清洗内容长度 = {len(cleaned_content)}")
            print(f"🔍 Debug: 清洗内容是否为空: {not cleaned_content.strip()}")

            if is_1688:
                # 使用专门的1688分析逻辑，传入清洗后的内容
                text_analysis = await self._analyze_1688_content(
                    scraped_content.title,
                    cleaned_content,  # 使用清洗后的内容
                    scraped_content.description,
                )
            else:
                # 使用通用分析逻辑，传入清洗后的内容
                text_analysis = await self._analyze_text_content(
                    scraped_content.title,
                    cleaned_content,  # 使用清洗后的内容
                    scraped_content.description,
                )

            # 立即检查AI分析结果
            print(f"🔍 Debug: AI分析返回结果类型: {type(text_analysis)}")
            print(f"🔍 Debug: text_analysis是否为None: {text_analysis is None}")
            if text_analysis:
                print(f"🔍 Debug: text_analysis键: {list(text_analysis.keys())}")
            else:
                print(f"🔍 Debug: text_analysis为None，需要检查AI模型连接和响应")

            if text_analysis:
                result.title = text_analysis.get("title", scraped_content.title)
                result.cleaned_content = text_analysis.get("cleaned_content", "")
                result.price_info = text_analysis.get("price_info", {})
                result.specifications = text_analysis.get("specifications", {})
                result.supplier_info = text_analysis.get("supplier_info", {})
                result.product_features = text_analysis.get("features", [])
                result.categories = text_analysis.get("categories", [])

                # 新增字段赋值
                result.product_attributes = text_analysis.get("product_attributes", {})
                result.packaging_info = text_analysis.get("packaging_info", {})
                result.sales_data = text_analysis.get("sales_data", {})
                result.dropship_data = text_analysis.get("dropship_data", {})
                result.service_info = text_analysis.get("service_info", {})
                result.variant_info = text_analysis.get("variant_info", {})

                # 添加调试信息
                print(f"🔍 Debug: text_analysis成功，包含{len(text_analysis)}个字段")
                print(f"🔍 Debug: price_info = {result.price_info}")
                print(f"🔍 Debug: product_attributes = {result.product_attributes}")
                print(f"🔍 Debug: sales_data = {result.sales_data}")
                print(f"🔍 Debug: dropship_data = {result.dropship_data}")
            else:
                # AI分析失败，记录错误并设置基本信息
                self.logger.error("AI文本分析返回None，分析失败")
                print(f"🔍 Debug: text_analysis为None，AI分析失败")
                result.success = False
                result.error_message = (
                    "AI分析返回空结果，可能是模型连接问题或内容格式问题"
                )
                result.title = scraped_content.title  # 至少保留原始标题
                return result

            # 4. 图片分析（根据配置决定是否启用）
            self.logger.info("步骤3: 图片内容分析...")
            enable_vision = self.ollama_config.get("enable_vision_analysis", False)
            if enable_vision and scraped_content.images:
                self.logger.info(
                    f"启用图片分析，发现 {len(scraped_content.images)} 张图片"
                )
                image_analysis = await self._analyze_images(
                    scraped_content.images[:5]
                )  # 限制分析前5张图片
                if image_analysis:
                    result.image_descriptions = image_analysis.get("descriptions", [])
                    self.logger.info(
                        f"图片分析完成，提取到 {len(result.image_descriptions)} 个图片描述"
                    )
                else:
                    self.logger.warning("图片分析失败")
            elif scraped_content.images:
                self.logger.info(
                    f"跳过图片分析（配置禁用），发现 {len(scraped_content.images)} 张图片"
                )
            else:
                self.logger.info("无图片需要分析")

            result.success = True
            self.logger.info("🎉 AI分析完成！数据提取成功")

        except Exception as e:
            self.logger.error(f"AI分析失败: {e}")
            result.success = False
            result.error_message = str(e)

        finally:
            result.processing_time = (datetime.now() - start_time).total_seconds()

        return result

    def _is_1688_content(self, scraped_content) -> bool:
        """检测是否为1688内容"""
        indicators = ["1688", "alibaba", "阿里巴巴", "批发", "代发", "起批量", "揽收率"]
        combined_text = (
            scraped_content.url
            + " "
            + scraped_content.title
            + " "
            + scraped_content.content
        ).lower()
        return (
            sum(1 for indicator in indicators if indicator.lower() in combined_text)
            >= 2
        )

    async def _analyze_1688_content(
        self, title: str, content: str, html_content: str
    ) -> Optional[Dict[str, Any]]:
        """使用qwen2.5:14b分析1688内容（优化版本，支持长文本切割分析）"""

        # 实现多轮切割分析以处理长文本
        try:
            # 检查内容长度，如果超过限制则进行切割分析
            max_chunk_size = 200000  # 利用大上下文能力，每块可以处理20万tokens

            if len(content) <= max_chunk_size:
                # 内容较短，直接分析
                return await self._analyze_single_chunk(title, content, "complete")
            else:
                # 内容较长，进行切割分析
                self.logger.info(f"内容长度 {len(content)} 超过限制，启动多轮分析")
                return await self._analyze_long_content_chunks(title, content)

        except Exception as e:
            self.logger.error(f"1688内容分析失败: {e}")
            return None

    async def _analyze_long_content_chunks(
        self, title: str, content: str
    ) -> Optional[Dict[str, Any]]:
        """对长内容进行切割并多轮分析"""
        # 从配置中获取分块大小
        max_chunk_size = self.ollama_config.get("max_chunk_size", 200000)
        chunks = []

        # 智能切割：尽量按段落或句子切割
        paragraphs = content.split("\n")
        current_chunk = ""

        for paragraph in paragraphs:
            # 如果当前块加上新段落不超过限制，继续添加
            if len(current_chunk + paragraph) <= max_chunk_size:
                current_chunk += paragraph + "\n"
            else:
                # 保存当前块并开始新块
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                current_chunk = paragraph + "\n"

        # 添加最后一块
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        self.logger.info(f"内容分割为 {len(chunks)} 个片段进行分析")

        # 分析每个片段
        chunk_results = []
        for i, chunk in enumerate(chunks):
            chunk_type = (
                "beginning" if i == 0 else "middle" if i < len(chunks) - 1 else "end"
            )
            result = await self._analyze_single_chunk(
                title, chunk, chunk_type, chunk_index=i + 1, total_chunks=len(chunks)
            )
            if result:
                chunk_results.append(result)

        # 合并分析结果
        if chunk_results:
            merged_result = await self._merge_chunk_results(chunk_results, title)
            if merged_result:
                # 根据配置决定是否使用智能最终总结
                enable_summary = self.ollama_config.get(
                    "enable_intelligent_summary", True
                )
                if enable_summary:
                    self.logger.info("启用智能最终总结")
                    final_result = await self._intelligent_final_summary(
                        merged_result, chunk_results, title, content
                    )
                    return final_result if final_result else merged_result
                else:
                    self.logger.info("跳过智能最终总结（根据配置）")
                    return merged_result
            return merged_result
        else:
            return None

    async def _intelligent_final_summary(
        self, merged_result: dict, chunk_results: list, title: str, full_content: str
    ) -> Optional[Dict[str, Any]]:
        """利用大上下文进行智能最终总结和数据完善"""
        try:
            # 构建包含所有信息的综合分析prompt
            chunk_summaries = []
            for i, result in enumerate(chunk_results):
                chunk_summaries.append(
                    f"=== 分段 {i+1} 分析结果 ===\n{json.dumps(result, ensure_ascii=False, indent=2)}"
                )

            # 限制full_content长度以确保在token限制内
            content_sample = (
                full_content[:100000] if len(full_content) > 100000 else full_content
            )

            prompt = f"""你是专业的1688商品数据分析专家。现在需要基于所有分段分析结果进行最终的智能总结和数据完善。

【商品基本信息】
标题: {title}
内容摘要: {content_sample}

【各分段分析结果】
{chr(10).join(chunk_summaries)}

【当前合并结果】
{json.dumps(merged_result, ensure_ascii=False, indent=2)}

【任务要求】
1. 综合所有分段信息，生成最准确、最完整的商品数据
2. 消除重复信息，修正数据冲突
3. 补充缺失的关联信息（如通过其他字段推断）
4. 验证数据的逻辑一致性（如价格、重量、尺寸的合理性）
5. 优化数据格式和精度
6. 确保所有数字字段都是准确的数值

【数据优化重点】
- 价格信息：确保价格区间、批发价、起订量的逻辑正确
- 重量数据：统一单位，确保净重 < 毛重
- 销售数据：验证年销量、评价数、已售数量的合理性
- 代发数据：确保揽收率、订单数、分销商数的一致性
- 规格信息：整合颜色、款式等变体信息

【特殊处理规则】
1. 数字提取规范：10万+ → 100000，400+ → 400，30万+ → 300000
2. 百分比规范：83.00% → 83.0，95% → 95.0
3. 重量单位：统一使用克(g)表示，1kg = 1000g
4. 空值处理：缺失数据用空字符串""或0表示
5. 评价标签：保持原格式"标签(数量)"

返回最终优化的完整JSON结果（严格按照原有结构）：
{{
    "title": "最终优化的产品标题",
    "cleaned_content": "综合整理的产品描述",
    "price_info": {{ ... }},
    "specifications": {{ ... }},
    "product_attributes": {{ ... }},
    "packaging_info": {{ ... }},
    "sales_data": {{ ... }},
    "dropship_data": {{ ... }},
    "supplier_info": {{ ... }},
    "service_info": {{ ... }},
    "variant_info": {{ ... }},
    "features": [...],
    "categories": [...]
}}

只返回JSON，不要其他文字。
"""

            self.logger.info("开始智能最终总结分析...")
            response = await self._ollama_request(
                prompt, model=self.ollama_config["text_model"]
            )

            if response:
                json_str = self._extract_json_from_response(response)
                if json_str:
                    final_result = json.loads(json_str)
                    self.logger.info("智能最终总结完成，数据质量得到提升")
                    return final_result

        except Exception as e:
            self.logger.warning(f"智能最终总结失败，使用合并结果: {e}")

        return None

    async def _analyze_single_chunk(
        self,
        title: str,
        content: str,
        chunk_type: str,
        chunk_index: int = 1,
        total_chunks: int = 1,
    ) -> Optional[Dict[str, Any]]:
        """分析单个内容片段"""

        # 根据片段类型调整分析重点
        if chunk_type == "beginning":
            focus = "重点提取：基本商品信息、价格信息、商品属性"
        elif chunk_type == "middle":
            focus = "重点提取：详细规格、包装信息、服务数据"
        elif chunk_type == "end":
            focus = "重点提取：供应商信息、服务保障、规格变体"
        else:  # complete
            focus = "提取所有可用信息"

        prompt = f"""你是专业的1688商品信息分析师。请分析以下1688商品页面内容并提取关键信息。

【分析说明】
- 这是第 {chunk_index}/{total_chunks} 个内容片段
- {focus}
- 如果信息不完整，返回空字符串或空数组，后续会合并结果

【商品信息】
标题: {title}
内容片段: {content}

请提取以下完整信息（基于200+保护规则）：

1. 基本商品信息（商品标题、URL、ID、核心功能描述等）
2. 价格信息（单价如￥2.80起、批发价、价格区间、起订量≥1件、会员价折扣等）
3. 商品属性详情（材质如PC+硅胶、重量如57g/75g、品牌、产地、进口状态等）
4. 物流包装信息（净重、毛重、外箱尺寸如59*34*34.5、装箱数如200PCS、总重量16.2kg等）
5. 销售数据（年销量如10万+、评价数量如30+、已售数量如3267件、评价星级4.5星、评价标签等）
6. 代发服务数据（24小时揽收率如82.00%、48小时揽收率如98.00%、月订单数如100以内、分销商数如400+、退货率0.00%等）
7. 供应商信息（公司名称如温州辛宠宠物用品有限公司、经营年限1年、详细地址、发货地浙江温州等）
8. 服务保障（发货承诺如48小时发货、承诺48小时发货、7天无理由退货、支持快递面单等）
9. 规格变体（总规格数量、颜色如红色/绿色/蓝色等、款式如蜻蜓带绳款/毛绒款等、唤醒模式等）
10. 图片视频资源（主图链接、详情图、视频封面等）

返回格式（严格按照以下JSON格式）：
{{
    "title": "清洁后的产品标题",
    "cleaned_content": "整理后的产品描述",
    "price_info": {{
        "unit_price": "单价",
        "wholesale_price": "批发价", 
        "price_range": "价格区间",
        "min_order": "最小起订量",
        "price_tiers": "阶梯价格"
    }},
    "specifications": {{
        "material": "材质",
        "size": "尺寸", 
        "weight": "重量",
        "color": "颜色选项"
    }},
    "product_attributes": {{
        "material": "材质详情",
        "product_category": "产品类别",
        "product_code": "货号",
        "net_weight": "净重（数字，克）",
        "gross_weight": "毛重（数字，克）", 
        "brand": "品牌",
        "origin": "产地",
        "is_import": "是否进口",
        "is_patent": "是否专利货源",
        "is_gift": "是否属于礼品"
    }},
    "packaging_info": {{
        "box_dimensions": "外箱尺寸",
        "box_quantity": "装箱数（数字）",
        "box_weight": "箱子重量（数字，kg）"
    }},
    "sales_data": {{
        "annual_sales": "年销量（提取数字，如10万+提取为100000）",
        "review_count": "评价数量（数字）",
        "sold_quantity": "已售数量（数字）",
        "review_tags": ["评价标签数组，如价格很便宜(6)"]
    }},
    "dropship_data": {{
        "pickup_rate_24h": "24小时揽收率（数字，百分比）",
        "pickup_rate_48h": "48小时揽收率（数字，百分比）",
        "monthly_orders": "月代发订单数（数字）",
        "downstream_stores": "下游铺货数（数字）",
        "distributor_count": "分销商数（数字，如400+提取为400）",
        "return_rate": "退货率（数字，百分比）",
        "listing_date": "商品发布时间"
    }},
    "supplier_info": {{
        "company": "公司名称",
        "business_years": "经营年限（数字）",
        "full_address": "完整地址",
        "shipping_location": "发货地",
        "location": "所在地区"
    }},
    "service_info": {{
        "shipping_promise": "发货承诺时间",
        "service_guarantees": ["服务保障列表"],
        "supported_couriers": "支持快递数量或类型"
    }},
    "variant_info": {{
        "total_variants": "总规格数量（数字）",
        "color_variants": ["颜色规格列表"],
        "variant_types": ["款式类型列表，如蜻蜓带绳款、毛绒款"],
        "special_modes": ["特殊模式，如唤醒模式、非唤醒模式"]
    }},
    "media_resources": {{
        "main_images": ["主图链接列表"],
        "detail_images": ["详情图链接列表"],
        "video_url": "产品视频链接",
        "video_cover": "视频封面链接"
    }},
    "features": ["产品特性列表，如耐咬、自嗨解闷神器、带绳"],
    "categories": ["产品分类，如宠物用品、啃咬玩具、猫咪玩具球"]
}}

重要提取规则（基于200+保护模式）：
1. 数字提取：10万+ → 100000，400+ → 400，30+ → 30，3267 → 3267
2. 百分比：82.00% → 82.0，98.00% → 98.0，0.00% → 0.0
3. 重量单位：统一转换为克(g)或千克(kg)，57g → 57，75g → 75，16.2kg → 16.2
4. 价格提取：￥2.80起 → 2.80，保留货币符号和"起"
5. 尺寸格式：59*34*34.5保持原格式，装箱数200PCS → 200
6. 公司信息：完整保留"温州辛宠宠物用品有限公司"
7. 地址信息：完整保留"中国 浙江 温州 瑞安市南滨街道林北村九弄9号"
8. 评价标签：保留完整格式，如"价格很便宜(6)"、"客服很热情(5)"
9. 物流承诺：重点识别"48小时发货"、"承诺48小时发货"等表述
10. 规格颜色：保留完整描述，如"带绳款猫球红色【非唤醒模式】"
11. 材质信息：PC+硅胶等保持原格式
12. 图片链接：提取所有https://cbu01.alicdn.com开头的图片链接
13. 商品ID：840138940155等数字ID完整保留
14. 空值：如果信息不存在，返回空字符串""或空数组[]，数字返回0
15. 片段分析：只提取当前片段中的信息，缺失的字段留空

只返回JSON，不要其他文字。
"""

        try:
            response = await self._ollama_request(
                prompt, model=self.ollama_config["text_model"]
            )

            # 添加调试信息
            print(f"🔍 Debug: Ollama原始响应长度: {len(response) if response else 0}")
            if response:
                print(f"🔍 Debug: Ollama原始响应前1000字符:")
                print(f"{response[:1000]}")
            else:
                print(f"🔍 Debug: Ollama响应为空")

            if response:
                # 尝试解析JSON
                json_str = self._extract_json_from_response(response)
                print(
                    f"🔍 Debug: 提取的JSON字符串长度: {len(json_str) if json_str else 0}"
                )
                if json_str:
                    print(f"🔍 Debug: 提取的JSON前500字符:")
                    print(f"{json_str[:500]}")
                    try:
                        result = json.loads(json_str)
                        print(f"🔍 Debug: JSON解析成功，包含 {len(result)} 个字段")
                        return result
                    except json.JSONDecodeError as json_error:
                        print(f"🔍 Debug: JSON解析失败: {json_error}")
                        print(f"🔍 Debug: 尝试解析的JSON: {json_str}")
                        self.logger.error(f"JSON解析失败: {json_error}")
                        return None
                else:
                    print(f"🔍 Debug: JSON提取失败")
            else:
                print(f"🔍 Debug: Ollama响应为空，可能是连接问题")
        except Exception as e:
            self.logger.error(f"片段 {chunk_index} 分析失败: {e}")
            print(f"🔍 Debug: 分析异常: {e}")

        return None

    async def _merge_chunk_results(
        self, chunk_results: list, title: str
    ) -> Optional[Dict[str, Any]]:
        """合并多个片段的分析结果"""
        try:
            self.logger.info(f"开始合并 {len(chunk_results)} 个片段结果")

            # 初始化合并结果（基于200+保护规则）
            merged_result = {
                "title": title,
                "cleaned_content": "",
                "price_info": {},
                "specifications": {},
                "product_attributes": {},
                "packaging_info": {},
                "sales_data": {},
                "dropship_data": {},
                "supplier_info": {},
                "service_info": {},
                "variant_info": {},
                "media_resources": {},
                "features": [],
                "categories": [],
            }

            # 合并策略：非空值优先，数组合并，对象合并
            for result in chunk_results:
                if not result:
                    continue

                # 合并标题（选择最清洁的）
                if result.get("title") and len(result["title"]) > len(
                    merged_result["title"]
                ):
                    merged_result["title"] = result["title"]

                # 合并描述内容
                if result.get("cleaned_content"):
                    if merged_result["cleaned_content"]:
                        merged_result["cleaned_content"] += (
                            "\n" + result["cleaned_content"]
                        )
                    else:
                        merged_result["cleaned_content"] = result["cleaned_content"]

                # 合并字典类型字段（基于200+保护规则）
                dict_fields = [
                    "price_info",
                    "specifications",
                    "product_attributes",
                    "packaging_info",
                    "sales_data",
                    "dropship_data",
                    "supplier_info",
                    "service_info",
                    "variant_info",
                    "media_resources",
                ]

                for field in dict_fields:
                    if result.get(field) and isinstance(result[field], dict):
                        for key, value in result[field].items():
                            if value and str(value).strip():  # 只合并非空值
                                # 对于数字字段，取最大值（通常更准确）
                                if (
                                    field in ["sales_data", "dropship_data"]
                                    and isinstance(
                                        merged_result[field].get(key), (int, float)
                                    )
                                    and isinstance(value, (int, float))
                                ):
                                    merged_result[field][key] = max(
                                        merged_result[field][key], value
                                    )
                                else:
                                    merged_result[field][key] = value

                # 合并数组类型字段
                array_fields = ["features", "categories"]
                for field in array_fields:
                    if result.get(field) and isinstance(result[field], list):
                        for item in result[field]:
                            if item and item not in merged_result[field]:
                                merged_result[field].append(item)

            # 使用AI进行最终优化合并
            final_result = await self._ai_optimize_merged_result(merged_result)

            self.logger.info("片段结果合并完成")
            return final_result if final_result else merged_result

        except Exception as e:
            self.logger.error(f"合并片段结果失败: {e}")
            return chunk_results[0] if chunk_results else None

    async def _ai_optimize_merged_result(
        self, merged_result: dict
    ) -> Optional[Dict[str, Any]]:
        """使用AI优化合并后的结果"""
        try:
            # 构建优化提示
            prompt = f"""请优化以下1688商品分析结果，去除重复信息，规范数据格式：

原始合并结果：
{json.dumps(merged_result, ensure_ascii=False, indent=2)}

优化要求：
1. 去除重复和冗余信息
2. 统一数据格式（数字、单位等）
3. 补充缺失的关联信息
4. 保持JSON结构完整
5. 确保数据的一致性和准确性

返回优化后的完整JSON结果，保持原有结构：
"""

            response = await self._ollama_request(
                prompt, model=self.ollama_config["text_model"]
            )
            if response:
                json_str = self._extract_json_from_response(response)
                if json_str:
                    optimized = json.loads(json_str)
                    self.logger.info("AI优化合并结果成功")
                    return optimized
        except Exception as e:
            self.logger.warning(f"AI优化合并结果失败，使用原始合并: {e}")

        return None

    async def _analyze_text_content(
        self, title: str, content: str, html_content: str
    ) -> Optional[Dict[str, Any]]:
        """使用qwen2.5:14b分析文本内容（通用版本，保持向后兼容）"""

        # 构建分析prompt
        prompt = f"""
请分析以下电商产品信息，提取关键数据并以JSON格式返回：

标题: {title}
内容: {content[:2000]}  # 限制长度

请提取以下信息：
1. 清洁后的产品标题
2. 价格信息（包括单价、批发价、价格区间等）
3. 产品规格参数
4. 供应商信息
5. 产品特性和卖点
6. 产品分类

返回格式（严格按照以下JSON格式）：
{{
    "title": "清洁后的产品标题",
    "cleaned_content": "整理后的产品描述",
    "price_info": {{
        "unit_price": "单价",
        "wholesale_price": "批发价",
        "price_range": "价格区间",
        "min_order": "最小起订量"
    }},
    "specifications": {{
        "material": "材质",
        "size": "尺寸",
        "color": "颜色",
        "weight": "重量"
    }},
    "supplier_info": {{
        "company": "公司名称",
        "location": "所在地区",
        "contact": "联系方式"
    }},
    "features": ["特性1", "特性2", "特性3"],
    "categories": ["分类1", "分类2"]
}}

只返回JSON，不要其他文字。
"""

        try:
            response = await self._ollama_request(
                prompt, model=self.ollama_config["text_model"]
            )
            if response:
                # 尝试解析JSON
                json_str = self._extract_json_from_response(response)
                if json_str:
                    return json.loads(json_str)
        except Exception as e:
            self.logger.error(f"文本分析失败: {e}")

        return None

    async def _analyze_images(self, image_urls: List[str]) -> Optional[Dict[str, Any]]:
        """使用视觉模型分析图片（如果配置了视觉模型）"""

        try:
            # 检查是否配置了视觉模型
            vision_model = self.ollama_config.get("vision_model")
            if not vision_model:
                self.logger.info("未配置视觉模型，跳过图片分析")
                return None

            descriptions = []

            # 分析每张图片
            for i, image_url in enumerate(image_urls[:3]):  # 限制分析前3张
                prompt = f"""
请分析这张电商产品图片，描述以下内容：
1. 产品外观和特征
2. 颜色和材质
3. 尺寸和规格（如果可见）
4. 包装或展示方式
5. 质量和工艺细节

请用简洁的中文描述，重点突出产品特点。
"""

                # 构建视觉分析请求
                vision_prompt = {
                    "model": vision_model,
                    "prompt": prompt,
                    "images": [image_url],
                }

                response = await self._ollama_vision_request(vision_prompt)
                if response:
                    descriptions.append(f"图片{i+1}: {response}")

            return {"descriptions": descriptions}

        except Exception as e:
            self.logger.error(f"图片分析失败: {e}")
            return None

    async def _ollama_request(self, prompt: str, model: str = None) -> Optional[str]:
        """发送Ollama文本分析请求"""

        if not model:
            model = self.ollama_config["text_model"]

        request_data = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": self.ollama_config["temperature"],
                "num_predict": self.ollama_config["max_tokens"],
            },
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_config['base_url']}/api/generate",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=self.ollama_config["timeout"]),
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("response", "")
                    else:
                        self.logger.error(f"Ollama请求失败: {response.status}")
                        return None
        except Exception as e:
            self.logger.error(f"Ollama请求异常: {e}")
            return None

    async def _ollama_vision_request(
        self, request_data: Dict[str, Any]
    ) -> Optional[str]:
        """发送Ollama视觉分析请求"""

        try:
            # 设置stream为False以获取完整响应
            request_data["stream"] = False

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_config['base_url']}/api/generate",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=self.ollama_config["timeout"]),
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("response", "")
                    else:
                        self.logger.error(f"Ollama视觉请求失败: {response.status}")
                        return None
        except Exception as e:
            self.logger.error(f"Ollama视觉请求异常: {e}")
            return None

    def _extract_json_from_response(self, response: str) -> Optional[str]:
        """从AI响应中提取JSON"""
        try:
            # 寻找JSON代码块
            json_pattern = r"```json\s*(.*?)\s*```"
            match = re.search(json_pattern, response, re.DOTALL)
            if match:
                return match.group(1).strip()

            # 寻找大括号包围的JSON
            brace_pattern = r"\{.*\}"
            match = re.search(brace_pattern, response, re.DOTALL)
            if match:
                return match.group(0).strip()

            # 如果没有找到，尝试整个响应
            if response.strip().startswith("{") and response.strip().endswith("}"):
                return response.strip()

            return None
        except Exception as e:
            self.logger.error(f"JSON提取失败: {e}")
            return None

    async def test_connection(self) -> bool:
        """测试Ollama连接"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.ollama_config['base_url']}/api/tags",
                    timeout=aiohttp.ClientTimeout(total=5),
                ) as response:
                    if response.status == 200:
                        models_data = await response.json()
                        available_models = [
                            model["name"] for model in models_data.get("models", [])
                        ]

                        # 检查指定模型是否可用
                        text_available = (
                            self.ollama_config["text_model"] in available_models
                        )

                        self.logger.info(f"Ollama连接成功")
                        self.logger.info(
                            f"文本模型 {self.ollama_config['text_model']} 可用: {text_available}"
                        )

                        # 检查视觉模型（如果配置了）
                        vision_model = self.ollama_config.get("vision_model")
                        if vision_model:
                            vision_available = vision_model in available_models
                            self.logger.info(
                                f"视觉模型 {vision_model} 可用: {vision_available}"
                            )
                            return text_available and vision_available
                        else:
                            self.logger.info("未配置视觉模型，仅使用文本模型")
                            return text_available
                    return False
        except Exception as e:
            self.logger.error(f"Ollama连接测试失败: {e}")
            return False
