import sqlite3
import json
import os
from pathlib import Path


def sync_images_with_filesystem():
    """同步数据库与文件系统中的图片"""

    conn = sqlite3.connect("data/database.db")
    cursor = conn.cursor()

    # 查询所有有图片的产品
    cursor.execute(
        'SELECT id, name, images FROM products WHERE images IS NOT NULL AND images != "[]"'
    )
    products = cursor.fetchall()

    print("🔄 开始同步图片数据...")
    print("=" * 60)

    for product_id, name, images_json in products:
        if images_json:
            db_images = json.loads(images_json)
            product_dir = Path(f"data/images/products/product_{product_id}")

            print(f"\n📦 产品 {product_id} ({name}):")
            print(f"  数据库记录: {len(db_images)} 张图片")

            if product_dir.exists():
                # 获取文件系统中的实际文件
                actual_files = []
                for file_path in product_dir.glob("*"):
                    if file_path.is_file():
                        # 构造标准的相对路径
                        relative_path = str(file_path.relative_to(Path.cwd())).replace(
                            "\\", "/"
                        )
                        actual_files.append(relative_path)

                print(f"  文件系统中: {len(actual_files)} 个文件")

                # 找出孤儿文件（文件存在但数据库中没有记录）
                orphan_files = [f for f in actual_files if f not in db_images]

                # 找出丢失文件（数据库有记录但文件不存在）
                missing_files = [f for f in db_images if f not in actual_files]

                if orphan_files:
                    print(f"  🗑️  孤儿文件 ({len(orphan_files)} 个):")
                    for file in orphan_files:
                        print(f"    - {Path(file).name}")

                    # 询问用户是否删除孤儿文件
                    choice = input(f"    删除这些孤儿文件吗？ [y/N]: ").lower().strip()
                    if choice == "y":
                        deleted_count = 0
                        for file_path in orphan_files:
                            try:
                                Path(file_path).unlink()
                                print(f"    ✅ 已删除: {Path(file_path).name}")
                                deleted_count += 1
                            except Exception as e:
                                print(f"    ❌ 删除失败: {Path(file_path).name} - {e}")
                        print(
                            f"    📊 删除统计: {deleted_count}/{len(orphan_files)} 个文件"
                        )

                if missing_files:
                    print(f"  ❓ 丢失文件 ({len(missing_files)} 个):")
                    for file in missing_files:
                        print(f"    - {Path(file).name}")

                    # 从数据库中清理丢失的文件记录
                    valid_images = [f for f in db_images if f not in missing_files]
                    if len(valid_images) != len(db_images):
                        cursor.execute(
                            "UPDATE products SET images = ? WHERE id = ?",
                            (json.dumps(valid_images), product_id),
                        )
                        print(
                            f"    🔧 已从数据库中清理 {len(missing_files)} 个丢失文件的记录"
                        )

                # 最终统计
                final_db_count = len([f for f in db_images if f not in missing_files])
                final_fs_count = len(
                    [
                        f
                        for f in actual_files
                        if f not in orphan_files
                        or input("删除孤儿文件吗？ [y/N]: ").lower().strip() != "y"
                    ]
                )

                if not orphan_files and not missing_files:
                    print(f"  ✅ 数据同步正常")
                else:
                    print(f"  🔧 同步完成")

            else:
                print(f"  ❌ 产品目录不存在: {product_dir}")

    conn.commit()
    conn.close()
    print("\n" + "=" * 60)
    print("🎉 图片数据同步完成！")


if __name__ == "__main__":
    sync_images_with_filesystem()
