#!/usr/bin/env python3
"""
Qwen2.5-14B 大上下文优化功能测试脚本
用于验证AI分析器的优化功能是否正常工作
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from scrapers.ai_analyzer import AIAnalyzer
from config import AI_CONFIG
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 模拟的长内容数据（1688商品页面示例）
SAMPLE_LONG_CONTENT = (
    """
深圳市科技有限公司 - 主营：电子产品批发

【商品标题】高品质无线蓝牙耳机 真无线立体声运动耳机 工厂直销批发

【基本信息】
货号：TWS-2024-001
品牌：SoundMax
材质：ABS塑料+硅胶
颜色：黑色、白色、蓝色、红色
重量：单只耳机4.5g，充电盒35g
尺寸：耳机尺寸25*20*15mm，充电盒尺寸70*35*25mm

【价格信息】
单价：￥28.50元/副
1-99副：￥28.50元/副
100-499副：￥26.80元/副  
500-999副：￥24.90元/副
1000副以上：￥22.50元/副
起订量：1副
支持混批：是

【技术规格】
蓝牙版本：5.2
传输距离：10-15米
频响范围：20Hz-20KHz
电池容量：耳机40mAh*2，充电盒500mAh
续航时间：单次使用6小时，配合充电盒使用30小时
充电时间：耳机1小时，充电盒2小时
防水等级：IPX5

【包装信息】
包装方式：彩盒包装
外箱尺寸：52*38*35cm
装箱数量：50副/箱
毛重：约12kg/箱
净重：约10kg/箱

【销售数据】
年销量：15万+副
评价数量：2856条
好评率：98.5%
已售数量：156,789副
评价标签：音质很好(356)、续航持久(289)、连接稳定(234)、性价比高(198)

【代发服务】
24小时揽收率：92.5%
48小时揽收率：98.8%
月代发订单数：8,500+单
下游铺货数：450+个店铺
分销商数：300+家
平均退货率：1.2%

【供应商信息】
公司名称：深圳市创新科技有限公司
经营年限：8年
公司地址：广东省深圳市宝安区西乡街道固戍社区红湾商务中心A座1208
发货地：广东深圳
主营产品：蓝牙耳机、数据线、充电器、手机配件

【服务保障】
发货承诺：72小时内发货
质保政策：12个月免费质保
售后服务：7天无理由退换
支持快递：顺丰、申通、圆通、中通、韵达等
支付方式：支付宝、微信、银行转账、信用支付

【产品特性】
1. 真无线设计，彻底摆脱线材束缚
2. 蓝牙5.2技术，连接更稳定，延迟更低
3. HiFi音质，三频均衡，音质清晰
4. 智能降噪，过滤环境噪音
5. 人体工学设计，佩戴舒适不易掉落
6. IPX5防水，运动出汗不担心
7. 30小时续航，满足全天候使用需求
8. 一键操作，支持语音助手唤醒

【适用场景】
运动健身、通勤路上、办公学习、游戏娱乐、商务通话

【质量认证】
CE认证、FCC认证、ROHS认证、3C认证

【工厂实力】
生产基地面积：5000平方米
员工人数：200+人
生产线数量：6条
日产能：10,000副
质检流程：5道质检工序
合作品牌：华为、小米、OPPO等知名品牌OEM代工

【物流信息】
库存数量：50,000+副
备货周期：现货供应
发货时效：当天下单，次日发货
配送范围：全国包邮（偏远地区除外）
物流合作：与多家知名物流公司建立合作关系

这是一个完整的1688商品页面内容示例，包含了商品的详细信息、价格体系、技术参数、包装物流、销售数据、代发服务、供应商信息等全方位的数据。内容较长，正好可以测试大上下文分析功能。
"""
    * 5
)  # 复制5次以创建更长的内容


class MockScrapedContent:
    """模拟抓取的内容对象"""

    def __init__(self, url, title, content, description="", images=None):
        self.url = url
        self.title = title
        self.content = content
        self.description = description
        self.images = images or []


async def test_ai_analyzer_modes():
    """测试AI分析器的不同模式"""
    logger.info("=" * 60)
    logger.info("🚀 开始测试Qwen2.5-14B大上下文优化功能")
    logger.info("=" * 60)

    # 显示配置信息
    logger.info(f"📋 可用的分析模式：")
    for mode_name, mode_config in AI_CONFIG.items():
        if isinstance(mode_config, dict) and "description" in mode_config:
            logger.info(f"  - {mode_name}: {mode_config['description']}")

    # 创建模拟内容
    mock_content = MockScrapedContent(
        url="https://detail.1688.com/offer/123456789.html",
        title="高品质无线蓝牙耳机测试商品",
        content=SAMPLE_LONG_CONTENT,
        description="这是一个用于测试大上下文分析功能的模拟商品",
    )

    logger.info(f"📝 测试内容长度：{len(mock_content.content)} 字符")

    # 测试不同模式
    test_modes = ["large_context_mode", "efficient_mode", "legacy_mode"]

    for mode in test_modes:
        if mode not in AI_CONFIG:
            logger.warning(f"⚠️  模式 {mode} 不存在，跳过测试")
            continue

        logger.info(f"\n🧪 测试模式：{mode}")
        logger.info("-" * 40)

        try:
            # 创建分析器
            analyzer = AIAnalyzer(analysis_mode=mode)

            # 显示模式信息
            mode_info = analyzer.get_current_mode_info()
            logger.info(f"📊 模式信息：{mode_info['description']}")
            logger.info(
                f"🔧 最大tokens：{analyzer.ollama_config.get('max_tokens', 'unknown')}"
            )
            logger.info(
                f"📦 分块大小：{analyzer.ollama_config.get('max_chunk_size', 'unknown')}"
            )
            logger.info(
                f"🧠 智能总结：{analyzer.ollama_config.get('enable_intelligent_summary', False)}"
            )

            # 测试连接
            logger.info("🔌 测试Ollama连接...")
            connection_ok = await analyzer.test_connection()
            if not connection_ok:
                logger.error(
                    "❌ Ollama连接失败，请确保Ollama服务正在运行且已加载qwen2.5:14b模型"
                )
                continue
            else:
                logger.info("✅ Ollama连接成功")

            # 执行分析
            logger.info("🔍 开始分析...")
            result = await analyzer.analyze_scraped_content(mock_content)

            if result.success:
                logger.info(f"✅ 分析成功！处理时间：{result.processing_time:.2f}秒")
                logger.info(f"📄 提取的标题：{result.title[:50]}...")
                logger.info(f"💰 价格信息：{len(result.price_info)} 项")
                logger.info(f"📋 规格信息：{len(result.specifications)} 项")
                logger.info(f"🏪 供应商信息：{len(result.supplier_info)} 项")
                logger.info(f"🚚 代发数据：{len(result.dropship_data)} 项")
            else:
                logger.error(f"❌ 分析失败：{result.error_message}")

        except Exception as e:
            logger.error(f"❌ 测试模式 {mode} 时出现异常：{e}")

    logger.info("\n" + "=" * 60)
    logger.info("🎉 测试完成！")
    logger.info("=" * 60)


async def test_mode_switching():
    """测试模式切换功能"""
    logger.info("\n🔄 测试模式切换功能...")

    analyzer = AIAnalyzer()

    # 显示初始模式
    initial_info = analyzer.get_current_mode_info()
    logger.info(f"📍 初始模式：{initial_info['mode']}")

    # 测试切换到不同模式
    for mode in ["efficient_mode", "legacy_mode", "large_context_mode"]:
        if analyzer.switch_mode(mode):
            current_info = analyzer.get_current_mode_info()
            logger.info(f"✅ 成功切换到：{current_info['mode']}")
        else:
            logger.error(f"❌ 切换到 {mode} 失败")

    # 测试无效模式
    if not analyzer.switch_mode("invalid_mode"):
        logger.info("✅ 正确拒绝了无效模式")


def main():
    """主函数"""
    try:
        # 运行异步测试
        asyncio.run(test_ai_analyzer_modes())
        asyncio.run(test_mode_switching())

        print("\n🎯 测试建议：")
        print("1. 如果所有模式都测试成功，推荐使用 large_context_mode")
        print("2. 如果遇到性能问题，可以切换到 efficient_mode")
        print("3. 查看日志了解详细的分析过程")
        print("4. 根据实际内容长度选择合适的模式")

    except KeyboardInterrupt:
        logger.info("⏹️  测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误：{e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
