"""
配置管理模块
管理应用程序的配置信息
"""

import os
from pathlib import Path

# 应用程序基本信息
APP_NAME = "Python_Ecom_Product_Source_Compare"
APP_VERSION = "2.3.6"
APP_AUTHOR = "AI Assistant"
APP_DESCRIPTION = "电商商品货源选品对比工具"

# 文件路径配置
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
DATABASE_PATH = DATA_DIR / "database.db"
IMAGES_DIR = DATA_DIR / "images"
PRODUCTS_IMAGES_DIR = IMAGES_DIR / "products"
RESOURCES_DIR = BASE_DIR / "resources"
ICONS_DIR = RESOURCES_DIR / "icons"
STYLES_DIR = RESOURCES_DIR / "styles"

# 数据库配置
DATABASE_CONFIG = {
    "path": str(DATABASE_PATH),
    "timeout": 30,
    "check_same_thread": False,
}

# 界面配置
UI_CONFIG = {
    "window_title": APP_NAME,
    "default_width": 1600,
    "default_height": 1250,
    "min_width": 800,
    "min_height": 600,
    "splitter_ratio": [400, 1200],  # 左右分栏比例
    "image_size": (500, 400),  # 图片显示尺寸
    "thumbnail_size": (180, 180),  # 缩略图尺寸
    "carousel_interval": 3000,  # 轮播间隔(毫秒)
}

# 样式配置
STYLE_CONFIG = {
    "theme": "dark",  # light/dark
    "font_family": "Microsoft YaHei UI",
    "font_size": 10,
    "primary_color": "#2196f3",
    "secondary_color": "#424242",
    "success_color": "#4caf50",
    "warning_color": "#ff9800",
    "error_color": "#f44336",
}

# 暗黑模式样式配置
DARK_STYLE_CONFIG = {
    "theme": "dark",
    "font_family": "Microsoft YaHei UI",
    "font_size": 10,
    "primary_color": "#2196f3",
    "secondary_color": "#616161",
    "success_color": "#4caf50",
    "warning_color": "#ff9800",
    "error_color": "#f44336",
    "background_color": "#1e1e1e",
    "surface_color": "#2d2d2d",
    "text_color": "#ffffff",
    "secondary_text_color": "#b0b0b0",
    "border_color": "#404040",
    "hover_color": "#404040",
    "pressed_color": "#505050",
}

# 图片配置
IMAGE_CONFIG = {
    "supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".gif", ".webp"],
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "quality": 85,  # 图片质量
    "auto_resize": True,
    "max_width": 1920,
    "max_height": 1080,
}

# 数据导入导出配置
EXPORT_CONFIG = {
    "formats": ["csv", "xlsx", "json"],
    "default_format": "xlsx",
    "encoding": "utf-8",
    "include_images": True,
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    "file_path": DATA_DIR / "logs" / "app.log",
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "date_format": "%Y-%m-%d %H:%M:%S",
    "console_output": True,
    "file_output": True,
}

# AI分析器配置
AI_CONFIG = {
    # 大上下文模式 - 充分利用Qwen2.5:14b标准版的128K token能力
    "large_context_mode": {
        "base_url": "http://localhost:11434",
        "text_model": "qwen2.5:14b",  # 标准版纯文本模型，支持128K tokens
        "timeout": 300,  # 5分钟超时，适应长文本处理
        "temperature": 0.7,
        "max_tokens": 900000,  # 使用Qwen2.5的大上下文能力（1M tokens）
        "max_chunk_size": 200000,  # 每个分块20万tokens，充分利用上下文
        "enable_intelligent_summary": True,  # 启用智能最终总结
        "enable_vision_analysis": True,  # 启用视觉分析以获取更多信息
        "description": "大上下文模式 - 最佳分析质量，适合复杂长内容，包含图片分析",
    },
    # 高效模式 - 平衡性能和质量
    "efficient_mode": {
        "base_url": "http://localhost:11434",
        "text_model": "qwen2.5:14b",
        "timeout": 180,  # 3分钟超时
        "temperature": 0.7,
        "max_tokens": 100000,  # 10万tokens，平衡速度质量
        "max_chunk_size": 50000,  # 每个分块5万tokens
        "enable_intelligent_summary": True,
        "enable_vision_analysis": True,  # 启用视觉分析以获取更多信息
        "description": "高效模式 - 平衡处理速度和分析质量，包含图片分析",
    },
    # 传统模式 - 向后兼容
    "legacy_mode": {
        "base_url": "http://localhost:11434",
        "text_model": "qwen2.5:14b",
        "timeout": 60,
        "temperature": 0.7,
        "max_tokens": 8000,  # 传统小token限制
        "max_chunk_size": 6000,  # 传统小分块
        "enable_intelligent_summary": False,
        "enable_vision_analysis": False,  # 禁用视觉分析
        "description": "传统模式 - 向后兼容，适合简单内容，纯文本分析",
    },
    # 当前使用的模式
    "current_mode": "large_context_mode",  # 默认使用大上下文模式
}

# AI分析策略配置
AI_ANALYSIS_STRATEGY = {
    "分段分析流程": {
        "1": "内容抓取完成",
        "2": "智能分块（基于段落和语义）",
        "3": "并行分析各分块（保留完整上下文）",
        "4": "合并分析结果",
        "5": "智能最终总结（利用大上下文整合所有信息）",
    },
    "优化重点": [
        "充分利用Qwen2.5-14B的1M token上下文长度",
        "减少分块数量，提高单次分析质量",
        "智能合并和最终总结",
        "数据一致性验证和优化",
    ],
}


def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        DATA_DIR,
        IMAGES_DIR,
        PRODUCTS_IMAGES_DIR,
        RESOURCES_DIR,
        ICONS_DIR,
        STYLES_DIR,
    ]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)


def get_database_url():
    """获取数据库连接URL"""
    ensure_directories()
    return f"sqlite:///{DATABASE_PATH}"


def get_image_path(image_name):
    """获取图片完整路径"""
    ensure_directories()
    return IMAGES_DIR / image_name


def get_config(key, default=None):
    """获取配置值"""
    keys = key.split(".")
    config = globals()

    for k in keys:
        if k in config:
            config = config[k]
        else:
            return default

    return config


def get_current_theme():
    """获取当前主题"""
    return STYLE_CONFIG.get("theme", "light")


def set_theme(theme):
    """设置主题"""
    STYLE_CONFIG["theme"] = theme


def get_theme_config():
    """获取当前主题配置"""
    theme = get_current_theme()
    if theme == "dark":
        return DARK_STYLE_CONFIG
    return STYLE_CONFIG


# 初始化配置
ensure_directories()
