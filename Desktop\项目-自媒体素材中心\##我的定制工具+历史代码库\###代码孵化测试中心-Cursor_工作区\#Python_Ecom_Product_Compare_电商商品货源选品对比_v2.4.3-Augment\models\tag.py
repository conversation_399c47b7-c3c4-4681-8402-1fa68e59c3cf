"""
标签模型 - 用于产品标签分类
"""

from datetime import datetime
from typing import Optional, List
from dataclasses import dataclass, field


@dataclass
class Tag:
    """标签模型"""

    # 基本属性
    id: int = 0
    name: str = ""
    color: str = "#1976d2"  # 默认蓝色
    description: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    # 使用统计
    usage_count: int = 0  # 使用该标签的产品数量

    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "color": self.color,
            "description": self.description,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "usage_count": self.usage_count,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "Tag":
        """从字典创建标签"""
        return cls(
            id=data.get("id", 0),
            name=data.get("name", ""),
            color=data.get("color", "#1976d2"),
            description=data.get("description", ""),
            created_at=datetime.fromisoformat(
                data.get("created_at", datetime.now().isoformat())
            ),
            updated_at=datetime.fromisoformat(
                data.get("updated_at", datetime.now().isoformat())
            ),
            usage_count=data.get("usage_count", 0),
        )

    def __str__(self) -> str:
        return f"Tag(id={self.id}, name='{self.name}', color='{self.color}')"

    def __repr__(self) -> str:
        return self.__str__()


@dataclass
class ProductTag:
    """产品标签关联模型"""

    # 关联属性
    product_id: int = 0
    tag_id: int = 0
    created_at: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "product_id": self.product_id,
            "tag_id": self.tag_id,
            "created_at": self.created_at.isoformat(),
        }

    @classmethod
    def from_dict(cls, data: dict) -> "ProductTag":
        """从字典创建产品标签关联"""
        return cls(
            product_id=data.get("product_id", 0),
            tag_id=data.get("tag_id", 0),
            created_at=datetime.fromisoformat(
                data.get("created_at", datetime.now().isoformat())
            ),
        )

    def __str__(self) -> str:
        return f"ProductTag(product_id={self.product_id}, tag_id={self.tag_id})"

    def __repr__(self) -> str:
        return self.__str__()
