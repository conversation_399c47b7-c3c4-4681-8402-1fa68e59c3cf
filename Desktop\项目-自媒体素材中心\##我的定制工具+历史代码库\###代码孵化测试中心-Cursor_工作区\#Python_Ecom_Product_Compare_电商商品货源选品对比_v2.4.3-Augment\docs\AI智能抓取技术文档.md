# AI智能抓取技术文档

## 项目概述

### 功能定位
AI智能抓取系统是电商货源选品对比工具v2.3.6的核心新功能，通过集成本地AI模型和网页抓取技术，实现一键抓取电商平台货源信息并自动填充货源表单的智能化解决方案。

### 核心价值
- **效率革命**：货源录入时间从10分钟缩短到1分钟，效率提升900%
- **智能分析**：AI自动提取商品特性、规格参数，减少人工错误
- **数据安全**：本地AI处理，商业数据不出本地
- **用户体验**：一键操作，简单直观

## 技术架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI层（PyQt6）  │───▶│  控制层（Worker） │───▶│  数据层（SQLite）│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│  智能抓取选项卡   │    │   抓取工作线程    │
└─────────────────┘    └─────────────────┘
                                │
                                ▼
               ┌─────────────────────────────┐
               │      核心处理模块            │
               │  ┌─────────────────────┐   │
               │  │   网页抓取器        │   │
               │  │  (web_scraper.py)  │   │
               │  └─────────────────────┘   │
               │           │                │
               │           ▼                │
               │  ┌─────────────────────┐   │
               │  │   AI分析引擎        │   │
               │  │ (ai_analyzer.py)    │   │
               │  └─────────────────────┘   │
               │           │                │
               │           ▼                │
               │  ┌─────────────────────┐   │
               │  │   数据映射器        │   │
               │  │ (data_mapper.py)    │   │
               │  └─────────────────────┘   │
               └─────────────────────────────┘
```

### 核心模块

#### 1. 网页抓取器 (web_scraper.py)
**职责**：负责从电商平台抓取商品信息
**技术栈**：Playwright + Python异步编程
**核心功能**：
- 模拟真实用户行为，突破反爬虫机制
- 支持多平台抓取（1688、淘宝、天猫等）
- 智能选择器，动态适配页面结构
- 错误重试机制，保证抓取稳定性

#### 2. AI分析引擎 (ai_analyzer.py)
**职责**：使用本地AI模型分析抓取的商品信息
**技术栈**：qwen2.5系列模型 + Ollama平台
**AI模型**：
- `qwen2.5:14b`：用于文本分析，提取商品标题、描述、规格参数
- `qwen2.5vl:latest`：用于视觉分析，识别商品图片特征

#### 3. 数据映射器 (data_mapper.py)
**职责**：将抓取和AI分析结果转换为货源表单格式
**核心功能**：
- 数据清洗和格式化
- 智能价格计算（批发价、代发价）
- 字段映射和验证

#### 4. UI组件 (smart_scraper_tab.py)
**职责**：提供用户交互界面
**核心功能**：
- 网址输入和验证
- 实时进度显示
- 结果预览和一键应用

## 详细技术实现

### 网页抓取技术

#### 核心抓取流程
```python
async def scrape_product_info(self, url: str) -> dict:
    """抓取商品信息的核心流程"""
    # 1. 平台识别
    platform = self._detect_platform(url)
    
    # 2. 浏览器初始化
    browser = await self._init_browser()
    page = await browser.new_page()
    
    # 3. 页面访问
    await self._navigate_to_page(page, url)
    
    # 4. 数据提取
    product_data = await self._extract_product_data(page, platform)
    
    # 5. 资源清理
    await browser.close()
    
    return product_data
```

#### 平台适配策略
```python
PLATFORM_SELECTORS = {
    '1688': {
        'title': '.d-title',
        'price': '.price-range .value',
        'images': '.vertical-img img',
        'description': '.description-content'
    },
    'taobao': {
        'title': '.tb-main-title',
        'price': '.tb-price .value',
        'images': '.tb-gallery img',
        'description': '.description'
    }
    # ... 更多平台配置
}
```

#### 反爬虫机制
```python
async def _init_browser(self):
    """初始化浏览器，配置反爬虫策略"""
    browser = await playwright.chromium.launch(
        headless=True,
        args=[
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-dev-shm-usage'
        ]
    )
    
    context = await browser.new_context(
        user_agent=self._get_random_user_agent(),
        viewport={'width': 1920, 'height': 1080}
    )
    
    return context
```

### AI分析技术

#### 文本分析流程
```python
async def analyze_text(self, text_content: dict) -> dict:
    """使用qwen2.5:14b进行文本分析"""
    prompt = self._build_text_analysis_prompt(text_content)
    
    response = await self.ollama_client.chat(
        model='qwen2.5:14b',
        messages=[{'role': 'user', 'content': prompt}]
    )
    
    return self._parse_text_analysis_response(response)
```

#### 图像分析流程
```python
async def analyze_images(self, image_urls: list) -> dict:
    """使用qwen2.5vl:latest进行图像分析"""
    analysis_results = []
    
    for image_url in image_urls[:5]:  # 只分析前5张图片
        image_data = await self._download_image(image_url)
        prompt = self._build_image_analysis_prompt()
        
        response = await self.ollama_client.chat(
            model='qwen2.5vl:latest',
            messages=[{
                'role': 'user',
                'content': prompt,
                'images': [image_data]
            }]
        )
        
        analysis_results.append(response)
    
    return self._aggregate_image_analysis(analysis_results)
```

#### AI提示词工程
```python
def _build_text_analysis_prompt(self, content):
    """构建文本分析提示词"""
    return f"""
    请分析以下电商商品信息，提取关键数据：
    
    商品标题：{content.get('title', '')}
    商品描述：{content.get('description', '')}
    价格信息：{content.get('price', '')}
    
    请以JSON格式返回：
    {{
        "clean_title": "清理后的商品标题",
        "wholesale_price": "批发价格（数字）",
        "retail_price": "零售价格（数字）",
        "product_specs": "产品规格参数",
        "supplier_name": "推荐的供应商名称",
        "description": "优化后的商品描述"
    }}
    """
```

### 数据映射技术

#### 映射规则配置
```python
FIELD_MAPPING = {
    'supplier_name': {
        'source': 'ai_analysis.supplier_name',
        'fallback': 'scraped_data.title',
        'processor': 'clean_supplier_name'
    },
    'wholesale_price': {
        'source': 'ai_analysis.wholesale_price',
        'fallback': 'scraped_data.price',
        'processor': 'parse_price'
    },
    'dropship_price': {
        'source': 'calculated',
        'formula': 'wholesale_price * 1.2',
        'processor': 'round_price'
    }
    # ... 更多字段映射
}
```

#### 智能价格计算
```python
def calculate_prices(self, scraped_data, ai_analysis):
    """智能价格计算逻辑"""
    # 1. 提取批发价
    wholesale_price = self._extract_wholesale_price(
        scraped_data, ai_analysis
    )
    
    # 2. 计算代发价（批发价 + 20%）
    dropship_price = round(wholesale_price * 1.2, 2)
    
    # 3. 价格验证
    if dropship_price < wholesale_price:
        dropship_price = wholesale_price * 1.1  # 最少10%利润
    
    return {
        'wholesale_price': wholesale_price,
        'dropship_price': dropship_price
    }
```

### 异步处理架构

#### 工作线程设计
```python
class ScrapingWorker(QThread):
    """抓取工作线程"""
    progress_updated = pyqtSignal(str, int)  # 进度信号
    result_ready = pyqtSignal(dict)          # 结果信号
    error_occurred = pyqtSignal(str)         # 错误信号
    
    def run(self):
        """异步执行抓取流程"""
        try:
            # 1. 网页抓取
            self.progress_updated.emit("正在抓取网页内容...", 20)
            scraped_data = self.scraper.scrape(self.url)
            
            # 2. AI分析
            self.progress_updated.emit("AI正在分析商品信息...", 60)
            ai_analysis = self.analyzer.analyze(scraped_data)
            
            # 3. 数据映射
            self.progress_updated.emit("正在处理数据映射...", 90)
            mapped_data = self.mapper.map_data(scraped_data, ai_analysis)
            
            # 4. 完成
            self.progress_updated.emit("抓取完成！", 100)
            self.result_ready.emit(mapped_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
```

#### 信号槽机制
```python
def setup_connections(self):
    """设置信号槽连接"""
    self.worker.progress_updated.connect(self.update_progress)
    self.worker.result_ready.connect(self.display_results)
    self.worker.error_occurred.connect(self.handle_error)
    
def update_progress(self, message: str, progress: int):
    """更新UI进度显示"""
    self.progress_label.setText(message)
    self.progress_bar.setValue(progress)
```

## 环境配置

### 自动化配置脚本 (setup_scraper.py)

#### 配置流程
```python
def main():
    """自动化配置主流程"""
    print("🤖 开始配置AI智能抓取环境...")
    
    # 1. 检查Python版本
    check_python_version()
    
    # 2. 安装依赖包
    install_dependencies()
    
    # 3. 配置Playwright
    setup_playwright()
    
    # 4. 检查AI模型
    setup_ai_models()
    
    # 5. 验证功能
    verify_functionality()
    
    print("✅ AI智能抓取环境配置完成！")
```

#### 依赖包管理
```python
REQUIRED_PACKAGES = [
    'playwright>=1.40.0',
    'fake-useragent>=1.4.0',
    'aiohttp>=3.9.0',
    'Pillow>=10.0.0',
    'requests>=2.31.0'
]

def install_dependencies():
    """安装必要的依赖包"""
    for package in REQUIRED_PACKAGES:
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], check=True, capture_output=True)
            print(f"✅ 已安装: {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {package}")
            raise
```

#### AI模型下载
```python
def setup_ai_models():
    """下载和配置AI模型"""
    models = ['qwen2.5:14b', 'qwen2.5vl:latest']
    
    for model in models:
        print(f"正在下载AI模型: {model}")
        result = subprocess.run([
            'ollama', 'pull', model
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 模型下载成功: {model}")
        else:
            print(f"❌ 模型下载失败: {model}")
            raise Exception(f"AI模型下载失败: {model}")
```

## 性能优化

### 缓存机制
```python
class SmartCache:
    """智能缓存系统"""
    
    def __init__(self):
        self.text_cache = {}
        self.image_cache = {}
        self.max_cache_size = 100
    
    def get_text_analysis(self, text_hash: str):
        """获取文本分析缓存"""
        return self.text_cache.get(text_hash)
    
    def cache_text_analysis(self, text_hash: str, result: dict):
        """缓存文本分析结果"""
        if len(self.text_cache) >= self.max_cache_size:
            # LRU清理策略
            oldest_key = next(iter(self.text_cache))
            del self.text_cache[oldest_key]
        
        self.text_cache[text_hash] = result
```

### 并发控制
```python
import asyncio
from asyncio import Semaphore

class ConcurrencyManager:
    """并发控制管理器"""
    
    def __init__(self, max_concurrent=3):
        self.semaphore = Semaphore(max_concurrent)
    
    async def run_with_limit(self, coro):
        """限制并发执行"""
        async with self.semaphore:
            return await coro
```

### 资源管理
```python
class ResourceManager:
    """资源管理器"""
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.browser = await playwright.chromium.launch()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.browser:
            await self.browser.close()
```

## 错误处理

### 分类错误处理
```python
class ScrapingError(Exception):
    """抓取相关错误基类"""
    pass

class NetworkError(ScrapingError):
    """网络相关错误"""
    pass

class AIAnalysisError(ScrapingError):
    """AI分析相关错误"""
    pass

class DataMappingError(ScrapingError):
    """数据映射相关错误"""
    pass
```

### 错误恢复机制
```python
async def scrape_with_retry(self, url: str, max_retries=3):
    """带重试机制的抓取"""
    for attempt in range(max_retries):
        try:
            return await self.scrape_product_info(url)
        except NetworkError as e:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(2 ** attempt)  # 指数退避
        except Exception as e:
            logging.error(f"抓取失败 (尝试 {attempt + 1}): {e}")
            if attempt == max_retries - 1:
                raise
```

## 安全和隐私

### 数据安全措施
- **本地AI处理**：所有AI分析都在本地进行，商业数据不上传到云端
- **临时数据清理**：抓取过程中的临时文件自动清理
- **安全传输**：网络请求使用HTTPS加密传输
- **访问控制**：遵循robots.txt规则，尊重网站访问策略

### 隐私保护
- **用户代理轮换**：使用随机User-Agent避免被识别
- **请求频率控制**：智能控制请求频率，避免对目标网站造成压力
- **数据脱敏**：敏感信息自动脱敏处理

## 测试验证

### 单元测试
```python
import unittest
from scrapers.web_scraper import WebScraper

class TestWebScraper(unittest.TestCase):
    """网页抓取器测试"""
    
    def setUp(self):
        self.scraper = WebScraper()
    
    def test_platform_detection(self):
        """测试平台识别"""
        url = "https://detail.1688.com/offer/123456.html"
        platform = self.scraper._detect_platform(url)
        self.assertEqual(platform, '1688')
    
    def test_price_extraction(self):
        """测试价格提取"""
        mock_data = {"price": "¥12.50-15.80"}
        price = self.scraper._extract_price(mock_data)
        self.assertEqual(price, 12.50)
```

### 集成测试
```python
def test_full_pipeline():
    """测试完整抓取流程"""
    url = "https://detail.1688.com/offer/test.html"
    
    # 执行完整流程
    result = run_scraping_pipeline(url)
    
    # 验证结果
    assert 'supplier_name' in result
    assert 'wholesale_price' in result
    assert 'dropship_price' in result
    assert result['dropship_price'] > result['wholesale_price']
```

## 部署和维护

### 版本兼容性
- **向后兼容**：新功能不影响现有功能使用
- **渐进式升级**：用户可选择是否启用AI功能
- **依赖隔离**：AI相关依赖独立管理，不影响核心功能

### 监控和日志
```python
import logging

# 配置专门的抓取日志
scraping_logger = logging.getLogger('scraping')
scraping_logger.setLevel(logging.INFO)

handler = logging.FileHandler('logs/scraping.log')
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
handler.setFormatter(formatter)
scraping_logger.addHandler(handler)
```

### 更新维护
- **模型更新**：支持AI模型的在线更新
- **选择器维护**：电商平台页面结构变化时的选择器更新
- **性能优化**：持续优化抓取速度和准确性

## 最佳实践

### 使用建议
1. **首次使用**：运行`python setup_scraper.py`配置环境
2. **网址格式**：使用商品详情页完整URL，避免移动端链接
3. **平台支持**：优先使用1688平台，抓取效果最佳
4. **结果验证**：抓取完成后检查结果准确性再应用

### 故障排除
1. **抓取失败**：检查网络连接和目标网站可访问性
2. **AI分析错误**：确认Ollama服务正常运行，模型已下载
3. **数据映射异常**：检查抓取数据完整性，重试抓取过程

### 性能调优
1. **并发控制**：避免同时进行多个抓取任务
2. **缓存利用**：相同商品避免重复抓取
3. **资源管理**：及时关闭浏览器进程，释放内存

## 技术路线图

### 近期优化 (v2.3.7)
- **支持更多平台**：扩展对京东、拼多多等平台的支持
- **提升准确率**：优化AI提示词，提高分析准确性
- **性能优化**：减少内存占用，提高抓取速度

### 中期发展 (v2.4.x)
- **批量抓取**：支持批量URL抓取功能
- **智能推荐**：基于历史数据推荐优质货源
- **数据分析**：提供货源质量评分和市场分析

### 长期规划 (v3.x)
- **云端同步**：支持数据云端备份和多设备同步
- **API接口**：提供RESTful API供第三方集成
- **移动端APP**：开发配套移动应用

---

**文档版本**：v2.3.6  
**最后更新**：2025-01-07  
**维护团队**：AI Assistant & 用户社区 