"""
内容清洗功能测试脚本
用于验证新的内容预处理功能是否能有效提升AI分析质量
"""

import sys
import asyncio
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from scrapers.content_cleaner import ContentCleaner
from scrapers.ai_analyzer import AIAnalyzer


class MockScrapedContent:
    """模拟抓取的内容对象"""

    def __init__(self, content: str, title: str = "", url: str = ""):
        self.content = content
        self.title = title
        self.url = url
        self.description = ""
        self.images = []


def test_content_cleaning():
    """测试内容清洗功能"""

    # 模拟包含大量HTML代码的1688商品页面内容
    sample_html_content = """
    <html>
    <head>
        <title>跨境耐咬自动逗猫玩具球</title>
        <script>
            function trackEvent() {
                console.log("tracking user");
            }
        </script>
        <style>
            .header { display: none; }
            .advertisement { color: red; }
        </style>
    </head>
    <body>
        <div class="header-nav">导航菜单</div>
        <div class="advertisement">广告内容</div>
        
        <div class="product-info">
            <h1>跨境耐咬自动逗猫玩具球</h1>
            <div class="price">
                <span>价格：¥2.80起</span>
                <span>批发价：¥2.50</span>
            </div>
            <div class="specs">
                <p>材质：PC+硅胶</p>
                <p>重量：75克</p>
                <p>净重：57克</p>
                <p>毛重：75克</p>
            </div>
            <div class="supplier">
                <p>供应商：温州辛宠宠物用品有限公司</p>
                <p>经营年限：1年</p>
                <p>发货地：浙江温州</p>
            </div>
            <div class="reviews">
                <p>评价：价格很便宜(6次)</p>
                <p>客服很热情(5次)</p>
                <p>入手推荐(4次)</p>
            </div>
            <div class="logistics">
                <p>包装：200件/箱</p>
                <p>外箱尺寸：59×34×34.5厘米</p>
                <p>发货承诺：48小时内发货</p>
            </div>
        </div>
        
        <script>trackEvent();</script>
        <div class="footer">页脚信息</div>
    </body>
    </html>
    """

    print("🧪 开始测试内容清洗功能...")
    print(f"📄 原始内容长度: {len(sample_html_content)} 字符")
    print("=" * 60)

    # 1. 初始化内容清洗器
    cleaner = ContentCleaner()

    # 2. 创建模拟的抓取内容对象
    mock_content = MockScrapedContent(
        content=sample_html_content,
        title="跨境耐咬自动逗猫玩具球",
        url="https://detail.1688.com/offer/test.html",
    )

    # 3. 执行内容清洗
    cleaned_content = cleaner.clean_scraped_content(mock_content)

    # 4. 获取清洗统计
    stats = cleaner.get_cleaning_stats(sample_html_content, cleaned_content)

    # 5. 显示清洗结果
    print("🧹 内容清洗结果:")
    print(f"📊 压缩率: {stats['compression_ratio']*100:.1f}%")
    print(f"🗑️  移除字符数: {stats['removed_chars']}")
    print(f"🇨🇳 中文字符数: {stats['chinese_chars']}")
    print(f"🔤 英文单词数: {stats['english_words']}")
    print(f"🔢 数字个数: {stats['numbers']}")
    print("=" * 60)

    print("🧽 清洗后的内容:")
    print(cleaned_content)
    print("=" * 60)

    # 6. 提取关键部分
    sections = cleaner.extract_key_sections(cleaned_content)
    print("📋 提取的关键部分:")
    for section_name, section_content in sections.items():
        if section_content:
            print(f"  {section_name}: {section_content[:100]}...")

    return cleaned_content


async def test_ai_analysis_with_cleaning():
    """测试带内容清洗的AI分析"""

    print("\n" + "=" * 60)
    print("🤖 开始测试AI分析（含内容清洗）...")

    # 使用上面清洗测试的内容
    sample_content = """
    <div class="product-detail">
        <h1>跨境耐咬自动逗猫玩具球</h1>
        <div class="price-section">
            <span class="price">¥2.80起</span>
            <span class="wholesale">批发价：¥2.50</span>
            <span class="min-order">起订量：1件</span>
        </div>
        <div class="specifications">
            <p>材质：PC+硅胶</p>
            <p>重量：75克</p>
            <p>颜色：多色可选</p>
            <p>尺寸：标准球形</p>
        </div>
        <div class="supplier-info">
            <p>供应商：温州辛宠宠物用品有限公司</p>
            <p>地址：浙江温州</p>
            <p>经营年限：1年</p>
        </div>
        <div class="features">
            <p>特点：价格很便宜</p>
            <p>材质：环保安全</p>
            <p>功能：自动逗猫</p>
        </div>
    </div>
    """

    # 创建模拟内容
    mock_content = MockScrapedContent(
        content=sample_content,
        title="跨境耐咬自动逗猫玩具球",
        url="https://detail.1688.com/offer/test.html",
    )

    # 初始化AI分析器（会自动集成内容清洗）
    analyzer = AIAnalyzer()

    # 执行分析
    try:
        print("🔄 正在执行AI分析...")
        result = await analyzer.analyze_scraped_content(mock_content)

        print("✅ AI分析完成!")
        print(f"📊 分析结果:")
        print(f"  成功: {result.success}")
        print(f"  标题: {result.title}")
        print(f"  清理内容: {result.cleaned_content[:100]}...")
        print(f"  价格信息: {result.price_info}")
        print(f"  规格信息: {result.specifications}")
        print(f"  供应商信息: {result.supplier_info}")
        print(f"  产品特征: {result.product_features}")
        print(f"  处理时间: {result.processing_time:.2f}秒")

    except Exception as e:
        print(f"❌ AI分析失败: {e}")


def main():
    """主函数"""
    print("🚀 内容清洗与AI分析测试")
    print("=" * 60)

    # 1. 测试内容清洗
    cleaned_content = test_content_cleaning()

    # 2. 测试AI分析（异步）
    asyncio.run(test_ai_analysis_with_cleaning())

    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("\n💡 优化建议：")
    print("1. 内容清洗显著减少了无用字符")
    print("2. AI分析应该能获得更准确的结果")
    print("3. 建议在正式使用前用真实数据测试")


if __name__ == "__main__":
    main()
