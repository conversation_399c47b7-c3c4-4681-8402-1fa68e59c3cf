<!doctype html>
<html>
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="ie=edge" />
<title>Markmap</title>
<style>
* {
  margin: 0;
  padding: 0;
}
html {
  font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}
#mindmap {
  display: block;
  width: 100vw;
  height: 100vh;
}
.markmap-dark {
  background: #27272a;
  color: white;
}
</style>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/markmap-toolbar@0.18.12/dist/style.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@highlightjs/cdn-assets@11.11.1/styles/default.min.css">
</head>
<body>
<svg id="mindmap"></svg>
<script src="https://cdn.jsdelivr.net/npm/d3@7.9.0/dist/d3.min.js"></script><script src="https://cdn.jsdelivr.net/npm/markmap-view@0.18.12/dist/browser/index.js"></script><script src="https://cdn.jsdelivr.net/npm/markmap-toolbar@0.18.12/dist/index.js"></script><script>(r => {
              setTimeout(r);
            })(function renderToolbar() {
  const {
    markmap,
    mm
  } = window;
  const {
    el
  } = markmap.Toolbar.create(mm);
  el.setAttribute('style', 'position:absolute;bottom:20px;right:20px');
  document.body.append(el);
})</script><script>((getMarkmap, getOptions, root2, jsonOptions) => {
              const markmap = getMarkmap();
              window.mm = markmap.Markmap.create(
                "svg#mindmap",
                (getOptions || markmap.deriveOptions)(jsonOptions),
                root2
              );
              if (window.matchMedia("(prefers-color-scheme: dark)").matches) {
                document.documentElement.classList.add("markmap-dark");
              }
            })(() => window.markmap,null,{"content":"&#x7535;&#x5546;&#x4ea7;&#x54c1;&#x5bf9;&#x6bd4;&#x9009;&#x54c1;&#x5de5;&#x5177; - &#x7b80;&#x5316;&#x7248; v2.1.7","children":[{"content":"&#x9879;&#x76ee;&#x7279;&#x70b9;","children":[{"content":"&#x2705; <strong>&#x7b80;&#x6d01;&#x67b6;&#x6784;</strong>&#xff1a;&#x57fa;&#x4e8e;PyQt6 + SQLite&#xff0c;&#x907f;&#x514d;&#x8fc7;&#x5ea6;&#x8bbe;&#x8ba1;","children":[],"payload":{"tag":"li","lines":"6,7"}},{"content":"&#x2705; <strong>&#x9ad8;&#x6027;&#x80fd;</strong>&#xff1a;&#x8f7b;&#x91cf;&#x7ea7;&#x5185;&#x6838;&#xff0c;&#x5feb;&#x901f;&#x54cd;&#x5e94;","children":[],"payload":{"tag":"li","lines":"7,8"}},{"content":"&#x2705; <strong>&#x6613;&#x4e8e;&#x7ef4;&#x62a4;</strong>&#xff1a;&#x6e05;&#x6670;&#x7684;&#x4ee3;&#x7801;&#x7ed3;&#x6784;&#xff0c;&#x6700;&#x5c0f;&#x5316;&#x4f9d;&#x8d56;","children":[],"payload":{"tag":"li","lines":"8,9"}},{"content":"&#x2705; <strong>&#x529f;&#x80fd;&#x5b8c;&#x6574;</strong>&#xff1a;&#x4ea7;&#x54c1;&#x7ba1;&#x7406;&#x3001;&#x8d27;&#x6e90;&#x5bf9;&#x6bd4;&#x3001;&#x56fe;&#x7247;&#x8f6e;&#x64ad;","children":[],"payload":{"tag":"li","lines":"9,10"}},{"content":"&#x2705; <strong>&#x7f8e;&#x89c2;&#x754c;&#x9762;</strong>&#xff1a;&#x73b0;&#x4ee3;&#x5316;UI&#x8bbe;&#x8ba1;&#xff0c;&#x826f;&#x597d;&#x7528;&#x6237;&#x4f53;&#x9a8c;","children":[],"payload":{"tag":"li","lines":"10,11"}},{"content":"&#x2705; <strong>&#x5feb;&#x901f;&#x8bbf;&#x95ee;</strong>&#xff1a;&#x4e00;&#x952e;&#x6253;&#x5f00;&#x8d27;&#x6e90;&#x7f51;&#x7ad9;&#x94fe;&#x63a5;","children":[],"payload":{"tag":"li","lines":"11,12"}},{"content":"&#x2705; <strong>&#x8be6;&#x7ec6;&#x4fe1;&#x606f;</strong>&#xff1a;&#x652f;&#x6301;&#x5546;&#x54c1;&#x63cf;&#x8ff0;&#x548c;&#x9500;&#x91cf;&#x53c2;&#x8003;","children":[],"payload":{"tag":"li","lines":"12,13"}},{"content":"&#x2705; <strong>&#x667a;&#x80fd;&#x8ba1;&#x7b97;</strong>&#xff1a;&#x6839;&#x636e;&#x8d27;&#x6e90;&#x6a21;&#x5f0f;&#x667a;&#x80fd;&#x8ba1;&#x7b97;&#x6210;&#x672c;&#x548c;&#x5229;&#x6da6;","children":[],"payload":{"tag":"li","lines":"13,15"}}],"payload":{"tag":"h2","lines":"4,5"}},{"content":"&#x4e3b;&#x8981;&#x529f;&#x80fd;","children":[{"content":"&#x1f525; &#x6838;&#x5fc3;&#x529f;&#x80fd;","children":[{"content":"<strong>&#x4ea7;&#x54c1;&#x7ba1;&#x7406;</strong>&#xff1a;&#x589e;&#x5220;&#x6539;&#x67e5;&#x4ea7;&#x54c1;&#x4fe1;&#x606f;","children":[],"payload":{"tag":"li","lines":"18,19"}},{"content":"<strong>&#x5546;&#x54c1;&#x63cf;&#x8ff0;</strong>&#xff1a;&#x652f;&#x6301;&#x8be6;&#x7ec6;&#x7684;&#x4ea7;&#x54c1;&#x63cf;&#x8ff0;&#x4fe1;&#x606f;","children":[],"payload":{"tag":"li","lines":"19,20"}},{"content":"<strong>&#x9500;&#x91cf;&#x53c2;&#x8003;</strong>&#xff1a;&#x6dfb;&#x52a0;&#x9500;&#x91cf;&#x53c2;&#x8003;&#x6570;&#x636e;&#xff0c;&#x4fbf;&#x4e8e;&#x9009;&#x54c1;&#x51b3;&#x7b56;","children":[],"payload":{"tag":"li","lines":"20,21"}},{"content":"<strong>&#x8d27;&#x6e90;&#x7ba1;&#x7406;</strong>&#xff1a;&#x591a;&#x8d27;&#x6e90;&#x4ef7;&#x683c;&#x5bf9;&#x6bd4;&#x5206;&#x6790;","children":[],"payload":{"tag":"li","lines":"21,22"}},{"content":"<strong>&#x667a;&#x80fd;&#x6210;&#x672c;&#x8ba1;&#x7b97;</strong>&#xff1a;&#x6839;&#x636e;&#x8d27;&#x6e90;&#x6a21;&#x5f0f;&#xff08;&#x6279;&#x53d1;/&#x4ee3;&#x53d1;&#xff09;&#x667a;&#x80fd;&#x8ba1;&#x7b97;&#x6210;&#x672c;","children":[],"payload":{"tag":"li","lines":"22,23"}},{"content":"<strong>&#x7f51;&#x7ad9;&#x94fe;&#x63a5;</strong>&#xff1a;&#x4e00;&#x952e;&#x6253;&#x5f00;&#x8d27;&#x6e90;&#x7f51;&#x7ad9;&#x94fe;&#x63a5;","children":[],"payload":{"tag":"li","lines":"23,24"}},{"content":"<strong>&#x8fd0;&#x8d39;&#x663e;&#x793a;</strong>&#xff1a;&#x542b;&#x8fd0;&#x8d39;&#x4fe1;&#x606f;&#x4ee5;&#x6a58;&#x7ea2;&#x8272;&#x9ad8;&#x4eae;&#x663e;&#x793a;","children":[],"payload":{"tag":"li","lines":"24,25"}},{"content":"<strong>&#x81ea;&#x5b9a;&#x4e49;&#x5b57;&#x6bb5;</strong>&#xff1a;&#x652f;&#x6301;&#x4ea7;&#x54c1;&#x89c4;&#x683c;&#x3001;&#x54c1;&#x724c;&#x7b49;&#x81ea;&#x5b9a;&#x4e49;&#x5c5e;&#x6027;","children":[],"payload":{"tag":"li","lines":"25,26"}},{"content":"<strong>&#x56fe;&#x7247;&#x7ba1;&#x7406;</strong>&#xff1a;&#x4ea7;&#x54c1;&#x56fe;&#x7247;&#x8f6e;&#x64ad;&#x67e5;&#x770b;","children":[],"payload":{"tag":"li","lines":"26,27"}},{"content":"<strong>&#x5bf9;&#x6bd4;&#x5206;&#x6790;</strong>&#xff1a;&#x667a;&#x80fd;&#x5bf9;&#x6bd4;&#x591a;&#x4e2a;&#x4ea7;&#x54c1;&#x7684;&#x6210;&#x672c;&#x548c;&#x5229;&#x6da6;","children":[],"payload":{"tag":"li","lines":"27,29"}}],"payload":{"tag":"h3","lines":"17,18"}},{"content":"&#x1f4ca; &#x6570;&#x636e;&#x5206;&#x6790;","children":[{"content":"&#x6700;&#x4f4e;/&#x6700;&#x9ad8;/&#x5e73;&#x5747;&#x6210;&#x672c;&#x8ba1;&#x7b97;","children":[],"payload":{"tag":"li","lines":"30,31"}},{"content":"&#x5229;&#x6da6;&#x548c;&#x5229;&#x6da6;&#x7387;&#x5206;&#x6790;","children":[],"payload":{"tag":"li","lines":"31,32"}},{"content":"&#x6839;&#x636e;&#x8d27;&#x6e90;&#x6a21;&#x5f0f;&#x667a;&#x80fd;&#x8ba1;&#x7b97;&#xff08;&#x6279;&#x53d1;&#x6a21;&#x5f0f;&#x663e;&#x793a;&#x603b;&#x6210;&#x672c;&#xff0c;&#x4ee3;&#x53d1;&#x6a21;&#x5f0f;&#x53ea;&#x8ba1;&#x7b97;&#x5355;&#x4e2a;&#x5229;&#x6da6;&#xff09;","children":[],"payload":{"tag":"li","lines":"32,33"}},{"content":"&#x6700;&#x4f18;&#x8d27;&#x6e90;&#x63a8;&#x8350;","children":[],"payload":{"tag":"li","lines":"33,34"}},{"content":"&#x7edf;&#x8ba1;&#x4fe1;&#x606f;&#x4eea;&#x8868;&#x677f;","children":[],"payload":{"tag":"li","lines":"34,36"}}],"payload":{"tag":"h3","lines":"29,30"}},{"content":"&#x1f3a8; &#x7528;&#x6237;&#x754c;&#x9762;","children":[{"content":"&#x5de6;&#x53f3;&#x5206;&#x680f;&#x5e03;&#x5c40;&#xff1a;&#x4ea7;&#x54c1;&#x5217;&#x8868; + &#x8be6;&#x60c5;/&#x5bf9;&#x6bd4;","children":[],"payload":{"tag":"li","lines":"37,38"}},{"content":"&#x5b9e;&#x65f6;&#x641c;&#x7d22;&#x548c;&#x591a;&#x79cd;&#x6392;&#x5e8f;&#x65b9;&#x5f0f;","children":[],"payload":{"tag":"li","lines":"38,39"}},{"content":"&#x54cd;&#x5e94;&#x5f0f;&#x8bbe;&#x8ba1;&#xff0c;&#x652f;&#x6301;&#x7a97;&#x53e3;&#x7f29;&#x653e;","children":[],"payload":{"tag":"li","lines":"39,40"}},{"content":"&#x73b0;&#x4ee3;&#x5316;&#x6837;&#x5f0f;&#xff0c;&#x7f8e;&#x89c2;&#x6613;&#x7528;","children":[],"payload":{"tag":"li","lines":"40,42"}}],"payload":{"tag":"h3","lines":"36,37"}}],"payload":{"tag":"h2","lines":"15,16"}},{"content":"&#x5feb;&#x901f;&#x5f00;&#x59cb;","children":[{"content":"&#x73af;&#x5883;&#x8981;&#x6c42;","children":[{"content":"Python 3.8+","children":[],"payload":{"tag":"li","lines":"45,46"}},{"content":"Windows 10/11 (&#x4e3b;&#x8981;&#x6d4b;&#x8bd5;&#x73af;&#x5883;)","children":[],"payload":{"tag":"li","lines":"46,48"}}],"payload":{"tag":"h3","lines":"44,45"}},{"content":"&#x5b89;&#x88c5;&#x6b65;&#x9aa4;","children":[{"content":"1. \n<p data-lines=\"50,51\"><strong>&#x514b;&#x9686;&#x6216;&#x4e0b;&#x8f7d;&#x9879;&#x76ee;</strong></p>\n<pre data-lines=\"51,57\"><code class=\"language-bash\"><span class=\"hljs-comment\"># &#x5982;&#x679c;&#x6709;git</span>\ngit <span class=\"hljs-built_in\">clone</span> &lt;repository-url&gt;\n\n<span class=\"hljs-comment\"># &#x6216;&#x8005;&#x76f4;&#x63a5;&#x4e0b;&#x8f7d;&#x538b;&#x7f29;&#x5305;&#x89e3;&#x538b;</span>\n</code></pre>","children":[],"payload":{"tag":"li","lines":"50,58","listIndex":1}},{"content":"2. \n<p data-lines=\"58,59\"><strong>&#x5b89;&#x88c5;&#x4f9d;&#x8d56;</strong></p>\n<pre data-lines=\"59,62\"><code class=\"language-bash\">pip install -r requirements.txt\n</code></pre>","children":[],"payload":{"tag":"li","lines":"58,63","listIndex":2}},{"content":"3. \n<p data-lines=\"63,64\"><strong>&#x542f;&#x52a8;&#x5e94;&#x7528;</strong></p>\n<p data-lines=\"65,66\"><strong>Windows&#x7528;&#x6237;</strong>&#xff1a;</p>\n<pre data-lines=\"66,73\"><code class=\"language-bash\"><span class=\"hljs-comment\"># &#x53cc;&#x51fb;&#x8fd0;&#x884c;</span>\nstart.bat\n\n<span class=\"hljs-comment\"># &#x6216;&#x8005;&#x547d;&#x4ee4;&#x884c;&#x8fd0;&#x884c;</span>\npython main.py\n</code></pre>\n<p data-lines=\"74,75\"><strong>&#x5176;&#x4ed6;&#x7cfb;&#x7edf;</strong>&#xff1a;</p>\n<pre data-lines=\"75,78\"><code class=\"language-bash\">python main.py\n</code></pre>","children":[],"payload":{"tag":"li","lines":"63,79","listIndex":3}}],"payload":{"tag":"h3","lines":"48,49"}}],"payload":{"tag":"h2","lines":"42,43"}},{"content":"&#x4f7f;&#x7528;&#x6307;&#x5357;","children":[{"content":"1. &#x6dfb;&#x52a0;&#x4ea7;&#x54c1;","children":[{"content":"&#x70b9;&#x51fb;&#x5de6;&#x4fa7;&quot;&#x6dfb;&#x52a0;&#x4ea7;&#x54c1;&quot;&#x6309;&#x94ae;","children":[],"payload":{"tag":"li","lines":"82,83"}},{"content":"&#x586b;&#x5199;&#x4ea7;&#x54c1;&#x540d;&#x79f0;&#x3001;&#x7f16;&#x7801;&#x3001;&#x552e;&#x4ef7;","children":[],"payload":{"tag":"li","lines":"83,84"}},{"content":"&#x6dfb;&#x52a0;&#x5546;&#x54c1;&#x63cf;&#x8ff0;&#x548c;&#x9500;&#x91cf;&#x53c2;&#x8003;&#xff08;&#x53ef;&#x9009;&#xff09;","children":[],"payload":{"tag":"li","lines":"84,85"}},{"content":"&#x4fdd;&#x5b58;&#x540e;&#x4ea7;&#x54c1;&#x51fa;&#x73b0;&#x5728;&#x5217;&#x8868;&#x4e2d;","children":[],"payload":{"tag":"li","lines":"85,87"}}],"payload":{"tag":"h3","lines":"81,82"}},{"content":"2. &#x7ba1;&#x7406;&#x8d27;&#x6e90;","children":[{"content":"&#x9009;&#x62e9;&#x4ea7;&#x54c1;&#x540e;&#xff0c;&#x5728;&#x53f3;&#x4fa7;&#x8be6;&#x60c5;&#x533a;&#x57df;&#x70b9;&#x51fb;&quot;&#x6dfb;&#x52a0;&#x8d27;&#x6e90;&quot;","children":[],"payload":{"tag":"li","lines":"88,89"}},{"content":"&#x586b;&#x5199;&#x4f9b;&#x5e94;&#x5546;&#x4fe1;&#x606f;&#x3001;&#x4ef7;&#x683c;&#x3001;&#x8054;&#x7cfb;&#x65b9;&#x5f0f;&#x7b49;","children":[],"payload":{"tag":"li","lines":"89,90"}},{"content":"&#x9009;&#x62e9;&#x8d27;&#x6e90;&#x6a21;&#x5f0f;&#xff08;&#x6279;&#x53d1;&#x6a21;&#x5f0f;/&#x4ee3;&#x53d1;&#x6a21;&#x5f0f;&#xff09;","children":[],"payload":{"tag":"li","lines":"90,91"}},{"content":"&#x6dfb;&#x52a0;&#x7f51;&#x7ad9;&#x94fe;&#x63a5;&#xff0c;&#x652f;&#x6301;&#x4e00;&#x952e;&#x6253;&#x5f00;&#x6d4f;&#x89c8;&#x5668;&#x8bbf;&#x95ee;","children":[],"payload":{"tag":"li","lines":"91,92"}},{"content":"&#x542b;&#x8fd0;&#x8d39;&#x4fe1;&#x606f;&#x4ee5;&#x6a58;&#x7ea2;&#x8272;&#x9ad8;&#x4eae;&#x663e;&#x793a;","children":[],"payload":{"tag":"li","lines":"92,93"}},{"content":"&#x7cfb;&#x7edf;&#x6839;&#x636e;&#x8d27;&#x6e90;&#x6a21;&#x5f0f;&#x667a;&#x80fd;&#x8ba1;&#x7b97;&#x6210;&#x672c;&#x548c;&#x5229;&#x6da6;","children":[],"payload":{"tag":"li","lines":"93,95"}}],"payload":{"tag":"h3","lines":"87,88"}},{"content":"3. &#x4ea7;&#x54c1;&#x5bf9;&#x6bd4;","children":[{"content":"&#x5728;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x4e2d;&#x9009;&#x62e9;&#x591a;&#x4e2a;&#x4ea7;&#x54c1;&#xff08;Ctrl+&#x70b9;&#x51fb;&#xff09;","children":[],"payload":{"tag":"li","lines":"96,97"}},{"content":"&#x70b9;&#x51fb;&quot;&#x5bf9;&#x6bd4;&#x9009;&#x4e2d;&quot;&#x6309;&#x94ae;","children":[],"payload":{"tag":"li","lines":"97,98"}},{"content":"&#x67e5;&#x770b;&#x8be6;&#x7ec6;&#x7684;&#x5bf9;&#x6bd4;&#x5206;&#x6790;&#x8868;&#x683c;","children":[],"payload":{"tag":"li","lines":"98,100"}}],"payload":{"tag":"h3","lines":"95,96"}},{"content":"4. &#x8d27;&#x6e90;&#x6a21;&#x5f0f;&#x8bf4;&#x660e;","children":[{"content":"<strong>&#x6279;&#x53d1;&#x6a21;&#x5f0f;</strong>&#xff1a;&#x663e;&#x793a;&#x603b;&#x6210;&#x672c;&#x548c;&#x542b;&#x8fd0;&#x8d39;&#x603b;&#x6210;&#x672c;&#xff0c;&#x9002;&#x7528;&#x4e8e;&#x9700;&#x8981;&#x56e4;&#x8d27;&#x7684;&#x4e1a;&#x52a1;","children":[],"payload":{"tag":"li","lines":"101,102"}},{"content":"<strong>&#x4ee3;&#x53d1;&#x6a21;&#x5f0f;</strong>&#xff1a;&#x53ea;&#x8ba1;&#x7b97;&#x5355;&#x4e2a;&#x5229;&#x6da6;&#xff0c;&#x4e0d;&#x663e;&#x793a;&#x603b;&#x6210;&#x672c;&#xff0c;&#x9002;&#x7528;&#x4e8e;&#x65e0;&#x5e93;&#x5b58;&#x7684;&#x4ee3;&#x53d1;&#x4e1a;&#x52a1;","children":[],"payload":{"tag":"li","lines":"102,103"}},{"content":"<strong>&#x6210;&#x672c;&#x8ba1;&#x7b97;</strong>&#xff1a;&#x4ee3;&#x53d1;&#x6a21;&#x5f0f;&#x7406;&#x8bba;&#x6210;&#x672c;&#x4e3a;0&#xff0c;&#x9664;&#x975e;&#x552e;&#x4ef7;&#x4f4e;&#x4e8e;&#x6210;&#x672c;+&#x8fd0;&#x8d39;&#x65f6;&#x663e;&#x793a;&#x4e8f;&#x635f;&#x8b66;&#x793a;","children":[],"payload":{"tag":"li","lines":"103,105"}}],"payload":{"tag":"h3","lines":"100,101"}},{"content":"5. &#x641c;&#x7d22;&#x548c;&#x6392;&#x5e8f;","children":[{"content":"&#x4f7f;&#x7528;&#x641c;&#x7d22;&#x6846;&#x5feb;&#x901f;&#x67e5;&#x627e;&#x4ea7;&#x54c1;","children":[],"payload":{"tag":"li","lines":"106,107"}},{"content":"&#x9009;&#x62e9;&#x4e0d;&#x540c;&#x6392;&#x5e8f;&#x65b9;&#x5f0f;&#x67e5;&#x770b;&#x4ea7;&#x54c1;&#x5217;&#x8868;","children":[],"payload":{"tag":"li","lines":"107,108"}},{"content":"&#x652f;&#x6301;&#x6309;&#x4ef7;&#x683c;&#x3001;&#x5229;&#x6da6;&#x3001;&#x521b;&#x5efa;&#x65f6;&#x95f4;&#x6392;&#x5e8f;","children":[],"payload":{"tag":"li","lines":"108,110"}}],"payload":{"tag":"h3","lines":"105,106"}}],"payload":{"tag":"h2","lines":"79,80"}},{"content":"&#x65b0;&#x7248;&#x672c;&#x7279;&#x6027; (v2.1.7)","children":[{"content":"&#x1f41b; &#x4fee;&#x590d;&#x5173;&#x952e;&#x95ee;&#x9898;","children":[{"content":"<strong>&#x4fee;&#x590d;&#x5bfc;&#x5165;&#x9519;&#x8bef;</strong>&#xff1a;&#x89e3;&#x51b3;&#x5bfc;&#x5165;&#x65f6; <code>&apos;ProductService&apos; object has no attribute &apos;update_product_images&apos;</code> &#x65b9;&#x6cd5;&#x8c03;&#x7528;&#x7684;&#x9519;&#x8bef;&#x3002;","children":[],"payload":{"tag":"li","lines":"113,114"}},{"content":"<strong>&#x4fee;&#x590d;&#x6807;&#x7b7e;&#x7ea6;&#x675f;&#x9519;&#x8bef;</strong>&#xff1a;&#x89e3;&#x51b3;&#x5bfc;&#x5165;&#x65f6; <code>UNIQUE constraint failed: tags.name</code> &#x7684;&#x95ee;&#x9898;&#x3002;","children":[],"payload":{"tag":"li","lines":"114,115"}},{"content":"<strong>&#x6539;&#x8fdb;&#x6807;&#x7b7e;&#x68c0;&#x67e5;&#x673a;&#x5236;</strong>&#xff1a;&#x5bfc;&#x5165;&#x524d;&#x68c0;&#x67e5;&#x6807;&#x7b7e;&#x662f;&#x5426;&#x5b58;&#x5728;&#xff0c;&#x907f;&#x514d;&#x91cd;&#x590d;&#x521b;&#x5efa;&#x3002;","children":[],"payload":{"tag":"li","lines":"115,116"}},{"content":"<strong>&#x4fee;&#x590d;&#x56fe;&#x7247;&#x5bfc;&#x51fa;&#x95ee;&#x9898;</strong>&#xff1a;&#x89e3;&#x51b3;&#x4e86;&#x5bfc;&#x51fa;&#x529f;&#x80fd;&#x4e2d;&#x56fe;&#x7247;&#x672a;&#x80fd;&#x6b63;&#x786e;&#x590d;&#x5236;&#x5230;&#x5bfc;&#x51fa;&#x6587;&#x4ef6;&#x5939;&#x7684;&#x95ee;&#x9898;&#xff0c;&#x786e;&#x4fdd;&#x6570;&#x636e;&#x5e93;&#x4e2d;&#x5b58;&#x50a8;&#x7684;&#x5b8c;&#x6574;&#x56fe;&#x7247;&#x8def;&#x5f84;&#x80fd;&#x591f;&#x88ab;&#x6b63;&#x786e;&#x89e3;&#x6790;&#x548c;&#x590d;&#x5236;&#x3002;","children":[],"payload":{"tag":"li","lines":"116,117"}},{"content":"<strong>&#x4fee;&#x590d;&#x56fe;&#x7247;&#x5bfc;&#x5165;&#x8def;&#x5f84;&#x5b58;&#x50a8;&#x4e0e;&#x663e;&#x793a;&#x95ee;&#x9898;</strong>&#xff1a;&#x89e3;&#x51b3;&#x4e86;&#x5bfc;&#x5165;&#x529f;&#x80fd;&#x4e2d;&#x56fe;&#x7247;&#x8def;&#x5f84;&#x5728;&#x6570;&#x636e;&#x5e93;&#x4e2d;&#x5b58;&#x50a8;&#x4e0d;&#x5b8c;&#x6574;&#xff08;&#x53ea;&#x5b58;&#x50a8;&#x6587;&#x4ef6;&#x540d;&#xff09;&#x5bfc;&#x81f4;&#x56fe;&#x7247;&#x65e0;&#x6cd5;&#x663e;&#x793a;&#x7684;&#x95ee;&#x9898;&#xff0c;&#x73b0;&#x5728;&#x5b58;&#x50a8;&#x5b8c;&#x6574;&#x7684;&#x76f8;&#x5bf9;&#x8def;&#x5f84;&#xff0c;&#x5e76;&#x786e;&#x4fdd;&#x56fe;&#x7247;&#x80fd;&#x591f;&#x6b63;&#x5e38;&#x52a0;&#x8f7d;&#x663e;&#x793a;&#x3002;","children":[],"payload":{"tag":"li","lines":"117,119"}}],"payload":{"tag":"h3","lines":"112,113"}},{"content":"&#x1f3f7;&#xfe0f; &#x6807;&#x7b7e;&#x7ba1;&#x7406;&#x589e;&#x5f3a;","children":[{"content":"&#x53f3;&#x952e;&#x83dc;&#x5355;&#x65b0;&#x589e;&quot;&#x5220;&#x9664;&#x6807;&#x7b7e;&quot;&#x529f;&#x80fd;&#xff0c;&#x652f;&#x6301;&#x5feb;&#x901f;&#x79fb;&#x9664;&#x4ea7;&#x54c1;&#x6807;&#x7b7e;&#x3002;","children":[],"payload":{"tag":"li","lines":"120,121"}},{"content":"&#x667a;&#x80fd;&#x6807;&#x7b7e;&#x663e;&#x793a;&#xff0c;&#x53ea;&#x663e;&#x793a;&#x5f53;&#x524d;&#x4ea7;&#x54c1;&#x5df2;&#x5173;&#x8054;&#x7684;&#x6807;&#x7b7e;&#x3002;","children":[],"payload":{"tag":"li","lines":"121,122"}},{"content":"&#x6807;&#x7b7e;&#x5220;&#x9664;&#x64cd;&#x4f5c;&#x663e;&#x793a;&#x6807;&#x7b7e;&#x540d;&#x79f0;&#xff0c;&#x7528;&#x6237;&#x4f53;&#x9a8c;&#x66f4;&#x53cb;&#x597d;&#x3002;","children":[],"payload":{"tag":"li","lines":"122,124"}}],"payload":{"tag":"h3","lines":"119,120"}},{"content":"&#x1f4c1; &#x667a;&#x80fd;&#x5bfc;&#x5165;&#x5bfc;&#x51fa;&#x7cfb;&#x7edf;","children":[{"content":"&#x5168;&#x65b0;&#x7684;&#x5bfc;&#x5165;&#x9009;&#x62e9;&#x754c;&#x9762;&#xff0c;&#x81ea;&#x52a8;&#x626b;&#x63cf;&#x5e76;&#x663e;&#x793a;&#x6240;&#x6709;&#x53ef;&#x7528;&#x7684;&#x5bfc;&#x51fa;&#x7248;&#x672c;&#x3002;","children":[],"payload":{"tag":"li","lines":"125,126"}},{"content":"&#x663e;&#x793a;&#x8be6;&#x7ec6;&#x7684;&#x7248;&#x672c;&#x4fe1;&#x606f;&#xff1a;&#x5bfc;&#x51fa;&#x65f6;&#x95f4;&#x3001;&#x4ea7;&#x54c1;&#x6570;&#x91cf;&#x3001;&#x6807;&#x7b7e;&#x6570;&#x91cf;&#x3002;","children":[],"payload":{"tag":"li","lines":"126,127"}},{"content":"&#x5bfc;&#x51fa;&#x7248;&#x672c;&#x6309;&#x65f6;&#x95f4;&#x6392;&#x5e8f;&#xff0c;&#x6700;&#x65b0;&#x7248;&#x672c;&#x663e;&#x793a;&#x5728;&#x524d;&#x3002;","children":[],"payload":{"tag":"li","lines":"127,128"}},{"content":"&#x652f;&#x6301;&#x5728;&#x5bfc;&#x5165;&#x754c;&#x9762;&#x76f4;&#x63a5;&#x5220;&#x9664;&#x4e0d;&#x9700;&#x8981;&#x7684;&#x5bfc;&#x51fa;&#x7248;&#x672c;&#x3002;","children":[],"payload":{"tag":"li","lines":"128,129"}},{"content":"&#x652f;&#x6301;&#x624b;&#x52a8;&#x6d4f;&#x89c8;&#x5176;&#x4ed6;&#x4f4d;&#x7f6e;&#x7684;&#x5bfc;&#x51fa;&#x6587;&#x4ef6;&#x5939;&#x3002;","children":[],"payload":{"tag":"li","lines":"129,130"}},{"content":"<strong>&#x56fe;&#x7247;&#x7f16;&#x53f7;&#x667a;&#x80fd;&#x8865;&#x4f4d;</strong>&#xff1a;&#x4f18;&#x5316;&#x4e86;&#x65b0;&#x5efa;&#x4ea7;&#x54c1;&#x56fe;&#x7247;&#x65f6;&#x7684;&#x7f16;&#x53f7;&#x903b;&#x8f91;&#xff0c;&#x73b0;&#x5728;&#x4f1a;&#x667a;&#x80fd;&#x5bfb;&#x627e;&#x6700;&#x5c0f;&#x53ef;&#x7528;&#x7684;&#x7f16;&#x53f7;&#x8fdb;&#x884c;&#x8865;&#x4f4d;&#xff0c;&#x800c;&#x4e0d;&#x662f;&#x7b80;&#x5355;&#x5730;&#x9012;&#x589e;&#xff0c;&#x6709;&#x6548;&#x5229;&#x7528;&#x7f16;&#x53f7;&#x7a7a;&#x7f3a;&#x3002;","children":[],"payload":{"tag":"li","lines":"130,132"}}],"payload":{"tag":"h3","lines":"124,125"}},{"content":"&#x1f4ca; &#x5b8c;&#x6574;&#x65e5;&#x5fd7;&#x7cfb;&#x7edf;","children":[{"content":"&#x7a0b;&#x5e8f;&#x8fd0;&#x884c;&#x65e5;&#x5fd7;&#x81ea;&#x52a8;&#x4fdd;&#x5b58;&#x5230;<code>data/logs/app.log</code>&#x6587;&#x4ef6;&#x3002;","children":[],"payload":{"tag":"li","lines":"133,134"}},{"content":"&#x65e5;&#x5fd7;&#x540c;&#x65f6;&#x8f93;&#x51fa;&#x5230;&#x63a7;&#x5236;&#x53f0;&#x548c;&#x6587;&#x4ef6;&#xff0c;&#x65b9;&#x4fbf;&#x8c03;&#x8bd5;&#x3002;","children":[],"payload":{"tag":"li","lines":"134,135"}},{"content":"&#x5bfc;&#x5165;&#x8fc7;&#x7a0b;&#x8be6;&#x7ec6;&#x65e5;&#x5fd7;&#x8bb0;&#x5f55;&#xff0c;&#x4fbf;&#x4e8e;&#x95ee;&#x9898;&#x8bca;&#x65ad;&#x3002;","children":[],"payload":{"tag":"li","lines":"135,136"}},{"content":"&#x5b8c;&#x6574;&#x7684;&#x9519;&#x8bef;&#x5806;&#x6808;&#x4fe1;&#x606f;&#x8bb0;&#x5f55;&#x3002;","children":[],"payload":{"tag":"li","lines":"136,137"}},{"content":"&#x81ea;&#x52a8;&#x65e5;&#x5fd7;&#x8f6e;&#x8f6c;&#xff0c;&#x7ba1;&#x7406;&#x6587;&#x4ef6;&#x5927;&#x5c0f;&#x3002;","children":[],"payload":{"tag":"li","lines":"137,139"}}],"payload":{"tag":"h3","lines":"132,133"}},{"content":"&#x1f3a8; &#x7528;&#x6237;&#x4f53;&#x9a8c;&#x4f18;&#x5316;","children":[{"content":"&#x5bfc;&#x5165;&#x8fc7;&#x7a0b;&#x663e;&#x793a;&#x8be6;&#x7ec6;&#x7684;&#x8fdb;&#x5ea6;&#x548c;&#x72b6;&#x6001;&#x4fe1;&#x606f;&#x3002;","children":[],"payload":{"tag":"li","lines":"140,141"}},{"content":"&#x66f4;&#x53cb;&#x597d;&#x7684;&#x9519;&#x8bef;&#x63d0;&#x793a;&#x548c;&#x786e;&#x8ba4;&#x673a;&#x5236;&#x3002;","children":[],"payload":{"tag":"li","lines":"141,142"}},{"content":"&#x5220;&#x9664;&#x5bfc;&#x51fa;&#x7248;&#x672c;&#x9700;&#x8981;&#x7528;&#x6237;&#x786e;&#x8ba4;&#xff0c;&#x907f;&#x514d;&#x8bef;&#x64cd;&#x4f5c;&#x3002;","children":[],"payload":{"tag":"li","lines":"142,143"}},{"content":"&#x5bfc;&#x5165;&#x5bfc;&#x51fa;&#x754c;&#x9762;&#x54cd;&#x5e94;&#x66f4;&#x6d41;&#x7545;&#x3002;","children":[],"payload":{"tag":"li","lines":"143,145"}}],"payload":{"tag":"h3","lines":"139,140"}},{"content":"&#x1f527; &#x6280;&#x672f;&#x6539;&#x8fdb;","children":[{"content":"&#x65e5;&#x5fd7;&#x914d;&#x7f6e;&#x7cfb;&#x7edf;&#xff1a;&#x5b8c;&#x6574;&#x7684;&#x65e5;&#x5fd7;&#x914d;&#x7f6e;&#x7ba1;&#x7406;&#xff0c;&#x652f;&#x6301;&#x7ea7;&#x522b;&#x548c;&#x683c;&#x5f0f;&#x81ea;&#x5b9a;&#x4e49;&#x3002;","children":[],"payload":{"tag":"li","lines":"146,147"}},{"content":"&#x8def;&#x5f84;&#x5904;&#x7406;&#x4f18;&#x5316;&#xff1a;&#x6539;&#x8fdb;&#x6587;&#x4ef6;&#x8def;&#x5f84;&#x5904;&#x7406;&#xff0c;&#x63d0;&#x5347;&#x8de8;&#x5e73;&#x53f0;&#x517c;&#x5bb9;&#x6027;&#x3002;","children":[],"payload":{"tag":"li","lines":"147,148"}},{"content":"&#x9519;&#x8bef;&#x5904;&#x7406;&#x589e;&#x5f3a;&#xff1a;&#x66f4;&#x5b8c;&#x5584;&#x7684;&#x5f02;&#x5e38;&#x5904;&#x7406;&#x673a;&#x5236;&#x3002;","children":[],"payload":{"tag":"li","lines":"148,149"}},{"content":"&#x6570;&#x636e;&#x9a8c;&#x8bc1;&#xff1a;&#x5bfc;&#x5165;&#x524d;&#x9a8c;&#x8bc1;&#x6570;&#x636e;&#x5b8c;&#x6574;&#x6027;&#xff0c;&#x907f;&#x514d;&#x6570;&#x636e;&#x635f;&#x574f;&#x3002;","children":[],"payload":{"tag":"li","lines":"149,151"}}],"payload":{"tag":"h3","lines":"145,146"}}],"payload":{"tag":"h2","lines":"110,111"}},{"content":"&#x65b0;&#x7248;&#x672c;&#x7279;&#x6027; (v2.1.6)","children":[{"content":"&#x1f3f7;&#xfe0f; &#x6807;&#x7b7e;&#x7cfb;&#x7edf;&#x5168;&#x9762;&#x5347;&#x7ea7;","children":[{"content":"&#x65b0;&#x589e;&#x4ea7;&#x54c1;&#x6807;&#x7b7e;&#x529f;&#x80fd;&#xff0c;&#x652f;&#x6301;&#x5feb;&#x6377;&#x7b5b;&#x9009;&#x548c;&#x5206;&#x7c7b;&#x7ba1;&#x7406;","children":[],"payload":{"tag":"li","lines":"154,155"}},{"content":"&#x6807;&#x7b7e;&#x5feb;&#x6377;&#x533a;&#x57df;&#xff0c;&#x70b9;&#x51fb;&#x6807;&#x7b7e;&#x5373;&#x53ef;&#x7b5b;&#x9009;&#x5bf9;&#x5e94;&#x4ea7;&#x54c1;","children":[],"payload":{"tag":"li","lines":"155,156"}},{"content":"&#x6807;&#x7b7e;&#x7ba1;&#x7406;&#x754c;&#x9762;&#xff0c;&#x652f;&#x6301;&#x521b;&#x5efa;&#x3001;&#x7f16;&#x8f91;&#x3001;&#x5220;&#x9664;&#x6807;&#x7b7e;","children":[],"payload":{"tag":"li","lines":"156,157"}},{"content":"&#x4ea7;&#x54c1;&#x5361;&#x7247;&#x76f4;&#x63a5;&#x663e;&#x793a;&#x6807;&#x7b7e;&#xff0c;&#x4fbf;&#x4e8e;&#x5feb;&#x901f;&#x8bc6;&#x522b;","children":[],"payload":{"tag":"li","lines":"157,158"}},{"content":"&#x53f3;&#x952e;&#x83dc;&#x5355;&#x5feb;&#x901f;&#x6dfb;&#x52a0;&#x6807;&#x7b7e;&#xff0c;&#x63d0;&#x5347;&#x4f7f;&#x7528;&#x6548;&#x7387;","children":[],"payload":{"tag":"li","lines":"158,160"}}],"payload":{"tag":"h3","lines":"153,154"}},{"content":"&#x1f4c1; &#x5b8c;&#x6574;&#x5bfc;&#x51fa;&#x5bfc;&#x5165;&#x529f;&#x80fd;","children":[{"content":"&#x5b8c;&#x6574;&#x7684;&#x6570;&#x636e;&#x5bfc;&#x51fa;&#x529f;&#x80fd;&#xff0c;&#x5305;&#x542b;&#x4ea7;&#x54c1;&#x3001;&#x8d27;&#x6e90;&#x3001;&#x56fe;&#x7247;&#x3001;&#x6807;&#x7b7e;&#x7b49;&#x6240;&#x6709;&#x6570;&#x636e;","children":[],"payload":{"tag":"li","lines":"161,162"}},{"content":"&#x667a;&#x80fd;&#x6587;&#x4ef6;&#x5939;&#x7ed3;&#x6784;&#xff0c;&#x6570;&#x636e;&#x548c;&#x56fe;&#x7247;&#x5206;&#x79bb;&#xff0c;&#x4fbf;&#x4e8e;&#x7ba1;&#x7406;","children":[],"payload":{"tag":"li","lines":"162,163"}},{"content":"&#x81ea;&#x52a8;&#x751f;&#x6210;&#x8bf4;&#x660e;&#x6587;&#x4ef6;&#xff0c;&#x65b9;&#x4fbf;&#x7406;&#x89e3;&#x5bfc;&#x51fa;&#x5185;&#x5bb9;","children":[],"payload":{"tag":"li","lines":"163,164"}},{"content":"&#x5b8c;&#x6574;&#x7684;&#x6570;&#x636e;&#x5bfc;&#x5165;&#x529f;&#x80fd;&#xff0c;&#x652f;&#x6301;&#x8de8;&#x8bbe;&#x5907;&#x6570;&#x636e;&#x8fc1;&#x79fb;","children":[],"payload":{"tag":"li","lines":"164,165"}},{"content":"&#x8fdb;&#x5ea6;&#x663e;&#x793a;&#xff0c;&#x5bfc;&#x5165;&#x5bfc;&#x51fa;&#x8fc7;&#x7a0b;&#x53ef;&#x89c6;&#x5316;","children":[],"payload":{"tag":"li","lines":"165,167"}}],"payload":{"tag":"h3","lines":"160,161"}},{"content":"&#x1f3a8; &#x754c;&#x9762;&#x7f8e;&#x5316;&#x4f18;&#x5316;","children":[{"content":"&#x6807;&#x7b7e;&#x6837;&#x5f0f;&#x5168;&#x9762;&#x5347;&#x7ea7;&#xff0c;&#x73b0;&#x4ee3;&#x5316;&#x6e10;&#x53d8;&#x6548;&#x679c;","children":[],"payload":{"tag":"li","lines":"168,169"}},{"content":"&#x6807;&#x7b7e;&#x4f4d;&#x7f6e;&#x4f18;&#x5316;&#xff0c;&#x4e0e;&#x8d27;&#x6e90;&#x4fe1;&#x606f;&#x5bf9;&#x9f50;&#x663e;&#x793a;","children":[],"payload":{"tag":"li","lines":"169,170"}},{"content":"&#x6697;&#x9ed1;&#x6a21;&#x5f0f;&#x4e0b;&#x6309;&#x94ae;&#x6587;&#x5b57;&#x663e;&#x793a;&#x4fee;&#x590d;","children":[],"payload":{"tag":"li","lines":"170,171"}},{"content":"&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x5e03;&#x5c40;&#x4f18;&#x5316;&#xff0c;&#x4fe1;&#x606f;&#x663e;&#x793a;&#x66f4;&#x52a0;&#x6e05;&#x6670;","children":[],"payload":{"tag":"li","lines":"171,172"}},{"content":"&#x72b6;&#x6001;&#x680f;&#x56fe;&#x6807;&#x5316;&#x663e;&#x793a;&#xff0c;&#x63d0;&#x5347;&#x89c6;&#x89c9;&#x6548;&#x679c;","children":[],"payload":{"tag":"li","lines":"172,174"}}],"payload":{"tag":"h3","lines":"167,168"}},{"content":"&#x1f527; &#x6570;&#x636e;&#x5904;&#x7406;&#x4f18;&#x5316;","children":[{"content":"&#x4fee;&#x590d;&#x8d27;&#x6e90;&#x6570;&#x91cf;&#x7edf;&#x8ba1;&#x95ee;&#x9898;&#xff0c;&#x786e;&#x4fdd;&#x6b63;&#x786e;&#x663e;&#x793a;","children":[],"payload":{"tag":"li","lines":"175,176"}},{"content":"&#x4f18;&#x5316;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x52a0;&#x8f7d;&#x6027;&#x80fd;&#xff0c;&#x6b63;&#x786e;&#x52a0;&#x8f7d;&#x5173;&#x8054;&#x6570;&#x636e;","children":[],"payload":{"tag":"li","lines":"176,177"}},{"content":"&#x6807;&#x7b7e;&#x7b5b;&#x9009;&#x529f;&#x80fd;&#xff0c;&#x652f;&#x6301;&#x591a;&#x6807;&#x7b7e;&#x7ec4;&#x5408;&#x7b5b;&#x9009;","children":[],"payload":{"tag":"li","lines":"177,178"}},{"content":"&#x6570;&#x636e;&#x7ed3;&#x6784;&#x5b8c;&#x5584;&#xff0c;&#x652f;&#x6301;&#x5b8c;&#x6574;&#x7684;&#x6807;&#x7b7e;&#x5173;&#x8054;&#x5173;&#x7cfb;","children":[],"payload":{"tag":"li","lines":"178,180"}}],"payload":{"tag":"h3","lines":"174,175"}}],"payload":{"tag":"h2","lines":"151,152"}},{"content":"&#x65b0;&#x7248;&#x672c;&#x7279;&#x6027; (v2.1.1)","children":[{"content":"&#x1f527; &#x6210;&#x672c;&#x8ba1;&#x7b97;&#x4f18;&#x5316;","children":[{"content":"&#x6839;&#x636e;&#x8d27;&#x6e90;&#x6a21;&#x5f0f;&#x667a;&#x80fd;&#x8c03;&#x6574;&#x6210;&#x672c;&#x8ba1;&#x7b97;&#x903b;&#x8f91;","children":[],"payload":{"tag":"li","lines":"183,184"}},{"content":"&#x6279;&#x53d1;&#x6a21;&#x5f0f;&#xff1a;&#x6b63;&#x5e38;&#x663e;&#x793a;&#x603b;&#x6210;&#x672c;&#x548c;&#x542b;&#x8fd0;&#x8d39;&#x603b;&#x6210;&#x672c;","children":[],"payload":{"tag":"li","lines":"184,185"}},{"content":"&#x4ee3;&#x53d1;&#x6a21;&#x5f0f;&#xff1a;&#x4e0d;&#x663e;&#x793a;&#x603b;&#x6210;&#x672c;&#xff0c;&#x53ea;&#x8ba1;&#x7b97;&#x5355;&#x4e2a;&#x5229;&#x6da6;","children":[],"payload":{"tag":"li","lines":"185,186"}},{"content":"&#x4ee3;&#x53d1;&#x5229;&#x6da6;&#x903b;&#x8f91;&#xff1a;&#x7406;&#x8bba;&#x6210;&#x672c;&#x4e3a;0&#xff0c;&#x552e;&#x4ef7;&#x8fc7;&#x4f4e;&#x65f6;&#x663e;&#x793a;&#x4e8f;&#x635f;&#x8b66;&#x793a;","children":[],"payload":{"tag":"li","lines":"186,188"}}],"payload":{"tag":"h3","lines":"182,183"}},{"content":"&#x1f3a8; &#x754c;&#x9762;&#x4f18;&#x5316;","children":[{"content":"&#x4ee3;&#x53d1;&#x6a21;&#x5f0f;&#x6807;&#x7b7e;&#x4f7f;&#x7528;&#x6a58;&#x8272;&#x9ad8;&#x4eae;&#x663e;&#x793a;&#xff0c;&#x4fbf;&#x4e8e;&#x533a;&#x5206;","children":[],"payload":{"tag":"li","lines":"189,190"}},{"content":"&#x5229;&#x6da6;&#x663e;&#x793a;&#x6839;&#x636e;&#x6a21;&#x5f0f;&#x8c03;&#x6574;&#xff08;&quot;&#x4ee3;&#x53d1;&#x5229;&#x6da6;&quot; vs &quot;&#x5229;&#x6da6;&quot;&#xff09;","children":[],"payload":{"tag":"li","lines":"190,191"}},{"content":"&#x4e8f;&#x635f;&#x63d0;&#x793a;&#x66f4;&#x52a0;&#x660e;&#x786e;","children":[],"payload":{"tag":"li","lines":"191,193"}}],"payload":{"tag":"h3","lines":"188,189"}}],"payload":{"tag":"h2","lines":"180,181"}},{"content":"&#x65b0;&#x7248;&#x672c;&#x7279;&#x6027; (v2.1.0)","children":[{"content":"&#x2728; &#x589e;&#x5f3a;&#x4ea7;&#x54c1;&#x4fe1;&#x606f;","children":[{"content":"&#x65b0;&#x589e;&#x5546;&#x54c1;&#x63cf;&#x8ff0;&#x5b57;&#x6bb5;&#xff0c;&#x652f;&#x6301;&#x8be6;&#x7ec6;&#x7684;&#x4ea7;&#x54c1;&#x63cf;&#x8ff0;","children":[],"payload":{"tag":"li","lines":"196,197"}},{"content":"&#x65b0;&#x589e;&#x9500;&#x91cf;&#x53c2;&#x8003;&#x5b57;&#x6bb5;&#xff0c;&#x4fbf;&#x4e8e;&#x9009;&#x54c1;&#x51b3;&#x7b56;","children":[],"payload":{"tag":"li","lines":"197,199"}}],"payload":{"tag":"h3","lines":"195,196"}},{"content":"&#x1f3ea; &#x6539;&#x8fdb;&#x8d27;&#x6e90;&#x7ba1;&#x7406;","children":[{"content":"&#x65b0;&#x589e;&quot;&#x6253;&#x5f00;&#x7f51;&#x7ad9;&quot;&#x6309;&#x94ae;&#xff0c;&#x4e00;&#x952e;&#x8bbf;&#x95ee;&#x8d27;&#x6e90;&#x94fe;&#x63a5;","children":[],"payload":{"tag":"li","lines":"200,201"}},{"content":"&#x542b;&#x8fd0;&#x8d39;&#x4fe1;&#x606f;&#x4ee5;&#x6a58;&#x7ea2;&#x8272;&#x9ad8;&#x4eae;&#x663e;&#x793a;","children":[],"payload":{"tag":"li","lines":"201,203"}}],"payload":{"tag":"h3","lines":"199,200"}},{"content":"&#x1f527; &#x6570;&#x636e;&#x5e93;&#x5347;&#x7ea7;","children":[{"content":"&#x81ea;&#x52a8;&#x68c0;&#x6d4b;&#x5e76;&#x6dfb;&#x52a0;&#x65b0;&#x5b57;&#x6bb5;","children":[],"payload":{"tag":"li","lines":"204,205"}},{"content":"&#x4fdd;&#x6301;&#x5411;&#x540e;&#x517c;&#x5bb9;&#x6027;","children":[],"payload":{"tag":"li","lines":"205,207"}}],"payload":{"tag":"h3","lines":"203,204"}},{"content":"&#x1f3a8; &#x754c;&#x9762;&#x4f18;&#x5316;","children":[{"content":"&#x6539;&#x8fdb;&#x4ea7;&#x54c1;&#x8be6;&#x60c5;&#x9875;&#x9762;&#x5e03;&#x5c40;","children":[],"payload":{"tag":"li","lines":"208,209"}},{"content":"&#x4f18;&#x5316;&#x8d27;&#x6e90;&#x4fe1;&#x606f;&#x663e;&#x793a;&#x6548;&#x679c;","children":[],"payload":{"tag":"li","lines":"209,211"}}],"payload":{"tag":"h3","lines":"207,208"}}],"payload":{"tag":"h2","lines":"193,194"}},{"content":"&#x9879;&#x76ee;&#x7ed3;&#x6784;","children":[{"content":"<pre data-lines=\"213,244\"><code data-lines=\"213,244\">New<span class=\"hljs-symbol\">/</span>\n&#x251c;&#x2500;&#x2500; main.py                 <span class=\"hljs-comment\"># &#x5e94;&#x7528;&#x7a0b;&#x5e8f;&#x5165;&#x53e3;</span>\n&#x251c;&#x2500;&#x2500; config.py              <span class=\"hljs-comment\"># &#x914d;&#x7f6e;&#x7ba1;&#x7406;</span>\n&#x251c;&#x2500;&#x2500; requirements.txt       <span class=\"hljs-comment\"># &#x4f9d;&#x8d56;&#x5305;&#x5217;&#x8868;</span>\n&#x251c;&#x2500;&#x2500; start.bat             <span class=\"hljs-comment\"># Windows&#x542f;&#x52a8;&#x811a;&#x672c;</span>\n&#x251c;&#x2500;&#x2500; README.md             <span class=\"hljs-comment\"># &#x9879;&#x76ee;&#x8bf4;&#x660e;</span>\n&#x251c;&#x2500;&#x2500; CHANGELOG.md          <span class=\"hljs-comment\"># &#x7248;&#x672c;&#x66f4;&#x65b0;&#x8bb0;&#x5f55;</span>\n&#x251c;&#x2500;&#x2500; &#x4f7f;&#x7528;&#x6307;&#x5357;.md           <span class=\"hljs-comment\"># &#x8be6;&#x7ec6;&#x4f7f;&#x7528;&#x6307;&#x5357;</span>\n&#x251c;&#x2500;&#x2500; models<span class=\"hljs-symbol\">/</span>               <span class=\"hljs-comment\"># &#x6570;&#x636e;&#x6a21;&#x578b;&#x5c42;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; __init__.py\n&#x2502;   &#x251c;&#x2500;&#x2500; product.py        <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x6a21;&#x578b;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; source.py         <span class=\"hljs-comment\"># &#x8d27;&#x6e90;&#x6a21;&#x578b;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; custom_field.py   <span class=\"hljs-comment\"># &#x81ea;&#x5b9a;&#x4e49;&#x5b57;&#x6bb5;&#x6a21;&#x578b;</span>\n&#x2502;   &#x2514;&#x2500;&#x2500; database.py       <span class=\"hljs-comment\"># &#x6570;&#x636e;&#x5e93;&#x7ba1;&#x7406;&#x5668;</span>\n&#x251c;&#x2500;&#x2500; views<span class=\"hljs-symbol\">/</span>                <span class=\"hljs-comment\"># &#x89c6;&#x56fe;&#x5c42;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; __init__.py\n&#x2502;   &#x251c;&#x2500;&#x2500; main_window.py    <span class=\"hljs-comment\"># &#x4e3b;&#x7a97;&#x53e3;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; product_list.py   <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x5217;&#x8868;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; product_detail.py <span class=\"hljs-comment\"># &#x4ea7;&#x54c1;&#x8be6;&#x60c5;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; comparison_view.py <span class=\"hljs-comment\"># &#x5bf9;&#x6bd4;&#x89c6;&#x56fe;</span>\n&#x2502;   &#x251c;&#x2500;&#x2500; image_viewer.py   <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x67e5;&#x770b;&#x5668;</span>\n&#x2502;   &#x2514;&#x2500;&#x2500; dialogs<span class=\"hljs-symbol\">/</span>          <span class=\"hljs-comment\"># &#x5bf9;&#x8bdd;&#x6846;</span>\n&#x2502;       &#x251c;&#x2500;&#x2500; __init__.py\n&#x2502;       &#x251c;&#x2500;&#x2500; product_dialog.py\n&#x2502;       &#x251c;&#x2500;&#x2500; source_dialog.py\n&#x2502;       &#x2514;&#x2500;&#x2500; custom_field_dialog.py\n&#x2514;&#x2500;&#x2500; data<span class=\"hljs-symbol\">/</span>                 <span class=\"hljs-comment\"># &#x6570;&#x636e;&#x5b58;&#x50a8;&#x76ee;&#x5f55;</span>\n    &#x251c;&#x2500;&#x2500; database.db       <span class=\"hljs-comment\"># SQLite&#x6570;&#x636e;&#x5e93;</span>\n    &#x2514;&#x2500;&#x2500; images<span class=\"hljs-symbol\">/</span>           <span class=\"hljs-comment\"># &#x56fe;&#x7247;&#x6587;&#x4ef6;</span>\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"213,244"}}],"payload":{"tag":"h2","lines":"211,212"}},{"content":"&#x6280;&#x672f;&#x7279;&#x70b9;","children":[{"content":"&#x67b6;&#x6784;&#x8bbe;&#x8ba1;","children":[{"content":"<strong>&#x5206;&#x5c42;&#x67b6;&#x6784;</strong>&#xff1a;&#x6a21;&#x578b;&#x5c42;&#x3001;&#x89c6;&#x56fe;&#x5c42;&#x3001;&#x670d;&#x52a1;&#x5c42;&#x6e05;&#x6670;&#x5206;&#x79bb;","children":[],"payload":{"tag":"li","lines":"248,249"}},{"content":"<strong>&#x76f4;&#x63a5;SQLite&#x64cd;&#x4f5c;</strong>&#xff1a;&#x907f;&#x514d;ORM&#x590d;&#x6742;&#x6027;&#xff0c;&#x63d0;&#x5347;&#x6027;&#x80fd;","children":[],"payload":{"tag":"li","lines":"249,250"}},{"content":"<strong>&#x4fe1;&#x53f7;&#x69fd;&#x673a;&#x5236;</strong>&#xff1a;&#x7ec4;&#x4ef6;&#x95f4;&#x677e;&#x8026;&#x5408;&#x901a;&#x4fe1;","children":[],"payload":{"tag":"li","lines":"250,251"}},{"content":"<strong>&#x914d;&#x7f6e;&#x5316;&#x7ba1;&#x7406;</strong>&#xff1a;&#x7edf;&#x4e00;&#x7684;&#x914d;&#x7f6e;&#x6587;&#x4ef6;&#x7ba1;&#x7406;","children":[],"payload":{"tag":"li","lines":"251,253"}}],"payload":{"tag":"h3","lines":"247,248"}},{"content":"&#x6027;&#x80fd;&#x4f18;&#x5316;","children":[{"content":"<strong>&#x5ef6;&#x8fdf;&#x52a0;&#x8f7d;</strong>&#xff1a;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x4e0d;&#x52a0;&#x8f7d;&#x5173;&#x8054;&#x6570;&#x636e;","children":[],"payload":{"tag":"li","lines":"254,255"}},{"content":"<strong>&#x5185;&#x5b58;&#x9ad8;&#x6548;</strong>&#xff1a;&#x53ca;&#x65f6;&#x91ca;&#x653e;&#x4e0d;&#x7528;&#x7684;&#x8d44;&#x6e90;","children":[],"payload":{"tag":"li","lines":"255,256"}},{"content":"<strong>&#x54cd;&#x5e94;&#x5f0f;UI</strong>&#xff1a;&#x5f02;&#x6b65;&#x64cd;&#x4f5c;&#xff0c;&#x907f;&#x514d;&#x754c;&#x9762;&#x5361;&#x987f;","children":[],"payload":{"tag":"li","lines":"256,257"}},{"content":"<strong>&#x641c;&#x7d22;&#x4f18;&#x5316;</strong>&#xff1a;500ms&#x5ef6;&#x8fdf;&#x641c;&#x7d22;&#xff0c;&#x51cf;&#x5c11;&#x9891;&#x7e41;&#x67e5;&#x8be2;","children":[],"payload":{"tag":"li","lines":"257,259"}}],"payload":{"tag":"h3","lines":"253,254"}},{"content":"&#x6570;&#x636e;&#x5b89;&#x5168;","children":[{"content":"<strong>&#x5916;&#x952e;&#x7ea6;&#x675f;</strong>&#xff1a;&#x4fdd;&#x8bc1;&#x6570;&#x636e;&#x5b8c;&#x6574;&#x6027;","children":[],"payload":{"tag":"li","lines":"260,261"}},{"content":"<strong>&#x4e8b;&#x52a1;&#x5904;&#x7406;</strong>&#xff1a;&#x786e;&#x4fdd;&#x6570;&#x636e;&#x4e00;&#x81f4;&#x6027;","children":[],"payload":{"tag":"li","lines":"261,262"}},{"content":"<strong>&#x9519;&#x8bef;&#x5904;&#x7406;</strong>&#xff1a;&#x5b8c;&#x5584;&#x7684;&#x5f02;&#x5e38;&#x6355;&#x83b7;&#x673a;&#x5236;","children":[],"payload":{"tag":"li","lines":"262,263"}},{"content":"<strong>&#x6570;&#x636e;&#x5907;&#x4efd;</strong>&#xff1a;&#x652f;&#x6301;&#x6570;&#x636e;&#x5bfc;&#x5165;&#x5bfc;&#x51fa;","children":[],"payload":{"tag":"li","lines":"263,265"}}],"payload":{"tag":"h3","lines":"259,260"}}],"payload":{"tag":"h2","lines":"245,246"}},{"content":"&#x5f00;&#x53d1;&#x8ba1;&#x5212;","children":[{"content":"&#x2705; &#x7b2c;&#x4e00;&#x9636;&#x6bb5;&#xff1a;&#x57fa;&#x7840;&#x6846;&#x67b6;","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x9879;&#x76ee;&#x7ed3;&#x6784;&#x642d;&#x5efa;","children":[],"payload":{"tag":"li","lines":"268,269"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x6570;&#x636e;&#x6a21;&#x578b;&#x8bbe;&#x8ba1;","children":[],"payload":{"tag":"li","lines":"269,270"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x6570;&#x636e;&#x5e93;&#x7ba1;&#x7406;","children":[],"payload":{"tag":"li","lines":"270,271"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x4e3b;&#x7a97;&#x53e3;&#x6846;&#x67b6;","children":[],"payload":{"tag":"li","lines":"271,273"}}],"payload":{"tag":"h3","lines":"267,268"}},{"content":"&#x2705; &#x7b2c;&#x4e8c;&#x9636;&#x6bb5;&#xff1a;&#x6838;&#x5fc3;&#x529f;&#x80fd;","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x4ea7;&#x54c1;&#x7ba1;&#x7406;&#xff08;&#x589e;&#x5220;&#x6539;&#x67e5;&#xff09;","children":[],"payload":{"tag":"li","lines":"274,275"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x57fa;&#x7840;&#x754c;&#x9762;&#x7ec4;&#x4ef6;","children":[],"payload":{"tag":"li","lines":"275,276"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x641c;&#x7d22;&#x548c;&#x6392;&#x5e8f;&#x529f;&#x80fd;","children":[],"payload":{"tag":"li","lines":"276,278"}}],"payload":{"tag":"h3","lines":"273,274"}},{"content":"&#x2705; &#x7b2c;&#x4e09;&#x9636;&#x6bb5;&#xff1a;&#x9ad8;&#x7ea7;&#x529f;&#x80fd;","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x8d27;&#x6e90;&#x7ba1;&#x7406;&#x5b8c;&#x5584;","children":[],"payload":{"tag":"li","lines":"279,280"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x56fe;&#x7247;&#x8f6e;&#x64ad;&#x7ec4;&#x4ef6;","children":[],"payload":{"tag":"li","lines":"280,281"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x81ea;&#x5b9a;&#x4e49;&#x5b57;&#x6bb5;&#x7ba1;&#x7406;","children":[],"payload":{"tag":"li","lines":"281,282"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x6570;&#x636e;&#x5bfc;&#x5165;&#x5bfc;&#x51fa;","children":[],"payload":{"tag":"li","lines":"282,284"}}],"payload":{"tag":"h3","lines":"278,279"}},{"content":"&#x2705; &#x7b2c;&#x56db;&#x9636;&#x6bb5;&#xff1a;&#x4f18;&#x5316;&#x5b8c;&#x5584;","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x6027;&#x80fd;&#x4f18;&#x5316;","children":[],"payload":{"tag":"li","lines":"285,286"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x754c;&#x9762;&#x7f8e;&#x5316;","children":[],"payload":{"tag":"li","lines":"286,287"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x9519;&#x8bef;&#x5904;&#x7406;&#x5b8c;&#x5584;","children":[],"payload":{"tag":"li","lines":"287,288"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x529f;&#x80fd;&#x6d4b;&#x8bd5;","children":[],"payload":{"tag":"li","lines":"288,290"}}],"payload":{"tag":"h3","lines":"284,285"}},{"content":"&#x2705; &#x7b2c;&#x4e94;&#x9636;&#x6bb5;&#xff1a;&#x589e;&#x5f3a;&#x529f;&#x80fd; (v2.1.0)","children":[{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x5546;&#x54c1;&#x63cf;&#x8ff0;&#x5b57;&#x6bb5;","children":[],"payload":{"tag":"li","lines":"291,292"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x9500;&#x91cf;&#x53c2;&#x8003;&#x5b57;&#x6bb5;","children":[],"payload":{"tag":"li","lines":"292,293"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x8d27;&#x6e90;&#x7f51;&#x7ad9;&#x94fe;&#x63a5;","children":[],"payload":{"tag":"li","lines":"293,294"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x8fd0;&#x8d39;&#x663e;&#x793a;&#x4f18;&#x5316;","children":[],"payload":{"tag":"li","lines":"294,295"}},{"content":"<svg width=\"16\" height=\"16\" viewBox=\"0 -3 24 24\"><path d=\"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"/></svg> &#x6570;&#x636e;&#x5e93;&#x81ea;&#x52a8;&#x5347;&#x7ea7;","children":[],"payload":{"tag":"li","lines":"295,297"}}],"payload":{"tag":"h3","lines":"290,291"}}],"payload":{"tag":"h2","lines":"265,266"}},{"content":"&#x8bb8;&#x53ef;&#x8bc1;","children":[],"payload":{"tag":"h2","lines":"297,298"}},{"content":"&#x8d21;&#x732e;&#x6307;&#x5357;","children":[{"content":"&#x5f00;&#x53d1;&#x73af;&#x5883;&#x8bbe;&#x7f6e;","children":[{"content":"<pre data-lines=\"306,321\"><code class=\"language-bash\"><span class=\"hljs-comment\"># &#x514b;&#x9686;&#x9879;&#x76ee;</span>\ngit <span class=\"hljs-built_in\">clone</span> &lt;repository-url&gt;\n\n<span class=\"hljs-comment\"># &#x521b;&#x5efa;&#x865a;&#x62df;&#x73af;&#x5883;</span>\npython -m venv venv\n<span class=\"hljs-built_in\">source</span> venv/bin/activate  <span class=\"hljs-comment\"># Linux/Mac</span>\nvenv\\Scripts\\activate     <span class=\"hljs-comment\"># Windows</span>\n\n<span class=\"hljs-comment\"># &#x5b89;&#x88c5;&#x4f9d;&#x8d56;</span>\npip install -r requirements.txt\n\n<span class=\"hljs-comment\"># &#x8fd0;&#x884c;&#x5e94;&#x7528;</span>\npython main.py\n</code></pre>","children":[],"payload":{"tag":"pre","lines":"306,321"}}],"payload":{"tag":"h3","lines":"305,306"}}],"payload":{"tag":"h2","lines":"301,302"}},{"content":"&#x95ee;&#x9898;&#x53cd;&#x9988;","children":[{"content":"&#x64cd;&#x4f5c;&#x7cfb;&#x7edf;&#x7248;&#x672c;","children":[],"payload":{"tag":"li","lines":"325,326"}},{"content":"Python&#x7248;&#x672c;","children":[],"payload":{"tag":"li","lines":"326,327"}},{"content":"&#x9519;&#x8bef;&#x4fe1;&#x606f;&#x622a;&#x56fe;","children":[],"payload":{"tag":"li","lines":"327,328"}},{"content":"&#x590d;&#x73b0;&#x6b65;&#x9aa4;","children":[],"payload":{"tag":"li","lines":"328,330"}}],"payload":{"tag":"h2","lines":"322,323"}},{"content":"&#x66f4;&#x65b0;&#x65e5;&#x5fd7;","children":[{"content":"v2.1.1 (2025-01-XX)","children":[{"content":"&#x1f527; &#x6839;&#x636e;&#x8d27;&#x6e90;&#x6a21;&#x5f0f;&#x4f18;&#x5316;&#x6210;&#x672c;&#x8ba1;&#x7b97;&#x903b;&#x8f91;","children":[],"payload":{"tag":"li","lines":"333,334"}},{"content":"&#x1f3a8; &#x4ee3;&#x53d1;&#x6a21;&#x5f0f;&#x754c;&#x9762;&#x6807;&#x8bc6;&#x4f18;&#x5316;","children":[],"payload":{"tag":"li","lines":"334,335"}},{"content":"&#x1f4a1; &#x4ee3;&#x53d1;&#x5229;&#x6da6;&#x8ba1;&#x7b97;&#x7cbe;&#x51c6;&#x5316;","children":[],"payload":{"tag":"li","lines":"335,336"}},{"content":"&#x26a0;&#xfe0f; &#x4e8f;&#x635f;&#x98ce;&#x9669;&#x63d0;&#x793a;&#x589e;&#x5f3a;","children":[],"payload":{"tag":"li","lines":"336,338"}}],"payload":{"tag":"h3","lines":"332,333"}},{"content":"v2.1.0 (2025-01-XX)","children":[{"content":"&#x2728; &#x65b0;&#x589e;&#x5546;&#x54c1;&#x63cf;&#x8ff0;&#x5b57;&#x6bb5;","children":[],"payload":{"tag":"li","lines":"339,340"}},{"content":"&#x2728; &#x65b0;&#x589e;&#x9500;&#x91cf;&#x53c2;&#x8003;&#x5b57;&#x6bb5;","children":[],"payload":{"tag":"li","lines":"340,341"}},{"content":"&#x2728; &#x65b0;&#x589e;&#x8d27;&#x6e90;&#x7f51;&#x7ad9;&#x94fe;&#x63a5;&#x529f;&#x80fd;","children":[],"payload":{"tag":"li","lines":"341,342"}},{"content":"&#x1f3a8; &#x4f18;&#x5316;&#x542b;&#x8fd0;&#x8d39;&#x663e;&#x793a;&#x6548;&#x679c;","children":[],"payload":{"tag":"li","lines":"342,343"}},{"content":"&#x1f527; &#x6570;&#x636e;&#x5e93;&#x81ea;&#x52a8;&#x5347;&#x7ea7;&#x529f;&#x80fd;","children":[],"payload":{"tag":"li","lines":"343,344"}},{"content":"&#x1f4da; &#x5b8c;&#x5584;&#x6587;&#x6863;&#x548c;&#x4f7f;&#x7528;&#x6307;&#x5357;","children":[],"payload":{"tag":"li","lines":"344,346"}}],"payload":{"tag":"h3","lines":"338,339"}},{"content":"v2.0.8 (2025-07-05)","children":[{"content":"&#x1f5bc;&#xfe0f; &#x56fe;&#x7247;&#x7ba1;&#x7406;&#x4f18;&#x5316;","children":[],"payload":{"tag":"li","lines":"347,348"}},{"content":"&#x1f41b; &#x4fee;&#x590d;&#x56fe;&#x7247;&#x8986;&#x76d6;&#x95ee;&#x9898;","children":[],"payload":{"tag":"li","lines":"348,349"}},{"content":"&#x1f527; &#x63d0;&#x5347;&#x7cfb;&#x7edf;&#x7a33;&#x5b9a;&#x6027;","children":[],"payload":{"tag":"li","lines":"349,351"}}],"payload":{"tag":"h3","lines":"346,347"}},{"content":"v2.0.7 (2024-12-28)","children":[{"content":"&#x1f41b; &#x4fee;&#x590d;&#x6697;&#x9ed1;&#x4e3b;&#x9898;&#x663e;&#x793a;&#x95ee;&#x9898;","children":[],"payload":{"tag":"li","lines":"352,353"}},{"content":"&#x1f3a8; &#x4f18;&#x5316;&#x4ea7;&#x54c1;&#x5217;&#x8868;&#x6837;&#x5f0f;","children":[],"payload":{"tag":"li","lines":"353,354"}},{"content":"&#x1f4da; &#x66f4;&#x65b0;&#x6587;&#x6863;","children":[],"payload":{"tag":"li","lines":"354,356"}}],"payload":{"tag":"h3","lines":"351,352"}}],"payload":{"tag":"h2","lines":"330,331"}}],"payload":{"tag":"h1","lines":"0,1"}},{})</script>
</body>
</html>
