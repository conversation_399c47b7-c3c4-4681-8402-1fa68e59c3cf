"""
图片轮播组件 - 支持同时显示三张图片
专门用于产品图片的轮播展示
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QFrame,
    QGraphicsOpacityEffect,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QPixmap, QFont, QPainter, QPen
import os
import logging
from utils.image_utils import load_image_safely


class ImageCarousel(QWidget):
    """图片轮播组件 - 支持同时显示三张图片"""

    # 信号定义
    image_changed = pyqtSignal(int)  # 图片改变
    image_clicked = pyqtSignal(str)  # 图片被点击

    def __init__(self, image_paths: list = None, auto_play: bool = False):
        super().__init__()
        self.image_paths = image_paths or []
        self.current_index = 0
        self.auto_play = auto_play
        self.auto_play_interval = 3000  # 3秒

        # 创建三个图片标签
        self.image_labels = []  # 存储三个图片位置的标签
        self.image_frames = []  # 存储三个图片框架

        self.setup_ui()
        self.setup_timer()
        self.update_display()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 主显示区域 - 水平布局支持三张图片
        self.display_container = QFrame()
        self.display_container.setStyleSheet(
            """
            QFrame {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                background: #ffffff;
            }
        """
        )
        self.display_container.setMinimumHeight(350)  # 增加容器高度

        # 三张图片的水平布局
        self.images_layout = QHBoxLayout(self.display_container)
        self.images_layout.setContentsMargins(10, 10, 10, 10)
        self.images_layout.setSpacing(10)

        # 创建三个图片位置
        for i in range(3):
            # 图片框架
            frame = QFrame()
            frame.setStyleSheet(
                """
                QFrame {
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    background: #f8f8f8;
                }
            """
            )
            frame.setMinimumSize(200, 250)  # 增加最小尺寸

            frame_layout = QVBoxLayout(frame)
            frame_layout.setContentsMargins(5, 5, 5, 5)

            # 图片标签
            image_label = QLabel()
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label.setStyleSheet(
                """
                QLabel {
                    border: none;
                    background: transparent;
                    border-radius: 4px;
                }
            """
            )
            image_label.setMinimumSize(180, 230)  # 设置图片标签的最小尺寸
            image_label.mousePressEvent = lambda event, idx=i: self.on_image_clicked(
                event, idx
            )

            frame_layout.addWidget(image_label)

            self.image_frames.append(frame)
            self.image_labels.append(image_label)
            self.images_layout.addWidget(frame)

        layout.addWidget(self.display_container)

        # 控制栏
        controls_layout = QHBoxLayout()
        controls_layout.setContentsMargins(10, 5, 10, 5)

        # 上一张按钮
        self.prev_button = QPushButton("◀ 上一组")
        self.prev_button.setFixedHeight(30)
        self.prev_button.setStyleSheet(
            """
            QPushButton {
                border: 1px solid #ccc;
                border-radius: 4px;
                background: #ffffff;
                font-size: 12px;
                font-weight: bold;
                padding: 4px 12px;
            }
            QPushButton:hover {
                background: #f0f0f0;
            }
            QPushButton:disabled {
                background: #f5f5f5;
                color: #ccc;
            }
        """
        )
        self.prev_button.clicked.connect(self.previous_group)
        controls_layout.addWidget(self.prev_button)

        # 播放/暂停按钮
        self.play_button = QPushButton("▶ 自动播放")
        self.play_button.setFixedHeight(30)
        self.play_button.setStyleSheet(
            """
            QPushButton {
                border: 1px solid #1976d2;
                border-radius: 4px;
                background: #1976d2;
                color: white;
                font-size: 12px;
                padding: 4px 12px;
            }
            QPushButton:hover {
                background: #1565c0;
            }
        """
        )
        self.play_button.clicked.connect(self.toggle_auto_play)
        controls_layout.addWidget(self.play_button)

        # 下一张按钮
        self.next_button = QPushButton("下一组 ▶")
        self.next_button.setFixedHeight(30)
        self.next_button.setStyleSheet(
            """
            QPushButton {
                border: 1px solid #ccc;
                border-radius: 4px;
                background: #ffffff;
                font-size: 12px;
                font-weight: bold;
                padding: 4px 12px;
            }
            QPushButton:hover {
                background: #f0f0f0;
            }
            QPushButton:disabled {
                background: #f5f5f5;
                color: #ccc;
            }
        """
        )
        self.next_button.clicked.connect(self.next_group)
        controls_layout.addWidget(self.next_button)

        controls_layout.addStretch()

        # 图片计数器
        self.counter_label = QLabel("0 / 0")
        self.counter_label.setStyleSheet("font-size: 12px; font-weight: bold;")
        controls_layout.addWidget(self.counter_label)

        controls_layout.addStretch()

        # 指示器区域
        self.indicators_layout = QHBoxLayout()
        self.indicators = []
        controls_layout.addLayout(self.indicators_layout)

        layout.addLayout(controls_layout)

    def setup_timer(self):
        """设置定时器"""
        self.auto_timer = QTimer()
        self.auto_timer.timeout.connect(self.auto_next_group)

    def set_images(self, image_paths: list):
        """设置图片列表"""
        self.image_paths = image_paths or []
        self.current_index = 0
        self.update_display()
        self.create_indicators()
        # 延迟更新显示，确保布局已经完成
        QTimer.singleShot(50, self.update_display)

    def get_display_layout(self):
        """根据图片数量获取显示布局"""
        image_count = len(self.image_paths)

        if image_count == 0:
            return []
        elif image_count == 1:
            # 1张图片：显示在中间位置
            return [None, 0, None]
        elif image_count == 2:
            # 2张图片：显示在位置1、2
            return [0, 1, None]
        else:
            # 3张及以上：显示连续的3张
            start_idx = self.current_index
            indices = []
            for i in range(3):
                idx = (start_idx + i) % image_count
                indices.append(idx)
            return indices

    def update_display(self):
        """更新显示"""
        # 先隐藏所有图片框架
        for frame in self.image_frames:
            frame.hide()

        # 清空所有图片标签
        for label in self.image_labels:
            label.clear()
            label.setText("")

        if not self.image_paths:
            # 显示中间的框架，显示"暂无图片"
            self.image_frames[1].show()
            self.image_labels[1].setText("暂无图片")
            self.counter_label.setText("0 / 0")
            self.prev_button.setEnabled(False)
            self.next_button.setEnabled(False)
            self.play_button.setEnabled(False)
            return

        # 获取显示布局
        layout = self.get_display_layout()

        # 根据布局显示图片
        for pos, img_idx in enumerate(layout):
            if img_idx is not None:
                self.image_frames[pos].show()
                self.load_image_to_label(self.image_labels[pos], img_idx)
            else:
                self.image_frames[pos].hide()

        # 更新计数器
        if len(self.image_paths) <= 3:
            self.counter_label.setText(f"{len(self.image_paths)} 张图片")
        else:
            group_start = self.current_index + 1
            group_end = min(self.current_index + 3, len(self.image_paths))
            self.counter_label.setText(
                f"{group_start}-{group_end} / {len(self.image_paths)}"
            )

        # 更新按钮状态
        image_count = len(self.image_paths)
        if image_count <= 3:
            # 图片少于等于3张，不需要翻页
            self.prev_button.setEnabled(False)
            self.next_button.setEnabled(False)
        else:
            # 图片超过3张，支持翻页
            self.prev_button.setEnabled(True)
            self.next_button.setEnabled(True)

        self.play_button.setEnabled(image_count > 3)  # 只有超过3张才启用自动播放

        # 更新指示器
        self.update_indicators()

        # 发出信号
        self.image_changed.emit(self.current_index)

    def load_image_to_label(self, label, img_idx):
        """加载图片到标签"""
        if img_idx >= len(self.image_paths):
            label.setText("图片不存在")
            return

        image_path = self.image_paths[img_idx]

        # 使用统一的图片加载函数
        pixmap = load_image_safely(image_path)

        if pixmap.isNull():
            label.setText("图片加载失败")
            # 只在DEBUG级别记录错误，避免控制台输出过多信息
            logging.debug(f"图片加载失败: {image_path}")
        else:
            # 缩放图片以适应标签，尽可能填充可用空间
            label_size = label.size()
            if label_size.width() > 10 and label_size.height() > 10:
                # 使用KeepAspectRatio，让图片尽可能填充标签
                scaled_pixmap = pixmap.scaled(
                    label_size,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation,
                )
                label.setPixmap(scaled_pixmap)
            else:
                # 使用更大的默认尺寸，让图片更好地填充空间
                scaled_pixmap = pixmap.scaled(
                    200,
                    250,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation,
                )
                label.setPixmap(scaled_pixmap)

    def create_indicators(self):
        """创建指示器"""
        # 清除现有指示器
        for indicator in self.indicators:
            indicator.deleteLater()
        self.indicators.clear()

        image_count = len(self.image_paths)

        # 如果图片少于等于3张，不需要指示器
        if image_count <= 3:
            return

        # 计算需要多少个指示器（按组计算）
        group_count = (image_count + 2) // 3  # 向上取整

        for i in range(group_count):
            indicator = QPushButton()
            indicator.setFixedSize(8, 8)
            indicator.setStyleSheet(
                """
                QPushButton {
                    border-radius: 4px;
                    background: #ccc;
                    border: none;
                }
                QPushButton:hover {
                    background: #999;
                }
            """
            )
            indicator.clicked.connect(lambda checked, group=i: self.goto_group(group))
            self.indicators.append(indicator)
            self.indicators_layout.addWidget(indicator)

    def update_indicators(self):
        """更新指示器状态"""
        if len(self.image_paths) <= 3:
            return

        current_group = self.current_index // 3

        for i, indicator in enumerate(self.indicators):
            if i == current_group:
                indicator.setStyleSheet(
                    """
                    QPushButton {
                        border-radius: 4px;
                        background: #1976d2;
                        border: none;
                    }
                """
                )
            else:
                indicator.setStyleSheet(
                    """
                    QPushButton {
                        border-radius: 4px;
                        background: #ccc;
                        border: none;
                    }
                    QPushButton:hover {
                        background: #999;
                    }
                """
                )

    def previous_group(self):
        """上一组图片"""
        if len(self.image_paths) > 3:
            self.current_index = max(0, self.current_index - 3)
            self.update_display()

    def next_group(self):
        """下一组图片"""
        if len(self.image_paths) > 3:
            self.current_index = min(len(self.image_paths) - 3, self.current_index + 3)
            self.update_display()

    def goto_group(self, group: int):
        """跳转到指定组"""
        if len(self.image_paths) > 3:
            self.current_index = group * 3
            self.current_index = min(self.current_index, len(self.image_paths) - 3)
            self.update_display()

    def auto_next_group(self):
        """自动下一组图片"""
        if len(self.image_paths) > 3:
            if self.current_index < len(self.image_paths) - 3:
                self.next_group()
            else:
                # 循环到第一组
                self.current_index = 0
                self.update_display()

    def toggle_auto_play(self):
        """切换自动播放"""
        if self.auto_timer.isActive():
            self.stop_auto_play()
        else:
            self.start_auto_play()

    def start_auto_play(self):
        """开始自动播放"""
        if len(self.image_paths) > 3:
            self.auto_timer.start(self.auto_play_interval)
            self.play_button.setText("⏸ 暂停播放")
            self.auto_play = True

    def stop_auto_play(self):
        """停止自动播放"""
        self.auto_timer.stop()
        self.play_button.setText("▶ 自动播放")
        self.auto_play = False

    def set_auto_play_interval(self, interval: int):
        """设置自动播放间隔（毫秒）"""
        self.auto_play_interval = interval
        if self.auto_timer.isActive():
            self.auto_timer.setInterval(interval)

    def on_image_clicked(self, event, position):
        """图片被点击"""
        if not self.image_paths:
            return

        layout = self.get_display_layout()

        if position < len(layout) and layout[position] is not None:
            img_idx = layout[position]
            if img_idx < len(self.image_paths):
                image_path = self.image_paths[img_idx]
                self.image_clicked.emit(image_path)

    def get_current_image_path(self):
        """获取当前中间位置的图片路径"""
        layout = self.get_display_layout()

        # 优先返回中间位置的图片
        if len(layout) > 1 and layout[1] is not None:
            idx = layout[1]
            if idx < len(self.image_paths):
                return self.image_paths[idx]

        # 如果中间位置没有图片，返回第一张可见的图片
        for img_idx in layout:
            if img_idx is not None and img_idx < len(self.image_paths):
                return self.image_paths[img_idx]

        return ""

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 延迟重新缩放图片，确保布局已经完成
        QTimer.singleShot(100, self.update_display)
