/*
 * 暗黑模式样式
 */

/* 全局样式重置 */
* {
    color: #ffffff;
}

/* 主窗口 */
QMainWindow {
    background-color: #1e1e1e;
    color: #ffffff;
}

/* 中央部件 */
QWidget#centralWidget {
    background-color: #1e1e1e;
}

/* 左侧面板 */
QWidget#leftPanel {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
}

/* 通用控件 */
QWidget {
    background-color: transparent;
    color: #ffffff;
    font-family: "Microsoft YaHei UI";
}

/* 主要容器背景 */
QMainWindow, 
QDialog,
QFrame {
    background-color: #1e1e1e;
}

/* 按钮样式 */
QPushButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QPushButton:pressed {
    background-color: #505050;
}

QPushButton:disabled {
    background-color: #252525;
    color: #666666;
    border-color: #333333;
}

/* 工具栏 */
QToolBar {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    padding: 4px;
    spacing: 4px;
}

QToolBar::separator {
    background-color: #404040;
    width: 1px;
    height: 16px;
    margin: 0 4px;
}

QToolBar QToolButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    min-width: 80px;
}

QToolBar QToolButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QToolBar QToolButton:pressed {
    background-color: #505050;
}

/* 菜单栏 */
QMenuBar {
    background-color: #2d2d2d;
    color: #ffffff;
    border-bottom: 1px solid #404040;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
}

QMenuBar::item:selected {
    background-color: #404040;
    border-radius: 4px;
}

QMenuBar::item:pressed {
    background-color: #505050;
}

/* 菜单 */
QMenu {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 2px;
}

QMenu::item {
    background-color: transparent;
    padding: 6px 24px;
    border-radius: 4px;
    margin: 1px;
}

QMenu::item:selected {
    background-color: #404040;
}

QMenu::separator {
    height: 1px;
    background-color: #404040;
    margin: 4px 0;
}

/* 状态栏 */
QStatusBar {
    background-color: #2d2d2d;
    color: #ffffff;
    border-top: 1px solid #404040;
}

QStatusBar::item {
    border: none;
}

QStatusBar QLabel {
    color: #ffffff;
    padding: 2px;
}

/* 分割器 */
QSplitter {
    background-color: #1e1e1e;
}

QSplitter::handle {
    background-color: #404040;
    border: 1px solid #404040;
    width: 3px;
    height: 3px;
    border-radius: 1px;
}

QSplitter::handle:hover {
    background-color: #2196f3;
}

/* 标签页 */
QTabWidget::pane {
    background-color: #2d2d2d;
    border: 1px solid #404040;
}

QTabBar::tab {
    background-color: #2d2d2d;
    color: #ffffff;
    padding: 8px 16px;
    border: 1px solid #404040;
    border-bottom: none;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #404040;
}

QTabBar::tab:hover {
    background-color: #404040;
}

/* 列表控件 */
QListWidget {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 2px;
}

QListWidget::item {
    border: none;
    padding: 2px;
}

QListWidget::item:selected {
    background-color: #404040;
    color: #ffffff;
}

QListWidget::item:hover {
    background-color: #353535;
}

/* 表格控件 */
QTableWidget {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    gridline-color: #404040;
    alternate-background-color: #333333;
}

QTableWidget::item {
    padding: 4px;
    border-bottom: 1px solid #404040;
}

QTableWidget::item:selected {
    background-color: #2196f3;
    color: #ffffff;
}

QHeaderView::section {
    background-color: #404040;
    color: #ffffff;
    padding: 6px;
    border: 1px solid #505050;
    font-weight: bold;
}

/* 输入框样式 */
QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
    background-color: #2a2a2a;
    color: #ffffff;
    border: 1px solid #3a3a3a;
    border-radius: 4px;
    padding: 4px 8px;
    selection-background-color: #2979ff;
    selection-color: #ffffff;
}

/* 输入框悬停状态 */
QLineEdit:hover, QTextEdit:hover, QSpinBox:hover, QDoubleSpinBox:hover {
    background-color: #323232;
    border: 1px solid #454545;
}

/* 输入框焦点状态 */
QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
    background-color: #323232;
    border: 1px solid #2979ff;
    outline: none;
}

/* 输入框禁用状态 */
QLineEdit:disabled, QTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled {
    background-color: #222222;
    color: #666666;
    border: 1px solid #2a2a2a;
}

/* SpinBox按钮样式 */
QSpinBox::up-button, QDoubleSpinBox::up-button,
QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #3a3a3a;
    border: none;
    border-radius: 2px;
    margin: 1px;
    width: 16px;
    height: 16px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #454545;
}

QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed,
QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
    background-color: #2979ff;
}

/* SpinBox箭头图标 */
QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
    width: 0;
    height: 0;
    border: 4px solid transparent;
    border-bottom: 6px solid #ffffff;
    margin: 0 2px;
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    width: 0;
    height: 0;
    border: 4px solid transparent;
    border-top: 6px solid #ffffff;
    margin: 0 2px;
}

QSpinBox::up-arrow:hover, QDoubleSpinBox::up-arrow:hover {
    border-bottom-color: #2979ff;
}

QSpinBox::down-arrow:hover, QDoubleSpinBox::down-arrow:hover {
    border-top-color: #2979ff;
}

/* 占位符文本颜色 */
QLineEdit[placeholderText], QTextEdit[placeholderText] {
    color: #666666;
}

/* 文本框 */
QTextEdit {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 6px;
    border-radius: 4px;
    font-size: 12px;
}

QTextEdit:focus {
    border-color: #2196f3;
}

/* 标签 */
QLabel {
    background-color: transparent;
    color: #ffffff;
}

/* 复选框 */
QCheckBox {
    color: #ffffff;
    background-color: transparent;
}

QCheckBox::indicator {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    width: 16px;
    height: 16px;
    border-radius: 2px;
}

QCheckBox::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
}

QCheckBox::indicator:checked:hover {
    background-color: #1976d2;
}

/* 单选按钮 */
QRadioButton {
    color: #ffffff;
    background-color: transparent;
}

QRadioButton::indicator {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    width: 16px;
    height: 16px;
    border-radius: 8px;
}

QRadioButton::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
}

/* 下拉框 */
QComboBox {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 4px 8px;
}

QComboBox:hover {
    border-color: #2196f3;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(resources/icons/down_arrow_white.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    selection-background-color: #404040;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #2d2d2d;
    width: 12px;
    border: none;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #404040;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #505050;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #2d2d2d;
    height: 12px;
    border: none;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #404040;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #505050;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}

/* 进度条 */
QProgressBar {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
    text-align: center;
    color: #ffffff;
}

QProgressBar::chunk {
    background-color: #2196f3;
    border-radius: 3px;
}

/* 对话框 */
QDialog {
    background-color: #1e1e1e;
    color: #ffffff;
}

QMessageBox {
    background-color: #1e1e1e;
    color: #ffffff;
}

QMessageBox QLabel {
    background-color: #1e1e1e;
    color: #ffffff;
}

QMessageBox QPushButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    min-width: 80px;
}

QMessageBox QPushButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QDialogButtonBox QPushButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
}

/* 图片显示容器 */
QFrame#imageDisplayContainer {
    background-color: #1e1e1e;
    border: 2px solid #404040;
    border-radius: 8px;
}

/* 图片框架 */
QFrame[objectName^="imageFrame"] {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 6px;
}

/* 图片标签 */
QLabel[objectName^="imageLabel"] {
    background-color: transparent;
    border: none;
    border-radius: 4px;
    color: #ffffff;
}

/* 图片控制按钮 */
QPushButton#prevButton,
QPushButton#nextButton {
    padding: 8px 16px;
    font-size: 12px;
    font-weight: bold;
    color: #ffffff;
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
}

QPushButton#prevButton:hover,
QPushButton#nextButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QPushButton#prevButton:pressed,
QPushButton#nextButton:pressed {
    background-color: #505050;
}

/* 信息标签 */
QLabel#infoLabel {
    font-size: 12px;
    color: #ffffff;
    font-weight: bold;
    background-color: transparent;
}

/* 标签组件 */
TagWidget {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
}

TagWidget QLabel {
    color: #ffffff;
}

/* 标签按钮 */
QPushButton#tagButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 14px;
    padding: 5px 12px;
    font-size: 11px;
    font-weight: 500;
    margin: 2px;
    min-width: 50px;
}

QPushButton#tagButton[isActive="true"] {
    background-color: #2196f3;
    color: #ffffff;
    border: none;
}

QPushButton#tagButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QPushButton#tagButton[isActive="true"]:hover {
    background-color: #1976d2;
}

QPushButton#tagButton:pressed {
    background-color: #505050;
}

QPushButton#tagButton[isActive="true"]:pressed {
    background-color: #1565c0;
}

/* 标签管理按钮 */
TagWidget QPushButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
}

TagWidget QPushButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

/* 滚动区域 */
QScrollArea {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
}

QScrollArea > QWidget > QWidget {
    background-color: #1e1e1e;
}

/* 标签容器 */
QWidget#tag_container {
    background-color: #1e1e1e;
}

/* 分组框 */
QGroupBox {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    margin-top: 12px;
    padding-top: 6px;
    font-weight: bold;
}

QGroupBox::title {
    color: #ffffff;
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 6px;
    background-color: #2d2d2d;
    border-radius: 2px;
}

/* 特殊样式 */
.welcome-title {
    color: #2196f3;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}

.welcome-version {
    color: #b0b0b0;
    font-size: 14px;
    margin-bottom: 30px;
}

.welcome-description {
    color: #ffffff;
    font-size: 12px;
    line-height: 1.5;
}

.quick-start-btn {
    background-color: #2196f3;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 5px;
    font-weight: bold;
}

.quick-start-btn:hover {
    background-color: #1976d2;
}

.quick-start-btn:pressed {
    background-color: #0d47a1;
}

/* 价格显示 */
.price-profitable {
    color: #4caf50;
    font-weight: bold;
}

.price-loss {
    color: #f44336;
    font-weight: bold;
}

.price-break-even {
    color: #ff9800;
    font-weight: bold;
}

/* 主题切换按钮 */
.theme-toggle-btn {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    min-width: 80px;
    font-weight: bold;
}

.theme-toggle-btn:hover {
    background-color: #404040;
    border-color: #2196f3;
}

.theme-toggle-btn:pressed {
    background-color: #505050;
}

/* 确保所有文本都是白色 */
QFrame {
    color: #ffffff;
}

QSplitter QWidget {
    color: #ffffff;
}

QStackedWidget {
    color: #ffffff;
}

QStackedWidget QWidget {
    color: #ffffff;
}

QStackedWidget QLabel {
    color: #ffffff;
}

/* 欢迎页面特殊样式 */
QWidget QLabel {
    color: #ffffff;
}

QVBoxLayout QLabel {
    color: #ffffff;
}

QHBoxLayout QLabel {
    color: #ffffff;
}

/* 图片轮播组件文字颜色 */
ImageCarousel QLabel {
    color: #ffffff;
}

ImageCarousel QPushButton {
    color: #ffffff;
}

/* 产品详情页面文字颜色 */
ProductDetailWidget QLabel {
    color: #ffffff;
}

/* 所有子控件的文字颜色 */
QWidget * {
    color: #ffffff;
}

/* 特殊的标签样式 */
QLabel[text="图片不存在"] {
    color: #ff6b6b;
}

QLabel[text="图片加载失败"] {
    color: #ff6b6b;
}

/* 价格标签保持原有颜色 */
QLabel.price-profitable {
    color: #4caf50;
}

QLabel.price-loss {
    color: #f44336;
}

QLabel.price-break-even {
    color: #ff9800;
}

/* 图片查看器和标签区域 */
QLabel {
    background-color: transparent;
    color: #ffffff;
}

/* 图片容器 */
QFrame#imageContainer {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
}

/* 图片标签 */
QLabel#imageLabel {
    background-color: #2d2d2d;
    border: none;
    padding: 8px;
}

/* 图片轮播区域 */
QWidget#carouselWidget {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
}

/* 产品信息区域 */
QFrame#productInfoFrame {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 8px;
}

/* 白色背景容器转暗色 */
QWidget#whiteContainer,
QWidget#whiteBackground {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
}

/* 列表区域 */
QListView, QTreeView {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
    color: #ffffff;
}

QListView::item, QTreeView::item {
    background-color: transparent;
    color: #ffffff;
    padding: 4px;
    margin: 2px;
    border-radius: 4px;
}

QListView::item:selected, QTreeView::item:selected {
    background-color: #404040;
    color: #ffffff;
}

QListView::item:hover, QTreeView::item:hover {
    background-color: #353535;
}

/* 分页控件 */
QPushButton#pageButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 30px;
}

QPushButton#pageButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QPushButton#pageButton:pressed {
    background-color: #505050;
}

/* 页码标签 */
QLabel#pageLabel {
    color: #ffffff;
    background-color: transparent;
    padding: 0 8px;
}

/* 搜索框和过滤器 */
QLineEdit#searchBox,
QComboBox#filterBox {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 4px 8px;
}

QLineEdit#searchBox:focus,
QComboBox#filterBox:focus {
    border-color: #2196f3;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background: #2d2d2d;
    width: 12px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #404040;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background: #505050;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0;
    background: none;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* 边框和分隔线 */
QFrame[frameShape="4"],
QFrame[frameShape="HLine"] {
    color: #404040;
}

QFrame[frameShape="5"],
QFrame[frameShape="VLine"] {
    color: #404040;
}

/* 产品列表项 */
ProductListItem {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    margin: 2px;
    padding: 4px;
}

ProductListItem:hover {
    background-color: #404040;
    border-color: #666666;
}

ProductListItem[selected="true"] {
    background-color: #404040;
    border-color: #2196f3;
}

ProductListItem QLabel {
    color: #ffffff;
}

ProductListItem QLabel.name {
    font-weight: bold;
    font-size: 14px;
}

ProductListItem QLabel.price {
    color: #2196f3;
    font-weight: bold;
    font-size: 16px;
}

ProductListItem QLabel.secondary {
    color: #9e9e9e;
    font-size: 12px;
}

/* 标签筛选区域 */
QWidget#tagFilterWidget {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 8px;
}

QWidget#tagFilterWidget QLabel {
    color: #ffffff;
}

/* 图片查看器 */
QWidget#imageViewer {
    background-color: #1e1e1e;
}

QWidget#imageViewerSplitter {
    background-color: #1e1e1e;
}

/* 图片列表区域 */
QWidget#imageListContainer {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
}

QLabel#imageListTitle {
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 5px;
}

QListWidget#imageList {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
    color: #ffffff;
}

QListWidget#imageList::item {
    background-color: #2d2d2d;
    color: #ffffff;
    padding: 4px;
}

QListWidget#imageList::item:selected {
    background-color: #404040;
    border: 1px solid #2196f3;
}

QListWidget#imageList::item:hover {
    background-color: #353535;
}

/* 图片显示区域 */
QWidget#imageDisplayWidget {
    background-color: #1e1e1e;
}

QFrame#imageDisplayContainer {
    background-color: #1e1e1e;
    border: 2px solid #404040;
    border-radius: 8px;
}

QFrame[objectName^="imageFrame"] {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 6px;
}

QLabel[objectName^="imageLabel"] {
    background-color: transparent;
    border: none;
    color: #ffffff;
}

/* 图片控制按钮区域 */
QWidget#imageControlsWidget {
    background-color: transparent;
}

QPushButton#prevButton,
QPushButton#nextButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: bold;
    min-width: 100px;
}

QPushButton#prevButton:hover,
QPushButton#nextButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QPushButton#prevButton:pressed,
QPushButton#nextButton:pressed {
    background-color: #505050;
}

QPushButton#prevButton:disabled,
QPushButton#nextButton:disabled {
    background-color: #252525;
    color: #666666;
    border-color: #333333;
}

QLabel#infoLabel {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
    background-color: transparent;
}

/* 图片操作按钮 */
QPushButton#addImageButton,
QPushButton#removeImageButton,
QPushButton#openFolderButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 11px;
    min-width: 60px;
}

QPushButton#addImageButton:hover,
QPushButton#removeImageButton:hover,
QPushButton#openFolderButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QPushButton#addImageButton:pressed,
QPushButton#removeImageButton:pressed,
QPushButton#openFolderButton:pressed {
    background-color: #505050;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #1e1e1e;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #404040;
    min-height: 20px;
    border-radius: 6px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #505050;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background-color: #1e1e1e;
}

/* 标签筛选区域 */
QFrame#tagFilterFrame,
QWidget#tagFilterArea {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 4px;
}

/* 所有白色背景容器转暗色 */
QFrame, 
QWidget#containerWidget,
QWidget#centralWidget,
QWidget#mainContainer,
QWidget#rightPanel {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
}

/* 分隔线 */
QFrame[frameShape="4"],
QFrame[frameShape="5"],
QFrame[frameShape="HLine"],
QFrame[frameShape="VLine"] {
    color: #404040;
    background-color: #404040;
}

/* 所有标签文本 */
QLabel {
    color: #ffffff;
    background-color: transparent;
}

/* 图片导航按钮 */
QPushButton#prevImageBtn,
QPushButton#nextImageBtn {
    background-color: rgba(45, 45, 45, 0.8);
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 4px 8px;
}

QPushButton#prevImageBtn:hover,
QPushButton#nextImageBtn:hover {
    background-color: rgba(64, 64, 64, 0.9);
    border-color: #2196f3;
}

/* 图片计数标签 */
QLabel#imageCounter {
    color: #ffffff;
    background-color: rgba(45, 45, 45, 0.8);
    border-radius: 4px;
    padding: 2px 8px;
}

/* 管理按钮 */
QPushButton#manageBtn {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 6px 12px;
}

QPushButton#manageBtn:hover {
    background-color: #404040;
    border-color: #2196f3;
}

/* 标签按钮 */
QPushButton[class="tag"] {
    background-color: #404040;
    color: #ffffff;
    border: 1px solid #505050;
    border-radius: 12px;
    padding: 4px 12px;
    margin: 2px;
}

QPushButton[class="tag"]:hover {
    background-color: #505050;
    border-color: #2196f3;
}

QPushButton[class="tag"]:checked {
    background-color: #2196f3;
    border-color: #1976d2;
}

/* 产品列表区域 */
QListWidget#productList {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
    color: #ffffff;
}

QListWidget#productList::item {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    margin: 2px;
    padding: 4px;
}

QListWidget#productList::item:selected {
    background-color: #404040;
    border-color: #2196f3;
}

QListWidget#productList::item:hover {
    background-color: #353535;
    border-color: #666666;
}

/* 标签管理按钮 */
QPushButton#manageButton,
QPushButton#clearButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
}

QPushButton#manageButton:hover,
QPushButton#clearButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

/* 标签区域样式 */
QWidget#tagWidget {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
}

QWidget#tagWidget QPushButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    min-width: 60px;
}

QWidget#tagWidget QPushButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

/* 图片查看区域样式 */
QWidget#imageViewer {
    background-color: #1e1e1e;
}

QLabel#imageLabel {
    background-color: #1e1e1e;
    border: none;
}

/* 图片轮播控件样式 */
QWidget#imageCarousel {
    background-color: #1e1e1e;
}

QWidget#imageCarousel QPushButton {
    background-color: rgba(45, 45, 45, 0.8);
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 3px;
    padding: 4px;
    min-width: 30px;
}

QWidget#imageCarousel QPushButton:hover {
    background-color: rgba(64, 64, 64, 0.9);
    border-color: #2196f3;
}

/* 产品列表区域样式 */
QWidget#productList {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
}

QWidget#productList QScrollArea {
    background-color: transparent;
    border: none;
}

QWidget#productList QWidget {
    background-color: transparent;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #1e1e1e;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #404040;
    min-height: 20px;
    border-radius: 6px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #505050;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background-color: #1e1e1e;
}

/* 搜索框样式 */
QLineEdit {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 4px 8px;
    border-radius: 4px;
}

QLineEdit:focus {
    border-color: #2196f3;
}

/* 下拉框样式 */
QComboBox {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 4px 8px;
    border-radius: 4px;
}

QComboBox:hover {
    border-color: #2196f3;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(resources/icons/down-arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    selection-background-color: #404040;
}

/* 标签组件样式 */
QWidget#tagWidget {
    background-color: #1e1e1e;
    border: 1px solid #404040;
    border-radius: 4px;
}

/* 标签头部区域 */
QWidget#tagHeaderWidget {
    background-color: transparent;
}

QLabel#tagTitleLabel {
    color: #ffffff;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
}

/* 标签管理和清除按钮 */
QPushButton#tagManageButton,
QPushButton#tagClearButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 11px;
}

QPushButton#tagManageButton:hover,
QPushButton#tagClearButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

QPushButton#tagManageButton:pressed,
QPushButton#tagClearButton:pressed {
    background-color: #505050;
}

/* 标签滚动区域 */
QScrollArea#tagScrollArea {
    background-color: transparent;
    border: none;
}

QWidget#tagContainer {
    background-color: transparent;
}

QWidget#tagFlowWidget {
    background-color: transparent;
}

/* 标签按钮样式 */
TagButton {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 12px;
    padding: 4px 12px;
    font-size: 11px;
    min-width: 60px;
}

TagButton:hover {
    background-color: #404040;
    border-color: #2196f3;
}

TagButton:checked {
    background-color: #2196f3;
    border-color: #1976d2;
}

/* 标签右键菜单 */
QMenu {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
}

QMenu::item {
    background-color: transparent;
    padding: 6px 24px;
}

QMenu::item:selected {
    background-color: #404040;
}

QMenu::separator {
    height: 1px;
    background-color: #404040;
    margin: 4px 0;
}

/* 产品详情页面标题样式 */
QLabel[objectName="sectionTitle"] {
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    background-color: transparent;
    border: none;
}

/* 产品名称标题 */
QLabel[objectName="productTitle"] {
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    background-color: transparent;
    border: none;
}

/* 基本信息标签 */
QLabel[objectName="infoLabel"] {
    color: #ffffff;
    background-color: transparent;
    border: none;
}

/* 价格标签 */
QLabel[objectName="priceLabel"] {
    color: #2196f3;
    font-weight: bold;
    font-size: 16px;
    background-color: transparent;
    border: none;
}

/* 销量标签 */
QLabel[objectName="salesLabel"] {
    color: #FF6B35;
    font-weight: bold;
    font-size: 14px;
    margin-top: 5px;
    background-color: transparent;
    border: none;
}

/* 时间标签 */
QLabel[objectName="timeLabel"] {
    color: #b0b0b0;
    font-size: 12px;
    margin-top: 10px;
    background-color: transparent;
    border: none;
} 